-- 2025-08-06 开放平台2.0
DROP TABLE IF EXISTS `mall_open_permission`;
CREATE TABLE `mall_open_permission` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `biz_type` int not null default 1 COMMENT '业务类型，1-供应商 2-试剂',
    `api_list` varchar(1000) COMMENT '接口列表，逗号分隔',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='开放应用权限';

alter table mall_open_permission add unique uk_biz_type(deleted,biz_type);
INSERT INTO mall_open_permission (`biz_type`, `api_list`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES (1, '/app-api/mall/open/common/**,/app-api/mall/open/product/**,/app-api/mall/open/order/**,/app-api/mall/open/delivery/**', '1', '2025-08-07 09:24:46', '1', '2025-08-07 09:31:07', b'0');

-- 开放应用重命名
alter table mall_openapp rename to mall_open_app;

alter table mall_open_app modify column supplier_id bigint comment '供应商ID';
alter table mall_open_app add column memo varchar(200) comment '备注' after ip_white_list;
alter table mall_open_app add column encoding_status tinyint(1) default 0 comment '消息加解密开关' after app_secret;
alter table mall_open_app add column encoding_key varchar(100) comment '消息加解密密钥' after encoding_status;
alter table mall_open_app add column biz_type int default 1 comment '业务类型 1-供应商' after app_key;

-- 2025-08-10 下单白名单
ALTER TABLE mall_basis_config ADD COLUMN order_white_list VARCHAR ( 200 ) DEFAULT NULL COMMENT '提交订单白名单' AFTER order_switch;

-- 2025-08-11 订单表增加发票申请字段
ALTER TABLE trade_order ADD COLUMN invoice_request bit(1) NOT NULL DEFAULT b'0' COMMENT '已申请开票' AFTER invoice_status;

-- 250812 商品运营区域增加置顶SKU字段
alter table product_seo_card add column top_sku varchar(1000) comment '置顶商品ID,逗号分隔' after content;

-- 250814 增加用户登录统计表
DROP TABLE IF EXISTS `mall_stats_user_login`;
CREATE TABLE `mall_stats_user_login` (
	`id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
	`count` bigint not null default 0 COMMENT '用户数',
	`stats_date` date NOT NULL COMMENT '统计日期',
	`tenant_id` bigint NOT NULL COMMENT '租户id',
	`creator` varchar(64) COMMENT '创建者',
	`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`updater` varchar(64) COMMENT '更新者',
	`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	`deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='用户登录数统计';

alter table mall_stats_user_login add index idx_stats_date(deleted,tenant_id,stats_date);
alter table member_user add index id_login_date(deleted,tenant_id,login_date);

-- 订单明细增加商品规格信息
alter table trade_order_item add sku_spec varchar(100) comment '规格，多个逗号分隔' after sku_name;




