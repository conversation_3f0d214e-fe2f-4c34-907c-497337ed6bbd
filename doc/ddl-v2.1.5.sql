-- 2025-05-20 协同订单
DROP TABLE IF EXISTS `trade_team_order`;
CREATE TABLE `trade_team_order` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id` bigint not null COMMENT '用户ID',
    `user_name` varchar(100) not null COMMENT '用户名',
    `dept_id` bigint COMMENT '部门ID',
    `no` varchar(20) not null COMMENT '编号',
    `team_user_no` varchar(30) COMMENT '用户工号',
    `team_user_id` bigint not null COMMENT '用户ID',
    `team_user_name` varchar(100) not null COMMENT '用户名',
    `receiver_address_id` bigint comment '用户地址ID',
    `receiver_name` varchar(50) comment '收件人姓名',
    `receiver_mobile` varchar(20) comment '收件人手机号',
    `receiver_province` bigint comment '省份编码',
    `receiver_province_name` varchar(30) comment '省份名称',
    `receiver_city` bigint comment '城市编码',
    `receiver_city_name` varchar(30) comment '城市名称',
    `receiver_county` bigint comment '区编码',
    `receiver_county_name` varchar(30) comment '区名称',
    `receiver_town` bigint comment '街道编码',
    `receiver_town_name` varchar(30) comment '街道名称',
    `receiver_detail_address` varchar(150) comment '收件人详细地址',
    `product_price` decimal(10,2) not null default 0 comment '商品金额',
    `accepter_name` varchar(50) comment '验收人姓名',
    `accepter_mobile` varchar(30) comment '验收人手机号',
    `accepter_email` varchar(100) comment '验收人邮箱',
    `status` int not null default 0 comment '状态, 0-待处理 1-处理完成 2-已取消',
    `user_remark` varchar(300) comment '下单备注',
    `user_deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否用户删除',
    `submit_time` datetime NOT NULL COMMENT '提交时间',
    `confirm_time` datetime COMMENT '确认时间',
    `reject_time` datetime COMMENT '拒绝时间',
    `complete_time` datetime COMMENT '下单完成时间',
    `cancel_time` datetime COMMENT '取消时间',
    `tenant_id` bigint NOT NULL COMMENT '租户id',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='协同订单';

alter table trade_team_order add unique uk_no(tenant_id,deleted,no);
alter table trade_team_order add index idx_user_id(tenant_id,deleted,user_id);
alter table trade_team_order add index idx_team_user_id(tenant_id,deleted,team_user_id);

DROP TABLE IF EXISTS `trade_team_order_item`;
CREATE TABLE `trade_team_order_item` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `team_order_id` bigint not null COMMENT '用户ID',
    `user_id` bigint not null COMMENT '用户ID',
    `dept_id` bigint COMMENT '部门ID',
    `sku_id` bigint not null COMMENT '商品ID',
    `supplier_name` varchar(100) not null COMMENT '供应商名称',
    `supplier_id` bigint not null COMMENT '供应商ID',
    `pic_url` varchar(250) not null COMMENT '商品图片',
    `sku_name` varchar(200) not null COMMENT '商品名称',
    `count` int not null default 0 comment '商品数量',
    `sku_price` decimal(10,2) not null default 0 comment '价格',
    `sku_total_price` decimal(10,2) not null default 0 comment '商品总价',
    `tenant_id` bigint NOT NULL COMMENT '租户id',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='协同订单项';

alter table trade_team_order_item add index idx_team_order_id(tenant_id,deleted,team_order_id);
alter table trade_team_order_item add index idx_user_id(tenant_id,deleted,user_id);

alter table trade_order add column team_order_id bigint comment '协同订单ID';
alter table trade_order add index idx_team_order_id(tenant_id,deleted,team_order_id);

alter table trade_team_order_item add column category_id int not null comment '商品分类ID' after sku_id;
alter table trade_team_order_item add column full_category_id varchar(100) not null comment '商品分类路径ID' after category_id;

-- 导出任务
DROP TABLE IF EXISTS `export_task`;
CREATE TABLE `export_task` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `business_key` varchar(64) not null COMMENT '业务id，比如账单号、订单号',
    `name` varchar(200) not null COMMENT '名称',
    `type` int not null default 0 comment '类型',
    `progress` int not null default 0 comment '进度',
    `stage` varchar(200) default null COMMENT '阶段',
    `reason` varchar(600) default null COMMENT '失败原因',
    `user_id` bigint not null COMMENT '用户ID',
    `cache_path` varchar(512) default null COMMENT '缓存路径',
    `ext` varchar(50) default null COMMENT '文件扩展名',
    `status` int not null default 0 comment '状态',
    `tenant_id` bigint NOT NULL COMMENT '租户id',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='导出任务';

-- 20250613 商品增加平台上下架状态
alter table product_spu add column platform_status int not null default 1 comment '平台上下架状态,0-下架 1-上架' after status;
alter table product_sku add column platform_status int not null default 1 comment '平台上下架状态,0-下架 1-上架' after seo_status;

-- 基础配置-增加域名关系识别表
DROP TABLE IF EXISTS `mall_basis_domain`;
CREATE TABLE `mall_basis_domain` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `domain` varchar(100) not null COMMENT '域名',
    `tenant_id` bigint NOT NULL COMMENT '租户id',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='域名关系表';

-- 20250614 订单列表线下结算标志
alter table trade_order add column offline_settlement bit(1) not null default 0 comment '线下结算标志' after need_purchase;


-- 20250626 消息增加索引
alter table trade_message add index idx_create_time(tenant_id,deleted,create_time);
alter table trade_message add index idx_message_type(tenant_id,deleted,message_type);
alter table trade_message add index idx_supplier_id(tenant_id,deleted,supplier_id);

DROP TABLE IF EXISTS `mall_supplier_form`;
CREATE TABLE `mall_supplier_form` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id` bigint not null COMMENT '用户ID',
    `enterprise_unified_id` varchar(100) not null COMMENT '社会统一信用代码',
    `enterprise_full_name` varchar(100) not null COMMENT '企业全称',
    `enterprise_short_name` varchar(30) not null COMMENT '企业简称',
    `contact` varchar(30) not null COMMENT '联系人',
    `phone` varchar(30) not null COMMENT '联系电话',
    `remark` varchar(500)  COMMENT '入驻说明',
    `file_legal_rep` json  COMMENT '法人身份证',
    `file_license` json  COMMENT '营业执照',
    `file_qualification` json COMMENT '行业资质证明',
    `file_other` json  COMMENT '补充材料',
    `submit_time` datetime comment '提交时间',
    `approve_time` datetime comment '审批时间',
    `approve_status` int default 0 comment '审批状态',
    `approve_memo` varchar(255) comment '审批备注',
    `approve_by` varchar(50) comment '审批人',
    `tenant_id` bigint NOT NULL COMMENT '租户id',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='供应商入驻申请';

alter table mall_supplier_form add index idx_full_name(tenant_id,deleted,enterprise_full_name);

-- 20250702 基本配置增加后台登录入口
alter table mall_basis_config add column admin_entry_switch bit(1) NOT NULL DEFAULT b'0' COMMENT '是否显示后台登录入口' after cust_service;
alter table mall_basis_config add column admin_entry_url varchar(100)  COMMENT '后台登录地址' after admin_entry_switch;

ALTER TABLE trade_bill ADD COLUMN settlement_way INT NOT NULL DEFAULT 0 COMMENT '结算方式 0-线上结算 1-线下结算' AFTER supplier_name;

-- 20250627 发票列表供应商ID
ALTER TABLE `trade_order_invoice` ADD COLUMN `supplier_id` BIGINT COMMENT '供应商id' AFTER order_id;

-- 20250704 订单确认时间
ALTER TABLE trade_order ADD COLUMN confirm_time datetime DEFAULT NULL COMMENT '订单确认时间' AFTER submit_time;

-- 20250704 订单发票表添加索引
ALTER TABLE trade_delivery DROP INDEX uk_tenant_id;
ALTER TABLE `trade_delivery` ADD COLUMN `supplier_id` BIGINT COMMENT '供应商id' AFTER order_no;
ALTER TABLE `trade_delivery` ADD COLUMN `supplier_name` VARCHAR(50) NULL COMMENT '供应商名称' AFTER supplier_id;
ALTER TABLE `trade_delivery_track` ADD COLUMN `supplier_id` BIGINT COMMENT '供应商id' AFTER num;
ALTER TABLE `trade_delivery_track` ADD COLUMN `supplier_name` VARCHAR(50) NULL COMMENT '供应商名称' AFTER supplier_id;
ALTER TABLE `trade_delivery_product_sku` ADD COLUMN `supplier_id` BIGINT COMMENT '供应商id' AFTER pic_url;
ALTER TABLE `trade_delivery_product_sku` ADD COLUMN `supplier_name` VARCHAR(50) NULL COMMENT '供应商名称' AFTER supplier_id;

-- 20250704 采购单增加项目类型名称
alter table trade_purchase add column project_type_name varchar(200) comment '项目类型名称' after project_type;

-- 20250725 京东分类映射表
DROP TABLE IF EXISTS `mall_vop_category_mapping`;
CREATE TABLE `mall_vop_category_mapping` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `last_category_id` bigint NOT NULL COMMENT '末级分类ID',
  `last_category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '末级分类名称',
  `full_category_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '完整分类id -分隔',
  `full_category_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '完整分类名称',
  `vop_last_category_id` bigint NOT NULL COMMENT 'vop末级分类ID',
  `vop_last_category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'vop末级分类名称',
  `vop_full_category_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'vop完整分类id -分隔',
  `vop_full_category_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'vop完整分类名称',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=70800 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='京东分类映射表';
