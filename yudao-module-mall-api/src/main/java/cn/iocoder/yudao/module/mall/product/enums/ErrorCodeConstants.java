package cn.iocoder.yudao.module.mall.product.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * Product 错误码枚举类
 *
 * product 系统，使用 1-008-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 商品分类相关 1008001000 ============
    ErrorCode CATEGORY_NOT_EXISTS = new ErrorCode(1008001000, "商品分类不存在");
    ErrorCode CATEGORY_PARENT_NOT_EXISTS = new ErrorCode(**********, "父分类不存在");
    ErrorCode CATEGORY_PARENT_NOT_FIRST_LEVEL = new ErrorCode(**********, "父分类不能是二级分类");
    ErrorCode CATEGORY_EXISTS_CHILDREN = new ErrorCode(**********, "存在子分类，无法删除");
    ErrorCode CATEGORY_DISABLED = new ErrorCode(**********, "商品分类({})已禁用，无法使用");
    ErrorCode CATEGORY_ID_EXISTS = new ErrorCode(**********, "商品分类ID已存在");

    // ========== 商品品牌相关编号 1008002000 ==========
    ErrorCode BRAND_NOT_EXISTS = new ErrorCode(1008002000, "品牌不存在");
    ErrorCode BRAND_DISABLED = new ErrorCode(1008002001, "品牌已禁用");
    ErrorCode BRAND_NAME_EXISTS = new ErrorCode(1008002002, "品牌名称已存在");

    // ========== 商品属性项 1008003000 ==========
    ErrorCode PROPERTY_NOT_EXISTS = new ErrorCode(1008003000, "属性项不存在");
    ErrorCode PROPERTY_EXISTS = new ErrorCode(1008003001, "属性项的名称已存在");
    ErrorCode PROPERTY_DELETE_FAIL_VALUE_EXISTS = new ErrorCode(1008003002, "属性项下存在属性值，无法删除");

    // ========== 商品属性值 1008004000 ==========
    ErrorCode PROPERTY_VALUE_NOT_EXISTS = new ErrorCode(1008004000, "属性值不存在");
    ErrorCode PROPERTY_VALUE_EXISTS = new ErrorCode(1008004001, "属性值的名称已存在");

    // ========== 商品 SPU 1008005000 ==========
    ErrorCode SPU_NOT_EXISTS = new ErrorCode(1008005000, "商品 SPU 不存在");
    ErrorCode SPU_SAVE_FAIL_CATEGORY_LEVEL_ERROR = new ErrorCode(1008005001, "商品分类不正确，原因：必须使用第三级的商品分类下");
    ErrorCode SPU_NOT_ENABLE = new ErrorCode(1008005002, "商品 SPU 不处于上架状态");

    // ========== 商品 SKU 1008006000 ==========
    ErrorCode SKU_NOT_EXISTS = new ErrorCode(1008006000, "商品 SKU 不存在");
    ErrorCode SKU_PROPERTIES_DUPLICATED = new ErrorCode(1008006001, "商品 SKU 的规格组合存在重复");
    ErrorCode SPU_ATTR_NUMBERS_MUST_BE_EQUALS = new ErrorCode(1008006002, "一个 SPU 下的每个 SKU，其规格项必须一致");
    ErrorCode SPU_SKU_NOT_DUPLICATE = new ErrorCode(1008006003, "一个 SPU 下的每个 SKU，必须不重复");
    ErrorCode SKU_STOCK_NOT_ENOUGH = new ErrorCode(1008006004, "商品 SKU 库存不足");

    ErrorCode SKU_FREIGHT_ERROR = new ErrorCode(1008006006, "商品获取运费失败:{}");
    ErrorCode PRODUCT_SPEC_EXISTS_ERROR = new ErrorCode(1008006007, "商品规格已存在:{}");

    ErrorCode AFTER_SALE_NOT_SUPPORT = new ErrorCode(1008006008, "非京东商品暂不支持售后");
    ErrorCode AFTER_SALE_NOT_SUPPORT_GIFT = new ErrorCode(1008006009, "赠品不支持售后");

    ErrorCode SKU_LOWEST_COUNT = new ErrorCode(1008006012, "商品最小购买数量必须大于{}");

    ErrorCode SUPPLIER_SKU_MAX_COUNT = new ErrorCode(1008006015, "当前供应商最大可添加商品数量不能超过{}");

    ErrorCode SUPPLIER_ON_SALE_SKU_MAX_COUNT = new ErrorCode(1008006016, "当前供应商最大可添加上架商品数量不能超过{}");

    ErrorCode SKU_SEO_IMPORT_EMPTY = new ErrorCode(1008006050, "seo导入商品不能为空");
    ErrorCode SKU_SEO_IMPORT_SUPPLIER_EMPTY = new ErrorCode(1008006051, "商品供应商不能为空");
    ErrorCode SKU_SEO_IMPORT_SKU_EMPTY = new ErrorCode(1008006052, "商品平台SKU和三方SKU不能同时为空");
    ErrorCode SKU_SEO_IMPORT_INNIT_SALES_COUNT_EMPTY = new ErrorCode(1008006054, "商品初始销量不能为空");
    ErrorCode SKU_TAG_IMPORT_TAG_EMPTY = new ErrorCode(1008006055, "标签不能为空");
    ErrorCode SKU_SEO_IMPORT_INNIT_SALES_INVALID= new ErrorCode(1008006056, "商品初始销量值无效，必须为自然数");

    ErrorCode PRODUCT_CATEGORY_IMPORT_VALIDATION_FAIL = new ErrorCode(1008006056, "商品分类校验不通过:{}");

    // ========== 心愿单
    ErrorCode WISH_NOT_EXISTS = new ErrorCode(1008007001, "心愿单不存在");

    ErrorCode COMMENT_NOT_EXISTS = new ErrorCode(**********, "商品评价不存在");
    ErrorCode COMMENT_AUDIT_STATUS_INVALID = new ErrorCode(**********, "评价审核失败，不是【待审核】状态");

    ErrorCode VOP_REQUEST_EXCEPTION = new ErrorCode(**********, "VOP请求异常");
    ErrorCode VOP_REQUEST_DATA_RESULT_EXCEPTION = new ErrorCode(**********, "VOP请求响应异常");
    ErrorCode VOP_TOKEN_NOT_INIT_EXCEPTION = new ErrorCode(**********, "VOP未初始化access_token");
    ErrorCode VOP_ACCOUNT_NOT_INIT_EXCEPTION = new ErrorCode(**********, "VOP未初始化账号信息");

    ErrorCode SKU_SUMMARY_NOT_EXISTS = new ErrorCode(**********, "供应商上架商品数统计不存在");

    ErrorCode TAG_GROUP_NOT_EXISTS = new ErrorCode(**********, "标签分组不存在");
    ErrorCode TAG_NOT_EXISTS = new ErrorCode(**********, "标签不存在");
    ErrorCode TAG_GROUP_DELETE_WITH_TAG = new ErrorCode(**********, "分组包含标签，请删除标签后操作!");
    ErrorCode TAG_ID_EXITS_INVALID = new ErrorCode(**********, "标签无效，请重试");
    ErrorCode TAG_SKU_ID_EXITS_INVALID = new ErrorCode(**********, "商品SKU无效，请重试");

    ErrorCode SEO_CARD_NOT_EXISTS = new ErrorCode(**********, "运营区域不存在");

    // 商品评级及回复
    ErrorCode PRODUCT_COMMENT_NOT_EXISTS = new ErrorCode(**********, "商品评价不存在");
    ErrorCode PRODUCT_COMMENT_REPLY_NOT_EXISTS = new ErrorCode(**********, "商品评价回复不存在");

    ErrorCode VOP_CATEGORY_MAPPING_NOT_EXISTS = new ErrorCode(1008001301, "京东分类映射不存在");
}
