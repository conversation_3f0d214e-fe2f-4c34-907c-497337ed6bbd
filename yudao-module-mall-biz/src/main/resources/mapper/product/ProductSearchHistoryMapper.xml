<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.product.dal.mysql.searchhistory.ProductSearchHistoryMapper">


    <resultMap id="BaseResultMap" type="cn.iocoder.yudao.module.mall.product.dal.dataobject.searchhistory.ProductSearchHistoryDO">
        <id column="id" property="id" />
        <result column="nickname" property="nickname"/>
        <result column="keyword" property="keyword"/>
        <result column="count" property="count"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectSearchHistoryPage" resultMap="BaseResultMap">

    select
        t1.id,
        t2.nickname,
        t1.keyword,
        t1.count,
        t1.update_time
        from product_search_history t1 left join member_user t2 on t1.user_id = t2.id
        <where>
            t1.deleted = 0
            <if test="params.nickname!= null and params.nickname!= ''">
                and t2.nickname like concat('%', #{params.nickname}, '%')
            </if>
            <if test="params.keyword != null and params.keyword != ''">
                and t1.keyword like concat('%', #{params.keyword}, '%')
            </if>
            <if test="params.updateTime != null">
                and t1.update_time between #{params.updateTime[0]} and #{params.updateTime[1]}
            </if>
        </where>
            <if test=" params.sortType != null ">
                <choose>
                    <when test="params.sortType == 1">
                        ORDER BY t1.count DESC
                    </when>
                    <when test="params.sortType == 2">
                        ORDER BY t1.count
                    </when>
                    <otherwise>
                        ORDER BY t1.update_time DESC
                    </otherwise>
                </choose>
            </if>

    </select>




</mapper>