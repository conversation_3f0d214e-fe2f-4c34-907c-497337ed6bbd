<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.product.dal.mysql.sku.ProductSkuMapper">

    <select id="selectSkuDetail2"
            parameterType="cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuReqVO"
            resultType="cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuDetailRespVO">
        select
        t1.*,
        t2.category1_id,
        t2.category2_id,
        t2.category3_id,
        t2.category1_name,
        t2.category2_name,
        t2.category3_name,
        t2.full_category_id,
        t2.full_category_name,
        t2.sort,
        t2.brand_id,
        t2.brand_name,
        t2.description,
        t2.description_h5
        from product_sku t1 left join product_spu t2 on t1.spu_id = t2.id
        <where>
            t1.deleted = 0
            <if test="params.skuId != null">
                and t1.id = #{params.skuId}
            </if>
            <if test="params.supplierId != null">
                and t1.supplier_id = #{params.suplierId}
            </if>
        </where>
    </select>

    <select id="selectPage2"
            parameterType="cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuPageReqVO"
            resultType="cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuPageRespVO">
        select
           t1.*,
           (t1.sales_count + t1.init_sales_count) as totalSalesCount,
           t2.category1_id,
           t2.category2_id,
           t2.category3_id,
           t2.category1_name,
           t2.category2_name,
           t2.category3_name,
           t2.full_category_id,
           t2.full_category_name,
           t2.sort,
           t2.brand_id,
           t2.brand_name
        from product_sku t1 left join product_spu t2 on t1.spu_id = t2.id
        <where>
            t1.deleted = 0
            <if test="params.skuName != null and params.skuName != ''">
                and t1.sku_name like concat('%', #{params.skuName}, '%')
            </if>
            <if test="params.id != null">
                and t1.id = #{params.id}
            </if>
            <if test="params.skuInnerId != null and params.skuInnerId != ''">
                and t1.sku_inner_id = #{params.skuInnerId}
            </if>
            <if test="params.category1Id != null">
                and t2.category1_id = #{params.category1Id}
            </if>
            <if test="params.category2Id != null">
                and t2.category2_id = #{params.category2Id}
            </if>
            <if test="params.category3Id != null">
                and t2.category3_id = #{params.category3Id}
            </if>
            <if test="params.fullCategoryId != null">
                and t2.full_category_id like concat(#{params.fullCategoryId}, '%')
            </if>
            <if test="params.status != null">
                and t1.status = #{params.status}
            </if>
            <if test="params.showStatus != null">
                and t1.show_status = #{params.showStatus}
            </if>
            <if test="params.seoStatus != null">
                and t1.seo_status = #{params.seoStatus}
            </if>
            <if test="params.supplierId != null">
                and t1.supplier_id = #{params.supplierId}
            </if>
            <if test="params.brandId != null">
                and t2.brand_id = #{params.brandId}
            </if>
            <if test="params.createTime != null and params.createTime.length == 2">
                and t1.create_time between #{params.createTime[0]} and #{params.createTime[1]}
            </if>
            <if test="params.skuIds != null and params.skuIds.size() > 0">
                and t1.id in
                <foreach item="itemId" collection="params.skuIds" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="params.tags != null and params.tags.size() > 0">
                and exists (
                    select 1 from product_tag_sku ptk where ptk.sku_id = t1.id and ptk.deleted = 0 and ptk.tag_id in
                    <foreach item="itemId" collection="params.tags" open="(" separator="," close=")">
                        #{itemId}
                    </foreach>
                )
            </if>
            <if test="params.tagStatus != null">
                <choose>
                    <when test="params.tagStatus == 0">
                        and not exists (
                            select 1 from product_tag_sku ptk where ptk.sku_id = t1.id and ptk.deleted = 0
                        )
                    </when>
                    <when test="params.tagStatus == 1">
                        and exists (
                        select 1 from product_tag_sku ptk where ptk.sku_id = t1.id and ptk.deleted = 0
                        )
                    </when>
                </choose>
            </if>
            <if test="params.salesCountMin != null">
                and t1.sales_count <![CDATA[>=]]> #{params.salesCountMin}
            </if>
            <if test="params.salesCountMax != null">
                and t1.sales_count <![CDATA[<=]]> #{params.salesCountMax}
            </if>
            <if test="params.salePriceMin != null">
                and t1.sale_price <![CDATA[>=]]> #{params.salePriceMin}
            </if>
            <if test="params.salePriceMax != null">
                and t1.sale_price <![CDATA[<=]]> #{params.salePriceMax}
            </if>
            <if test="params.sortType!= null">
                <choose>
                    <when test="params.sortType == 10">
                        order by t1.create_time desc
                    </when>
                    <when test="params.sortType == 11">
                        order by t1.create_time
                    </when>
                    <when test="params.sortType == 20">
                        order by t1.sales_count desc
                    </when>
                    <when test="params.sortType == 21">
                        order by t1.sales_count
                    </when>
                    <when test="params.sortType == 30">
                        order by t1.init_sales_count desc
                    </when>
                    <when test="params.sortType == 31">
                        order by t1.init_sales_count
                    </when>
                    <when test="params.sortType == 40">
                        order by t1.sale_price desc
                    </when>
                    <when test="params.sortType == 41">
                        order by t1.sale_price
                    </when>
                    <when test="params.sortType == 50">
                        order by totalSalesCount desc
                    </when>
                    <when test="params.sortType == 51">
                        order by totalSalesCount
                    </when>
                    <otherwise>
                        order by t1.create_time desc
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="selectDeleteSkuPages" resultType="cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO">
        select id from product_sku
        where deleted = 1
        <if test="updateTime != null">
            and update_time >= #{updateTime}
        </if>
    </select>

    <update id="deductInventory">
        update product_sku set stock = stock-#{count}  WHERE sku_id = #{skuId} and stock-#{count} > 0
    </update>

    <select id="getProductSkuStatusResult" resultType="cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuStatusResult">
        select status,count(*) as count from product_sku
        where  deleted = 0
        <if test="supplierId != null">
            and supplier_id= #{supplierId}
        </if>
        GROUP BY status
    </select>


    <select id="selectSpuCategory" resultType="String">
        select t2.full_category_id from product_sku t1, product_spu t2
        where t1.sku_inner_id = #{skuInnerId}
        and t1.supplier_id = #{supplierId}
        and t1.spu_id = t2.id
        and t1.deleted = 0
        limit 1
    </select>

    <select id="getProductCountByCategory" resultType="cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SkuCountCategorySummaryRespVO">
        SELECT
        product_spu.category1_name AS category_name,
        COUNT( product_sku.id ) AS product_count
        FROM
        product_sku
        INNER JOIN product_spu ON product_sku.spu_id = product_spu.id
        WHERE
        product_sku.deleted = 0 AND product_sku.tenant_id = #{tenantId}
        GROUP BY
        product_spu.category1_name
        ORDER BY
        product_count DESC;
    </select>

    <select id="selectSkuId"
            parameterType="cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuPageReqVO"
            resultType="Long">
        select
        t1.id
        from product_sku t1 left join product_spu t2 on t1.spu_id = t2.id
        <where>
            t1.deleted = 0
            <if test="params.category1Id != null">
                and t2.category1_id = #{params.category1Id}
            </if>
            <if test="params.category2Id != null">
                and t2.category2_id = #{params.category2Id}
            </if>
            <if test="params.category3Id != null">
                and t2.category3_id = #{params.category3Id}
            </if>
            <if test="params.fullCategoryId != null">
                and t2.full_category_id like concat(#{params.fullCategoryId}, '%')
            </if>
            <if test="params.supplierId != null">
                and t1.supplier_id = #{params.supplierId}
            </if>
            <if test="params.brandId != null">
                and t2.brand_id = #{params.brandId}
            </if>
        </where>
    </select>

    <select id="selectExportSku"
            parameterType="cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuExportReqVO"
            resultType="cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuExportRespVO">
        SELECT
        row_number() over (order by t1.id) as row_num,
        t1.supplier_id,
        t1.supplier_name,
        t1.id as sku_id,
        t1.sku_inner_id,
        t1.spu_id,
        t1.spu_name,
        t2.brand_name,
        t1.sku_name,
        t1.create_time,
        if(t1.status = 1, '上架', '下架') AS status,
        if(t1.show_status = 1, '显示', '隐藏') as show_status,
        t1.show_status,
        t2.category1_name,
        t2.category2_name,
        t2.category3_name,
        t1.sale_price,
        t1.market_price,
        ( t1.market_price - t1.sale_price ) / t1.market_price AS cost_saving_rate,
        t1.sales_count,
        t1.sales_amount
        FROM product_sku t1
        LEFT JOIN product_spu t2 ON t1.spu_id = t2.id
        <where>
            t1.deleted = 0
            <if test="params.skuName != null and params.skuName != ''">
                AND t1.sku_name LIKE CONCAT('%', #{params.skuName}, '%')
            </if>
            <if test="params.id != null">
                AND t1.id = #{params.id}
            </if>
            <if test="params.skuInnerId != null and params.skuInnerId != ''">
                AND t1.sku_inner_id = #{params.skuInnerId}
            </if>
            <if test="params.category1Id != null">
                AND t2.category1_id = #{params.category1Id}
            </if>
            <if test="params.category2Id != null">
                AND t2.category2_id = #{params.category2Id}
            </if>
            <if test="params.category3Id != null">
                AND t2.category3_id = #{params.category3Id}
            </if>
            <if test="params.fullCategoryId != null">
                AND t2.full_category_id LIKE CONCAT(#{params.fullCategoryId}, '%')
            </if>
            <if test="params.status != null">
                AND t1.status = #{params.status}
            </if>
            <if test="params.showStatus != null">
                AND t1.show_status = #{params.showStatus}
            </if>
            <if test="params.seoStatus != null">
                AND t1.seo_status = #{params.seoStatus}
            </if>
            <if test="params.supplierId != null">
                AND t1.supplier_id = #{params.supplierId}
            </if>
            <if test="params.brandId != null">
                AND t2.brand_id = #{params.brandId}
            </if>
            <if test="params.createTime != null and params.createTime.length == 2">
                AND t1.create_time BETWEEN #{params.createTime[0]} AND #{params.createTime[1]}
            </if>
            <if test="params.tags != null and params.tags.size() > 0">
                AND EXISTS (
                SELECT 1 FROM product_tag_sku ptk
                WHERE ptk.sku_id = t1.id AND ptk.deleted = 0
                AND ptk.tag_id IN
                <foreach item="itemId" collection="params.tags" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
                )
            </if>
            <if test="params.tagStatus != null">
                <choose>
                    <when test="params.tagStatus == 0">
                        AND NOT EXISTS (
                        SELECT 1 FROM product_tag_sku ptk WHERE ptk.sku_id = t1.id AND ptk.deleted = 0
                        )
                    </when>
                    <when test="params.tagStatus == 1">
                        AND EXISTS (
                        SELECT 1 FROM product_tag_sku ptk WHERE ptk.sku_id = t1.id AND ptk.deleted = 0
                        )
                    </when>
                </choose>
            </if>
            <if test="params.salesCountMin != null">
                AND t1.sales_count <![CDATA[>=]]> #{params.salesCountMin}
            </if>
            <if test="params.salesCountMax != null">
                AND t1.sales_count <![CDATA[<=]]> #{params.salesCountMax}
            </if>
            <if test="params.salePriceMin != null">
                AND t1.sale_price <![CDATA[>=]]> #{params.salePriceMin}
            </if>
            <if test="params.salePriceMax != null">
                AND t1.sale_price <![CDATA[<=]]> #{params.salePriceMax}
            </if>
            <if test="params.sortType != null">
                <choose>
                    <when test="params.sortType == 10">
                        ORDER BY t1.create_time DESC
                    </when>
                    <when test="params.sortType == 11">
                        ORDER BY t1.create_time
                    </when>
                    <when test="params.sortType == 20">
                        ORDER BY t1.sales_count DESC
                    </when>
                    <when test="params.sortType == 21">
                        ORDER BY t1.sales_count
                    </when>
                    <when test="params.sortType == 30">
                        ORDER BY t1.init_sales_count DESC
                    </when>
                    <when test="params.sortType == 31">
                        ORDER BY t1.init_sales_count
                    </when>
                    <when test="params.sortType == 40">
                        ORDER BY t1.sale_price DESC
                    </when>
                    <when test="params.sortType == 41">
                        ORDER BY t1.sale_price
                    </when>
                    <otherwise>
                        ORDER BY t1.create_time DESC
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="selectExportSeoSku"
            parameterType="cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuExportReqVO"
            resultType="cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSeoSkuExportRespVO">
        SELECT
        row_number() over (order by t1.id) as row_num,
        t1.supplier_id,
        t1.supplier_name,
        t1.id as sku_id,
        t1.sku_inner_id,
        t1.spu_id,
        t1.spu_name,
        t2.brand_name,
        t1.sku_name,
        t1.create_time,
        if(t1.status = 1, '上架', '下架') AS status,
        if(t1.show_status = 1, '显示', '隐藏') as show_status,
        t1.show_status,
        t2.category1_name,
        t2.category2_name,
        t2.category3_name,
        t1.sale_price,
        t1.market_price,
        ( t1.market_price - t1.sale_price ) / t1.market_price AS cost_saving_rate,
        t1.sales_count,
        t1.init_sales_count,
        t1.sales_amount
        FROM product_sku t1
        LEFT JOIN product_spu t2 ON t1.spu_id = t2.id
        <where>
            t1.deleted = 0
            <if test="params.skuName != null and params.skuName != ''">
                AND t1.sku_name LIKE CONCAT('%', #{params.skuName}, '%')
            </if>
            <if test="params.id != null">
                AND t1.id = #{params.id}
            </if>
            <if test="params.skuInnerId != null and params.skuInnerId != ''">
                AND t1.sku_inner_id = #{params.skuInnerId}
            </if>
            <if test="params.category1Id != null">
                AND t2.category1_id = #{params.category1Id}
            </if>
            <if test="params.category2Id != null">
                AND t2.category2_id = #{params.category2Id}
            </if>
            <if test="params.category3Id != null">
                AND t2.category3_id = #{params.category3Id}
            </if>
            <if test="params.fullCategoryId != null">
                AND t2.full_category_id LIKE CONCAT(#{params.fullCategoryId}, '%')
            </if>
            <if test="params.status != null">
                AND t1.status = #{params.status}
            </if>
            <if test="params.showStatus != null">
                AND t1.show_status = #{params.showStatus}
            </if>
            <if test="params.seoStatus != null">
                AND t1.seo_status = #{params.seoStatus}
            </if>
            <if test="params.supplierId != null">
                AND t1.supplier_id = #{params.supplierId}
            </if>
            <if test="params.brandId != null">
                AND t2.brand_id = #{params.brandId}
            </if>
            <if test="params.createTime != null and params.createTime.length == 2">
                AND t1.create_time BETWEEN #{params.createTime[0]} AND #{params.createTime[1]}
            </if>
            <if test="params.tags != null and params.tags.size() > 0">
                AND EXISTS (
                SELECT 1 FROM product_tag_sku ptk
                WHERE ptk.sku_id = t1.id AND ptk.deleted = 0
                AND ptk.tag_id IN
                <foreach item="itemId" collection="params.tags" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
                )
            </if>
            <if test="params.tagStatus != null">
                <choose>
                    <when test="params.tagStatus == 0">
                        AND NOT EXISTS (
                        SELECT 1 FROM product_tag_sku ptk WHERE ptk.sku_id = t1.id AND ptk.deleted = 0
                        )
                    </when>
                    <when test="params.tagStatus == 1">
                        AND EXISTS (
                        SELECT 1 FROM product_tag_sku ptk WHERE ptk.sku_id = t1.id AND ptk.deleted = 0
                        )
                    </when>
                </choose>
            </if>
            <if test="params.salesCountMin != null">
                AND t1.sales_count <![CDATA[>=]]> #{params.salesCountMin}
            </if>
            <if test="params.salesCountMax != null">
                AND t1.sales_count <![CDATA[<=]]> #{params.salesCountMax}
            </if>
            <if test="params.salePriceMin != null">
                AND t1.sale_price <![CDATA[>=]]> #{params.salePriceMin}
            </if>
            <if test="params.salePriceMax != null">
                AND t1.sale_price <![CDATA[<=]]> #{params.salePriceMax}
            </if>
            <if test="params.sortType != null">
                <choose>
                    <when test="params.sortType == 10">
                        ORDER BY t1.create_time DESC
                    </when>
                    <when test="params.sortType == 11">
                        ORDER BY t1.create_time
                    </when>
                    <when test="params.sortType == 20">
                        ORDER BY t1.sales_count DESC
                    </when>
                    <when test="params.sortType == 21">
                        ORDER BY t1.sales_count
                    </when>
                    <when test="params.sortType == 30">
                        ORDER BY t1.init_sales_count DESC
                    </when>
                    <when test="params.sortType == 31">
                        ORDER BY t1.init_sales_count
                    </when>
                    <when test="params.sortType == 40">
                        ORDER BY t1.sale_price DESC
                    </when>
                    <when test="params.sortType == 41">
                        ORDER BY t1.sale_price
                    </when>
                    <otherwise>
                        ORDER BY t1.create_time DESC
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

</mapper>