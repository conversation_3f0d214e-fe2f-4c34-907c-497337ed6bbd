<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.product.dal.mysql.spec.ProductSkuSpecMapper">

    <delete id="deleteById2" parameterType="java.lang.Long">
        delete from product_sku_spec where id = #{value}
    </delete>

    <delete id="deleteBySkuId" parameterType="java.lang.Long">
        delete from product_sku_spec where sku_id = #{value}
    </delete>


</mapper>