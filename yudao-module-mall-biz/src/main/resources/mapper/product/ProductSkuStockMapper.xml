<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.product.dal.mysql.skustock.ProductSkuStockMapper">


     <update id="deductInventory">
        update product_sku_stock set stock = stock-#{count}  WHERE sku_id = #{skuId} and stock-#{count} >= 0
     </update>

    <update id="restoreInventory">
        update product_sku_stock set stock = stock+#{count}  WHERE sku_id = #{skuId}
    </update>

</mapper>