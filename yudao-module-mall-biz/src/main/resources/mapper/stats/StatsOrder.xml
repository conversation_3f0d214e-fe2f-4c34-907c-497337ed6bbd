<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.basis.dal.mysql.stats.StatsOrderMapper">

    <delete id="deleteByOrderV1">
        delete from mall_stats_order t
        <where>
        t.deleted = 0
        <if test="params.orderId != null">
            and t.order_id = #{params.orderId}
        </if>
        <if test="params.orderIds != null and params.orderIds.size() > 0">
            and t.order_id in
            <foreach item="itemId" collection="params.orderIds" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        </where>
    </delete>

    <delete id="deleteItemsByOrderV1">
        delete from mall_stats_order_item t
        <where>
        t.deleted = 0
        <if test="params.orderId != null">
            and t.order_id = #{params.orderId}
        </if>
        <if test="params.orderIds != null and params.orderIds.size() > 0">
            and t.order_id in
            <foreach item="itemId" collection="params.orderIds" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        </where>
    </delete>

    <delete id="deleteByOrderCancelV1">
        delete from mall_stats_order_cancel t
        <where>
            t.deleted = 0
            <if test="params.orderId != null">
                and t.order_id = #{params.orderId}
            </if>
            <if test="params.orderIds != null and params.orderIds.size() > 0">
                and t.order_id in
                <foreach item="itemId" collection="params.orderIds" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
        </where>
    </delete>
    
    <sql id="saleConditionV1">
        and t.tenant_id = #{params.tenantId}
        and t.deleted = 0
        <if test="params.startDate != null and params.startDate != ''">
            and t.stats_date <![CDATA[>=]]> #{params.startDate}
        </if>
        <if test="params.endDate != null and params.endDate != ''">
            and t.stats_date <![CDATA[<=]]> #{params.endDate}
        </if>
        <if test="params.month != null">
            and month(t.stats_date) = #{params.month}
        </if>
        <if test="params.year != null">
            and year(t.stats_date) = #{params.year}
        </if>
        <if test="params.supplierId != null">
            and t.supplier_id = #{params.supplierId}
        </if>
    </sql>

    <sql id="saleConditionV2">
        and t2.tenant_id = #{params.tenantId}
        and t2.deleted = 0
        <if test="params.skuId != null">
            and t2.skuId = #{params.skuId}
        </if>
        <if test="params.skuIdList != null and params.skuIdList.size() > 0">
            and t2.skuId in
            <foreach item="itemId" collection="params.skuIdList" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        <if test="params.categoryId1 != null">
            and t2.category_id1 = #{params.categoryId1}
        </if>
        <if test="params.categoryId2 != null">
            and t2.category_id2 = #{params.categoryId2}
        </if>
        <if test="params.categoryId3 != null">
            and t2.category_id3 = #{params.categoryId3}
        </if>
    </sql>

    <sql id="saleConditionV3">
        and t3.tenant_id = #{params.tenantId}
        and t3.deleted = 0
        <if test="params.startDate != null and params.startDate != ''">
            and t3.stats_date <![CDATA[>=]]> #{params.startDate}
        </if>
        <if test="params.endDate != null and params.endDate != ''">
            and t3.stats_date <![CDATA[<=]]> #{params.endDate}
        </if>
        <if test="params.month != null">
            and month(t3.stats_date) = #{params.month}
        </if>
        <if test="params.year != null">
            and year(t3.stats_date) = #{params.year}
        </if>
        <if test="params.supplierId != null">
            and t3.supplier_id = #{params.supplierId}
        </if>
    </sql>

    <!-- 净销量查询, 净销量 = 总销量 - 取消 - 售后 -->
    <select id="queryTotalOrderSaleOnDate" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.OrderStatsReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderSaleVO">
        select
            stat1.*,
            stat2.*,
            (stat1.totalPrice - stat2.afterSaleTotalPrice) as netTotalPrice
        from
        (
            select
                COALESCE(count(t.order_id),0) as totalCount,
                COALESCE(sum(t.total_price),0) as totalPrice,
                COALESCE(sum(t.delivery_price),0) as deliveryPrice,
                COALESCE(sum(t.sku_price),0) as skuPrice
            from mall_stats_order t
            where 1 = 1
                <include refid="saleConditionV1"></include>
                and not exists(
                    select 1 from mall_stats_order_cancel t3 where t.order_id = t3.order_id
                    <include refid="saleConditionV3"></include>
                )
        ) as stat1,
        (
            select
                COALESCE(count(t.order_id),0) as afterSaleTotalCount,
                COALESCE(sum(t.refund_price),0) as afterSaleTotalPrice
            from mall_stats_after_sale t
            where
            t.finish_status = 50
            <include refid="saleConditionV1"></include>
            and not exists(
                select 1 from mall_stats_order_cancel t3 where t.order_id = t3.order_id
                <include refid="saleConditionV3"></include>
            )
        ) as stat2
    </select>

    <!-- 净销量查询, 净销量 = 总销量 - 取消 - 售后 -->
    <select id="querySupplierSaleStats" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.OrderStatsReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderSaleVO">
        select
            stat1.supplierId,
            sup.name as supplierName,
            stat1.totalCount,
            stat1.totalPrice,
            stat1.deliveryPrice,
            stat1.skuPrice,
            COALESCE(stat2.afterSaleTotalPrice,0) as afterSaleTotalPrice,
            COALESCE(stat2.afterSaleTotalCount,0) as afterSaleTotalCount,
            (COALESCE(stat1.totalPrice,0) - COALESCE(stat2.afterSaleTotalPrice,0)) as netTotalPrice
        from
        (
            select
                t.supplier_id as supplierId,
                count(t.order_id) as totalCount,
                sum(t.total_price) as totalPrice,
                sum(t.delivery_price) as deliveryPrice,
                sum(t.sku_price) as skuPrice
            from mall_stats_order t
            where 1 = 1
                <include refid="saleConditionV1"></include>
                and not exists(
                    select 1 from mall_stats_order_cancel t3 where t.order_id = t3.order_id
                    <include refid="saleConditionV3"></include>
                )
            group by t.supplier_id
        ) as stat1
        left join
        (
            select
                t.supplier_id as supplierId,
                count(t.order_id) as afterSaleTotalCount,
                sum(t.refund_price) as afterSaleTotalPrice
            from mall_stats_after_sale t
            where
                t.finish_status = 50
                <include refid="saleConditionV1"></include>
                and not exists(
                select 1 from mall_stats_order_cancel t3 where t.order_id = t3.order_id
                <include refid="saleConditionV3"></include>
                )
            group by t.supplier_id
        ) as stat2 on stat1.supplierId = stat2.supplierId
        left join mall_supplier sup on sup.id = stat1.supplierId
    </select>

    <!-- 净销量查询, 净销量 = 总销量 - 取消 - 售后 -->
    <select id="queryOrderSaleOnDate" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.OrderStatsReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderSaleVO">
        select
            stat1.statsDate,
            stat1.totalCount,
            stat1.totalPrice,
            stat1.deliveryPrice,
            stat1.skuPrice,
            COALESCE(stat2.afterSaleTotalPrice,0) as afterSaleTotalPrice,
            COALESCE(stat2.afterSaleTotalCount,0) as afterSaleTotalCount,
            (COALESCE(stat1.totalPrice,0) - COALESCE(stat2.afterSaleTotalPrice,0)) as netTotalPrice
        from
            (
                select
                    t.stats_date as statsDate,
                    count(t.order_id) as totalCount,
                    sum(t.total_price) as totalPrice,
                    sum(t.delivery_price) as deliveryPrice,
                    sum(t.sku_price) as skuPrice
                from mall_stats_order t
                where 1 = 1
                    <include refid="saleConditionV1"></include>
                    and not exists(
                            select 1 from mall_stats_order_cancel t3 where t.order_id = t3.order_id
                                    <include refid="saleConditionV3"></include>
                        )
                group by t.stats_date
            ) as stat1
                left join
            (
                select
                    t.stats_date as statsDate,
                    count(t.order_id) as afterSaleTotalCount,
                    sum(t.refund_price) as afterSaleTotalPrice
                from mall_stats_after_sale t
                where
                    t.finish_status = 50
                    <include refid="saleConditionV1"></include>
                    and not exists(
                        select 1 from mall_stats_order_cancel t3 where t.order_id = t3.order_id
                                 <include refid="saleConditionV3"></include>
                    )
                group by t.stats_date
            ) as stat2
            on stat1.statsDate = stat2.statsDate
    </select>

    <!-- 销量GMV查询 -->
    <select id="queryOrderSaleOnMonth" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.OrderStatsReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderSaleVO">
        select
            stat1.statsMonth,
            stat1.totalCount,
            stat1.totalPrice,
            stat1.deliveryPrice,
            stat1.skuPrice,
            COALESCE(stat2.afterSaleTotalPrice,0) as afterSaleTotalPrice,
            COALESCE(stat2.afterSaleTotalCount,0) as afterSaleTotalCount,
            (COALESCE(stat1.totalPrice,0) - COALESCE(stat2.afterSaleTotalPrice,0)) as netTotalPrice
        from
        (
            select
                DATE_FORMAT(t.stats_date, '%Y-%m') as statsMonth,
                count(t.order_id) as totalCount,
                sum(t.total_price) as totalPrice,
                sum(t.delivery_price) as deliveryPrice,
                sum(t.sku_price) as skuPrice
            from mall_stats_order t
            where 1 = 1
            <include refid="saleConditionV1"></include>
                and not exists(
                select 1 from mall_stats_order_cancel t3 where t.order_id = t3.order_id
                    <include refid="saleConditionV3"></include>
                )
            group by DATE_FORMAT(t.stats_date, '%Y-%m')
        ) as stat1
        left join
        (
            select
                DATE_FORMAT(t.stats_date, '%Y-%m') as statsMonth,
                count(t.order_id) as afterSaleTotalCount,
                sum(t.refund_price) as afterSaleTotalPrice
            from mall_stats_after_sale t
            where
                t.finish_status = 50
                <include refid="saleConditionV1"></include>
                and not exists(
                select 1 from mall_stats_order_cancel t3 where t.order_id = t3.order_id
                    <include refid="saleConditionV3"></include>
                )
            group by DATE_FORMAT(t.stats_date, '%Y-%m')
        ) as stat2
        on stat1.statsMonth = stat2.statsMonth
    </select>

    <select id="querySkuSaleOnDate" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.OrderStatsReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsSkuSaleVO">
        select
        t2.stats_date as statsDate,
        sum(t2.quantity) as totalCount,
        sum(t2.total_price) as totalPrice
        from mall_stats_order_item t2
        <where>
            <include refid="saleConditionV2"></include>
            and not exists(
                select 1 from mall_stats_order_cancel t3 where t3.deleted = 0 and t3.tenant_id = #{params.tenantId} and t2.order_id = t3.order_id
            )
        </where>
        group by t2.stats_date
    </select>

    <select id="queryTopSaleSku" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.OrderStatsReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsTopSkuSaleVO">
        select
            t.sku_id,
            sum(t.total_price) as totalPrice,
            sum(t.quantity) as totalCount,
            sum(t.after_sale_count * t.price) as afterSalePrice,
            sum(t.total_price - t.after_sale_count * t.price) as netPrice
        from mall_stats_order_item t
        where
              t.deleted = 0
              and t.tenant_id = #{params.tenantId}
              and t.stats_date between #{params.startDate} and #{params.endDate}
          and not exists(select 1 from mall_stats_order_cancel t2 where t2.deleted = 0 and t2.tenant_id = #{params.tenantId} and  t.order_id = t2.order_id)
        group by t.sku_id order by totalPrice desc limit #{params.rankCount}
    </select>

    <select id="queryTopSaleCategory2" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.OrderStatsReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsTopSkuSaleVO">
        select
            t.category_id2,
            sum(t.total_price) as totalPrice,
            sum(t.quantity) as totalCount,
            sum(t.after_sale_count * t.price) as afterSalePrice,
            sum(t.total_price - t.after_sale_count * t.price) as netPrice
        from mall_stats_order_item t
        where
            t.deleted = 0
          and t.tenant_id = #{params.tenantId}
          and t.stats_date between #{params.startDate} and #{params.endDate}
          and not exists(select 1 from mall_stats_order_cancel t2 where t2.deleted = 0 and t2.tenant_id = #{params.tenantId} and  t.order_id = t2.order_id)
        group by t.category_id2 order by totalPrice desc limit #{params.rankCount}
    </select>

    <select id="queryTopSaleCategory3" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.OrderStatsReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsTopSkuSaleVO">
        select
            t.category_id3,
            sum(t.total_price) as totalPrice,
            sum(t.quantity) as totalCount,
            sum(t.after_sale_count * t.price) as afterSalePrice,
            sum(t.total_price - t.after_sale_count * t.price) as netPrice
        from mall_stats_order_item t
        where
            t.deleted = 0
          and t.tenant_id = #{params.tenantId}
          and t.stats_date between #{params.startDate} and #{params.endDate}
          and not exists(select 1 from mall_stats_order_cancel t2 where t2.deleted = 0 and t2.tenant_id = #{params.tenantId} and  t.order_id = t2.order_id)
        group by t.category_id3 order by totalPrice desc limit #{params.rankCount}
    </select>





</mapper>