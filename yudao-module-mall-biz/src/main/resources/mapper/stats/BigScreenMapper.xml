<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.external.bigscreen.dal.mysql.bigscreen.BigScreenMapper">


    <select id="getLoginedUserCount" resultType="Long">
        SELECT
        COUNT( DISTINCT user_no )
        FROM
        member_user
        WHERE
        deleted = 0
        AND tenant_id = #{tenantId};
    </select>

    <select id="getRealTimeOrderList" resultType="cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenRealTimeOrderRespVO">
        SELECT
        DATE_FORMAT( trade_order.submit_time, '%Y-%m-%d' ) AS order_date,
        trade_order.order_price,
        member_user.nickname,
        member_user.dept_name
        FROM
        trade_order
        LEFT JOIN member_user ON trade_order.user_id = member_user.id
        WHERE
        trade_order.`status` != 9
        AND parent_type = 0
        AND trade_order.tenant_id = #{tenantId}
        ORDER BY
        trade_order.submit_time DESC
        LIMIT 20;
    </select>

    <select id="getSellTotal" resultType="Long">
        SELECT
        SUM( count - after_sale_count ) AS sellTotal
        FROM
        trade_order_item
        INNER JOIN trade_order ON trade_order_item.order_id = trade_order.id
        WHERE
        trade_order.`status` not in (1, 9)
        AND trade_order.parent_type = 0
        AND trade_order_item.deleted = 0
        AND trade_order_item.tenant_id = #{tenantId};
    </select>

    <select id="getTodaySellCount" resultType="Long">
        SELECT
        SUM( count - after_sale_count ) AS sellTotal
        FROM
        trade_order_item
        INNER JOIN trade_order
        ON trade_order_item.order_id=trade_order.id
        WHERE
        trade_order.`status` not in (1, 9)
        AND trade_order.parent_type = 0
        AND DATE ( trade_order_item.create_time ) = CURDATE()
        AND trade_order_item.deleted = 0
        AND trade_order_item.tenant_id = #{tenantId};
    </select>

    <select id="getTodayOrderCount" resultType="Long">
        SELECT
        COUNT(*) AS total
        FROM
        trade_order
        WHERE
        deleted = 0
        AND parent_type = 0
        AND `status` not in (1, 9)
        AND DATE ( create_time ) = CURDATE()
        AND tenant_id = #{tenantId};
    </select>

    <select id="getTodayOrderAmount" resultType="BigDecimal">
        SELECT
        SUM(
        order_price - IFNULL( refund_price, 0 )) AS sellAmount
        FROM
        trade_order
        WHERE
        trade_order.`status` not in (1, 9)
        AND trade_order.parent_type = 0
        AND DATE ( trade_order.submit_time )= CURDATE()
        AND trade_order.deleted = 0
        AND trade_order.tenant_id = #{tenantId};
    </select>

    <select id="getOrderCount" resultType="Long">
        SELECT
        COUNT(*) AS total
        FROM
        trade_order
        WHERE
        deleted = 0
        AND parent_type = 0
        AND `status` not in (1, 9)
        AND tenant_id = #{tenantId};
    </select>

    <select id="getOrderAmount" resultType="BigDecimal">
        SELECT
        SUM( order_price - IFNULL(refund_price,0) ) AS total
        FROM
        trade_order
        WHERE
        deleted = 0
        AND parent_type = 0
        AND `status` not in (1, 9)
        AND tenant_id = #{tenantId};
    </select>

    <select id="getPendingApprovalOrderCount" resultType="Long">
        SELECT
        COUNT(*) AS total
        FROM
        trade_order
        WHERE
        deleted = 0
        AND parent_type = 0
        AND `status` != 9
        AND ( audit_status = 2 )
        AND tenant_id = #{tenantId};
    </select>

    <select id="getApprovalOrderCount" resultType="Long">
        SELECT
        COUNT(*) AS total
        FROM
        trade_order
        WHERE
        deleted = 0
        AND parent_type = 0
        AND `status` != 9
        AND ( audit_status = 4 )
        AND tenant_id = #{tenantId};
    </select>

    <select id="getRejectOrderCount" resultType="Long">
        SELECT
        COUNT(*) AS total
        FROM
        trade_order
        WHERE
        deleted = 0
        AND parent_type = 0
        AND `status` != 9
        AND ( audit_status = 3 )
        AND tenant_id = #{tenantId};
    </select>

    <select id="getPendingSettlementOrderCount" resultType="Long">
        SELECT
        COUNT(*) AS total
        FROM
        trade_order
        WHERE
        deleted = 0
        AND parent_type = 0
        AND `status` = 8
        AND ( settle_status = 0 )
        AND tenant_id = #{tenantId};
    </select>

    <select id="getPendingSettlementAmount" resultType="BigDecimal">
        SELECT
        SUM(
        order_price - IFNULL( refund_price, 0 )) AS total
        FROM
        trade_order
        WHERE
        deleted = 0
        AND parent_type = 0
        AND `status` = 8
        AND ( settle_status = 0 )
        AND tenant_id = #{tenantId};
    </select>

    <select id="getSupplierProductSummary" resultType="cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenSupplierProductSummaryRespVO">
        SELECT
        supplier_name AS supplierName,
        COUNT(*) AS skuTotal
        FROM
        product_sku
        WHERE
        deleted = 0
        AND status = 1
        AND tenant_id = #{tenantId}
        GROUP BY
        supplier_name
        ORDER BY
        skuTotal DESC;
    </select>

    <select id="getSupplierProductProportion" resultType="cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenSupplierProductProportionRespVO">
        SELECT
        supplier_name AS supplierName,
        COUNT(*) AS skuTotal,
        ROUND(( COUNT(*) / ( SELECT COUNT(*) FROM product_sku WHERE deleted = 0 AND tenant_id = #{tenantId})) * 100, 2 ) AS proportion
        FROM
        product_sku
        WHERE
        deleted = 0
        AND status = 1
        AND tenant_id = #{tenantId}
        GROUP BY
        supplier_name
        ORDER BY
        skuTotal DESC;
    </select>

    <select id="getProductCategoryProportion" resultType="cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenProductCategoryProportionRespVO">
        SELECT
        product_spu.category1_name AS categoryName,
        COUNT(*) AS skuTotal,
        ROUND(( COUNT(*) / ( SELECT COUNT(*) FROM product_sku WHERE deleted = 0 AND tenant_id = #{tenantId})) * 100, 2 ) AS proportion
        FROM
        product_sku
        INNER JOIN product_spu ON product_sku.spu_id = product_spu.id
        INNER JOIN product_category ON product_spu.category1_id = product_category.category_id
        WHERE
        product_sku.deleted = 0
        AND product_sku.`status` = 1
        AND product_sku.tenant_id = #{tenantId}
        GROUP BY
        categoryName
        ORDER BY
        skuTotal DESC;
    </select>

    <select id="getSellProductCategorySummary" resultType="cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenSellProductCategorySummaryRespVO">
        SELECT
        product_spu.category1_name AS categoryName,
        SUM( trade_order_item.count ) AS sellTotal
        FROM
        trade_order_item
        INNER JOIN trade_order ON trade_order_item.order_id = trade_order.id
        INNER JOIN product_sku ON trade_order_item.sku_id = product_sku.id
        INNER JOIN product_spu ON product_sku.spu_id = product_spu.id
        WHERE
        trade_order.`status` != 9
        AND trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND trade_order_item.deleted = 0
        AND trade_order.deleted = 0
        AND trade_order_item.tenant_id = #{tenantId}
        GROUP BY
        categoryName
        ORDER BY
        sellTotal DESC;
    </select>

    <select id="getAssetArchiveOrderSummary" resultType="Long">
        SELECT
        COUNT( DISTINCT order_no ) AS assetArchiveOrderTotal
        FROM
        trade_order_item_assets
        WHERE
        tenant_id = #{tenantId}
        AND deleted = 0;
    </select>

    <select id="getAssetArchiveSkuSummary" resultType="Long">
        SELECT
        SUM( toi.count - toi.after_sale_count ) AS assetArchiveOrderTotal
        FROM
        trade_order_item_assets toia
        INNER JOIN trade_order_item toi ON toia.order_item_id = toi.id
        WHERE
        toia.tenant_id = #{tenantId}
        AND toia.deleted = 0;
    </select>


</mapper>