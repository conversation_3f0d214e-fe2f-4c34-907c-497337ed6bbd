<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.basis.dal.mysql.stats.StatsOrderItemMapper">


    <sql id="statsOrderItemConditionV1">
        <if test="params.skuId != null">
            and t.sku_id = #{params.skuId}
        </if>
        <if test="params.thirdSkuId != null">
            and t.third_sku_id = #{params.thirdSkuId}
        </if>
        <if test="params.submitTime != null and params.submitTime.length == 2">
            and t.submit_time between #{params.submitTime[0]} and #{params.submitTime[1]}
        </if>
        <if test="params.statsDate != null and params.statsDate.length == 2">
            and t.stats_date between #{params.statsDate[0]} and #{params.statsDate[1]}
        </if>
        <if test="params.thirdOrderId != null and params.thirdOrderId != ''">
            and t2.third_order_id = #{params.thirdOrderId}
        </if>
        <if test="params.supplierId != null">
            and t.supplier_id = #{params.supplierId}
        </if>
        <if test="params.userName != null">
            and t.user_name = #{params.userName}
        </if>
        <if test="params.userId != null">
            and t.user_id = #{params.userId}
        </if>
        <if test="params.deptCode != null">
            and t.dept_code = #{params.deptCode}
        </if>
        <if test="params.orderNo != null">
            and t.order_no = #{params.orderNo}
        </if>
        <if test="params.categoryId1 != null">
            and t.category_id1 = #{params.categoryId1}
        </if>
        <if test="params.categoryId2 != null">
            and t.category_id2 = #{params.categoryId2}
        </if>
        <if test="params.categoryId3 != null">
            and t.category_id3 = #{params.categoryId3}
        </if>
        <if test="params.orderStatus != null">
            and t2.status = #{params.orderStatus}
        </if>
        <if test="params.skuTags != null and params.skuTags.size() > 0">
            and exists(
            select 1 from trade_order_item tt1, trade_order_item_tag tt2 where tt1.deleted = 0 and  tt1.order_id = t.order_id and tt2.deleted = 0
            and tt1.id = tt2.order_item_id and tt2.product_tag_id in
            <foreach item="itemId" collection="params.skuTags" open="(" separator="," close=")">
                #{itemId}
            </foreach>
            )
        </if>
        <if test="params.tagStatus != null">
            <choose>
                <when test="params.tagStatus == 0">
                    and not exists (
                    select 1 from trade_order_item toi where toi.id = t.order_item_id and toi.deleted = 0 and toi.sku_tags is not null
                    )
                </when>
                <when test="params.tagStatus == 1">
                    and exists (
                    select 1 from trade_order_item toi where toi.id = t.order_item_id and toi.deleted = 0 and toi.sku_tags is not null
                    )
                </when>
            </choose>
        </if>
    </sql>

    <sql id="statsAfterSaleConditionV2">
        <if test="params.skuId != null">
            and t.sku_id = #{params.skuId}
        </if>
        <if test="params.thirdSkuId != null">
            and t.third_sku_id = #{params.thirdSkuId}
        </if>
        <if test="params.statsDate != null and params.statsDate.length == 2">
            and t.stats_date between #{params.statsDate[0]} and #{params.statsDate[1]}
        </if>
        <if test="params.thirdOrderId != null and params.thirdOrderId != ''">
            and t2.third_order_id = #{params.thirdOrderId}
        </if>
        <if test="params.supplierId != null">
            and t.supplier_id = #{params.supplierId}
        </if>
        <if test="params.userName != null">
            and t.user_name = #{params.userName}
        </if>
        <if test="params.userId != null">
            and t.user_id = #{params.userId}
        </if>
        <if test="params.deptCode != null">
            and t.dept_code = #{params.deptCode}
        </if>
        <if test="params.orderNo != null">
            and t.order_no = #{params.orderNo}
        </if>
        <if test="params.categoryId1 != null">
            and t.category_id1 = #{params.categoryId1}
        </if>
        <if test="params.categoryId2 != null">
            and t.category_id2 = #{params.categoryId2}
        </if>
        <if test="params.categoryId3 != null">
            and t.category_id3 = #{params.categoryId3}
        </if>
        <if test="params.skuTags != null and params.skuTags.size() > 0">
            and exists(
            select 1 from trade_order_item tt1, trade_order_item_tag tt2 where tt1.deleted = 0 and  tt1.order_id = t.order_id and tt2.deleted = 0
            and tt1.id = tt2.order_item_id and tt2.product_tag_id in
            <foreach item="itemId" collection="params.skuTags" open="(" separator="," close=")">
                #{itemId}
            </foreach>
            )
        </if>
        <if test="params.tagStatus != null">
            <choose>
                <when test="params.tagStatus == 0">
                    and not exists (
                    select 1 from trade_order_item toi where toi.id = t.order_item_id and toi.deleted = 0 and toi.sku_tags is not null
                    )
                </when>
                <when test="params.tagStatus == 1">
                    and exists (
                    select 1 from trade_order_item toi where toi.id = t.order_item_id and toi.deleted = 0 and toi.sku_tags is not null
                    )
                </when>
            </choose>
        </if>
    </sql>

    <select id="selectPageV2" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderItemPageReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderItemRespVO">
        select
            t.*,
            t2.status as orderStatus
        from mall_stats_order_item t, trade_order t2
        where
            t.deleted = 0
          and t.order_id = t2.id
          and t2.deleted = 0
          and t2.parent_type = 0
          <include refid="statsOrderItemConditionV1"></include>
    </select>

    <select id="selectPageV3" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderItemPageReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderItemExportVO">
        select
        t.sku_id as skuId,
        t.third_sku_id as thirdSkuId,
        t.sku_name as skuName,
        (select category_name from product_category where category_id = t.category_id1 limit 1) as categoryName1,
        (select category_name from product_category where category_id = t.category_id2 limit 1) as categoryName2,
        (select category_name from product_category where category_id = t.category_id3 limit 1) as categoryName3,
        t.quantity as quantity,
        t.price as price,
        t.total_price as totalPrice,
        t2.no as orderNo,
        t2.third_order_id as thirdOrderId,
        t2.delivery_price as deliveryPrice,
        t2.order_price as orderPrice,
        t2.refund_price as refundPrice,
        t2.supplier_name as supplierName,
        t2.status as orderStatus,
        t2.invoice_status as invoiceStatus,
        t2.submit_time as submitTime,
        t2.finish_time as finishTime,
        t2.user_name as userName,
        t3.user_no as userNo,
        t3.dept_name as deptName,
        t2.receiver_name as receiverName,
        t2.receiver_detail_address as receiverAddress,
        t2.platform as platform
        from mall_stats_order_item t, trade_order t2
        left join member_user t3 on t2.user_id = t3.id
        where
        t.deleted = 0
        and t.order_id = t2.id
        and t2.deleted = 0
        and t2.parent_type = 0
        <include refid="statsOrderItemConditionV1"></include>
    </select>

    <select id="selectTotalSaleOnDateV1" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderItemPageReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsSaleTotalRespVO">
        select
           stat1.*,
           stat2.*,
           stat3.*,
           COALESCE((stat1.totalPrice -stat3.cancelTotalPrice - stat2.afterSaleTotalPrice),0) as netTotalPrice
        from
        (
        select
            COALESCE(count(distinct t.order_id),0) as totalCount,
            COALESCE(sum(t.total_price),0) as totalPrice
            from mall_stats_order_item t, trade_order t2
            where
            t.deleted = 0
            and t.order_id = t2.id
            and t2.deleted = 0
            and t2.parent_type = 0
            <include refid="statsOrderItemConditionV1"></include>
        ) as stat1,
        (
            select
                COALESCE(count(distinct t.order_id),0) as afterSaleTotalCount,
                COALESCE(sum(t.refund_price),0) as afterSaleTotalPrice
            from mall_stats_after_sale t, trade_order t2
            where
            t.deleted = 0
            and t.order_id = t2.id
            and t2.deleted = 0
            and t.finish_status = 50
            <include refid="statsAfterSaleConditionV2"></include>
        ) as stat2,
        (
        select
        COALESCE(sum(t.total_price),0) as cancelTotalPrice
        from mall_stats_order_item t, trade_order t2
        where
        t.deleted = 0
        and t.order_id = t2.id
        and t2.deleted = 0
        and exists(
        select 1 from mall_stats_order_cancel soc where t.order_id = soc.order_id and soc.deleted = 0 and soc.cancel_type != 20
        )
        <include refid="statsOrderItemConditionV1"></include>
        ) as stat3
    </select>

    <select id="selectSaleOnDate" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderItemPageReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsSaleDateRespVO">
        select
           stat1.statsDate,
           stat1.orderCount,
           stat1.totalPrice,
           (COALESCE(stat1.totalPrice,0) - COALESCE(stat2.afterSaleTotalPrice,0) - COALESCE(stat3.cancelTotalPrice,0)) as netTotalPrice,
           COALESCE(stat2.afterSaleTotalCount,0) as afterSaleTotalCount,
           COALESCE(stat3.cancelTotalPrice,0) as cancelTotalPrice,
           COALESCE(stat2.afterSaleTotalPrice,0) as afterSaleTotalPrice
        from
        (
            select
            t.stats_date as statsDate,
            COALESCE(count(distinct t.order_id),0) as orderCount,
            COALESCE(sum(t.total_price),0) as totalPrice
            from mall_stats_order_item t, trade_order t2
            where
            t.deleted = 0
            and t.order_id = t2.id
            and t2.deleted = 0
            and t2.parent_type = 0
            <include refid="statsOrderItemConditionV1"></include>
            group by t.stats_date
        ) as stat1 left join (
            select
            t.stats_date as statsDate,
            COALESCE(count(t.order_id),0) as afterSaleTotalCount,
            COALESCE(sum(t.refund_price),0) as afterSaleTotalPrice
            from mall_stats_after_sale t, trade_order t2
            where
            t.deleted = 0
            and t.order_id = t2.id
            and t2.deleted = 0
            and t.finish_status = 50
            <include refid="statsAfterSaleConditionV2"></include>
            group by t.stats_date
        ) as stat2
        on stat1.statsDate = stat2.statsDate
        left join (
            select
            t.stats_date as statsDate,
            COALESCE(sum(t.total_price),0) as cancelTotalPrice
            from mall_stats_order_item t, trade_order t2
            where
            t.deleted = 0
            and t.order_id = t2.id
            and t2.deleted = 0
            and t2.parent_type = 0
            and exists(
                select 1 from mall_stats_order_cancel soc where t.order_id = soc.order_id and soc.deleted = 0 and soc.cancel_type != 20
            )
            <include refid="statsOrderItemConditionV1"></include>
            group by t.stats_date
        ) as stat3 on stat1.statsDate = stat3.statsDate
        order by stat1.statsDate desc
    </select>

    <select id="selectSaleOnYearMonth" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderItemPageReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsSaleDateRespVO">
        select
        stat1.yearMonth,
        stat1.orderCount,
        stat1.totalPrice,
        (COALESCE(stat1.totalPrice,0) - COALESCE(stat2.afterSaleTotalPrice,0) - COALESCE(stat3.cancelTotalPrice,0)) as netTotalPrice,
        COALESCE(stat2.afterSaleTotalCount,0) as afterSaleTotalCount,
        COALESCE(stat2.afterSaleTotalPrice,0) as afterSaleTotalPrice,
        COALESCE(stat3.cancelTotalPrice,0) as cancelTotalPrice
        from
        (
            select
            DATE_FORMAT(t.stats_date, '%Y-%m') as yearMonth,
            COALESCE(count(distinct t.order_id),0) as orderCount,
            COALESCE(sum(t.total_price),0) as totalPrice
            from mall_stats_order_item t, trade_order t2
            where
            t.deleted = 0
            and t.order_id = t2.id
            and t2.deleted = 0
            and t2.parent_type = 0
            <include refid="statsOrderItemConditionV1"></include>
            group by DATE_FORMAT(t.stats_date, '%Y-%m')
        ) as stat1 left join
        (
        select
            DATE_FORMAT(t.stats_date, '%Y-%m') as yearMonth,
            count(t.order_id) as afterSaleTotalCount,
            sum(t.refund_price) as afterSaleTotalPrice
            from mall_stats_after_sale t, trade_order t2
            where
            t.deleted = 0
            and t.order_id = t2.id
            and t2.deleted = 0
            and t.finish_status = 50
            <include refid="statsAfterSaleConditionV2"></include>
            group by DATE_FORMAT(t.stats_date, '%Y-%m')
        ) as stat2 on stat1.yearMonth = stat2.yearMonth
        left join (
            select
            DATE_FORMAT(t.stats_date, '%Y-%m') as yearMonth,
            COALESCE(sum(t.total_price),0) as cancelTotalPrice
            from mall_stats_order_item t, trade_order t2
            where
            t.deleted = 0
            and t.order_id = t2.id
            and t2.deleted = 0
            and t2.parent_type = 0
            and exists(
                select 1 from mall_stats_order_cancel soc where t.order_id = soc.order_id and soc.deleted = 0 and soc.cancel_type != 20
            )
            <include refid="statsOrderItemConditionV1"></include>
            group by DATE_FORMAT(t.stats_date, '%Y-%m')
        ) as stat3 on stat1.yearMonth = stat3.yearMonth
        order by stat1.yearMonth desc
    </select>

    <select id="selectSaleOnSkuV1" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderItemPageReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsSaleSkuRespVO">
        select
            stat1.*,
            (COALESCE(stat1.totalPrice,0) - COALESCE(stat2.afterSaleTotalPrice,0) - COALESCE(stat3.cancelTotalPrice,0)) as netTotalPrice,
            COALESCE(stat2.afterSaleTotalCount,0) as afterSaleTotalCount,
            COALESCE(stat2.afterSaleTotalPrice,0) as afterSaleTotalPrice,
            COALESCE(stat3.cancelTotalPrice,0) as cancelTotalPrice
        from
        (
            select
                at.*,
                ps.supplier_name as supplierName,
                ps.sku_inner_id as thirdSkuId,
                ps.sku_name
            from
            (
            select
                t.sku_id as skuId,
                COALESCE(count(distinct t.order_id),0) as orderCount,
                COALESCE(sum(t.total_price),0) as totalPrice
                from mall_stats_order_item t, trade_order t2
                where
                t.deleted = 0
                and t.total_price <![CDATA[>]]> 0
                and t.order_id = t2.id
                and t2.deleted = 0
                and t2.parent_type = 0
                <include refid="statsOrderItemConditionV1"></include>
            group by t.sku_id
            ) as at left join product_sku ps on at.skuId = ps.id
            order by at.totalPrice desc
        ) as stat1 left join
        (
            select
            t.sku_id as skuId,
            COALESCE(count(t.order_id),0) as afterSaleTotalCount,
            COALESCE(sum(t.refund_price),0) as afterSaleTotalPrice
            from mall_stats_after_sale t, trade_order t2
            where
            t.deleted = 0
            and t.order_id = t2.id
            and t2.deleted = 0
            and t.finish_status = 50
            <include refid="statsAfterSaleConditionV2"></include>
            group by t.sku_id
        ) as stat2 on stat1.skuId = stat2.skuId
        left join (
            select
            t.sku_id as skuId,
            COALESCE(sum(t.total_price),0) as cancelTotalPrice
            from mall_stats_order_item t, trade_order t2
            where
            t.deleted = 0
            and t.total_price <![CDATA[>]]> 0
            and t.order_id = t2.id
            and t2.deleted = 0
            and t2.parent_type = 0
            and exists(
                select 1 from mall_stats_order_cancel soc where t.order_id = soc.order_id and soc.deleted = 0 and soc.cancel_type != 20
            )
            <include refid="statsOrderItemConditionV1"></include>
            group by t.sku_id
        ) as stat3 on stat1.skuId = stat3.skuId
        <if test="params.sortType!= null">
            <choose>
                <when test="params.sortType == 10">
                    order by stat1.totalPrice desc
                </when>
                <when test="params.sortType == 20">
                    order by stat1.orderCount desc
                </when>
                <when test="params.sortType == 30">
                    order by afterSaleTotalCount desc
                </when>
                <when test="params.sortType == 40">
                    order by afterSaleTotalPrice desc
                </when>
                <when test="params.sortType == 50">
                    order by cancelTotalPrice desc
                </when>
                <when test="params.sortType == 60">
                    order by netTotalPrice desc
                </when>
                <when test="params.sortType == 11">
                    order by stat1.totalPrice
                </when>
                <when test="params.sortType == 21">
                    order by stat1.orderCount
                </when>
                <when test="params.sortType == 31">
                    order by afterSaleTotalCount
                </when>
                <when test="params.sortType == 41">
                    order by afterSaleTotalPrice
                </when>
                <when test="params.sortType == 51">
                    order by cancelTotalPrice
                </when>
                <when test="params.sortType == 61">
                    order by netTotalPrice
                </when>
                <otherwise>
                    order by totalPrice desc
                </otherwise>
            </choose>
        </if>
    </select>



</mapper>