<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.basis.dal.mysql.stats.StatsAfterSaleMapper">


    <sql id="statsAfterSaleConditionV1">
        <if test="params.skuId != null">
            and t.sku_id = #{params.skuId}
        </if>
        <if test="params.thirdSkuId != null">
            and t.third_sku_id = #{params.thirdSkuId}
        </if>
        <if test="params.finishTime != null and params.finishTime.length == 2">
            and t.finish_time between #{params.finishTime[0]} and #{params.finishTime[1]}
        </if>
        <if test="params.thirdOrderId != null and params.thirdOrderId != ''">
            and t2.third_order_id = #{params.thirdOrderId}
        </if>
        <if test="params.supplierId != null">
            and t.supplier_id = #{params.supplierId}
        </if>
        <if test="params.userName != null">
            and t.user_name = #{params.userName}
        </if>
        <if test="params.userId != null">
            and t.user_id = #{params.userId}
        </if>
        <if test="params.deptCode != null">
            and t.dept_code = #{params.deptCode}
        </if>
        <if test="params.orderNo != null">
            and t.order_no = #{params.orderNo}
        </if>
        <if test="params.categoryId1 != null">
            and t.category_id1 = #{params.categoryId1}
        </if>
        <if test="params.categoryId2 != null">
            and t.category_id2 = #{params.categoryId2}
        </if>
        <if test="params.categoryId3 != null">
            and t.category_id3 = #{params.categoryId3}
        </if>
        <if test="params.finishStatus != null">
            and t.finish_status = #{params.finishStatus}
        </if>
        <if test="params.afterSaleWay != null">
            and t.after_sale_way = #{params.afterSaleWay}
        </if>
        <if test="params.afterSaleType != null">
            and t.after_sale_type = #{params.afterSaleType}
        </if>
        <if test="params.skuTags != null and params.skuTags.size() > 0">
            and exists(
            select 1 from trade_order_item tt1, trade_order_item_tag tt2 where tt1.deleted = 0 and  tt1.order_id = t.order_id and tt2.deleted = 0
            and tt1.id = tt2.order_item_id and tt2.product_tag_id in
            <foreach item="itemId" collection="params.skuTags" open="(" separator="," close=")">
                #{itemId}
            </foreach>
            )
        </if>
        <if test="params.tagStatus != null">
            <choose>
                <when test="params.tagStatus == 0">
                    and not exists (
                    select 1 from trade_order_item toi where toi.id = t.order_item_id and toi.deleted = 0 and toi.sku_tags is not null
                    )
                </when>
                <when test="params.tagStatus == 1">
                    and exists (
                    select 1 from trade_order_item toi where toi.id = t.order_item_id and toi.deleted = 0 and toi.sku_tags is not null
                    )
                </when>
            </choose>
        </if>
    </sql>

    <select id="selectPageV2" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsAfterSalePageReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsAfterSaleRespVO">
        select
        t.after_sale_id as afterSaleId,
        t.sku_id as skuId,
        t.third_sku_id as thirdSkuId,
        t.sku_name as skuName,
        t.quantity as quantity,
        t.sku_price as skuPrice,
        t.after_sale_type as afterSaleType,
        t.after_sale_way as afterSaleWay,
        t.finish_status as finishStatus,
        t.finish_time as finishTime,
        t.order_id as orderId,
        t.order_item_id as orderItemId,
        t2.no as orderNo,
        t2.third_order_id as thirdOrderId,
        t2.delivery_price as deliveryPrice,
        t2.order_price as orderPrice,
        t2.refund_price as refundPrice,
        t2.supplier_name as supplierName,
        t2.status as orderStatus,
        t2.invoice_status as invoiceStatus,
        t2.submit_time as orderSubmitTime,
        t2.finish_time as orderFinishTime,
        t2.user_name as userName,
        t3.user_no as userNo,
        t3.dept_name as deptName,
        t2.receiver_name as receiverName,
        t2.receiver_detail_address as receiverAddress,
        t2.platform as platform
        from mall_stats_after_sale t, trade_order t2
        left join member_user t3 on t2.user_id = t3.id
        where
            t.deleted = 0
          and t.order_id = t2.id
          and t2.deleted = 0
          and t2.parent_type = 0
          <include refid="statsAfterSaleConditionV1"></include>
    </select>

    <select id="selectPageV3" parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsAfterSalePageReqVO" resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsAfterSaleExportVO">
        select
        t.*,
        (select category_name from product_category where category_id = t.category_id1 limit 1) as categoryName1,
        (select category_name from product_category where category_id = t.category_id2 limit 1) as categoryName2,
        (select category_name from product_category where category_id = t.category_id3 limit 1) as categoryName3,
        t2.status as orderStatus
        from mall_stats_after_sale t, trade_order t2
        where
        t.deleted = 0
        and t.order_id = t2.id
        and t2.deleted = 0
        and t2.parent_type = 0
        <include refid="statsAfterSaleConditionV1"></include>
    </select>


</mapper>