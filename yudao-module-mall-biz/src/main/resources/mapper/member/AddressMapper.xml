<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.member.dal.mysql.address.AddressMapper">


    <select id="getAddressIncludeDelete" resultType="cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AddressDO">
        SELECT id, user_id, name, mobile, province_id, province_name, city_id, city_name, county_id, county_name, town_id, town_name, consignee_zip, consignee_address, defaulted, alias, telephone, email, create_time, update_time, creator, updater, deleted
        FROM member_address WHERE id = #{id} AND user_id = #{userId}
    </select>

</mapper>