<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.basis.dal.mysql.project.ProjectWhiteSkuMapper">

    <delete id="deleteById2" parameterType="java.lang.Long">
        delete from mall_project_white_sku where id = #{value}
    </delete>

    <delete id="deleteBatchIds2" parameterType="java.util.List">
        delete from mall_project_white_sku where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBySkuId" parameterType="java.lang.Long">
        delete from mall_project_white_sku where sku_id = #{value}
    </delete>

</mapper>
