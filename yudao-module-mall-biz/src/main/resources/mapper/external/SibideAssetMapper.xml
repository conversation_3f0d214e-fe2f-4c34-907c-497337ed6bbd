<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace='cn.iocoder.yudao.module.mall.external.assets.mapper.ExtSibideAssetMapper'>


    <select id="selectOrderCount" resultType="Long">
        select count(*) FROM SEC_MALL_DJ.ASSETS_ACCEPT_DATA where DATA_ID = #{orderNo}
    </select>

    <insert id="insertOrder">
        insert into SEC_MALL_DJ.ASSETS_ACCEPT_DATA (DATA_ID,DATA_SOURCE,CG_SUBJECT,BASE_DEP_CODE,BASE_DEP_NAME,BASE_TEACHER_NO_REQUEST,BASE_TEACHER_NAME_REQUEST,PROVIDER,PROVIDER_LINKMAN,PROVIDER_TELEPHONE,SHOP_MAN,SOURCE,IS_CENTER,DATA_STATUS,INVOICE_NO,PURCHASE_DATE,IMAGE_LIST)
                values (#{data.orderNo},'mall',#{data.skuName},#{data.dept},#{data.deptName},#{data.user},#{data.userName},#{data.supplier},#{data.extSupUser},#{data.extSupPhone},#{data.userName},'1',0,'待建账',#{data.invoice},TO_DATE(#{data.buyDate}, 'YYYY-MM-DD'),#{data.invoiceUrl})
    </insert>

    <update id="updateOrder">
        update SEC_MALL_DJ.ASSETS_ACCEPT_DATA set
            DATA_SOURCE = 'mall',
            BASE_DEP_CODE = #{data.dept},
            BASE_DEP_NAME = #{data.deptName},
            BASE_TEACHER_NO_REQUEST = #{data.user},
            BASE_TEACHER_NAME_REQUEST = #{data.userName},
            PROVIDER = #{data.supplier},
            PROVIDER_LINKMAN = #{data.extSupUser},
            PROVIDER_TELEPHONE = #{data.extSupPhone},
            CG_SUBJECT = #{data.skuName},
            SHOP_MAN = #{data.userName},
            SOURCE = '1',
            IS_CENTER = 0,
            INVOICE_NO = #{data.invoice},
            PURCHASE_DATE = TO_DATE(#{data.buyDate}, 'YYYY-MM-DD'),
            IMAGE_LIST = #{data.invoiceUrl}
        where DATA_ID = #{data.orderNo}
    </update>

    <insert id="insertOrderItem">
        insert into SEC_MALL_DJ.ASSETS_ACCEPT_LIST_DATA (DATA_LIST_ID,DATA_ID,NAME,PRICE,NUM,MODEL,SPEC,BRAND,DATA_STATUS)
            values (#{data.orderDetailNo},#{data.orderNo},#{data.skuName},#{data.price},#{data.quantity},#{data.format},#{data.model},'-','正常')
    </insert>

    <delete id="deleteOrderItems">
        delete from SEC_MALL_DJ.ASSETS_ACCEPT_LIST_DATA where DATA_ID = #{orderNo} and DATA_LIST_ID = #{orderDetailNo}
    </delete>

    <select id="selectAssetStatus" parameterType="java.util.Map" resultType="cn.iocoder.yudao.module.mall.external.assets.dto.sibide.ExtSiBiDeAssetsStatusDTO">
        select
               DATA_ID as orderNo,
               DATA_LIST_ID as orderDetailNo,
               BPM_NO as acceptNo,
               LAST_AUDIT_USER_NAME as approveBy,
               ADVICE as approveMemo,
               CURRENT_NODE_TIME as approveTime
               DATA_STATUS as approveStatus
        from SEC_MALL_DJ.ASSETS_REQUEST_INFO
        <where>
            <if test="params.orderNo != null">
                and DATA_ID = #{params.orderNo}
            </if>
            <if test="params.orderDetailNo != null">
                and DATA_LIST_ID = #{params.orderDetailNo}
            </if>
            <if test="params.bpmNo != null">
                and BPM_NO = #{params.bpmNo}
            </if>
        </where>
        order by CURRENT_NODE_TIME desc
    </select>

    <select id="selectAssetStatusV2" parameterType="java.util.Map" resultType="cn.iocoder.yudao.module.mall.external.assets.dto.sibide.ExtSiBiDeAssetsStatusDTO">
        select
        DATA_ID as orderNo,
        DATA_LIST_ID as orderDetailNo,
        BPM_NO as acceptNo,
        LAST_AUDIT_USER_NAME as approveBy,
        ADVICE as approveMemo,
        CURRENT_NODE_TIME as approveTime,
        DATA_STATUS as approveStatus,
        DATA_STATUS_NAME as approveStatusName,
        NAME as assetName,
        CODE as assetCode,
        ASSETS_SORT_CODE as category6,
        ASSETS_SORT_GB_CODE as category16
        from SEC_ASSETS.V_ASSETS_LIST_DATA_STATUS
        <where>
            <if test="params.orderNo != null">
                and DATA_ID = #{params.orderNo}
            </if>
            <if test="params.orderDetailNo != null">
                and DATA_LIST_ID = #{params.orderDetailNo}
            </if>
        </where>
        order by CURRENT_NODE_TIME desc
    </select>

    <select id="selectAssetList" parameterType="java.util.Map" resultType="cn.iocoder.yudao.module.mall.external.assets.dto.sibide.ExtSiBiDeAssetsDetailDTO">
        select
            DATA_ID as orderNo,
            DATA_LIST_ID as orderDetailNo,
            BPM_NO as acceptNo,
            ASSETS_CODE as assetCode,
            ASSETS_NAME as assetName,
            ASSETS_SORT_GB_CODE as category16,
            ASSETS_SORT_CODE as category6,
            ASSETS_TYPE as assetType,
            PRICE as price,
            NUM as count,
            BASE_ROOM_NAME as placeName,
            DATA_STATUS as assetStatus
        from SEC_MALL_DJ.ASSETS_ACC_PUR_DATA
        <where>
            <if test="params.orderNo != null">
                and DATA_ID = #{params.orderNo}
            </if>
            <if test="params.orderDetailNo != null">
                and DATA_LIST_ID = #{params.orderDetailNo}
            </if>
            <if test="params.bpmNo != null">
                and BPM_NO = #{params.bpmNo}
            </if>
        </where>
    </select>

</mapper>