<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.trade.dal.mysql.purchase.PurchaseMapper">

    <resultMap id="PurchaseDO" type="cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO">
        <result column="id" property="id"></result>
        <result column="status" property="status"></result>
        <result column="user_id" property="userId"></result>
        <result column="user_name" property="userName"></result>
        <result column="economic_code" property="economicCode"></result>
        <result column="order_count" property="orderCount"></result>
        <result column="total_amount" property="totalAmount"></result>
        <result column="project_no" property="projectNo"></result>
        <result column="project_name" property="projectName"></result>
        <result column="project_type" property="projectType"></result>
        <result column="project_charge_no" property="projectChargeNo"></result>
        <result column="project_charge_name" property="projectChargeName"></result>
        <result column="project_department_name" property="projectDepartmentName"></result>
        <result column="project_department_no" property="projectDepartmentNo"></result>
        <result column="ycrh_remark" property="ycrhRemark"></result>
        <result column="bpm_no" property="bpmNo"></result>
        <result column="purchase_reason" property="purchaseReason"></result>
        <result column="attachments" property="attachments"></result>
        <result column="audit_status" property="auditStatus"></result>
        <result column="audit_result" property="auditResult"></result>
        <result column="cancel_time" property="cancelTime"></result>
        <result column="accepter_name" property="accepterName"></result>
        <result column="accepter_mobile" property="accepterMobile"></result>
        <result column="accepter_email" property="accepterEmail"></result>
        <result column="accepter_notify_time" property="accepterNotifyTime"></result>
        <result column="tenant_id" property="tenantId"></result>
        <result column="create_time" property="createTime"></result>
        <result column="update_time" property="updateTime"></result>
    </resultMap>

    <select id="selectById2" resultMap="PurchaseDO">
        select t1.* from trade_purchase t1 where
              t1.id = #{id}
             <if test="userId != null">
                and t1.user_id = #{userId}
             </if>
             and t1.deleted = 0
    </select>

    <select id="selectPage2" parameterType="cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo.PurchasePageReqVO" resultMap="PurchaseDO">
        select
            t1.id,
            t1.status,
            t1.user_id,
            t2.nickname as user_name,
            t1.economic_code,
            t1.order_count,
            t1.total_amount,
            t1.project_no,
            t1.project_name,
            t1.project_type,
            t1.project_charge_no,
            t1.project_charge_name,
            t1.project_department_name,
            t1.project_department_no,
            t1.ycrh_remark,
            t1.bpm_no,
            t1.audit_status,
            t1.cancel_time,
            t1.create_time,
            t1.update_time
        from trade_purchase t1 left join member_user t2 on t1.user_id = t2.id
        <where>
            t1.deleted = 0
            <if test="params.id != null">
                and t1.id = #{params.id}
            </if>
            <if test="params.bpmNo != null and params.bpmNo != ''">
                and t1.bpm_no like concat('%', #{params.bpmNo}, '%')
            </if>
            <if test="params.userId != null">
                and t1.user_id = #{params.userId}
            </if>
            <if test="params.userName != null and params.userName != ''">
                and exists(select 1 from member_user t2 where t1.user_id = t2.id and t2.nickname like concat(#{params.userName}, '%'))
            </if>
            <if test="params.userNo != null and params.userNo != ''">
                and exists(select 1 from member_user t2 where t1.user_id = t2.id and t2.user_no = #{params.userNo} )
            </if>
            <if test="params.status != null">
                and t1.status = #{params.status}
            </if>
            <if test="params.auditStatus != null">
                and t1.audit_status = #{params.auditStatus}
            </if>
            <if test="params.projectNo != null and params.projectNo != ''">
                and t1.project_no = #{params.projectNo}
            </if>
            <if test="params.projectName != null and params.projectName != ''">
                and t1.project_name like concat(#{params.projectName}, '%')
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                and exists(select 1 from trade_purchase_order t3, trade_order t4 where t1.id = t3.purchase_id and t3.order_id = t4.id and t4.no = #{params.orderNo} )
            </if>
            <if test="params.skuName != null and params.skuName != ''">
                and exists(select 1 from trade_purchase_order t31, trade_order t41, trade_order_item t51
                        where t1.id = t31.purchase_id and t31.order_id = t41.id and t41.id = t51.order_id and t51.sku_name like concat(#{params.skuName}, '%')
                )
            </if>
            <if test="params.skuId != null and params.skuId != ''">
                and exists(select 1 from trade_purchase_order t32, trade_order t42, trade_order_item t52
                where t1.id = t32.purchase_id and t32.order_id = t42.id and t42.id = t52.order_id and t52.sku_id = #{params.skuId}
                )
            </if>
            <if test="params.createTime != null and params.createTime.length == 2">
                and t1.create_time between #{params.createTime[0]} and #{params.createTime[1]}
            </if>
        </where>
        order by t1.id desc
    </select>


</mapper>