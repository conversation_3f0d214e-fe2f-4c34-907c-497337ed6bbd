<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.trade.dal.mysql.orderstatistics.OrderStatisticsMapper">


    <select id="queryOrderStatistics" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderStatisticsVO">
        select sum(order_num) as orderNum, sum(order_amount) as orderAmount, statics_time as staticsTime
        from trade_order_statistics
        where  deleted = 0
        <if test="supplierId != null">
            and supplier_id = #{supplierId}
        </if>
        and type = #{type}
        and statics_time &gt;=  #{staticsStartTime} and statics_time &lt;=  #{staticsEndTime}
        group by statics_time
    </select>



    <select id="queryOrderStatisticsTotal" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderStatisticsTotalResult">
        select count(*) as orderNum, status,sum(order_price) as orderAmount
        from trade_order
        where  deleted = 0
        and parent_type = 0
        <if test="supplierId != null">
            and supplier_id = #{supplierId}
        </if>
        group by status
    </select>

    <select id="querySettleOrderStatisticsTotal" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderStatisticsTotalResult">
        select count(*) as orderNum, settle_status as status,sum(order_price) as orderAmount
        from trade_order
        where  deleted = 0
        and parent_type = 0
        <if test="supplierId != null">
            and supplier_id = #{supplierId}
        </if>
        group by settle_status
    </select>

    <select id="getSupplierSellProportion" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.SupplierSellProportionRespVO">
        SELECT t1.supplier_id                                     AS supplierId,
        t1.supplier_name                                   AS supplierName,
        count(1)                                           AS orderCount,
        SUM(t1.order_price - COALESCE(t1.refund_price, 0)) AS totalAmount,
        (
        SUM(t1.order_price - COALESCE(t1.refund_price, 0))
        /
        (SELECT SUM(order_price - COALESCE(refund_price, 0))
        FROM trade_order
        WHERE tenant_id = #{tenantId}
        AND status NOT IN (1, 9)
        AND parent_type = 0
        AND deleted = 0)
        ) * 100                                        AS proportion
        FROM trade_order t1
        WHERE t1.tenant_id = #{tenantId}
        AND t1.status NOT IN (1, 9)
        AND t1.parent_type = 0
        AND t1.deleted = 0
        GROUP BY t1.supplier_id, t1.supplier_name
        ORDER BY totalAmount DESC;
    </select>

    <select id="getSupplierSellProductCountSummary" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.SupplierSellCountSummaryRespVO">
        SELECT
        product_sku.supplier_name,
        SUM( trade_order_item.count - trade_order_item.after_sale_count ) AS sku_sell_total
        FROM
        trade_order_item
        INNER JOIN trade_order ON trade_order_item.order_id = trade_order.id
        INNER JOIN product_sku ON trade_order_item.sku_id = product_sku.id
        WHERE
        trade_order.STATUS NOT IN ( 1, 9 )
        AND trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND trade_order_item.deleted = 0
        AND trade_order.deleted = 0
        AND trade_order_item.tenant_id = #{tenantId}
        GROUP BY
        supplier_name
        ORDER BY
        sku_sell_total DESC;
    </select>

    <select id="getSellProductTotalSummary" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.ProductSellCountSummaryRespVO">
        SELECT
        product_sku.id AS skuId,
        product_sku.sku_name AS productName,
        SUM( trade_order_item.count - trade_order_item.after_sale_count ) AS sellTotal
        FROM
        trade_order_item
        INNER JOIN trade_order ON trade_order_item.order_id = trade_order.id
        INNER JOIN product_sku ON trade_order_item.sku_id = product_sku.id
        WHERE
        trade_order.STATUS NOT IN (1, 9)
        AND trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND trade_order_item.deleted = 0
        AND trade_order_item.tenant_id = #{tenantId}
        GROUP BY
        skuId,
        productName
        ORDER BY
        sellTotal DESC
        LIMIT 10;
    </select>

    <select id="getSellProductAmountSummary" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.ProductSellAmountSummaryRespVO">
        SELECT
        product_sku.id AS skuId,
        product_sku.sku_name AS productName,
        SUM( ( trade_order_item.count - trade_order_item.after_sale_count ) * trade_order_item.sku_price ) AS sellAmount
        FROM
        trade_order_item
        INNER JOIN trade_order ON trade_order_item.order_id = trade_order.id
        INNER JOIN product_sku ON trade_order_item.sku_id = product_sku.id
        WHERE
        trade_order.STATUS NOT IN ( 1, 9 )
        AND trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND trade_order_item.deleted = 0
        AND trade_order_item.tenant_id = #{tenantId}
        GROUP BY
        skuId,
        productName
        ORDER BY
        sellAmount DESC
        LIMIT 10;
    </select>

    <select id="getSellCount" resultType="Long">
        SELECT
        SUM( trade_order_item.count - trade_order_item.after_sale_count ) AS sellCount
        FROM
        trade_order_item
        INNER JOIN trade_order ON trade_order_item.order_id = trade_order.id
        WHERE
        trade_order.STATUS NOT IN ( 1, 9 )
        AND trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND trade_order_item.deleted = 0
        AND trade_order_item.tenant_id = #{tenantId};
    </select>

    <select id="getSellAmount" resultType="BigDecimal">
        SELECT
        SUM( order_price - refund_price )
        FROM
        trade_order
        WHERE
        trade_order.STATUS NOT IN ( 1, 9 )
        AND trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND tenant_id = #{tenantId};
    </select>

    <select id="getNotSettleAmount" resultType="BigDecimal">
        SELECT
        SUM( order_price - refund_price ) AS orderAmount
        FROM
        trade_order
        WHERE
        trade_order.STATUS = 8
        AND trade_order.settle_status = 0
        AND trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND tenant_id = #{tenantId};
    </select>

    <select id="getOrderSummary" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderSummaryVO">
        SELECT
        trade_order.STATUS AS orderStatus,
        COUNT( 1 ) AS orderCount,
        SUM( order_price - refund_price ) AS orderAmount
        FROM
        trade_order
        WHERE
        trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND tenant_id = #{tenantId}
        GROUP BY
        trade_order.STATUS;
    </select>

    <select id="getAfterSaleSummary" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderSummaryVO">
        SELECT
        COUNT( 1 ) AS orderCount,
        SUM( order_price - refund_price ) AS orderAmount
        FROM
        trade_order
        WHERE
        trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND tenant_id = #{tenantId}
        AND trade_order.id IN (
        SELECT DISTINCT
        trade_after_sale.order_id AS id
        FROM
        trade_after_sale
        WHERE
        deleted = 0
        AND trade_after_sale.`status` NOT IN ( 61 ));
    </select>

    <select id="getAuditSummary" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderSummaryVO">
        SELECT
        trade_order.audit_status AS auditStatus,
        COUNT( 1 ) AS orderCount,
        SUM( order_price - refund_price ) AS orderAmount
        FROM
        trade_order
        WHERE
        trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND trade_order.`status` NOT IN ( 1, 9 )
        AND tenant_id = #{tenantId}
        GROUP BY
        auditStatus;
    </select>

    <select id="getSettlementSummary" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderSummaryVO">
        SELECT
        trade_order.settle_status AS settlementStatus,
        COUNT( 1 ) AS orderCount,
        SUM( order_price - refund_price ) AS orderAmount
        FROM
        trade_order
        WHERE
        trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND trade_order.`status` NOT IN ( 1, 9 )
        AND tenant_id = #{tenantId}
        GROUP BY
        settlementStatus;
    </select>

    <select id="getOrderSummaryByDept" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderDeptSummaryRespVO">
        SELECT
        member_user.dept_name AS deptName,
        SUM( order_price - refund_price ) AS orderAmount,
        COUNT(1) AS orderCount
        FROM
        trade_order
        INNER JOIN member_user ON trade_order.user_id = member_user.id
        WHERE
        trade_order.STATUS NOT IN ( 1, 9 )
        AND trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND trade_order.tenant_id = #{tenantId}
        GROUP BY
        member_user.dept_name
        ORDER BY
        orderAmount DESC
    </select>

    <select id="getOrderSummaryByProject" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderProjectSummaryRespVO">
        SELECT
        trade_purchase.project_name AS projectName,
        COUNT( trade_order.id ) AS orderCount,
        SUM( trade_order.order_price - trade_order.refund_price ) AS orderAmount
        FROM
        trade_order
        JOIN trade_purchase_order ON trade_order.id = trade_purchase_order.order_id
        JOIN trade_purchase ON trade_purchase_order.purchase_id = trade_purchase.id
        WHERE
        trade_order.STATUS NOT IN ( 1, 9 )
        AND trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND trade_order.tenant_id = #{tenantId}
        GROUP BY
        projectName
        ORDER BY
        orderAmount DESC
        LIMIT 10;
    </select>

    <select id="getOrderSummaryBySupplier" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderSupplierSummaryRespVO">
        SELECT
        trade_order.supplier_name AS supplierName,
        COUNT( trade_order.id ) AS orderCount,
        SUM( trade_order.order_price - trade_order.refund_price ) AS orderAmount
        FROM
        trade_order
        WHERE
        trade_order.STATUS NOT IN ( 1, 9 )
        AND trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND trade_order.tenant_id = #{tenantId}
        GROUP BY
        supplierName
        ORDER BY
        orderAmount DESC
        LIMIT 10;
    </select>

    <select id="getAfterSaleOrderSummary" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderAfterSaleSummaryRespVO">
        SELECT
        trade_order.supplier_name AS supplierName,
        COUNT( 1 ) AS orderCount,
        SUM( order_price - refund_price ) AS orderAmount
        FROM
        trade_order
        WHERE
        trade_order.settle_status = 0
        AND trade_order.parent_type = 0
        AND trade_order.deleted = 0
        AND tenant_id = #{tenantId}
        AND trade_order.id IN (
        SELECT DISTINCT
        trade_after_sale.order_id AS id
        FROM
        trade_after_sale
        WHERE
        deleted = 0
        AND trade_after_sale.`status` NOT IN ( 61 ))
        GROUP BY
        trade_order.supplier_name
        ORDER BY
        orderCount DESC;
    </select>

    <select id="dailySaleSummary" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderSaleDaySummaryRespVO">
        <![CDATA[
        WITH RECURSIVE DateRange AS (
            SELECT
                #{orderSaleSummaryReqVO.startTime} AS DAY
            UNION ALL
            SELECT
                DATE_ADD(DAY, INTERVAL 1 DAY)
            FROM
                DateRange
            WHERE
                DAY < #{orderSaleSummaryReqVO.endTime}
        )
        SELECT
            DATE_FORMAT(t.DAY, '%Y-%m-%d') AS timeString,
            COALESCE(COUNT(trade_order.id), 0) AS count,
            COALESCE(SUM(trade_order.order_price - trade_order.refund_price), 0.00) AS amount
        FROM
            DateRange t
        LEFT JOIN
            trade_order ON DATE(trade_order.submit_time) = t.DAY
            AND trade_order.deleted = 0
            AND trade_order.parent_type = 0
            AND trade_order.STATUS NOT IN (1, 9)
            AND tenant_id = #{tenantId}
        GROUP BY
            t.DAY
        ORDER BY
            t.DAY;
    ]]>
    </select>

    <select id="weeklySalesSummary" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderSaleWeekSummaryRespVO">
        <![CDATA[
            WITH RECURSIVE DateRange AS ( SELECT #{orderSaleSummaryReqVO.startTime} AS date
                UNION ALL SELECT DATE + INTERVAL 1 DAY FROM DateRange WHERE DATE + INTERVAL 1 DAY <= #{orderSaleSummaryReqVO.endTime}
            ) SELECT
            YEARWEEK( dr.DATE, 1 ) AS WEEK,
            MIN(
            DATE_FORMAT( DATE_SUB( dr.DATE, INTERVAL WEEKDAY( dr.DATE ) DAY ), '%Y-%m-%d' )) AS weekStart,
            MAX(
            DATE_FORMAT( DATE_ADD( DATE_SUB( dr.DATE, INTERVAL WEEKDAY( dr.DATE ) DAY ), INTERVAL 6 DAY ), '%Y-%m-%d' )) AS weekEnd,
            COALESCE ( COUNT( trade_order.id ), 0 ) AS count,
            COALESCE ( SUM( trade_order.order_price - trade_order.refund_price ), 0.00 ) AS amount
            FROM
                DateRange dr
                LEFT JOIN trade_order ON YEARWEEK( trade_order.submit_time, 1 ) = YEARWEEK( dr.DATE, 1 )
                AND trade_order.deleted = 0
                AND trade_order.parent_type = 0
                AND trade_order.STATUS NOT IN ( 1, 9 )
                AND tenant_id = #{tenantId}

            WHERE
                dr.DATE BETWEEN #{orderSaleSummaryReqVO.startTime} AND #{orderSaleSummaryReqVO.endTime}

            GROUP BY
                YEARWEEK( dr.DATE, 1 )
            ORDER BY
                weekStart;
        ]]>
    </select>

    <select id="monthlySalesSummary" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderSaleMonthSummaryRespVO">
        <![CDATA[
        WITH RECURSIVE DateRange AS (
            SELECT #{orderSaleMonthSummaryReqVO.startMonth} AS MONTH
            UNION ALL
            SELECT DATE_FORMAT(DATE_ADD(CONCAT(MONTH, '-01'), INTERVAL 1 MONTH), '%Y-%m')
            FROM DateRange
            WHERE MONTH < #{orderSaleMonthSummaryReqVO.endMonth}
        )
        SELECT
            t.MONTH AS MONTH,
            COALESCE(COUNT(trade_order.id), 0) AS count,
            COALESCE(SUM(trade_order.order_price - trade_order.refund_price), 0.00) AS amount
        FROM
            DateRange t
            LEFT JOIN trade_order
                ON DATE_FORMAT(trade_order.submit_time, '%Y-%m') = t.MONTH
                AND trade_order.deleted = 0
                AND trade_order.parent_type = 0
                AND trade_order.STATUS NOT IN (1, 9)
                AND tenant_id = #{tenantId}
        GROUP BY
            t.MONTH
        ORDER BY
            t.MONTH;
    ]]>
    </select>


</mapper>