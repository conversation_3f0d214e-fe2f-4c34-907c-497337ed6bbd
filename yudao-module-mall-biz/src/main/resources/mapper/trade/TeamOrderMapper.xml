<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.trade.dal.mysql.order.TradeTeamOrderMapper">

    <select id="selectPage2" parameterType="cn.iocoder.yudao.module.mall.trade.controller.app.teamorder.vo.AppTradeTeamOrderPageReqVO" resultType="cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeTeamOrderDO">
        select
            t1.*
        from trade_team_order t1
        <where>
            t1.deleted = 0
            <if test="params.no != null and params.no != ''">
                and t1.no like concat('%', #{params.no}, '%')
            </if>
            <if test="params.status != null">
                and t1.status = #{params.status}
            </if>
            <if test="params.notStatus != null">
                and t1.status != #{params.notStatus}
            </if>
            <if test="params.userId != null">
                and t1.user_id = #{params.userId}
            </if>
            <if test="params.teamUserId != null">
                and t1.team_user_id = #{params.teamUserId}
            </if>
            <if test="params.userDeleted != null">
                and t1.user_deleted = #{params.userDeleted}
            </if>
            <if test="params.statusList != null and params.statusList.size() > 0">
                and t1.status in
                <foreach item="itemId" collection="params.statusList" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="params.notStatusList != null and params.notStatusList.size() > 0">
                and t1.status not in
                <foreach item="itemId" collection="params.notStatusList" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="params.keyword != null and params.keyword != ''">
                and exists(select 1 from trade_team_order_item t2 where t1.id = t2.team_order_id and t2.deleted = 0 and t2.sku_name LIKE CONCAT('%', #{params.keyword}, '%') )
            </if>
        </where>
        order by submit_time desc, id desc
    </select>


    <select id="selectStatsList" parameterType="cn.iocoder.yudao.module.mall.trade.controller.app.teamorder.vo.AppTradeTeamOrderPageReqVO" resultType="java.util.Map">
        select
        t1.status,
        count(*) as count
        from trade_team_order t1
        <where>
            t1.deleted = 0
            <if test="params.status != null">
                and t1.status = #{params.status}
            </if>
            <if test="params.notStatus != null">
                and t1.status != #{params.notStatus}
            </if>
            <if test="params.userId != null">
                and t1.user_id = #{params.userId}
            </if>
            <if test="params.teamUserId != null">
                and t1.team_user_id = #{params.teamUserId}
            </if>
            <if test="params.userDeleted != null">
                and t1.user_deleted = #{params.userDeleted}
            </if>
            <if test="params.statusList != null and params.statusList.size() > 0">
                and t1.status in
                <foreach item="itemId" collection="params.statusList" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="params.notStatusList != null and params.notStatusList.size() > 0">
                and t1.status not in
                <foreach item="itemId" collection="params.notStatusList" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
        </where>
        group by t1.status
    </select>


</mapper>