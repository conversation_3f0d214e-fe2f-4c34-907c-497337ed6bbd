spring:
  application:
    name: mall-server
  cloud:
    nacos:
      # Nacos Config 配置项，对应 NacosConfigProperties 配置属性类
      config:
        name: ${spring.application.name} # 使用的 Nacos 配置集的 dataId，默认为 spring.application.name
        file-extension: yaml # 使用的 Nacos 配置集的 dataId 的文件拓展名，同时也是 Nacos 配置集的配置格式，默认为 properties
        shared-configs:
          - data-id: service-env.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: log.yml
            group: DEFAULT_GROUP
            refresh: true
  profiles:
    active: local

server:
  port: 48083

