--- #################### 注册中心相关配置 ####################

spring:
  cloud:
    nacos:
      server-addr: 192.168.0.8:8848
      discovery:
        namespace: b106dbef-1598-47b9-81b0-a2f2cefcfa29 # 命名空间。这里使用 dev 开发环境
        username: nacos
        password: JcNa23weer!@rrt
        metadata:
          version: 1.0.0 # 服务实例的版本号，可用于灰度发布

--- #################### 配置中心相关配置 ####################

spring:
  cloud:
    nacos:
      # Nacos Config 配置项，对应 NacosConfigProperties 配置属性类
      config:
        server-addr: 192.168.0.8:8848 # Nacos 服务器地址
        namespace: b106dbef-1598-47b9-81b0-a2f2cefcfa29        # 命名空间 dev 的ID，不能直接使用 dev 名称。创建命名空间的时候需要指定ID为 dev，这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        name: ${spring.application.name} # 使用的 Nacos 配置集的 dataId，默认为 spring.application.name
        file-extension: yaml # 使用的 Nacos 配置集的 dataId 的文件拓展名，同时也是 Nacos 配置集的配置格式，默认为 properties
        username: nacos
        password: JcNa23weer!@rrt
