spring:
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Dubbo 或者 Feign 等会存在重复定义的服务

  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 168h # 设置过期时间为 168 小时

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui.html

knife4j:
  enable: true # 2.2 是否开启 Swagger 文档的 Knife4j UI 界面
  setting:
    language: zh_cn

# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    lazy-loading-enabled: true  # 启用懒加载
    aggressive-lazy-loading: false
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      # 重要说明：如果将配置放到 Nacos 时，请注意将 id-type 设置为对应 DB 的类型，否则会报错；详细见 https://gitee.com/zhijiantianya/yudao-cloud/issues/I5W2N0 讨论
      id-type: AUTO # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
#      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
#      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
#      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  type-aliases-package: ${yudao.info.base-package}.dal.dataobject
  mapper-locations: classpath*:mapper/**/*.xml

--- #################### RPC 远程调用相关配置 ####################
dubbo:
  scan:
    base-packages: ${yudao.info.base-package}.api # 指定 Dubbo 服务实现类的扫描基准包
  protocol:
    name: dubbo # 协议名称
    port: -1 # 协议端口，-1 表示自增端口，从 20880 开始
  registry:
    address: spring-cloud://localhost # 设置使用 Spring Cloud 注册中心

--- #################### MQ 消息队列相关配置 ####################
spring:
  cloud:
    # Spring Cloud Stream 配置项，对应 BindingServiceProperties 类
    stream:
      function:
        definition: "busConsumer;tenantInitConsumer;productSkuConsumer;orderAutoApproveConsumer;vopProductFetchConsumer;\
        clientVopSkuUpdateConsumer;clientTenantSkuPoolInitProducer;clientTenantSkuPoolChangeProducer;orderAssetsConsumer;\
        orderSplitConsumer;orderStatusConsumer;orderAfterSaleConsumer;vopProductMsgConsumer;vopOrderMsgConsumer;\
        orderBpmStatusConsumer;"
      # Binding 配置项，对应 BindingProperties Map
      bindings:
        tenantInitConsumer-in-0:
          destination: all_tenant_init
          group: mall_tenant_init_consumer_group
        productSkuIndexSync-out-0:
          destination: product_sku_index_sync
        productSkuConsumer-in-0:
          destination: product_sku_index_sync
          group: mall_product_sku_consumer_group
        orderAutoApprove-out-0:
          destination: order_auto_approve
        orderAutoApproveConsumer-in-0:
          destination: order_auto_approve
          group: mall_order_consumer_group
        vopProductFetch-out-0:
          destination: product_vop_fetch
        vopProductFetchConsumer-in-0:
          destination: product_vop_fetch
          group: mall_product_vop_consumer_group
        clientVopSkuUpdateConsumer-in-0:
          destination: client_vop_sku_update
          group: client_vop_sku_update_consumer_group
        clientTenantVopSkuPoolChangeProducer-out-0:
          destination: client_tenant_vop_sku_pool_change
          group: client_tenant_vop_sku_pool_change_producer_group
        orderAssets-out-0:
          destination: order_assets
        orderAssetsConsumer-in-0:
          destination: order_assets
          group: mall_order_assets_consumer_group
        orderSplit-out-0:
          destination: order_split
        orderSplitConsumer-in-0:
          destination: order_split
          group: mall_order_split_consumer_group
        orderStatus-out-0:
          destination: order_status
        orderStatusConsumer-in-0:
          destination: order_status
          group: mall_order_status_consumer_group
        orderAfterSale-out-0:
          destination: order_after_sale
        orderAfterSaleConsumer-in-0:
          destination: order_after_sale
          group: mall_order_after_sale_consumer_group
        vopProductMsg-out-0:
          destination: vop_product_msg
        vopProductMsgConsumer-in-0:
          destination: vop_product_msg
          group: mall_vop_product_msg_consumer_group
        vopOrderMsg-out-0:
          destination: vop_order_msg
        vopOrderMsgConsumer-in-0:
          destination: vop_order_msg
          group: mall_vop_order_msg_consumer_group
        orderBpmStatus-out-0:
          destination: order_bpm_msg
        orderBpmStatusConsumer-in-0:
          destination: order_bpm_msg
          group: mall_order_bpm_msg_consumer_group
      # Spring Cloud Stream RocketMQ 配置项
      rocketmq:
        # RocketMQ Binder 配置项，对应 RocketMQBinderConfigurationProperties 类
        binder:
          name-server: 127.0.0.1:9876 # RocketMQ Namesrv 地址
        default: # 默认 bindings 全局配置
          producer: # RocketMQ Producer 配置项，对应 RocketMQProducerProperties 类
            group: mall_producer_group # 生产者分组
            send-type: SYNC # 发送模式，SYNC 同步
        bindings:
          springCloudBusInput:
            consumer:
              message-model: BROADCASTING # 重要，解决 Spring Cloud Bus RocketMQ 默认不是 BROADCASTING 广播消费的问题

    # Spring Cloud Bus 配置项，对应 BusProperties 类
    bus:
      enabled: true # 是否开启，默认为 true
      id: ${spring.application.name}:${server.port} # 编号，Spring Cloud Alibaba 建议使用“应用:端口”的格式
      destination: springCloudBus # 目标消息队列，默认为 springCloudBus

--- #################### 定时任务相关配置 ####################

xxl:
  job:
    executor:
      appname: ${spring.application.name} # 执行器 AppName
      logpath: ${user.home}/logs/xxl-job/${spring.application.name} # 执行器运行日志文件存储磁盘路径
#    accessToken: 30b94b4e97184928b7646bc92200acac # 执行器通讯TOKEN

--- #################### 芋道相关配置 ####################

yudao:
  info:
    version: 1.0.0
    base-package: cn.iocoder.yudao.module.mall
  web:
    admin-ui:
      url: http://dashboard.yudao.iocoder.cn # Admin 管理后台 UI 的地址
  swagger:
    title: 商城
    description: 提供商城的所有功能
    version: ${yudao.info.version}
    base-package: ${yudao.info.base-package}
  error-code: # 错误码相关配置项
    constants-class-list:
      - cn.iocoder.yudao.module.mall.member.enums.ErrorCodeConstants
      - cn.iocoder.yudao.module.mall.pay.enums.ErrorCodeConstants
      - cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants
      - cn.iocoder.yudao.module.mall.promotion.enums.ErrorCodeConstants
      - cn.iocoder.yudao.module.mall.trade.enums.ErrorCodeConstants
      - cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
      - /admin-api/mall/vop-access-token/authorize/callback # vop授权code回调处理
      - /app-api/member/auth/sso-login # 统一身份登录
      - /app-api/member/auth/social-login # 社交快捷登录
      - /app-api/mall/basis-config/get #商城基础配置
      - /app-api/mall/open/** #商城开放接口
      - /app-api/trade/delivery/notify/callback #物流更新回调
      - /app-api/mall/external/** #商城外部接口
    ignore-tables:
      - config_product_category
      - config_delivery_company
      - mall_area

  sms-code: # 短信验证码相关的配置项
    expire-times: 10m
    send-frequency: 1m
    send-maximum-quantity-per-day: 10
    begin-code: 9999 # 这里配置 9999 的原因是，测试方便。
    end-code: 9999 # 这里配置 9999 的原因是，测试方便。
  trade:
    order:
      app-code: mall # 应用编码
      expire-time: 7d # 支付的过期时间

debug: false

mall:
  open-api:
    oauth-validate: true # 开放API打开校验

invoice:
  directory: /opt/invoicePdf

bill:
  directory: /opt/bill
