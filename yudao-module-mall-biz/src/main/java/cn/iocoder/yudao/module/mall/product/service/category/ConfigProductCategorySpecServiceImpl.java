package cn.iocoder.yudao.module.mall.product.service.category;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ConfigProductCategorySpecCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ConfigProductCategorySpecUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecPageReqVO;
import cn.iocoder.yudao.module.mall.product.convert.spec.ProductSpecConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategorySpecDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.category.ConfigProductCategorySpecMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.PRODUCT_SPEC_EXISTS_ERROR;

/**
 * 商品规格 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConfigProductCategorySpecServiceImpl implements ConfigProductCategorySpecService {

    @Resource
    private ConfigProductCategorySpecMapper specMapper;

    @Override
    @TenantIgnore
    public Long createSpec(ConfigProductCategorySpecCreateReqVO createReqVO) {
        List<ConfigProductCategorySpecDO> list = specMapper.selectList(Wrappers.<ConfigProductCategorySpecDO>lambdaQuery()
                .eq(ConfigProductCategorySpecDO::getCategoryId, createReqVO.getCategoryId())
                .eq(ConfigProductCategorySpecDO::getName, createReqVO.getName()));
        if(!CollectionUtils.isEmpty(list)){
            throw exception(PRODUCT_SPEC_EXISTS_ERROR);
        }
        // 插入
        ConfigProductCategorySpecDO spec = ProductSpecConvert.INSTANCE.convert(createReqVO);
        specMapper.insert(spec);
        // 返回
        return spec.getId();
    }

    @Override
    @TenantIgnore
    public void updateSpec(ConfigProductCategorySpecUpdateReqVO updateReqVO) {
        // 校验存在
        validateSpecExists(updateReqVO.getId());
        // 校验同一个分类下的规格名称是否重复
        List<ConfigProductCategorySpecDO> list = specMapper.selectList(Wrappers.<ConfigProductCategorySpecDO>lambdaQuery()
                .eq(ConfigProductCategorySpecDO::getSpecType, updateReqVO.getSpecType())
                .eq(ConfigProductCategorySpecDO::getName, updateReqVO.getName())
                .eq(ConfigProductCategorySpecDO::getCategoryId, updateReqVO.getCategoryId())
                .ne(ConfigProductCategorySpecDO::getId, updateReqVO.getId()));

        if(!CollectionUtils.isEmpty(list)){
            throw exception(PRODUCT_SPEC_EXISTS_ERROR);
        }

        // 更新
        ConfigProductCategorySpecDO updateObj = ProductSpecConvert.INSTANCE.convert(updateReqVO);
        specMapper.updateById(updateObj);
    }

    @Override
    @TenantIgnore
    public void deleteSpec(Long id) {
        // 校验存在
        validateSpecExists(id);
        // 删除
        specMapper.deleteById(id);
    }

    private void validateSpecExists(Long id) {
        if (specMapper.selectById(id) == null) {
            throw exception(PRODUCT_SPEC_EXISTS_ERROR);
        }
    }

    @Override
    @TenantIgnore
    public ConfigProductCategorySpecDO getSpec(Long id) {
        return specMapper.selectById(id);
    }

    @Override
    @TenantIgnore
    public List<ConfigProductCategorySpecDO> getSpecList(Collection<Long> ids) {
        return specMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public PageResult<ConfigProductCategorySpecDO> getSpecPage(ProductSpecPageReqVO pageReqVO) {
        return specMapper.selectPage(pageReqVO);
    }

    @Override
    @TenantIgnore
    public List<ConfigProductCategorySpecDO> getSpecList4product(Long productCategoryId) {
        List<ConfigProductCategorySpecDO> list = new ArrayList<>();
        if(productCategoryId != null) {
            list.addAll(getListByCategory(productCategoryId));
        }

        return list;
    }

    /**
     * 根据商品分类查询商品规格列表
     * @param productCategoryId
     * @return
     */
    private List<ConfigProductCategorySpecDO> getListByCategory(Long productCategoryId) {
        return specMapper.selectList(Wrappers.lambdaQuery(ConfigProductCategorySpecDO.class).
                eq(ConfigProductCategorySpecDO::getCategoryId, productCategoryId));
    }

}
