package cn.iocoder.yudao.module.mall.trade.controller.admin.orderoperatelog.vo;

import cn.iocoder.yudao.module.mall.trade.enums.order.OrderOperateTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
 * 订单操作日志记录 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class OrderOperateLogBaseVO {

    @Schema(description = "订单id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单id不能为空")
    private Long orderId;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "用户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户名称")
    private String userName;

    @Schema(description = "操作分类", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "操作分类不能为空")
    private Integer operateType;

    @Schema(description = "操作内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "操作内容不能为空")
    private String content;

    public String getOperateTypeDesc() {
        if(this.operateType == null) {
            return null;
        }
        OrderOperateTypeEnum orderOperateTypeEnum = OrderOperateTypeEnum.fromCode(this.operateType);
        if(orderOperateTypeEnum == null) {
            return null;
        }
        return orderOperateTypeEnum.getDesc();
    }
}
