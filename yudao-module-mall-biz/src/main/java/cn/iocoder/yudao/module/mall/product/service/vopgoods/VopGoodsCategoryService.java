package cn.iocoder.yudao.module.mall.product.service.vopgoods;

import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsCategoryItem;
import cn.iocoder.yudao.module.mall.product.convert.vopgoods.VopGoodsConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.category.ConfigProductCategoryMapper;
import cn.iocoder.yudao.module.mall.product.dal.mysql.category.ProductCategoryMapper;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getCategoryInfoList.GetCategoryInfoGoodsResp;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetCategoryInfoListResponse;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetChildCategoryListResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class VopGoodsCategoryService {

    @Resource
    private VopGoodsService vopGoodsService;
    @Resource
    private ProductCategoryMapper productCategoryMapper;
    @Resource
    private ConfigProductCategoryMapper configProductCategoryMapper;

    public List<VopGoodsCategoryItem> getChildCategoryList(Long parentCategoryId) {
        VopGoodsGetCategoryInfoListResponse response = vopGoodsService.getCategoryInfoList(String.valueOf(parentCategoryId));
        List<com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getCategoryInfoList.GetCategoryInfoGoodsResp> list = response.getOpenRpcResult().getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        VopGoodsGetChildCategoryListResponse listResponse = vopGoodsService.getChildCategoryList(list.get(0).getCategoryId());
        List<com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getChildCategoryList.GetCategoryInfoGoodsResp> childList = listResponse.getOpenRpcResult().getResult();
        if (CollectionUtils.isEmpty(childList)) {
            return Collections.emptyList();
        }
        List<VopGoodsCategoryItem> itemList = new ArrayList<>();
        childList.forEach(item -> {
            if (Objects.equals(item.getNeedShow(), ProductCategoryStatusEnum.DISABLE.getStatus())) {
                return;
            }
            VopGoodsCategoryItem categoryItem = VopGoodsConvert.INSTANCE.convertCategory(item);
            List<VopGoodsCategoryItem> childCategoryList = getChildCategoryList(categoryItem.getCategoryId());
            categoryItem.setChildCategoryList(childCategoryList);
            itemList.add(categoryItem);
        });
        return itemList;
    }

    public List<ProductCategoryDO> queryParentList(ProductCategoryDO categoryDO) {
        if (Objects.equals(categoryDO.getCategoryLevel(), 0)) {
            return Collections.emptyList();
        }

        if (Objects.equals(categoryDO.getCategoryLevel(), 1)) {
            return queryParentListById(categoryDO.getParentId());
        }
        if (Objects.equals(categoryDO.getCategoryLevel(), 2)) {
            List<ProductCategoryDO> categoryDOS = new ArrayList<>();
            List<ProductCategoryDO> itList = queryParentListById(categoryDO.getParentId());
            if (!CollectionUtils.isEmpty(itList)) {
                categoryDOS.addAll(itList);
                for (ProductCategoryDO category : itList) {
                    categoryDOS.addAll(queryParentListById(category.getParentId()));
                }
            }
            return categoryDOS;
        }
        return Collections.emptyList();
    }

    public List<ProductCategoryDO> queryParentListById(Long parentId){
        return productCategoryMapper.selectList(Wrappers.<ProductCategoryDO>lambdaQuery()
                .eq(ProductCategoryDO::getStatus, ProductCategoryStatusEnum.ENABLE.getStatus())
                .eq(ProductCategoryDO::getCategoryId, parentId));
    }

    /**
     * 筛选所有 第一级别的分类
     * @param parentCategoryId
     * @return
     */
    public List<VopGoodsCategoryItem> getAllLevelOneCategoryList(Long parentCategoryId) {
        VopGoodsGetChildCategoryListResponse response = vopGoodsService.getChildCategoryList(parentCategoryId);
        List<com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getChildCategoryList.GetCategoryInfoGoodsResp> list = response.getOpenRpcResult().getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<VopGoodsCategoryItem> itemList = new ArrayList<>();
        list.forEach(item -> {
            if (Objects.equals(item.getNeedShow(), ProductCategoryStatusEnum.DISABLE.getStatus())) {
                return;
            }
            itemList.add(VopGoodsConvert.INSTANCE.convertCategory(item));
        });
        return itemList;
    }


    public void init(){
//        6728, 13720, 9855, 9847, 16750, 12379, 13678, 9987, 27508, 12473, 17329, 27546, 9192, 6994, 6144, 6196, 9259, 14379, 1316, 1315, 1319, 1318, 1320, 18017, 18008, 6233, 652, 670, 15901, 5025, 737, 27983, 15980, 13314, 1620, 1672, 11729, 15298, 5272,
        String ss = "1713, 15248, 31443, 4053, 12259, 12218, 14065, 27338, 4938";

        String[] arr = ss.split(",");

        for (String s : arr) {
            List<ConfigProductCategoryDO> categoryDOS = new ArrayList<>();
            Long id = Long.parseLong(s.trim());
            VopGoodsGetCategoryInfoListResponse infoListResponse = vopGoodsService.getCategoryInfoList(String.valueOf(id));

            List<GetCategoryInfoGoodsResp> infoGoodsResps = infoListResponse.getOpenRpcResult().getResult();
            if (CollectionUtils.isEmpty(infoGoodsResps)) {
                continue;
            }
            categoryDOS.add(VopGoodsConvert.INSTANCE.convertCategoryDO(infoGoodsResps.get(0)));
            insertVO(id, categoryDOS);

            if (!CollectionUtils.isEmpty(categoryDOS)) {
                configProductCategoryMapper.insertBatch(categoryDOS);
            }
        }
    }


    public void insertVO(Long parentCategoryId, List<ConfigProductCategoryDO> categoryDOS) {
        VopGoodsGetChildCategoryListResponse listResponse = vopGoodsService.getChildCategoryList(parentCategoryId);
        List<com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getChildCategoryList.GetCategoryInfoGoodsResp> childList = listResponse.getOpenRpcResult().getResult();
        if (CollectionUtils.isEmpty(childList)) {
            return;
        }
        childList.forEach(item -> {
            categoryDOS.add(VopGoodsConvert.INSTANCE.convertCategoryDO(item));
            insertVO(item.getCategoryId(), categoryDOS);
        });
    }

    @TenantIgnore
    public ConfigProductCategoryDO queryConfigCategory(Long categoryId) {
        LambdaQueryWrapper<ConfigProductCategoryDO> wrapper = Wrappers.lambdaQuery(ConfigProductCategoryDO.class);
        wrapper.eq(ConfigProductCategoryDO::getCategoryId,categoryId);
        ConfigProductCategoryDO category = configProductCategoryMapper.selectOne(wrapper);
        if(category == null) {
            log.error("ConfigProductCategory-not-exist: {}", categoryId);
        }
        return category;
    }
}
