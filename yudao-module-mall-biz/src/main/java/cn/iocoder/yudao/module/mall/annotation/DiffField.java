package cn.iocoder.yudao.module.mall.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2024/1/27
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DiffField {

    /**
     * 字段的别名：不填则默认字段名
     */
    String name() default "";


    /**
     * 字段是否在DIFF中忽略：默认不忽略
     */
    boolean ignored() default false;


}
