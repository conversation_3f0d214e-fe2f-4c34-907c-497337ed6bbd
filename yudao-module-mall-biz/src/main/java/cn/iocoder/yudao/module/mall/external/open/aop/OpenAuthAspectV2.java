package cn.iocoder.yudao.module.mall.external.open.aop;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.common.OpenAppDO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.common.OpenPermissionDO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.common.openapp.OpenAppService;
import cn.iocoder.yudao.module.mall.basis.service.common.openapp.OpenPermissionService;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.external.open.annotation.OpenAuth;
import cn.iocoder.yudao.module.mall.external.open.context.OpenContextHolder;
import cn.iocoder.yudao.module.mall.external.open.vo.OpenHeaderInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 开放API认证切面V2
 * 所有请求必须使用POST方式，参数为JSON格式统一放在request-body中，header中必须携带以下信息：
 * - j-token: 由平台提供的应用令牌，存储在mall_open_app表中
 * - j-nonce: 随机数，20位以内字符串
 * - j-timestamp: 时间戳(System.currentTimeMillis)，有效期为10分钟
 * - j-sign: 签名
 * 签名算法：DigestUtil.sha256Hex(appKey + nonce + timestamp + data + appSecret)
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Aspect
@Component
@Order(-1)
public class OpenAuthAspectV2 {

    // 服务依赖
    @Resource
    private OpenAppService openAppService;
    @Resource
    private OpenPermissionService openPermissionService;
    @Resource
    private SupplierService supplierService;

    // 配置项
    @Value("${mall.open-api.oauth-validate:true}")
    private Boolean oauthValidateEnable;

    // 常量定义
    public static final String HEADER_TOKEN_V1 = "app-token";
    private static final String HEADER_NONCE_V1 = "app-nonce";
    private static final String HEADER_TIMESTAMP_V1 = "app-timestamp";
    private static final String HEADER_SIGN_V1 = "app-sign";

    public static final String HEADER_TOKEN = "j-token";
    public static final String HEADER_NONCE = "j-nonce";
    public static final String HEADER_TIMESTAMP = "j-time";
    public static final String HEADER_SIGN = "j-sign";

    private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();
    private static final int TIMESTAMP_LIFE_MINUTES = 10;
    private static final String DEBUG_SIGN = "jcySign2024";

    /**
     * 切入点：拦截所有开放API控制器的方法
     */
    @Pointcut("execution(public * cn.iocoder.yudao.module.mall.external.open.controller..*(..))")
    private void pointCut() {}

    /**
     * 环绕通知：处理开放API认证逻辑
     */
    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 1. 检查是否需要认证
        OpenAuth openAuth = getOpenAuthAnnotation(joinPoint);
        if (openAuth == null) {
            return joinPoint.proceed();
        }

        // 2. 执行前置验证
        if (!handlePreValidation()) {
            return null;
        }

        // 3. 执行业务方法
        try {
            return joinPoint.proceed();
        } finally {
            // 4. 清理上下文
            TenantContextHolder.clear();
        }
    }

    /**
     * 获取OpenAuth注解
     */
    private OpenAuth getOpenAuthAnnotation(ProceedingJoinPoint joinPoint) {
        // 先检查类上的注解
        OpenAuth openAuth = joinPoint.getTarget().getClass().getAnnotation(OpenAuth.class);
        if (openAuth != null) {
            return openAuth;
        }

        // 再检查方法上的注解
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        return method.getAnnotation(OpenAuth.class);
    }

    /**
     * 处理前置验证
     */
    private boolean handlePreValidation() {
        try {
            HttpServletRequest request = ServletUtils.getRequest();
            logRequestInfo(request);

            // 1. 验证请求方法
            validateRequestMethod(request);

            // 2. 构建并验证头部信息
            OpenHeaderInfoVO headerInfo = buildAndValidateHeaderInfo();

            // 3. 获取应用信息
            OpenAppDO openApp = openAppService.getOpenAppFromCache(headerInfo.getToken());
            validateOpenApp(headerInfo, openApp);

            // 4. 设置租户上下文
            TenantContextHolder.setTenantId(openApp.getTenantId());
            TenantContextHolder.setIgnore(false);

            // 5. 解析请求体
            String requestBody = getRequestBody(request);

            // 6. 验证签名
            validateSign(headerInfo, requestBody, openApp);

            // 7. 验证业务信息
            validateBizInfo(openApp, requestBody);

            return true;
        } catch (ServiceException e) {
            handleServiceException(e);
        } catch (Exception e) {
            log.error("开放接口调用校验异常", e);
            throw e;
        }
        return false;
    }

    /**
     * 记录请求信息
     */
    private void logRequestInfo(HttpServletRequest request) {
        String reqUri = request.getRequestURI();
        String reqIp = ServletUtils.getClientIP();
        log.info("open-api path：{}, IP：{}", reqUri, reqIp);
    }

    /**
     * 验证请求方法
     */
    private void validateRequestMethod(HttpServletRequest request) {
        if (ObjectUtil.notEqual(RequestMethod.POST.name(), request.getMethod())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_METHOD_INVALID);
        }
    }

    /**
     * 构建并验证头部信息
     */
    private OpenHeaderInfoVO buildAndValidateHeaderInfo() {
        HttpServletRequest request = ServletUtils.getRequest();
        OpenHeaderInfoVO headerInfo = new OpenHeaderInfoVO();

        // 获取头部信息（兼容新旧版本）
        headerInfo.setToken(getHeaderValue(request, HEADER_TOKEN, HEADER_TOKEN_V1));
        headerInfo.setNonce(getHeaderValue(request, HEADER_NONCE, HEADER_NONCE_V1));
        headerInfo.setSign(getHeaderValue(request, HEADER_SIGN, HEADER_SIGN_V1));
        String timestampStr = getHeaderValue(request, HEADER_TIMESTAMP, HEADER_TIMESTAMP_V1);

        // 验证必填字段
        validateRequiredHeaders(headerInfo, timestampStr);

        // 设置时间戳
        headerInfo.setTimestamp(Long.parseLong(timestampStr));

        // 如果验证关闭，直接返回
        if (isValidateDisabled()) {
            return headerInfo;
        }

        // 验证时间戳有效性
        validateTimestamp(headerInfo.getTimestamp());

        return headerInfo;
    }

    /**
     * 获取头部值（兼容新旧版本）
     */
    private String getHeaderValue(HttpServletRequest request, String newHeader, String oldHeader) {
        String value = request.getHeader(newHeader);
        return StrUtil.blankToDefault(value, request.getHeader(oldHeader));
    }

    /**
     * 验证必填头部字段
     */
    private void validateRequiredHeaders(OpenHeaderInfoVO headerInfo, String timestamp) {
        if (StrUtil.isBlank(headerInfo.getToken())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TOKEN_NULL);
        }
        if (StrUtil.isBlank(headerInfo.getNonce())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_NONCE_NULL);
        }
        if (StrUtil.isBlank(timestamp)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TIMESTAMP_NULL);
        }
        if (!NumberUtil.isNumber(timestamp)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TIMESTAMP_INVALID);
        }
        if (StrUtil.isBlank(headerInfo.getSign())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_SIGN_NULL);
        }
    }

    /**
     * 验证时间戳有效性
     */
    private void validateTimestamp(long timestamp) {
        long diff = Math.abs(System.currentTimeMillis() - timestamp);
        if (diff > DateUtils.MILLIS_PER_MINUTE * TIMESTAMP_LIFE_MINUTES) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TIMESTAMP_INVALID);
        }
    }

    /**
     * 获取请求体
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            return IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("open接口获取参数异常: {}", request.getServletPath(), e);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_PARAM_ERROR);
        }
    }

    /**
     * 验证开放应用
     */
    private void validateOpenApp(OpenHeaderInfoVO headerInfo, OpenAppDO openApp) {
        // 验证应用是否存在
        if (openApp == null) {
            log.warn("token无效: {}", headerInfo.getToken());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TOKEN_INVALID);
        }

        // 验证应用状态
        if (ObjectUtil.notEqual(openApp.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            log.warn("开放应用状态为关闭:{}", headerInfo.getToken());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TOKEN_INVALID);
        }

        // 验证IP白名单
        validateIpWhitelist(openApp);

        // 验证接口权限
        validateApiPermission(openApp);
    }

    /**
     * 验证IP白名单
     */
    private void validateIpWhitelist(OpenAppDO openApp) {
        if(StrUtil.isBlank(openApp.getIpWhiteList())) {
            return;
        }
        String reqIp = ServletUtils.getClientIP();
        List<String> ips = StrUtil.split(openApp.getIpWhiteList(), "\n");
        if (!CollUtil.contains(ips, reqIp)) {
            log.warn("源IP不在白名单: {}, {}", reqIp, openApp.getIpWhiteList());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_IP_BLOCKED);
        }
    }

    /**
     * 验证接口权限
     */
    private void validateApiPermission(OpenAppDO openApp) {
        OpenPermissionDO permission = openPermissionService.getFromCache(openApp.getBizType());
        if (permission == null) {
            log.warn("开放权限为空: {}", openApp.getBizType());
            return;
        }

        HttpServletRequest request = ServletUtils.getRequest();
        String servletPath = request.getServletPath();
        List<String> apiList = StrUtil.split(permission.getApiList(), ",");

        boolean authorized = false;
        // 优化后 - 避免每次创建流
        for (String apiPattern : apiList) {
            if (PATH_MATCHER.match(apiPattern, servletPath)) {
                authorized = true;
                break;
            }
        }

        if (!authorized) {
            log.info("接口访问无权限:{}, {}", servletPath, openApp.getBizType());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_API_NOT_AUTHORIZED);
        }
    }

    /**
     * 验证业务信息
     */
    private void validateBizInfo(OpenAppDO openApp, String requestBody) {
        if (ObjectUtil.equal(openApp.getBizType(), 1)) {
            validateSupplierInfo(openApp, requestBody);
        }
    }

    /**
     * 验证供应商信息
     */
    private void validateSupplierInfo(OpenAppDO openApp, String requestBody) {
        if (openApp.getSupplierId() == null) {
            log.error("开放应用下供应商ID为NULL,{}，配置数据异常", openApp.getAppKey());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_SUPPLIER_INVALID);
        }

        SupplierDO supplier = supplierService.getSupplier(openApp.getSupplierId());
        if (supplier == null) {
            log.error("开放应用下供应商ID不存在,{}, {}，配置数据异常", openApp.getAppKey(), openApp.getSupplierId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_SUPPLIER_INVALID);
        }

        // 初始化业务上下文
        HttpServletRequest request = ServletUtils.getRequest();
        OpenContextHolder.setSupplierId(openApp.getSupplierId());
        OpenContextHolder.setSupplierName(supplier.getName());
        WebFrameworkUtils.setOpenSupplierId(request, openApp.getSupplierId());

        log.info("open-api supplierName：{}, path：{} body：{}",
                supplier.getName(), request.getRequestURI(), requestBody);
    }

    /**
     * 验证签名
     */
    private void validateSign(OpenHeaderInfoVO headerInfo, String body, OpenAppDO openApp) {
        // 调试模式下允许特定签名通过
        if (isValidateDisabled() && ObjectUtil.equal(headerInfo.getSign(), DEBUG_SIGN)) {
            return;
        }

        // 验证请求体是否为JSON
        body = body == null ? "" : body;
        if (StringUtils.isNotBlank(body) && !JsonUtils.isJson(body)) {
            log.info("request body格式不为json: {}", body);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_PARAM_INVALID);
        }

        // 生成签名
        String signSource = openApp.getAppKey()
                + headerInfo.getNonce()
                + headerInfo.getTimestamp()
                + body
                + openApp.getAppSecret();

        String generatedSign = DigestUtil.sha256Hex(signSource);

        // 验证签名
        if (ObjectUtil.notEqual(headerInfo.getSign(), generatedSign)) {
            log.info("sign校验失败, 应用： {}，生成的sign：{}, 原始字符串: {}",
                    openApp.getAppKey(), generatedSign, signSource);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_SIGN_ERROR);
        }
    }

    /**
     * 处理服务异常
     */
    private void handleServiceException(ServiceException e) {
        log.info("开放接口调用校验失败: {}", e.getMessage());
        CommonResult<?> result = CommonResult.error(e.getCode(), e.getMessage());

        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (!(requestAttributes instanceof ServletRequestAttributes)) {
            throw e;
        }

        HttpServletResponse response = ((ServletRequestAttributes) requestAttributes).getResponse();
        if (response != null) {
            ServletUtils.writeJSON(response, result);
        }
    }

    /**
     * 检查是否禁用验证
     */
    private boolean isValidateDisabled() {
        return oauthValidateEnable == null || !oauthValidateEnable;
    }

}