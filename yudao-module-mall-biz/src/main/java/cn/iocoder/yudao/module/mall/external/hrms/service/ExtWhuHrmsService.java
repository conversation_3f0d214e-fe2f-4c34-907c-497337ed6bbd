package cn.iocoder.yudao.module.mall.external.hrms.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.HrmsConfigDO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtDeptDTO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtMemberDTO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtPageReqDTO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtPageRespDTO;
import cn.iocoder.yudao.module.mall.external.hrms.enums.HrmsClientTypeEnum;
import cn.iocoder.yudao.module.mall.external.hrms.impl.whu.WhuClient;
import cn.iocoder.yudao.module.mall.external.hrms.impl.whu.dto.*;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 武汉大学人员信息实现
 * <AUTHOR>
 */
@Component
@Slf4j
public class ExtWhuHrmsService extends AbstractExtHrmsService {

    @Resource
    private WhuClient whuClient;

    @Override
    public String getClientCode() {
        return HrmsClientTypeEnum.WHU.getType();
    }

    @Override
    public void fillMemberInfo(String userNo, MemberUserDO userDO) {
        HrmsConfigDO configDO = getConfigDO();
        if(configDO == null || !configDO.isActive()) {
            log.info("【外部人事系统】武大配置未启用，处理忽略");
            return;
        }
        WhuStaffDTO dto = whuClient.getStaffInfo(configDO, userNo);
        if(dto != null) {
            userDO.setName(dto.getName());
            userDO.setDeptName(dto.getDeptName());
            userDO.setDeptCode(dto.getDeptCode());
            userDO.setMobile(dto.getMobile());
            if(StringUtils.isBlank(userDO.getNickname())) {
                userDO.setNickname(dto.getName());
            }
        }
    }

    @Override
    public ExtMemberDTO queryMemberInfo(String userNo) {
        HrmsConfigDO configDO = getConfigDO();
        if(configDO == null || !configDO.isActive()) {
            log.info("【外部人事系统】武大配置未启用，处理忽略");
            return null;
        }

        WhuStaffDTO wuhDTO = whuClient.getStaffInfo(configDO, userNo);
        if(wuhDTO == null) {
            return null;
        }
        ExtMemberDTO extDTO = new ExtMemberDTO();
        BeanUtil.copyProperties(wuhDTO, extDTO);
        return extDTO;
    }

    @Override
    public ExtPageRespDTO<ExtMemberDTO> pageQueryMember(ExtPageReqDTO reqDTO) {
        HrmsConfigDO configDO = getConfigDO();
        if(configDO == null || !configDO.isActive()) {
            log.info("【外部人事系统】武大配置未启用，处理忽略");
            return null;
        }
        WhuStaffPageReqDTO targetReqDTO = new WhuStaffPageReqDTO();
        handlePageReqDTO(reqDTO, targetReqDTO);
        if(StrUtil.isNotBlank(reqDTO.getKeyword())) {
            if(Validator.isLetter(reqDTO.getKeyword())) {
                targetReqDTO.setXM(reqDTO.getKeyword());
            } else {
                targetReqDTO.setZGH(reqDTO.getKeyword());
            }
        }
        WhuPageRespDTO<WhuStaffDTO> targetRespDTO = whuClient.getStaffPage(configDO, targetReqDTO);
        return handlePageRespDTO(targetRespDTO, ExtMemberDTO.class);
    }

    @Override
    public ExtPageRespDTO<ExtDeptDTO> pageQueryDept(ExtPageReqDTO reqDTO) {
        HrmsConfigDO configDO = getConfigDO();
        if(configDO == null || !configDO.isActive()) {
            log.info("【外部人事系统】武大配置未启用，处理忽略");
            return null;
        }

        WhuDeptPageReqDTO targetReqDTO = new WhuDeptPageReqDTO();
        handlePageReqDTO(reqDTO, targetReqDTO);
        WhuPageRespDTO<WhuDeptDTO> targetRespDTO = whuClient.getDeptPage(configDO, targetReqDTO);
        return handlePageRespDTO(targetRespDTO, ExtDeptDTO.class);
    }

    private void handlePageReqDTO(ExtPageReqDTO reqDTO, WhuPageDTO targetReqDTO) {
        targetReqDTO.setPage(reqDTO.getPageNo());
        targetReqDTO.setPer_page(reqDTO.getPageSize());
    }

    private <T> ExtPageRespDTO<T> handlePageRespDTO(WhuPageRespDTO<?> targetRespDTO, Class<T> clazz) {
        ExtPageRespDTO<T> respDTO = new ExtPageRespDTO<>();
        if(targetRespDTO == null) {
            respDTO.setTotal(0L);
            return respDTO;
        }
        respDTO.setPageNo(targetRespDTO.getPage());
        respDTO.setPageSize(targetRespDTO.getPer());
        respDTO.setTotal(targetRespDTO.getTotal());
        respDTO.setList(BeanUtil.copyToList(targetRespDTO.getList(), clazz));

        return respDTO;
    }

}
