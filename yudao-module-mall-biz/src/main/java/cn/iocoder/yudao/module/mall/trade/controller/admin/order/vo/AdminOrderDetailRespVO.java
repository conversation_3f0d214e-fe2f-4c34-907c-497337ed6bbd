package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo.PurchaseRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.OrderDetailRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/8/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AdminOrderDetailRespVO extends OrderDetailRespVO {

    /**
     * 采购信息
     */
    private PurchaseRespVO purchaseInfo;

}
