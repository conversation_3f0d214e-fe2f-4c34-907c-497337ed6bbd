package cn.iocoder.yudao.module.mall.external.ycrh.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Getter
public enum YcrhMethodEnum {
    /**
     * 业财融合接口
     */
    GET_YG_INFO("getYgInfo","获取员工信息"),
    SEARCH_YG_INFO("getZwzgzd","检索员工信息"),
    GET_USER_PROJECT_INFO("getFzrxmxx","获取负责人项目信息"),
    CHECK_BALANCE("checkXmye","项目余额校验"),
    GET_BALANCE("getXmye","项目余额查询"),
    GET_BALANCE_BATCH("getXmyeByBatch","批量项目余额查询"),
    GET_BALANCE_INFO("getXmed","项目额度查询"),
    GET_BALANCE_INFO_BATCH("getXmedByBatch","批量项目额度查询"),
    GET_PROJECT_INFO("getXmxx","项目信息"),
    PUSH_ORDER("pushOrder","订单推送"),
    SPLIT_ORDER("splitOrder","订单拆分"),
    CANCEL_ORDER("cancelOrder","取消订单"),
    ORDER_AFTER_SALE("afsNotice","订单售后"),
    QUERY_ORDER("queryOrder","订单状态查询"),
    QUERY_ORDER_LIST("queryOrderList","订单状态批量查询"),
    QUERY_ORDER_NUMBER("queryOrderNumber","订单编号批量查询"),
    PUSH_ORDER_ASSET_INFO("pushOrderZcInfo","订单资产信息追加"),
    PUSH_BILL("pushBill","账单推送"),
    CANCEL_BILL("cancelBill","账单撤消"),
    GET_BILL("getBillStatus","账单查询"),
    GET_APPROVER("sp.getRoleAndUser","获取审批人员"),
    UPLOAD_VOUCHER("sp.uploadJsonStr","上传单据"),
    DELETE_VOUCHER("sp.deleteJsonStr","删除单据"),
    GET_VOUCHER("sp.getSpxxdlJsonStr","查询每级单据审批状态"),
    GET_FINAL_VOUCHER("sp.getSpztJsonStr","查询单据最终审批状态"),
    COMPLETE_VOUCHER("sp.completeApproval","归档审批单"),
    UPLOAD_BPM_ATTACHMENTS("sp.uploadAccessoryAutoByJsonParam","上传附件"),
    UPLOAD_VOUCHER_FILE("voucherUpload","上传凭证附件"),
    INVOICE_CHECK("checkInvoice","发票验真"),
    BILL_VOUCHER_DETAIL("getYybxd","预约报销单查询");


    private String methodName;

    private String desc;

    YcrhMethodEnum(String methodName, String desc) {
        this.methodName = methodName;
        this.desc = desc;
    }

    public static YcrhMethodEnum getByMethodName(String methodName) {
        for (YcrhMethodEnum item : YcrhMethodEnum.values()) {
            if (item.getMethodName().equals(methodName)) {
                return item;
            }
        }
        return null;
    }
}
