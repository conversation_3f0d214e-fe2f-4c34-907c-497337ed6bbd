package cn.iocoder.yudao.module.mall.product.service.seocard;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo.ProductSeoCardCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo.ProductSeoCardPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo.ProductSeoCardUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.seocard.ProductSeoCardDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 运营区域 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductSeoCardService {

    /**
     * 创建运营区域
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSeoCard(@Valid ProductSeoCardCreateReqVO createReqVO);

    /**
     * 更新运营区域
     *
     * @param updateReqVO 更新信息
     */
    void updateSeoCard(@Valid ProductSeoCardUpdateReqVO updateReqVO);

    /**
     * 更新状态
     * @param id
     * @param status
     */
    void updateSeoCardStatus(Long id, Integer status);

    /**
     * 删除运营区域
     *
     * @param id 编号
     */
    void deleteSeoCard(Long id);

    /**
     * 获得运营区域
     *
     * @param id 编号
     * @return 运营区域
     */
    ProductSeoCardDO getSeoCard(Long id);

    /**
     * 获得运营区域列表
     *
     * @param ids 编号
     * @return 运营区域列表
     */
    List<ProductSeoCardDO> getSeoCardList(Collection<Long> ids);

    List<ProductSeoCardDO> getSeoCardList(Collection<Long> ids, Integer status);

    /**
     * 获得运营区域分页
     *
     * @param pageReqVO 分页查询
     * @return 运营区域分页
     */
    PageResult<ProductSeoCardDO> getSeoCardPage(ProductSeoCardPageReqVO pageReqVO);


}
