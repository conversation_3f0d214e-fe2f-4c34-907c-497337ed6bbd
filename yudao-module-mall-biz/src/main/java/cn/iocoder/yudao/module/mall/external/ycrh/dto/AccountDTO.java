package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class AccountDTO {

    /**
     * 对方单位名称
     */
    @JsonProperty("DFDW")
    private String companyName;

    /**
     * 省(例:北京)
     */
    @JsonProperty("DFPRO")
    private String province;
    /**
     * 市(例:北京)
     */
    @JsonProperty("DFCITY")
    private String city;

    /**
     * 对方银行名称
     */
    @JsonProperty("DFYH")
    private String bankName;

    /**
     * 银行账号
     */
    @JsonProperty("DFZH")
    private String bankAccount;

    /**
     * 附言
     */
    @JsonProperty("BZ")
    private String remark;

    /**
     * 金额（传accountList集合时必填）
     */
    @JsonProperty("HJJE")
    private BigDecimal amount;
}
