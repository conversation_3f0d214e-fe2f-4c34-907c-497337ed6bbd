package cn.iocoder.yudao.module.mall.trade.controller.admin.settle.vo;

import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderBaseVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderItemBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 订单结算响应VO")
@Data
public class OrderSettleVO extends TradeOrderBaseVO {

    /**
     * 序号
     */
    @Schema(description = "订单ID")
    private Integer index;

    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private Long orderId;

    /**
     * 结算状态： 0 待结算 1已结算
     */
    @Schema(description = "结算状态： 0 待结算 1已结算")
    private Integer settleStatus;

    /**
     * 结算状态描述： 0 待结算 1已结算
     */
    @Schema(description = "结算状态描述： 0 待结算 1已结算")
    private String settleStatusDesc;

    @Schema(description = "是否线下结算")
    private Boolean offlineSettlement;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

//    /**
//     * 子订单编号
//     */
//    @Schema(description = "子订单编号")
//    private String childOrderNo;

//    /**
//     * 京东订单编号
//     */
//    @Schema(description = "京东订单编号")
//    private Long jdOrderId;

//    /**
//     * 京东子订单编号
//     */
//    @Schema(description = "京东子订单编号")
//    private Long childJdOrderId;

    /**
     * 工号
     */
    @Schema(description = "工号")
    private String receiverNo;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String receiverName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String receiverMobile;

    /**
     * 发票单号
     */
    @Schema(description = "发票单号")
    private String invoiceNo;

    /**
     * 订单创建时间
     */
    @Schema(description = "订单创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    /**
     * 订单更新时间
     */
    @Schema(description = "订单更新时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;

    /**
     * 商品金额
     *
     */
    @Schema(description = "商品金额")
    private BigDecimal productPrice;

    /**
     * 运费金额
     */
    @Schema(description = "运费金额")
    private BigDecimal deliveryPrice;

    /**
     * 订单总金额
     *
     */
    @Schema(description = "订单总金额")
    private BigDecimal orderTotalPrice;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 发票信息
     */
    @Schema(description = "发票信息")
    private TradeOrderInvoiceVO tradeOrderInvoice;


    /**
     * 订单项列表
     */
    @Schema(description = "订单项列表")
    private List<TradeOrderItemBaseVO> items;

}
