package cn.iocoder.yudao.module.mall.product.controller.app.productcomment;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo.AppProductCommentCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo.AppProductCommentOrderPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo.AppProductCommentRespVO;
import cn.iocoder.yudao.module.mall.product.convert.productcomment.ProductCommentConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentDO;
import cn.iocoder.yudao.module.mall.product.service.productcomment.ProductCommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 用户 APP - 商品评价
 */
@Tag(name = "用户 APP - 商品评价")
@RestController
@RequestMapping("/product/comment")
@Validated
public class AppProductCommentController {

    @Resource
    private ProductCommentService commentService;

    /**
     * 创建商品评价
     * @param createReqVO
     * @return
     */
    @PostMapping("/create")
    @Operation(summary = "创建商品评价")
    @PreAuthenticated
    public CommonResult<Long> createComment(@Valid @RequestBody AppProductCommentCreateReqVO createReqVO) {
        // 获取登录用户、用户 IP 地址
        Long loginUserId = getLoginUserId();
        return success(commentService.createComment(createReqVO,loginUserId));
    }

    /**
     * 删除商品评价
     * @param id
     * @return
     */
    @PostMapping("/delete")
    @Operation(summary = "删除商品评价")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthenticated
    public CommonResult<Boolean> deleteComment(@RequestParam("id") Long id) {
        commentService.deleteComment(id);
        return success(true);
    }

    /**
     * 获得商品评价
     * @param id
     * @return
     */
    @GetMapping("/get")
    @Operation(summary = "获得商品评价")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppProductCommentRespVO> getComment(@RequestParam("id") Long id) {
        ProductCommentDO comment = commentService.getComment(id);
        return success(ProductCommentConvert.INSTANCE.convert(comment));
    }

    /**
     * 获得商品评价
     * @param reqVO
     * @return
     */
    @GetMapping("/getProductCommentPage")
    @Operation(summary = "获得商品评价")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<PageResult<AppProductCommentRespVO>> getProductCommentPage(@Valid AppProductCommentOrderPageReqVO reqVO) {
        PageResult<ProductCommentDO> productCommentPage = commentService.getProductCommentPage(reqVO);
        return success(ProductCommentConvert.INSTANCE.convertPage(productCommentPage));
    }

    /**
     * 点赞
     * @param id
     * @return
     */
    @GetMapping("/like")
    @Operation(summary = "点赞")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult like(@RequestParam("id") Long id) {
        commentService.like(id);
        return success(null);
    }
}
