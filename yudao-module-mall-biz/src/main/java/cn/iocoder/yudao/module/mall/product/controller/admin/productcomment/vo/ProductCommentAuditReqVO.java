package cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo;

import cn.iocoder.yudao.module.mall.product.enums.comment.ProductCommentAuditStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 管理后台 - 商品评价审核 Request VO
 */
@Schema(description = "管理后台 - 商品评价审核 Request VO")
@Data
public class ProductCommentAuditReqVO {

    /**
     * 评论id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 审核状态  0-待审核 1-审核通过 2-审核拒绝
     * {@link ProductCommentAuditStatusEnum}
     */
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

}
