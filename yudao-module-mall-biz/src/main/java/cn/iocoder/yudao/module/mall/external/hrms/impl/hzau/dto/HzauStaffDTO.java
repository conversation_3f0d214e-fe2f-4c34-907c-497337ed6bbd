package cn.iocoder.yudao.module.mall.external.hrms.impl.hzau.dto;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 教职工信息
 * <AUTHOR>
 */
@Data
public class HzauStaffDTO {

    @JsonProperty("GH")
    private String userNo;
    @JsonProperty("XM")
    private String name;
    @JsonProperty("DWQC")
    private String deptName;
    @JsonProperty("YJDWBM")
    private String deptCode;
    /**
     * 101-在岗 102-内部退养 210-离岗 220-长期出国 310-离退休 320-离校 die-去世
     */
    @JsonProperty("ZGQKM")
    private String stateCode;
    private String stateName;

    public String getRemark() {
        if(ObjectUtil.equal("102", stateCode)) {
            return "内部退养";
        }
        if(ObjectUtil.equal("220", stateCode)) {
            return "长期出国";
        }
        if(ObjectUtil.equal("310", stateCode)) {
            return "离退休";
        }
        if(ObjectUtil.equal("320", stateCode)) {
            return "离校";
        }
        if(ObjectUtil.equal("die", stateCode)) {
            return "去世";
        }

        return null;
    }

}
