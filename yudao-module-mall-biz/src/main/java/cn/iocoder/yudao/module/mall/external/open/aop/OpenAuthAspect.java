//package cn.iocoder.yudao.module.mall.external.open.aop;
//
//import cn.hutool.core.util.NumberUtil;
//import cn.hutool.crypto.digest.DigestUtil;
//import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
//import cn.iocoder.yudao.framework.common.exception.ServiceException;
//import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
//import cn.iocoder.yudao.framework.common.pojo.CommonResult;
//import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
//import cn.iocoder.yudao.framework.common.util.object.ObjectUtils;
//import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
//import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
//import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
//import cn.iocoder.yudao.module.mall.basis.dal.dataobject.openapp.OpenAppDO;
//import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
//import cn.iocoder.yudao.module.mall.basis.service.openapp.OpenAppService;
//import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
//import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
//import cn.iocoder.yudao.module.mall.external.open.annotation.OpenAuth;
//import cn.iocoder.yudao.module.mall.external.open.context.OpenContextHolder;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.io.IOUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.time.DateUtils;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//import org.aspectj.lang.reflect.MethodSignature;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.lang.reflect.Method;
//import java.nio.charset.StandardCharsets;
//import java.util.Objects;
//
///**
// * 开放api目前只做了签名校验，
// * 所有请求直接用post方式，参数为json格式统一放request-body，header中必须携带以下信息：
// * header: app-token, nonce, timestamp, sign
// * app-token，值需要由平台提供，数据存储在mall_openapp表中；
// * app-nonce为随机数，20位以内字符串即可；
// * app-timestamp为时间戳，如system.currentTimeMillis，有效期为10分钟
// * app-sign为签名
// * sign算法：DigestUtil.sha256Hex(appKey + nonce + timestamp + data + appSecret)
// * <AUTHOR>
// * @since 2023-09-08
// */
//@Component
//@Aspect
//@Slf4j
//@Order(-1)
//public class OpenAuthAspect {
//
//    @Resource
//    private OpenAppService openappService;
//    @Resource
//    private SupplierService supplierService;
//    @Value("${mall.open-api.oauth-validate:true}")
//    private Boolean oauthValidateEnable;
//
//    private static final String HEADER_TOKEN = "app-token";
//    private static final String HEADER_NONCE = "app-nonce";
//    private static final String HEADER_TIMESTAMP = "app-timestamp";
//    private static final String HEADER_SIGN = "app-sign";
//    /**
//     * timestamp有效期，单位为分钟
//     */
//    private static final int TIMESTAMP_LIFE = 10;
//    /**
//     * 调试用的签名，当校验开关关闭时，会放行此调试签名
//     */
//    private static final String DEBUG_SIGN = "jcySign2024";
//
//    @Pointcut("execution(public * cn.iocoder.yudao.module.mall.external.open.controller..*(..))")
//    private void pointCut() {}
//
//    @Around("pointCut()")
//    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
//        // 判断类或方法是否包含OpenAuth注解
//        OpenAuth openAuthAnno = joinPoint.getTarget().getClass().getAnnotation(OpenAuth.class);
//        if(openAuthAnno == null) {
//            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
//            Method method = methodSignature.getMethod();
//            if (Objects.isNull(method)) {
//                return joinPoint.proceed();
//            }
//            openAuthAnno = method.getAnnotation(OpenAuth.class);
//            if(openAuthAnno == null) {
//                return joinPoint.proceed();
//            }
//        }
//        if(!handlePreValidation()) {
//            return null;
//        }
//
//        Object methodResult = joinPoint.proceed();
//        TenantContextHolder.clear();
//
//        return methodResult;
//    }
//
//    private boolean handlePreValidation() {
//        try {
//            HttpServletRequest request = ServletUtils.getRequest();
//            log.info("open-api path：{}, IP：{}",  request.getRequestURI(), ServletUtils.getClientIP());
//            String servletPath = request.getServletPath();
//            if(!request.getMethod().equalsIgnoreCase("post")) {
//                throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_METHOD_INVALID);
//            }
//            String appToken = request.getHeader(HEADER_TOKEN);
//            String nonce = request.getHeader(HEADER_NONCE);
//            String timestamp = request.getHeader(HEADER_TIMESTAMP);
//            String sign = request.getHeader(HEADER_SIGN);
//            validateHeader(appToken, nonce, timestamp, sign);
//
//            appToken = appToken.trim();
//            OpenAppDO openapp = openappService.getOpenAppFromCache(appToken);
//            if(openapp == null) {
//                log.warn("app-token无效: {}", appToken);
//                throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TOKEN_INVALID);
//            }
//            TenantContextHolder.setTenantId(openapp.getTenantId());
//            TenantContextHolder.setIgnore(false);
//
//            SupplierDO supplier = supplierService.getSupplier(openapp.getSupplierId());
//            if(supplier == null) {
//                log.warn("供应商ID{}不存在，配置数据异常", openapp.getSupplierId());
//                throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_SUPPLIER_INVALID);
//            }
////            if(!supplier.getStatus().equals(SupplierStatusEnum.ENABLE.getStatus())) {
////                log.warn("供应商{}运营状态状态为关闭", supplier.getName());
////                throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_SUPPLIER_INVALID);
////            }
//            if(!openapp.getStatus().equals(CommonStatusEnum.ENABLE.getStatus())) {
//                log.warn("供应商{}开放接口已经关闭", supplier.getName());
//                throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TOKEN_INVALID);
//            }
//
//            OpenContextHolder.setSupplierId(openapp.getSupplierId());
//            OpenContextHolder.setSupplierName(supplier.getName());
//            WebFrameworkUtils.setOpenSupplierId(request, openapp.getSupplierId());
//
//            // 获取requestBody
//            String requestBody = "";
//            try {
//                requestBody = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
//            } catch (Exception e) {
//                log.error("open接口获取参数异常: {}", servletPath, e);
//                throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_PARAM_ERROR);
//            }
//
//            log.info("open-api supplierName：{}, path：{} body：{}", supplier.getName(), request.getRequestURI(), requestBody);
//            validateSign(nonce, Long.parseLong(timestamp), sign, requestBody, openapp);
//
//            return true;
//        } catch (ServiceException e) {
//            log.info("开放接口调用校验失败: {}", e.getMessage());
//            CommonResult<?> result = CommonResult.error(e.getCode(), e.getMessage());
//            HttpServletResponse response = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getResponse();
//            ServletUtils.writeJSON(response, result);
//        } catch (Exception e) {
//            log.error("开放接口调用校验异常", e);
//            throw e;
//        }
//
//        return false;
//    }
//
//    private void validateHeader(String appToken, String nonce, String timestamp, String sign) {
//        if(StringUtils.isBlank(appToken)) {
//            log.info("app-token不能为空");
//            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TOKEN_NULL);
//        }
//        if(StringUtils.isBlank(nonce)) {
//            log.info("nonce不能为空");
//            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_NONCE_NULL);
//        }
//        if(StringUtils.isBlank(timestamp)) {
//            log.info("timestamp不能为空");
//            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TIMESTAMP_NULL);
//        }
//        if(!NumberUtil.isNumber(timestamp)) {
//            log.info("timestamp值无效: {}", timestamp);
//            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TIMESTAMP_INVALID);
//        }
//
//        // 本地调试时可以放开校验
//        if(oauthValidateEnable == null || !oauthValidateEnable) {
//            return;
//        }
//        // 校验timestamp，误差10分钟内;
//        long nowMills = System.currentTimeMillis();
//        long diff = Math.abs(nowMills - Long.parseLong(timestamp));
//        if(diff > DateUtils.MILLIS_PER_MINUTE * TIMESTAMP_LIFE) {
//            log.info("timestamp失效");
//            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_TIMESTAMP_INVALID);
//        }
//
//        if(StringUtils.isBlank(sign)) {
//            log.info("sign不能为空");
//            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_SIGN_NULL);
//        }
//    }
//
//    private void validateSign(String nonce, long timestamp, String sign, String body, OpenAppDO openapp) {
//        // 本地调试时可以放开校验
//        if(oauthValidateEnable == null || !oauthValidateEnable) {
//            if(ObjectUtils.equalsAny(sign, DEBUG_SIGN)) {
//                // 只对调试签名放行
//                return;
//            }
//        }
//
//        body = body == null ? "" : body;
//        if(StringUtils.isNotBlank(body) && !JsonUtils.isJson(body)) {
//            log.info("request body格式不为json: {}", body);
//            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_PARAM_INVALID);
//        }
//        StringBuilder sb = new StringBuilder();
//        sb.append(openapp.getAppKey());
//        sb.append(nonce);
//        sb.append(timestamp);
//        sb.append(body);
//        sb.append(openapp.getAppSecret());
//        String generateSign = DigestUtil.sha256Hex(sb.toString());
//        if(!sign.equals(generateSign)) {
//            log.info("sign校验失败，生成的sign：{}, 拼接后字符串: {}", generateSign, sb.toString());
//            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_SIGN_ERROR);
//        }
//    }
//
//    public static void main(String[] args) {
//        String key = "061938997055140";
//        String secret = "2kjfooyffswf980tioyu6bi4tuyzs9cl";
//        String nonce = "aabbcddeew2";
//        String data = "{}";
//        long timestamp = System.currentTimeMillis();
//        String source = key + nonce + timestamp + data + secret;
//        System.out.println("timestamp===" + timestamp);
//        System.out.println("source===" + source);
//        System.out.println("sign===" + DigestUtil.sha256Hex(source));
//    }
//
//}
