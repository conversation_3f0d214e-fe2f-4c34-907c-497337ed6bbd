package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class CheckBalanceReqDTO {

    /**
     * 部门编号
     */
    @JsonProperty("bmbh")
    private String departmentNo;

    /**
     * 项目编号
     */
    @JsonProperty("xmbh")
    private String projectNo;

    /**
     * 项目负责人工号
     */
    @JsonProperty("xmfzrbh")
    private String chargeNo;

    /**
     * 校验金额
     */
    @JsonProperty("jyje")
    private BigDecimal checkAmount;

    /**
     * 经济分类科目编号
     */
    @JsonProperty("jjflkmbh")
    private String economyClass;

    /**
     * 商品明细
     */
    @JsonProperty("wbSkuInfo")
    private List<BalanceWbSkuInfoDTO> wbSkuInfo;

}
