package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Schema(description = "管理后台 - 商品 SKU 添加标签")
@Data
@ToString(callSuper = true)
public class ProductSkuAddTagReqVO  {

    @Schema(description = "标签编号")
    @NotEmpty
    private List<Long> tagIdList;

    @Schema(description = "商品SKU编号")
    @NotEmpty
    private List<Long> skuIdList;

}
