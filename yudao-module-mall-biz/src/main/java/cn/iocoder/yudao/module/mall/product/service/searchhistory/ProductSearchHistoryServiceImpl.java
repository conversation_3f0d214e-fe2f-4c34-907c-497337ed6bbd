package cn.iocoder.yudao.module.mall.product.service.searchhistory;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.redis.util.RedisUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.product.controller.admin.usersearchhistory.vo.UserSearchHistoryPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.searchhistory.ProductSearchHistoryDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.searchhistory.ProductSearchHistoryMapper;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.ttl.TtlRunnable;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.stat.descriptive.summary.Product;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.basis.dal.redis.RedisKeyConstants.MALL_PRODUCT_HOT_SEARCH;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.USER_SEARCH_HISTORY_NOT_EXISTS;

/**
 * 会员搜索历史 User Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Valid
@Slf4j
public class ProductSearchHistoryServiceImpl extends ServiceImpl<ProductSearchHistoryMapper, ProductSearchHistoryDO> implements ProductSearchHistoryService {

    @Resource
    private ProductSearchHistoryMapper searchHistoryMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ThreadPoolExecutor threadPoolExecutor;
    @Resource
    private RedisUtils redisUtils;

    @Override
    public List<ProductSearchHistoryDO> getList(Long userId, Integer count) {
        if(userId == null || count == null) {
            return null;
        }

        PageResult<ProductSearchHistoryDO> pageResult = searchHistoryMapper.selectPage(new PageParam().setPageSize(count),
                Wrappers.lambdaQuery(ProductSearchHistoryDO.class)
                .eq(ProductSearchHistoryDO::getUserId, userId)
                .orderByDesc(ProductSearchHistoryDO::getUpdateTime));

        return pageResult.getList();
    }

    @Override
    public List<ProductSearchHistoryDO> getHotList(Integer count) {

        // 优先读缓存
        List<ProductSearchHistoryDO> cacheList = redisUtils.get(MALL_PRODUCT_HOT_SEARCH+":"+TenantContextHolder.getTenantId());
        if (cacheList != null) {
            return cacheList;
        }

        QueryWrapper<ProductSearchHistoryDO> wrapper = new QueryWrapper<>();
        wrapper.select("keyword", "SUM(count) AS count")
                .apply("keyword REGEXP '^[一-龥]{2,7}$'")
                .groupBy("keyword")
                .orderByDesc("SUM(count)")
                .last("LIMIT " + count);

        List<ProductSearchHistoryDO> list = searchHistoryMapper.selectList(wrapper);

        redisUtils.set(MALL_PRODUCT_HOT_SEARCH+":"+TenantContextHolder.getTenantId(),list,5, TimeUnit.MINUTES);

        return list;

    }

    @Override
    public void deleteByUser(Long userId) {
        searchHistoryMapper.delete(Wrappers.lambdaQuery(ProductSearchHistoryDO.class)
                .eq(ProductSearchHistoryDO::getUserId, userId));
    }

    @Override
    public void deleteByKeyWord(Long userId,String keyword) {
        searchHistoryMapper.delete(Wrappers.lambdaQuery(ProductSearchHistoryDO.class)
                .eq(ProductSearchHistoryDO::getUserId, userId)
                .eq(ProductSearchHistoryDO::getKeyword, keyword));
    }

    @Override
    public PageResult<ProductSearchHistoryDO> getUserSearchHistoryPage(UserSearchHistoryPageReqVO reqVO) {

        // 分页查询用户搜索历史
        Page<ProductSearchHistoryDO> pageInfo = new Page(reqVO.getPageNo(), reqVO.getPageSize());
        searchHistoryMapper.selectSearchHistoryPage(pageInfo,reqVO);
        PageResult<ProductSearchHistoryDO> pageResult = new PageResult((int) pageInfo.getCurrent(), (int) pageInfo.getSize(), (int) pageInfo.getPages(), pageInfo.getRecords(), pageInfo.getTotal());
        return pageResult;
    }

    @Override
    public void deleteById(Long id) {
        validateUserSearchHistoryExists(id);
        searchHistoryMapper.deleteById(id);
    }

    private void validateUserSearchHistoryExists(Long id) {
        if (searchHistoryMapper.selectById(id) == null) {
            throw exception(USER_SEARCH_HISTORY_NOT_EXISTS);
        }
    }




    @Override
    public void addUserSearchHistory(Long userId, String keyword) {
        if(TenantContextHolder.getTenantId() == null) {
            return;
        }
        if(userId == null || StringUtils.isBlank(keyword)) {
            return;
        }
        // 忽略超长的关键字
        if(keyword.length() > 50) {
            return;
        }

        threadPoolExecutor.execute(TtlRunnable.get(() -> {
            String lockKey = "mall:lock:search" + userId;
            RLock lock = redissonClient.getLock(lockKey);
            try {
                lock.lock();

                ProductSearchHistoryDO persistDO = getByUserKeyword(userId, keyword);
                if(persistDO != null) {
                    Integer count = persistDO.getCount();
                    persistDO.setCount(count + 1);
                    persistDO.setUpdateTime(LocalDateTime.now());
                    searchHistoryMapper.updateById(persistDO);
                    return;
                }

                ProductSearchHistoryDO bean = new ProductSearchHistoryDO();
                bean.setUserId(userId);
                bean.setKeyword(keyword);
                bean.setCount(1);
                searchHistoryMapper.insert(bean);
            } catch (Exception e) {
                log.info("addUserSearchHistory error: ", e);
            } finally {
                if(lock.isHeldByCurrentThread() && lock.isLocked()) {
                    lock.unlock();
                }
            }
        }));
    }

    private ProductSearchHistoryDO getByUserKeyword(Long userId, String keyword) {
        return searchHistoryMapper.selectOne(Wrappers.lambdaQuery(ProductSearchHistoryDO.class)
                .eq(ProductSearchHistoryDO::getUserId, userId)
                .eq(ProductSearchHistoryDO::getKeyword, keyword));
    }

}
