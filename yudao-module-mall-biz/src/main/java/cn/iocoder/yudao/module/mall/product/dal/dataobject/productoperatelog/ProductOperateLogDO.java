package cn.iocoder.yudao.module.mall.product.dal.dataobject.productoperatelog;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 商品操作日志记录 DO
 *
 * <AUTHOR>
 */
@TableName("product_operate_log")
@KeySequence("product_operate_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductOperateLogDO extends BaseDO {

    /**
     * 日志主键
     */
    @TableId
    private Long id;

    /**
     * spuId
     */
    private Long spuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 操作分类
     */
    private Integer operateType;
    /**
     * 操作内容
     */
    private String content;

}
