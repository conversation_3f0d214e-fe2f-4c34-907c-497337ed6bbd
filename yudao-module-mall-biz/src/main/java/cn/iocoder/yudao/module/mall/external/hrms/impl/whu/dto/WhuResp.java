package cn.iocoder.yudao.module.mall.external.hrms.impl.whu.dto;

import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 武大信息中心开放平台统一返回类
 *
 * <AUTHOR>
 */
@Data
public class WhuResp<T> {

    private String code;

    private String message;

    private String description;

    private String uuid;

    private T result;

    @JsonIgnore
    public boolean isSuccess() {
        return "10000".equals(this.code);
    }

    /**
     * 如果失败 抛出异常
     *
     * @param checkFail
     * @return
     */
    public T getResult(boolean checkFail) {
        if (!isSuccess()) {
            throw exception(ErrorCodeConstants.HRMS_WHU_FAIL, this.message);
        }
        return result;
    }
}
