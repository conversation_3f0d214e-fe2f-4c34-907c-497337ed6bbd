package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
* 商品 SKU Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ProductSkuBaseVO {

    @Schema(description = "商品SPU编号", example = "小米")
    private Long spuId;

    @Schema(description = "商品SPU名称", example = "小米")
    private String spuName;

    @Schema(description = "供应商编号", example = "1")
    private Long supplierId;

    @Schema(description = "供应商类型", example = "1")
    private Integer supplierType;

    @Schema(description = "供应商名称", example = "小米")
    private String supplierName;

    @Schema(description = "商品 SKU 名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String skuName;

    @Schema(description = "市场价", example = "1024")
    private BigDecimal marketPrice;

    @Schema(description = "销售价", example = "1024")
    private BigDecimal salePrice;

    @Schema(description = "图片地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/xx.png")
    @NotNull(message = "图片地址不能为空")
    private String picUrl;

    @Schema(description = "SKU 状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "SKU 状态不能为空")
    @InEnum(ProductSpuStatusEnum.class)
    private Integer status;

    @Schema(description = "商品显示状态 1-显示 0-不显示", example = "1")
    private Integer showStatus;

    @Schema(description = "SEO运营状态 1-打开 0-关闭", example = "1")
    private Integer seoStatus;

    @Schema(description = "最低起始数量")
    private Integer lowestBuy;

    @Schema(description = "SKU 平台上下架状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(ProductSpuStatusEnum.class)
    private Integer platformStatus;

}
