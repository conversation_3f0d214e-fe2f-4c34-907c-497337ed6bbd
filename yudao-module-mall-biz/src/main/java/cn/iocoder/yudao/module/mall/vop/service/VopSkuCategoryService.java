package cn.iocoder.yudao.module.mall.vop.service;

import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportRespExcelVO;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportTask;
import cn.iocoder.yudao.module.mall.vop.controller.admin.vopskucategory.vo.*;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopSkuCategoryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 京东sku分类关系 Service 接口
 *
 * <AUTHOR>
 */
public interface VopSkuCategoryService extends IService<VopSkuCategoryDO> {

    /**
     * 创建京东sku分类关系
     *
     * @param createReqVO 创建信息
     * @param importTask 导入任务
     * @param importRespList 导入响应列表
     * @return 编号
     */
    Long createVopSkuCategory(@Valid VopSkuCategoryCreateReqVO createReqVO, AsyncImportTask importTask, List<AsyncImportRespExcelVO> importRespList);

    /**
     * 更新京东sku分类关系
     * @param updateReqVO 更新信息
     */
    void updateVopSkuCategory(@Valid VopSkuCategoryUpdateReqVO updateReqVO);

    /**
     * 删除京东sku分类关系
     *
     * @param id 编号
     */
    void deleteVopSkuCategory(Long id);

    /**
     * 获得京东sku分类关系
     *
     * @param id 编号
     * @return 京东sku分类关系
     */
    VopSkuCategoryDO getVopSkuCategory(Long id);

    /**
     * 获得京东sku分类关系列表
     *
     * @param ids 编号
     * @return 京东sku分类关系列表
     */
    List<VopSkuCategoryDO> getVopSkuCategoryList(Collection<Long> ids);

    /**
     * 获得京东sku分类关系分页
     *
     * @param pageReqVO 分页查询
     * @return 京东sku分类关系分页
     */
    PageResult<VopSkuCategoryDO> getVopSkuCategoryPage(VopSkuCategoryPageReqVO pageReqVO);

    /**
     * 获得京东sku分类关系列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 京东sku分类关系列表
     */
    List<VopSkuCategoryDO> getVopSkuCategoryList(VopSkuCategoryExportReqVO exportReqVO);

    /**
     * 根据京东商品编号获得京东sku分类关系
     * @param jdSkuId
     * @return
     */
    VopSkuCategoryDO getVopSkuCategoryByJdSkuId(String jdSkuId);

    /**
     * 获得京东sku分类关系列表
     * @return
     */
    List<VopSkuCategoryDO> getList();

    /**
     * 导入京东sku分类关系
     * @param list
     */
    void importExcel(List<VopSkuCategoryImportExcelVO> list);

    /**
     * 导入京东sku分类关系Excel文件
     * @param file
     */
    void importExcelFile(org.springframework.web.multipart.MultipartFile file);
}
