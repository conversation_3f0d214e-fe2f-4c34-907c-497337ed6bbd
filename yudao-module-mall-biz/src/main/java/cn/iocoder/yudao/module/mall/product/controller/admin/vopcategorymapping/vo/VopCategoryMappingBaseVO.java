package cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
 * 京东分类映射 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class VopCategoryMappingBaseVO {

    @Schema(description = "末级分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "末级分类ID不能为空")
    private Long lastCategoryId;

    @Schema(description = "末级分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "末级分类名称不能为空")
    private String lastCategoryName;

    @Schema(description = "完整分类id -分隔")
    private String fullCategoryId;

    @Schema(description = "完整分类名称")
    private String fullCategoryName;

    @Schema(description = "vop末级分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "vop末级分类ID不能为空")
    private Long vopLastCategoryId;

    @Schema(description = "vop末级分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "vop末级分类名称不能为空")
    private String vopLastCategoryName;

    @Schema(description = "vop完整分类id -分隔")
    private String vopFullCategoryId;

    @Schema(description = "vop完整分类名称")
    private String vopFullCategoryName;

}
