package cn.iocoder.yudao.module.mall.external.sso.cas.dynamic;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.tenant.TenantIdUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.SsoConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.integration.SsoConfigService;
import cn.iocoder.yudao.module.mall.enums.basis.SsoTypeEnum;
import de.danielbechler.util.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jasig.cas.client.validation.Assertion;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @author: HF
 * @description: TODO
 * @date: 2024/4/12 15:26
 */

@Component
@Slf4j
public class CasConfigService {

    private static final String assertionKey = "_const_cas_assertion_";

    @Resource
    private RedisTemplate<String, Assertion> casRedisTemplate;
    @Resource
    private SsoConfigService ssoConfigService;

    public boolean isLoginByCas() {
        SsoConfigDO configDO = ssoConfigService.getSsoConfig();
        return configDO != null && ObjectUtil.equal(configDO.getSsoType(), SsoTypeEnum.SSO_CAS.getType());
    }

    public SsoConfigDO getSsoConfig() {
        SsoConfigDO configDO = ssoConfigService.getSsoConfig();
        if(configDO != null && ObjectUtil.equal(configDO.getSsoType(), SsoTypeEnum.SSO_CAS.getType())) {
            return configDO;
        }

        return null;
    }

    public Map<String, String> getCasConfig(long tenantId) {
        SsoConfigDO configDO = ssoConfigService.getSsoConfig();
        Assert.notNull(configDO, "SSO配置为空");
        if (ObjectUtil.notEqual(configDO.getSsoType(), SsoTypeEnum.SSO_CAS.getType())) {
            log.error("SSO未配置CAS相关信息");
            return null;
        }
        Map<String, String> result = new HashMap<>();

        result.put("validation-type", configDO.getCasValidationType());
        result.put("serverName", configDO.getCasClientHostUrl());
        result.put("casServerUrlPrefix", configDO.getCasServerUrlPrefix());
        result.put("casServerLoginUrl", configDO.getCasServerLoginUrl());
        result.put("redirectAfterValidation", "false");

        return result;
    }

    public Long getTenantId(HttpServletRequest servletRequest) {
        if (TenantContextHolder.getTenantId() == null) {
            String tcode = servletRequest.getParameter("tcode");
            Long tenantId = null;
            if (StringUtils.isBlank(tcode)) {
                tenantId = WebFrameworkUtils.getTenantId(servletRequest);
            } else {
                try {
                    tenantId = TenantIdUtils.decryptTenantId(tcode);
                } catch (Exception e) {
                    log.error("cas-login filter tcode invalid: {}", tcode);
                }
            }
            if (tenantId == null) {
                return null;
            }
            TenantContextHolder.setTenantId(tenantId);
            TenantContextHolder.setIgnore(false);
        }
        return TenantContextHolder.getTenantId();
    }

    public Long parseTenantId(Map<String, Object> jsonMap) {
        if (jsonMap == null) {
            return null;
        }
        Integer eType = MapUtil.getInt(jsonMap, "etype");
        if(eType != null && ObjectUtil.notEqual(SsoTypeEnum.SSO_CAS.getType(), eType)) {
            return null;
        }

        String tenantIdCode = MapUtil.getStr(jsonMap, "tcode");
        if(StrUtil.isBlank(tenantIdCode)) {
            return null;
        }

        try {
            Long tenantId = TenantIdUtils.decryptTenantId(tenantIdCode);
            TenantContextHolder.setTenantId(tenantId);
            TenantContextHolder.setIgnore(false);

            return TenantContextHolder.getTenantId();
        } catch (Exception e) {
            log.error("解析租户ID失败:{}", tenantIdCode, e);
        }

        return null;
    }

    public void putAssertionToRedis(String key, Assertion assertion) {
        casRedisTemplate.opsForValue().set(key, assertion, 4, TimeUnit.HOURS);
    }

    public void removeAssertionInRedis(String key) {
        casRedisTemplate.delete(key);
    }

    public Assertion getAssertionFromRedis(String key) {
        Assertion assertion = casRedisTemplate.opsForValue().get(key);
        return assertion;
    }

    public String getAssertionRedisKey(long tid, String uid) {
        return "CAS_" + tid + "_" + uid;
    }

    public HashMap<String, Object> parseJsonFromBody(HttpServletRequest request) {
        try {
            String body = IOUtils.toString(request.getInputStream(), "UTF-8");
            return JsonUtils.parseObject2(body, HashMap.class);
        } catch (Exception e) {
            log.error("cas过滤器解析参数异常：{}, url: {}", e.getMessage(), request.getServletPath());
        }
        return null;
    }

}
