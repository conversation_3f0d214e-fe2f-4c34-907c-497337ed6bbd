package cn.iocoder.yudao.module.mall.product.controller.admin.sku;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.MapUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.mall.annotation.ProductOperateLog;
import cn.iocoder.yudao.module.mall.enums.vop.VopBizTypeEnum;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.*;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupSimpleVO;
import cn.iocoder.yudao.module.mall.product.convert.sku.ProductSkuConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductOperateTypeEnum;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.spu.ProductSpuService;
import cn.iocoder.yudao.module.mall.product.service.tag.ProductTagGroupService;
import cn.iocoder.yudao.module.mall.product.service.tag.ProductTagSkuService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderItemService;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTask;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import cn.iocoder.yudao.module.mall.vop.service.VopMessageHandleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;

@Tag(name = "管理后台 - 商品 sku")
@RestController
@RequestMapping("/product/sku")
@Validated
public class ProductSkuController {

    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private ProductSpuService productSpuService;
    @Resource
    private VopMessageHandleService vopMessageHandleService;
    @Resource
    private ProductTagGroupService tagGroupService;
    @Resource
    private ProductTagSkuService tagSkuService;
    @Resource
    private TradeOrderItemService orderItemService;

    @GetMapping("/get-option-list")
    @Operation(summary = "获得商品 SKU 选项的列表")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:query','product:sku-statistics:query', 'product:sku:seo')")
    public CommonResult<List<ProductSkuOptionRespVO>> getSkuOptionList() {
        // 获得 SKU 列表
        List<ProductSkuDO> skus = productSkuService.getSkuList();
        if (CollUtil.isEmpty(skus)) {
            return success(Collections.emptyList());
        }

        // 获得对应的 SPU 映射
        Map<Long, ProductSpuDO> spuMap = productSpuService.getSpuMap(convertSet(skus, ProductSkuDO::getSpuId));
        // 转换为返回结果
        List<ProductSkuOptionRespVO> skuVOs = ProductSkuConvert.INSTANCE.convertList05(skus);
        skuVOs.forEach(sku -> MapUtils.findAndThen(spuMap, sku.getSpuId(),
                spu -> sku.setSpuId(spu.getId()).setSpuName(spu.getSpuName())));
        return success(skuVOs);
    }

    @GetMapping("/list-by-spu")
    @Operation(summary = "获得商品 SKU 列表")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:query','product:sku-statistics:query', 'product:sku:seo:update')")
    public CommonResult<List<ProductSkuRespVO>> getSkuListBySpu(@RequestParam Long spuId) {
        return success(productSkuService.getSkuDetailListBySpu(spuId));
    }

    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('product:spu:query')")
    public CommonResult<PageResult<ProductSkuPageRespVO>> getSpuPage(@Valid ProductSkuPageReqVO pageVO) {
        return success(productSkuService.selectPage2(pageVO));
    }

    @GetMapping("/seo-page")
    @PreAuthorize("@ss.hasPermission('product:sku:seo')")
    public CommonResult<PageResult<ProductSkuPageRespVO>> getSeoSkuPage(@Valid ProductSkuPageReqVO pageVO) {
        PageResult<ProductSkuPageRespVO> pageResult = productSkuService.selectPage2(pageVO);
        tagSkuService.fillTag(pageResult);
        return success(pageResult);
    }

    @GetMapping("/get-detail")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:query', 'product:sku:seo')")
    public CommonResult<ProductSkuDetailRespVO> getSkuDetail(@Valid ProductSkuReqVO pageVO) {
        return success(productSkuService.selectSkuDetail2(pageVO));
    }

    @PostMapping("/update-price")
    @Operation(summary = "更新商品 SKU 价格")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:update-stock-price')")
    @ProductOperateLog(content = "修改商品价格",type = ProductOperateTypeEnum.UPDATE,spuId = "#skus[0].spuId")
    public CommonResult<Boolean> updatePrice(@Valid @RequestBody List<ProductSkuPriceReqVO> skus) {
        Assert.notEmpty(skus, "商品sku不能为空");
        productSkuService.updatePrice(skus);
        return success(true);
    }

    @PostMapping("/update-stock")
    @Operation(summary = "更新商品 SKU 库存")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:update-stock-price')")
    @ProductOperateLog(content = "修改商品库存",type = ProductOperateTypeEnum.UPDATE,spuId = "#skus[0].spuId")
    public CommonResult<Boolean> updateStock(@Valid @RequestBody List<ProductSkuStockReqVO> skus) {
        Assert.notEmpty(skus, "商品sku不能为空");
        productSkuService.updateStock(skus);
        return success(true);
    }

    @PostMapping("/update-show-status")
    @Operation(summary = "更新商品显示状态")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:update-show-status', 'product:sku:seo:update')")
    public CommonResult<Boolean> updateSpuShowStatus(@Valid @RequestBody List<ProductSkuShowStatusUpdateReqVO> updateReqVOS) {
        updateReqVOS.forEach(reqVO -> {
            productSkuService.updateSkuShowStatus(reqVO.getId(), reqVO.getShowStatus());
        });

        return success(true);
    }

    @PostMapping("/update-platform-status")
    @Operation(summary = "更新商品显示状态")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:update-platform-status', 'product:sku:seo:update')")
    public CommonResult<Boolean> updateSkuPlatformStatus(@Valid @RequestBody List<ProductSkuStatusUpdateReqVO> updateReqVOS) {
        updateReqVOS.forEach(reqVO -> {
            productSkuService.updateSkuPlatformStatus(reqVO.getId(), reqVO.getStatus());
        });

        return success(true);
    }

    @PostMapping("/update-status")
    @Operation(summary = "更新商品 SKU 状态")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:update', 'product:sku:seo:update')")
    public CommonResult<Boolean> updateSkuStatus(@Valid @RequestBody ProductSkuStatusUpdateReqVO updateReqVO) {
        productSkuService.updateSkuStatus(updateReqVO.getId(), updateReqVO.getStatus());
        return success(true);
    }

    @GetMapping("/fetch-vop-message")
    @Operation(summary = "手动补偿拉取VOP商品相关消息")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:update','product:sku-statistics:update')")
    public CommonResult<Boolean> fetchVopMessage(@RequestParam Long loop) {
        for (int i = 0; i < loop; i++) {
            vopMessageHandleService.fetchVopMessage(VopBizTypeEnum.PRODUCT.getType(), "6,4,16,48");
        }

        return success(true);
    }

    @PostMapping("/seo/update")
    @Operation(summary = "更新商品 SEO 字段")
    @PreAuthorize("@ss.hasAnyPermissions('product:sku:seo:update')")
    public CommonResult<Boolean> updateSeoInfo(@Valid @RequestBody List<ProductSkuSeoUpdateReqVO> skus) {
        Assert.notEmpty(skus, "商品sku不能为空");
        productSkuService.updateSeoInfo(skus);
        return success(true);
    }

    /**
     * 获得SEO导入模板
     * @param response
     * @throws IOException
     */
    @GetMapping("/seo/get-import-template")
    @PreAuthorize("@ss.hasPermission('product:sku:seo:import')")
    @Operation(summary = "获得会员导入模板")
    public void seoImportTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<ProductSeoSkuImportExcelVO> list = Arrays.asList(
                ProductSeoSkuImportExcelVO.builder()
                        .supplierId(3L)
                        .skuId(1165656L)
                        .skuInnerId("112236489")
                        .initSalesCount(100)
                        .build()
        );
        // 输出
        ExcelUtils.write(response, "SEO商品导入模板.xls", "SKU列表", ProductSeoSkuImportExcelVO.class, list);
    }

    @PostMapping("/seo/import")
    @Operation(summary = "导入SEO商品")
    @PreAuthorize("@ss.hasPermission('product:sku:seo:import')")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.PRODUCT_SKU_SEO_IMPORT, isNested = false)
    public CommonResult<String> seoImportExcel(@RequestParam("file") MultipartFile file) throws Exception {
        List<ProductSeoSkuImportExcelVO> list = ExcelUtils.read(file, ProductSeoSkuImportExcelVO.class);
        productSkuService.doImportSeoList(list);
        return success(AsyncFrontTaskContext.getTaskId());
    }

    @PostMapping("/seo/reset")
    @Operation(summary = "重置商品SEO状态")
    @PreAuthorize("@ss.hasPermission('product:sku:seo:reset')")
    public CommonResult<Boolean> resetSkuSeoStatus(@Valid @RequestBody ProductSkuIdReqVO reqVO) {
        productSkuService.resetSkuSeoStatus(reqVO.getIds());
        return success(true);
    }

    @PostMapping("/seo/save")
    @Operation(summary = "添加商品打开SEO")
    @PreAuthorize("@ss.hasPermission('product:sku:seo:update')")
    public CommonResult<Boolean> addSkuSeo(@Valid @RequestBody ProductSkuIdReqVO reqVO) {
        productSkuService.addSkuSeo(reqVO.getIds());
        return success(true);
    }

    @GetMapping("/dict-tags")
    @Operation(summary = "标签字典库查询")
    @PreAuthorize("@ss.hasAnyPermissions('product:sku:seo', 'product:sku:query')")
    public CommonResult<List<ProductTagGroupSimpleVO>> getDictTags() {
        return success(tagGroupService.getAllSimpleList());
    }

    @PostMapping("/add-tags")
    @Operation(summary = "商品添加标签")
    @PreAuthorize("@ss.hasPermission('product:sku:add-tag')")
    public CommonResult<Boolean> tagTag2sku(@Valid @RequestBody ProductSkuAddTagReqVO reqVO) {
        tagSkuService.saveTagSku(reqVO.getTagIdList(), reqVO.getSkuIdList());
        return success(true);
    }

    @PostMapping("/clear-tags")
    @Operation(summary = "商品清除标签")
    @PreAuthorize("@ss.hasPermission('product:sku:clear-tag')")
    public CommonResult<Boolean> clearSkuTag(@Valid @RequestBody ProductSkuClearTagReqVO reqVO) {
        tagSkuService.deleteTagSkuBySku(reqVO.getSkuIdList());
        return success(true);
    }

    @PostMapping("/seo/refresh-order-tags")
    @Operation(summary = "刷新商品订单标签")
    @PreAuthorize("@ss.hasPermission('product:sku:add-tag')")
    public CommonResult<Boolean> refreshOrderTags( @Valid @RequestBody ProductSkuIdReqVO reqVO) {
        if(CollUtil.isNotEmpty(reqVO.getIds())) {
            for(Long id : reqVO.getIds()) {
                orderItemService.refreshTagBySku(id);
            }
        }
        return success(true);
    }

    /**
     * 获得SKU标签导入模板
     * @param response
     * @throws IOException
     */
    @GetMapping("/tag/get-import-template")
    @PreAuthorize("@ss.hasPermission('product:sku:tag:import')")
    @Operation(summary = "获得SKU标签导入模板")
    public void tagImportTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<ProductSkuTagImportExcelVO> list = Arrays.asList(
                ProductSkuTagImportExcelVO.builder()
                        .supplierId(3L)
                        .skuId(1165656L)
                        .skuInnerId("112236489")
                        .tagIds("1-10")
                        .build()
        );
        // 输出
        ExcelUtils.write(response, "SKU标签导入模板.xls", "SKU列表", ProductSkuTagImportExcelVO.class, list);
    }

    @PostMapping("/tag/import")
    @Operation(summary = "导入SKU标签")
    @PreAuthorize("@ss.hasPermission('product:sku:tag:import')")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.PRODUCT_SKU_TAG_IMPORT, isNested = false)
    public CommonResult<String> tagImportExcel(@RequestParam("file") MultipartFile file) throws Exception {
        List<ProductSkuTagImportExcelVO> list = ExcelUtils.read(file, ProductSkuTagImportExcelVO.class);
        productSkuService.doImportTagList(list);
        return success(AsyncFrontTaskContext.getTaskId());
    }

    @GetMapping("/export")
    @Operation(summary = "导出商品SKU")
    @PreAuthorize("@ss.hasPermission('product:sku:export')")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.PRODUCT_SKU_EXPORT)
    public CommonResult<String> exportSku(ProductSkuExportReqVO exportReqVO) {
        String taskId =  productSkuService.exportSku(exportReqVO);
        return success(taskId);
    }

    @GetMapping("/seo/export")
    @Operation(summary = "导出运营商品SKU")
    @PreAuthorize("@ss.hasPermission('product:sku:seo:export')")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.PRODUCT_SEO_SKU_EXPORT)
    public CommonResult<String> exportSeoSku(ProductSkuExportReqVO exportReqVO) {
        String taskId =  productSkuService.exportSeoSku(exportReqVO);
        return success(taskId);
    }

    @PostMapping("/batch-query-index-status")
    @Operation(summary = "批量查询SKU索引状态")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:query', 'product:sku:query')")
    public CommonResult<List<ProductSkuIndexStatusRespVO>> batchQuerySkuIndexStatus(@Valid @RequestBody ProductSkuIndexStatusBatchReqVO reqVO) {
        List<ProductSkuIndexStatusRespVO> result = productSkuService.batchQuerySkuIndexStatus(reqVO.getSkuIds());
        return success(result);
    }
}
