package cn.iocoder.yudao.module.mall.product.controller.app.sku.vo;

import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.AreaStockInfoResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23
 */
@Data
public class SkuStockInfo {
    /**
     * 商品状态 1上架 0下架
     */
    private int skuState;
    /**
     * 市场价
     */
    private BigDecimal marketPrice;
    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 售后提醒
     */
    private String returnRuleStr;

    /**
     * 售卖状态
     */
    @Schema(description = "售卖状态")
    private Integer saleStatus;

    //sku库存状态
    private List<AreaStockInfoResp> areaStockInfoResp;
    /**
     * 轮播图
     */
    private List<String> sliderPicUrls;

    /**
     * 当前商品是否含有促销活动
     */
    private Boolean hasPromotion;

    /**
     * 用户总可购买次数，hasPromotion为true时有效
     */
    private Integer limitedNum;

    /**
     * 用户剩余可购买次数，hasPromotion为true时有效
     */
    private Integer remainNum;
}
