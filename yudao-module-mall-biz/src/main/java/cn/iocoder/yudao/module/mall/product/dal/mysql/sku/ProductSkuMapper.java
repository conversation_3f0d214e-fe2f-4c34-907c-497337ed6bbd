package cn.iocoder.yudao.module.mall.product.dal.mysql.sku;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SkuCountCategorySummaryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.*;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.TradeOrderItemBaseReqVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.*;

/**
 * 商品 SKU Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSkuMapper extends BaseMapperX<ProductSkuDO> {

    default List<ProductSkuDO> selectListBySpuId(Long spuId) {
        return selectList(ProductSkuDO::getSpuId, spuId);
    }

    default List<ProductSkuDO> selectSkuIdListBySpuId(Long spuId) {
        return selectList(new LambdaQueryWrapperX<ProductSkuDO>()
                .select(ProductSkuDO::getId, ProductSkuDO::getSupplierId)
                .eq(ProductSkuDO::getSpuId, spuId));
    }

    default List<ProductSkuDO> selectListBySpuIdAndStatus(Long spuId,
                                                          Integer status) {
        return selectList(new LambdaQueryWrapperX<ProductSkuDO>()
                .eq(ProductSkuDO::getSpuId, spuId)
                .eqIfPresent(ProductSkuDO::getStatus, status));
    }

    default List<ProductSkuDO> selectListBySpuIds(Collection<Long> spuIds) {
        return selectList(ProductSkuDO::getSpuId, spuIds);
    }

    default void deleteBySpuId(Long spuId) {
        delete(new LambdaQueryWrapperX<ProductSkuDO>().eq(ProductSkuDO::getSpuId, spuId));
    }

    /**
     * 更新 SKU 库存（增加）
     *
     * @param id        编号
     * @param incrCount 增加库存（正数）
     */
    default void updateStockIncr(Long id, Integer incrCount) {
        Assert.isTrue(incrCount > 0);
        LambdaUpdateWrapper<ProductSkuDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<ProductSkuDO>()
                .setSql(" stock = stock + " + incrCount)
                .eq(ProductSkuDO::getId, id);
        update(null, lambdaUpdateWrapper);
    }

    /**
     * 更新 SKU 库存（减少）
     *
     * @param id        编号
     * @param incrCount 减少库存（负数）
     * @return 更新条数
     */
    default int updateStockDecr(Long id, Integer incrCount) {
        Assert.isTrue(incrCount < 0);
        LambdaUpdateWrapper<ProductSkuDO> updateWrapper = new LambdaUpdateWrapper<ProductSkuDO>()
                .setSql(" stock = stock + " + incrCount) // 负数，所以使用 + 号
                .eq(ProductSkuDO::getId, id); // cas 逻辑
        return update(null, updateWrapper);
    }

    default List<ProductSkuDO> selectListByAlarmStock() {
        return selectList(new QueryWrapper<ProductSkuDO>().apply("stock <= warn_stock"));
    }

    /**
     * 批量查询skuId
     *
     * @param param
     * @return
     */
    default PageResult<ProductSkuDO> selectSkuIdPage(AppProductSkuPageReqVO param) {
        return selectPage(param, new LambdaQueryWrapperX<ProductSkuDO>()
                .select(ProductSkuDO::getId, ProductSkuDO::getStatus, ProductSkuDO::getShowStatus, ProductSkuDO::getSupplierId)
                .ge(param.getUpdateTime() != null, ProductSkuDO::getUpdateTime, param.getUpdateTime()));
    }

    /**
     * 批量查询已删除商品id
     *
     * @param param
     * @return
     */
    default PageResult<ProductSkuDO> selectDeleteSkuIdPage(AppProductSkuPageReqVO param) {
        Page<ProductSkuDO> productSkuDOPage = selectDeleteSkuPages(new Page<>(param.getPageNo(), param.getPageSize()), param.getUpdateTime());
        return new PageResult(param.getPageNo(), param.getPageSize(), (int) productSkuDOPage.getPages(), productSkuDOPage.getRecords(), productSkuDOPage.getTotal());
    }

    Page<ProductSkuDO> selectDeleteSkuPages(Page<ProductSkuDO> page, @Param("updateTime") Date updateTime);

    String selectSpuCategory(@Param("skuInnerId") String skuInnerId, @Param("supplierId") Long supplierId);

    /**
     * 统计商品数量分类排行
     * @param tenantId
     * @return
     */
    @TenantIgnore
    List<SkuCountCategorySummaryRespVO> getProductCountByCategory(@Param("tenantId") Long tenantId);

    /**
     * 根据id或者innerId查询商品信息
     * @param skuId
     * @return
     */
    default ProductSkuDO getSkuByIdOrInnerId(String skuId) {
        List<ProductSkuDO> productSkuDOS = getSkusByIdOrInnerId(skuId);
        if (CollectionUtils.isNotEmpty(productSkuDOS)) {
            return productSkuDOS.get(0);
        }
        return null;
    }

    /**
     * 根据id或者innerId查询商品信息
     * @param skuId
     * @return
     */
    default List<ProductSkuDO> getSkusByIdOrInnerId(String skuId) {
        return getSkuByIdOrInnerIds(Arrays.asList(skuId));
    }

    /**
     * 根据id或者innerId 批量查询商品信息
     * @param skuIds
     * @return
     */
    default List<ProductSkuDO> getSkuByIdOrInnerIds(List<String> skuIds) {
        if(CollectionUtils.isEmpty(skuIds)) {
            return null;
        }
        List<Long> skuIdList = new ArrayList<>();
        List<String> skuInnerIdList = new ArrayList<>();
        for (String skuId : skuIds) {
            if (NumberUtil.isNumber(skuId)) {
                skuIdList.add(Long.valueOf(skuId));
            }
            skuInnerIdList.add(skuId);
        }
        LambdaQueryWrapper<ProductSkuDO> wrapper = Wrappers.lambdaQuery(ProductSkuDO.class)
                .in(CollectionUtils.isNotEmpty(skuIdList),ProductSkuDO::getId, skuIdList)
                .or()
                .in(CollectionUtils.isNotEmpty(skuInnerIdList),ProductSkuDO::getSkuInnerId, skuInnerIdList);
        List<ProductSkuDO> productSkuDOS = selectList(wrapper);
        return productSkuDOS;
    }


    List<ProductSkuStatusResult> getProductSkuStatusResult(@Param("tenantId") Long tenantId, @Param("supplierId") Long supplierId);


    default int updateSalesCountAndAmount(TradeOrderItemBaseReqVO orderItemBaseVO) {
        ProductSkuDO productSkuDO = this.selectById(orderItemBaseVO.getSkuId());
        if (productSkuDO != null) {
            ProductSkuDO up = new ProductSkuDO();
            up.setId(productSkuDO.getId());
            int saleCount = productSkuDO.getSalesCount() != null ? productSkuDO.getSalesCount() + orderItemBaseVO.getCount() : orderItemBaseVO.getCount();
            up.setSalesCount(Math.max(saleCount, 0));
            BigDecimal updateAmount = orderItemBaseVO.getSkuPrice().multiply(new BigDecimal(orderItemBaseVO.getCount()));
            BigDecimal salesAmount = productSkuDO.getSalesAmount() != null ? productSkuDO.getSalesAmount().add(updateAmount) : updateAmount;
            up.setSalesAmount(salesAmount.max(new BigDecimal("0")));
            return this.updateById(up);
        }
        return 0;
    }

    Page<ProductSkuPageRespVO> selectPage2(Page pageInfo, @Param("params") ProductSkuPageReqVO reqVO);

    List<Long> selectSkuId(@Param("params") ProductSkuPageReqVO reqVO);

    ProductSkuDetailRespVO selectSkuDetail2(@Param("params") ProductSkuReqVO reqVO);

    List<ProductSkuExportRespVO> selectExportSku(@Param("params") ProductSkuExportReqVO exportReqVO);

    List<ProductSeoSkuExportRespVO> selectExportSeoSku(@Param("params") ProductSkuExportReqVO exportReqVO);

}
