package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class SkuInfoDTO {

    /**
     * 商品编号
     */
    private String skuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品分类ID
     */
    private String skuTypeID;

    /**
     * 商品分类
     */
    private String skuType;

    /**
     * 商品总价
     */
    private BigDecimal skuPrice;

    /**
     * 经济分类(只能传已开通经济分类)
     */
    @JsonProperty("JJFL")
    private String economyClass;

    /**
     * 数量
     */
    @JsonProperty("SL")
    private Integer count;

    /**
     * 单价
     */
    @JsonProperty("DJ")
    private BigDecimal unitPrice;

    /**
     * 商品分类 1:低值 2:固定资产 3:免建账 4:试剂
     */
    private String goodsType;

    //=================推送账单相关字段========================

    /**
     * 6大类资产类别代码 ( goodsType为"2"时必填)
     */
    @JsonProperty("ZCLBDM6")
    private String assetsTypeSix;

    /**
     * 16大类资产类别代码  ( goodsType为"2"时必填)
     */
    @JsonProperty("ZCLBDM16")
    private String assetsTypeSixteen;

    /**
     * 资产编号  ( goodsType为"2"时必填)
     */
    @JsonProperty("ZCBH")
    private String assetsNo;

    /**
     * 资产验收单编号  ( goodsType为"2"时必填)
     */
    @JsonProperty("ZCYSDBH")
    private String assetsAcceptanceNo;

    /**
     * 凭证分录摘要
     */
    @JsonProperty("zy")
    private String zy;
}
