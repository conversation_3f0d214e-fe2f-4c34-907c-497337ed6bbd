package cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping;


import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo.VopGoodsCategoryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsCategoryItem;
import cn.iocoder.yudao.module.mall.product.convert.vopcategorymapping.VopGoodsCategoryConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopGoodsCategoryDO;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.module.mall.product.service.vopcategorymapping.VopGoodsCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@Tag(name="管理后台 - 京东分类")
@RestController
@RequestMapping("/mall/vop-goods-category")
@Validated
public class VopGoodsCategoryController {
    @Resource
    private VopGoodsCategoryService vopCategoryService;

    /**
     * 查询京东的一级类目数据
     * @return
     */
    @GetMapping("/get-Primary-category")
    @Operation(summary = "查询京东一级类目数据")
    public CommonResult<List<VopGoodsCategoryRespVO>> getPrimaryCategoryList() {
        List<VopGoodsCategoryDO> vopGoodsPrimaryCategoryList = vopCategoryService.getVopGoodsPrimaryCategoryList();
        return success(VopGoodsCategoryConvert.INSTANCE.convertList(vopGoodsPrimaryCategoryList));
    }

    /**
     * 查询所有的子类目数据
     * @param parentCategoryId
     * @return
     */
    @GetMapping("/getUnderChildCategoryList")
    @Operation(summary = "查询所有的子类目数据")
    public CommonResult<List<VopGoodsCategoryItem>> getUnderChildCategoryList(@RequestParam(value = "parentCategoryId", required = false) Long parentCategoryId) {
        return CommonResult.success(vopCategoryService.getChildCategoryList(parentCategoryId));
    }


}
