package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 平台配置物流公司 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ConfigDeliveryCompanyBaseVO {

    @Schema(description = "物流公司编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物流公司编码不能为空")
    private String com;

    @Schema(description = "物流公司名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物流公司名称不能为空")
    private String name;

    @Schema(description = "物流公司类型")
    private String type;

}
