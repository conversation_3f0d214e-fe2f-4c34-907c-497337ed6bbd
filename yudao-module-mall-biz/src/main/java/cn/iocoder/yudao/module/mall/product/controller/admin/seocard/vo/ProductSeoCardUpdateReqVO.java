package cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 运营区域更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSeoCardUpdateReqVO extends ProductSeoCardBaseVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "区域内容,json格式")
    private String content;

    @Schema(description = "置顶商品,逗号分隔")
    private String topSku;

}
