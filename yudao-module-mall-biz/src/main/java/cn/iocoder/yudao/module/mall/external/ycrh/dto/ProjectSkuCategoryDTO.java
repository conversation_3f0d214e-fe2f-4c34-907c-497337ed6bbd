package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 项目所绑定的商品分类
 * <AUTHOR>
 * @date 2024/6/6
 */
@Data
public class ProjectSkuCategoryDTO {

    /**
     * 分类编码
     */
    @JsonProperty("spflbh")
    private String categoryCode;

    /**
     * 分类名称
     */
    @JsonProperty("spflmc")
    private String categoryName;

    /**
     * 全分类ID，中划线分隔
     */
    private String fullCategoryId;

    /**
     * 是否需要上传附件, 0-不需要 1-需要
     */
    @JsonProperty("needUpload")
    private String status;

}
