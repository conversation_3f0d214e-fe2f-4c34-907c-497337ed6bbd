package cn.iocoder.yudao.module.mall.product.controller.admin.usersearchhistory.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
public class UserSearchHistoryPageReqVO extends PageParam {

    /**
     * 用户名称
     */
    private String nickname;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段")
    private Integer sortType;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updateTime;

}
