package cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - sku库存更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSkuStockUpdateReqVO extends ProductSkuStockBaseVO {

    @Schema(description = "自增ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "自增ID不能为空")
    private Long id;

}
