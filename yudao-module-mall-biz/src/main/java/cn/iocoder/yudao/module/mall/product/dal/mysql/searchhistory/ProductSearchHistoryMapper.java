package cn.iocoder.yudao.module.mall.product.dal.mysql.searchhistory;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.product.controller.admin.usersearchhistory.vo.UserSearchHistoryPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.searchhistory.ProductSearchHistoryDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 会员搜索历史 User Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSearchHistoryMapper extends BaseMapperX<ProductSearchHistoryDO> {
    Page<ProductSearchHistoryDO> selectSearchHistoryPage(Page pageInfo, @Param("params") UserSearchHistoryPageReqVO reqVO);

}
