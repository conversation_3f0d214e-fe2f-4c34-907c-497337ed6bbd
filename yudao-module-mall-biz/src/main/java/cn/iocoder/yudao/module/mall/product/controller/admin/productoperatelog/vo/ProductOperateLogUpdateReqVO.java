package cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商品操作日志记录更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductOperateLogUpdateReqVO extends ProductOperateLogBaseVO {

    @Schema(description = "日志主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "日志主键不能为空")
    private Long id;

}
