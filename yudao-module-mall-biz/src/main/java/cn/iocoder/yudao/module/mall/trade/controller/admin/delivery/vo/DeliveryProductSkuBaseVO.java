package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 物流订单商品sku关系 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class DeliveryProductSkuBaseVO {

    @Schema(description = "物流信息表id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物流信息表id不能为空")
    private Long deliveryId;

    @Schema(description = "商品sku id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品sku id不能为空")
    private Long skuId;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "图片地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "图片地址不能为空")
    private String picUrl;

}
