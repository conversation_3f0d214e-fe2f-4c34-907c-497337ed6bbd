package cn.iocoder.yudao.module.mall.member.controller.admin.user;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.member.controller.admin.user.vo.*;
import cn.iocoder.yudao.module.mall.member.convert.user.UserConvert;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppTradeOrderPageItemRespVO;
import cn.iocoder.yudao.module.mall.trade.convert.order.TradeOrderConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;

/**
 * 管理后台 - 会员管理
 */
@Tag(name = "管理后台 - 会员管理")
@RestController
@RequestMapping("/member/user")
@Validated
@Slf4j
public class MemberUserController {

    @Resource
    private MemberUserService memberUserService;

    @Resource
    private TradeOrderService tradeOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建会员")
    @PreAuthorize("@ss.hasPermission('member:user:create')")
    public CommonResult<Long> createMemberUser(@Valid @RequestBody MemberUserCreateReqVO createReqVO) {
        Long tenantId = memberUserService.createMemberUser(createReqVO);
        return success(tenantId);
    }

    @PutMapping("/update")
    @Operation(summary = "更新会员")
    @PreAuthorize("@ss.hasPermission('member:user:update')")
    public CommonResult<Boolean> updateMemberUser(@Valid @RequestBody MemberUserUpdateReqVO updateReqVO) {
        memberUserService.updateMemberUser(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除会员")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:user:delete')")
    public CommonResult<Boolean> deleteMemberUser(@RequestParam("id") Long id) {
        memberUserService.deleteById(id);
        return success(true);
    }

    /**
     * 会员列表
     *
     * @param reqVO
     * @return
     */
    @PostMapping("/page")
    @Operation(summary = "会员列表")
    @PreAuthorize("@ss.hasPermission('member:user:query')")
    public CommonResult<PageResult<UserInfoRespVO>> getBillPage(@RequestBody @Valid UserQueryReqVO reqVO) {
        PageResult<MemberUserDO> userPage = memberUserService.getUserPage(reqVO);
        return success(UserConvert.INSTANCE.convertPage(userPage));
    }

    /**
     * 获取会员详情
     * @param id
     * @return
     */
    @GetMapping("/get-detail")
    @Operation(summary = "获取会员详情")
    @Parameter(name = "id", description = "订单编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('member:user:query')")
    public CommonResult<UserInfoDetailRespVO> getOrderDetail(@RequestParam("id") String id) {
        MemberUserDO user = memberUserService.getUser(Long.valueOf(id));
        Assert.notNull(user,"会员不存在");
        return success( UserConvert.INSTANCE.convert02(user));
    }

    /**
     * 获得会员关联订单
     * @param reqVO
     * @return
     */
    @PostMapping("/get-ref-order")
    @Operation(summary = "获得会员关联订单")
    @PreAuthorize("@ss.hasPermission('member:user:query')")
    public CommonResult<PageResult<AppTradeOrderPageItemRespVO>> getRefOrders(@RequestBody UserOrderPageReqVo reqVO) {
        PageResult<TradeOrderDO> orderPage = tradeOrderService.getOrderPage(UserConvert.INSTANCE.convertVO(reqVO));
        if (orderPage.getTotal() == 0) {
            return success(null);
        }
        // 查询订单项
        List<TradeOrderItemDO> orderItems = tradeOrderService.getOrderItemListByOrderId(
                convertSet(orderPage.getList(), TradeOrderDO::getId));
        // 最终组合
        return success(TradeOrderConvert.INSTANCE.convertPage02(orderPage, orderItems));
    }

    /**
     * 登录用户数
     * @return
     */
    @GetMapping("/count")
    @Operation(summary = "登录用户数")
    @PreAuthorize("@ss.hasPermission('member:user:query')")
    public CommonResult<Long> getUserCount(){
        return success(memberUserService.getUserCount());
    }

    @PostMapping("/build-dept-v2")
    @PreAuthorize("@ss.hasPermission('member:user:update')")
    public CommonResult<Boolean> buildDept(@RequestParam Long parentId)  {
        memberUserService.buildDeptArchitectureV2(parentId);
        return success(true);
    }

    @PostMapping("/build-dept-v3")
    @PreAuthorize("@ss.hasPermission('member:user:update')")
    public CommonResult<Boolean> buildDeptV3()  {
        memberUserService.buildDeptArchitectureV3();
        return success(true);
    }

    @PostMapping("/build-dept-v4")
    @PreAuthorize("@ss.hasPermission('member:user:update')")
    public CommonResult<Boolean> buildDeptV4()  {
        memberUserService.buildDeptArchitectureV4();
        return success(true);
    }

}

