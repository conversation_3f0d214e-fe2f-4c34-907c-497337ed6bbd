package cn.iocoder.yudao.module.mall.external.sso.cas.controller.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "用户 APP - CAS 统一登录 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppSsoCasLoginReqVO {

    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 用户工号
     */
    private String userNo;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 部门编号
     */
    private String deptNo;

}
