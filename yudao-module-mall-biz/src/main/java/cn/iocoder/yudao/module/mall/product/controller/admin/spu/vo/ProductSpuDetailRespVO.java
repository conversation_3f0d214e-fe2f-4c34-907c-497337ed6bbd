package cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo;

import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecValueRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 商品 SPU 详细 Response VO") // 包括关联的 SKU 等信息
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSpuDetailRespVO extends ProductSpuRespVO {

    @Schema(description = "供应商内部商品spuId")
    private String spuInnerId;

    @Schema(description = "商品详情", example = "我是商品描述")
    private String description;

    @Schema(description = "H5端商品描述详情", example = "")
    private String descriptionH5;

    @Schema(description = "商品通用属性")
    private List<ProductSpecValueRespVO> spuSpecValueList;

    @Schema(description = "商品sku")
    private List<ProductSkuRespVO> skus;
}
