package cn.iocoder.yudao.module.mall.product.convert.productoperatelog;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productoperatelog.ProductOperateLogDO;

/**
 * 商品操作日志记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductOperateLogConvert {

    ProductOperateLogConvert INSTANCE = Mappers.getMapper(ProductOperateLogConvert.class);

    ProductOperateLogDO convert(ProductOperateLogCreateReqVO bean);

    ProductOperateLogDO convert(ProductOperateLogUpdateReqVO bean);

    ProductOperateLogRespVO convert(ProductOperateLogDO bean);

    List<ProductOperateLogRespVO> convertList(List<ProductOperateLogDO> list);

    PageResult<ProductOperateLogRespVO> convertPage(PageResult<ProductOperateLogDO> page);

    List<ProductOperateLogExcelVO> convertList02(List<ProductOperateLogDO> list);

}
