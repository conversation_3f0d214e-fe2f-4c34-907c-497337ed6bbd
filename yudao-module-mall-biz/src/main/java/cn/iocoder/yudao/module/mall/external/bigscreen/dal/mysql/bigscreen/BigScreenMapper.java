package cn.iocoder.yudao.module.mall.external.bigscreen.dal.mysql.bigscreen;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.*;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderStatusEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface BigScreenMapper extends BaseMapperX {

    /**
     * 已登录用户
     * @param tenantId
     * @return
     */
    @TenantIgnore
    Long getLoginedUserCount(@Param("tenantId") Long tenantId);

    /**
     * 最近20个实时订单列表
     * @param tenantId
     * @return
     */
    @TenantIgnore
    List<BigScreenRealTimeOrderRespVO> getRealTimeOrderList(@Param("tenantId") Long tenantId);

    /**
     * 销售总数
     * @param tenantId
     * @return
     */
    @TenantIgnore
    Long getSellTotal(@Param("tenantId") Long tenantId);

    /**
     * 今日销售商品数
     * @param tenantId
     * @return
     */
    @TenantIgnore
    Long getTodaySellCount(@Param("tenantId") Long tenantId);

    /**
     * 今日订单数
     * @param tenantId
     * @return
     */
    @TenantIgnore
    Long getTodayOrderCount(@Param("tenantId") Long tenantId);

    /**
     * 今日订单金额
     * @param tenantId
     * @return
     */
    @TenantIgnore
    BigDecimal getTodayOrderAmount(@Param("tenantId") Long tenantId);

    /**
     * 今日订单个数
     * @param tenantId
     * @return
     */
    @TenantIgnore
    Long getOrderCount(@Param("tenantId") Long tenantId);

    /**
     * 销售总额
     * @param tenantId
     * @return
     */
    @TenantIgnore
    BigDecimal getOrderAmount(@Param("tenantId") Long tenantId);

    /**
     * 供应商商品统计
     * @param tenantId
     * @return
     */
    @TenantIgnore
    List<BigScreenSupplierProductSummaryRespVO> getSupplierProductSummary(@Param("tenantId") Long tenantId);

    /**
     * 供应商商品统计
     * @param tenantId
     * @return
     */
    @TenantIgnore
    List<BigScreenSupplierProductProportionRespVO> getSupplierProductProportion(@Param("tenantId") Long tenantId);

    /**
     * 商品品类占比
     * @param tenantId
     * @return
     */
    @TenantIgnore
    List<BigScreenProductCategoryProportionRespVO> getProductCategoryProportion(@Param("tenantId") Long tenantId);

    /**
     * 销售商品品类占比
     * @param tenantId
     * @return
     */
    @TenantIgnore
    List<BigScreenSellProductCategorySummaryRespVO> getSellProductCategorySummary(@Param("tenantId") Long tenantId);

    /**
     * 待审批
     * @param tenantId
     * @return
     */
    @TenantIgnore
    Long  getPendingApprovalOrderCount(@Param("tenantId") Long tenantId);

    /**
     * 审批通过订单数
     * @param tenantId
     * @return
     */
    @TenantIgnore
    Long  getApprovalOrderCount(@Param("tenantId") Long tenantId);

    /**
     * 审批驳回
     * @param tenantId
     * @return
     */
    @TenantIgnore
    Long  getRejectOrderCount(@Param("tenantId") Long tenantId);

    /**
     * 待结算订单数量
     * @param tenantId
     * @return
     */
    @TenantIgnore
    Long  getPendingSettlementOrderCount(@Param("tenantId") Long tenantId);

    /**
     * 待结算金额
     * @param tenantId
     * @return
     */
    @TenantIgnore
    BigDecimal  getPendingSettlementAmount(@Param("tenantId") Long tenantId);

    /**
     * 资产建档订单数
     * @param tenantId
     * @return
     */
    @TenantIgnore
    Long getAssetArchiveOrderSummary(@Param("tenantId") Long tenantId);

    /**
     * 资产建档商品数
     * @param tenantId
     * @return
     */
    @TenantIgnore
    Long getAssetArchiveSkuSummary(@Param("tenantId") Long tenantId);
}
