package cn.iocoder.yudao.module.mall.trade.controller.admin.orderassets.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 订单明细固资信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TradeOrderItemAssetsRespVO extends TradeOrderItemAssetsBaseVO {

    @Schema(description = "id", required = true)
    private Long id;

    @Schema(description = "订单ID", required = true)
    private Long orderId;

    @Schema(description = "订单明细ID", required = true)
    private Long orderItemId;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "完成时间", required = true)
    private LocalDateTime assetFinishTime;

    @Schema(description = "采购原因")
    private String purchaseReason;

    @Schema(description = "项目编号")
    private String projectNo;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目所属部门名称")
    private String projectDepartmentName;

    @Schema(description = "项目所属部门编号")
    private String projectDepartmentNo;

    @Schema(description = "资产验收证明材料URL,逗号分隔")
    private String assetProofFile;

    @Schema(description = "业财凭证名称")
    private String voucherName;

    @Schema(description = " 业财凭证上传状态")
    private Boolean voucherStatus;

}
