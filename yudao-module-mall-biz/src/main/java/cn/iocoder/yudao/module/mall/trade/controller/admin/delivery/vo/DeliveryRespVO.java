package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 物流信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeliveryRespVO extends DeliveryRespSimpleVO {

    @Schema(description = "物流轨迹")
    private List<DeliveryTrackRespVO> trackList;

    @Schema(description = "物流对应的商品")
    private List<DeliveryProductSkuRespVO> skuList;

}
