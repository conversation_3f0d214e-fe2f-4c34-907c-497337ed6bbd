package cn.iocoder.yudao.module.mall.product.controller.app.spec.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Schema(description = "APP - 商品规格值 Request VO")
@Data
public class AppProductSpecValueVO {

    @Schema(description = "规格值Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "规格Id，0代表自定义规格", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long specId;

    @Schema(description = "规格名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "规格名称不能为空")
    @Size(min = 1, max = 100, message = "specName不能超过100个字符")
    private String specName;

    @Schema(description = "规格值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "规格值不能为空")
    @Size(min = 1, max = 200, message = "specName不能超过200个字符")
    private String specValue;

}
