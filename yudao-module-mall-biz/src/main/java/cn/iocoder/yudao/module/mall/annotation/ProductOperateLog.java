package cn.iocoder.yudao.module.mall.annotation;

import cn.iocoder.yudao.module.mall.product.enums.spu.ProductOperateTypeEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.OrderOperateTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2024/1/30
 */

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ProductOperateLog {

    String content() default "";

    ProductOperateTypeEnum type();

    String spuId() default "";
}

