package cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品 SPU 分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSpuPageReqVO extends PageParam {

    @Schema(description = "商品品牌编号")
    private Long brandId;

    @Schema(description = "商品品牌名称")
    private String brandName;

    @Schema(description = "完整分类id -分隔")
    private String fullCategoryId;

    @Schema(description = "规格类型：0 单规格 1 多规格")
    private Integer specType;

    @Schema(description = "商品名称")
    private String spuName;

    @Schema(description = "排序字段")
    private Integer sort;

    @Schema(description = "状态")
    private Integer spuStatus;

    @Schema(description = "平台状态")
    private Integer platformStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "一级分类id")
    private Long category1Id;

    @Schema(description = "二级分类id")
    private Long category2Id;

    @Schema(description = "三级分类id")
    private Long category3Id;

    @Schema(description = "一级分类名称")
    private String category1Name;

    @Schema(description = "二级分类名称")
    private String category2Name;

    @Schema(description = "三级分类名称")
    private String category3Name;

    @Schema(description = "最低销量")
    private Integer salesCountMin;

    @Schema(description = "最高销量")
    private Integer salesCountMax;

    @Schema(description = "最低价格")
    private Integer salePriceMin;

    @Schema(description = "最高价格")
    private Integer salePriceMax;

}
