package cn.iocoder.yudao.module.mall.product.convert.skustock;

import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.module.mall.product.api.sku.dto.Stock;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuOpenVO;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.response.getNewStockById.GetStockByIdGoodsResp;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skustock.ProductSkuStockDO;

/**
 * sku库存 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSkuStockConvert {

    ProductSkuStockConvert INSTANCE = Mappers.getMapper(ProductSkuStockConvert.class);

    ProductSkuStockDO convert(ProductSkuStockCreateReqVO bean);

    ProductSkuStockDO convert(ProductSkuStockUpdateReqVO bean);

    ProductSkuStockRespVO convert(ProductSkuStockDO bean);

    List<ProductSkuStockRespVO> convertList(List<ProductSkuStockDO> list);

    PageResult<ProductSkuStockRespVO> convertPage(PageResult<ProductSkuStockDO> page);

    List<ProductSkuStockExcelVO> convertList02(List<ProductSkuStockDO> list);

    default List<Stock> convertStocks(List<ProductSkuStockDO> productSkuStocks) {
        return productSkuStocks.stream().map(skuStock -> convertStock(skuStock)).collect(Collectors.toList());
    }

    default Stock convertStock(ProductSkuStockDO productSkuStock) {
        if (productSkuStock == null) {
            return null;
        }
        int stockStateType = productSkuStock.getStock() >= 1 ? 33 : 34;
        String stockStateDesc = stockStateType == 33 ? "有货" : "无货";
        return new Stock()
                .setSkuId(productSkuStock.getSkuId())
                .setStockStateType(stockStateType)
                .setStockStateDesc(stockStateDesc)
                .setRemainNumInt(productSkuStock.getStock());
    }


    default List<Stock> convertStocks01(List<GetStockByIdGoodsResp> resps, Map<String, Long> jdSkuMap) {
        if (CollectionUtils.isEmpty(resps)) {
            return null;
        }
        List<Stock> stocks = new ArrayList<>();
        for (GetStockByIdGoodsResp resp : resps) {
            Stock stock = convertStock01(resp);
            stock.setSkuId(jdSkuMap.get(stock.getSkuId().toString()));
            stocks.add(stock);
        }
        return stocks;
    }

    Stock convertStock01(GetStockByIdGoodsResp resp);


    default ProductSkuStockDO convert(AppProductSkuOpenVO appProductSkuOpenVO) {
        Integer stockCount = appProductSkuOpenVO.getStockCount() != null ? appProductSkuOpenVO.getStockCount() : 0;
        Integer warnStock = stockCount / 10 > 1 ? stockCount / 10 : 1;
        ProductSkuStockDO productSkuStockDO = new ProductSkuStockDO()
                .setSupplierId(appProductSkuOpenVO.getSupplierId())
                .setSkuId(appProductSkuOpenVO.getSkuId())
                .setStock(stockCount)
                .setWarnStock(warnStock)
                .setReserveStock(0);
        return productSkuStockDO;
    }

    default List<ProductSkuStockDO> convertList01(List<AppProductSkuOpenVO> appProductSkuOpenVOS) {
        List<ProductSkuStockDO> list = new ArrayList<>();
        appProductSkuOpenVOS.forEach(item -> {
            list.add(convert(item));
        });

        return list;
    }

}
