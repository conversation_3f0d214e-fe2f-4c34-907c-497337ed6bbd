package cn.iocoder.yudao.module.mall.product.dal.mysql.skustock;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skustock.ProductSkuStockDO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * sku库存 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSkuStockMapper extends BaseMapperX<ProductSkuStockDO> {

    default PageResult<ProductSkuStockDO> selectPage(ProductSkuStockPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductSkuStockDO>()
                .eqIfPresent(ProductSkuStockDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(ProductSkuStockDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(ProductSkuStockDO::getStock, reqVO.getStock())
                .eqIfPresent(ProductSkuStockDO::getWarnStock, reqVO.getWarnStock())
                .eqIfPresent(ProductSkuStockDO::getReserveStock, reqVO.getReserveStock())
                .betweenIfPresent(ProductSkuStockDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductSkuStockDO::getId));
    }

    default List<ProductSkuStockDO> selectList(ProductSkuStockExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ProductSkuStockDO>()
                .eqIfPresent(ProductSkuStockDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(ProductSkuStockDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(ProductSkuStockDO::getStock, reqVO.getStock())
                .eqIfPresent(ProductSkuStockDO::getWarnStock, reqVO.getWarnStock())
                .eqIfPresent(ProductSkuStockDO::getReserveStock, reqVO.getReserveStock())
                .betweenIfPresent(ProductSkuStockDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductSkuStockDO::getId));
    }


    default List<ProductSkuStockDO> getSkuStocks(List<Long> skuIds) {
        return selectList(new LambdaQueryWrapperX<ProductSkuStockDO>()
                .inIfPresent(ProductSkuStockDO::getSkuId, skuIds));
    }

    default ProductSkuStockDO getSkuStock(Long skuId) {
        List<ProductSkuStockDO> skuStocks = getSkuStocks(Arrays.asList(skuId));
        if(CollectionUtils.isEmpty(skuStocks)) {
            return null;
        }
        return skuStocks.get(0);
    }

    /**
     * 扣减库存
     * @param skuId
     * @param count
     * @return 返回行数
     */
    int deductInventory(@Param("skuId") Long skuId, @Param("count") Integer count);

    /**
     * 库存还原，针对订单取消情况
     * @param skuId
     * @param count
     * @return
     */
    int restoreInventory(@Param("skuId") Long skuId, @Param("count") Integer count);
}
