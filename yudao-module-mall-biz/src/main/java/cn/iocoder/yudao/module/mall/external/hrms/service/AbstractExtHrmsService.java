package cn.iocoder.yudao.module.mall.external.hrms.service;

import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.HrmsConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.integration.HrmsConfigService;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtDeptDTO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtMemberDTO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtPageReqDTO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtPageRespDTO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;

import javax.annotation.Resource;

/**
 * 外部系统人员信息接口
 * <AUTHOR>
 */
public abstract class AbstractExtHrmsService {

    @Resource
    private HrmsConfigService hrmsConfigService;

    protected HrmsConfigDO getConfigDO() {
        return hrmsConfigService.getHrmsConfig();
    }

    abstract public String getClientCode();

    /**
     * 填充会员信息，根据工号查询并将相关字段填充到bean中
     * @param userNo 会员工号
     * @param userDO 会员信息
     */
    abstract public void fillMemberInfo(String userNo, MemberUserDO userDO);

    /**
     * 根据工号查询外部会员信息
     * @param userNo
     * @return
     */
    abstract public ExtMemberDTO queryMemberInfo(String userNo);

    /**
     * 分页查询会员
     * @param reqDTO
     * @return
     */
    abstract public ExtPageRespDTO<ExtMemberDTO> pageQueryMember(ExtPageReqDTO reqDTO);

    /**
     * 分页查询部门
     * @param reqDTO
     * @return
     */
    abstract public ExtPageRespDTO<ExtDeptDTO> pageQueryDept(ExtPageReqDTO reqDTO);


}
