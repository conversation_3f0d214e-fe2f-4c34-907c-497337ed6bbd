package cn.iocoder.yudao.module.mall.external.open.context;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.alibaba.ttl.TransmittableThreadLocal;

/**
 * 开放平台上下文;
 * <AUTHOR>
 * @since 2023-09-08
 */
public class OpenContextHolder {

    /**
     * 当前供应商编号
     */
    private static final ThreadLocal<Long> SUPPLIER_ID = new TransmittableThreadLocal<>();
    /**
     * 当前供应商名称
     */
    private static final ThreadLocal<String> SUPPLIER_NAME = new TransmittableThreadLocal<>();

    /**
     * 获得供应商编号。
     *
     * @return 供应商编号
     */
    public static Long getSupplierId() {
        return SUPPLIER_ID.get();
    }

    /**
     * 获得供应商编号。如果不存在，则抛出 NullPointerException 异常
     *
     * @return 租户编号
     */
    public static Long getRequiredSupplierId() {
        Long tenantId = getSupplierId();
        if (tenantId == null) {
            throw new NullPointerException("TenantContextHolder 不存在供应商编号！请检查openapp配置");
        }
        return tenantId;
    }

    public static void setSupplierId(Long tenantId) {
        SUPPLIER_ID.set(tenantId);
    }

    /**
     * 获得供应商名称。
     *
     * @return 供应商名称
     */
    public static String getSupplierName() {
        return SUPPLIER_NAME.get();
    }

    /**
     * 获得供应商名称。如果不存在，则抛出 NullPointerException 异常
     *
     * @return 租户编号
     */
    public static String getRequiredSupplierName() {
        String name = getSupplierName();
        if (name == null) {
            throw new NullPointerException("TenantContextHolder 不存在供应商名称！请检查openapp配置");
        }
        return name;
    }

    public static void setSupplierName(String supplierName) {
        SUPPLIER_NAME.set(supplierName);
    }

    public static void clear() {
        SUPPLIER_ID.remove();
        SUPPLIER_NAME.remove();
    }

}
