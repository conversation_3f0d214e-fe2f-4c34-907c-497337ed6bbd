package cn.iocoder.yudao.module.mall.product.service.skustock;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenSkuStockUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.AreaDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.Stock;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuCreateOrUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.ProductSkuStockCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.ProductSkuStockExportReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.ProductSkuStockPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.ProductSkuStockUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.convert.area.AreaConvert;
import cn.iocoder.yudao.module.mall.product.convert.skustock.ProductSkuStockConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skustock.ProductSkuStockDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.sku.ProductSkuMapper;
import cn.iocoder.yudao.module.mall.product.dal.mysql.skustock.ProductSkuStockMapper;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppTradeOrderCreateReqVO.Item;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsStockService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.request.getNewStockById.AreaBaseInfoGoodsReq;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.request.getNewStockById.GetStockByIdGoodsReq;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.request.getNewStockById.SkuNumBaseGoodsReq;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.response.getNewStockById.GetStockByIdGoodsResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.yudao.module.mall.trade.enums.ErrorCodeConstants.ORDER_CREATE_SKU_STOCK_NOT_ENOUGH2;

/**
 * sku库存 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductSkuStockServiceImpl extends ServiceImpl<ProductSkuStockMapper, ProductSkuStockDO> implements ProductSkuStockService {

    @Resource
    private ProductSkuStockMapper skuStockMapper;

    @Resource
    private ProductSkuMapper productSkuMapper;

    @Resource
    private SupplierService supplierService;

    @Resource
    private VopGoodsStockService vopGoodsStockService;

    @Override
    public Long createSkuStock(ProductSkuStockCreateReqVO createReqVO) {
        // 插入
        ProductSkuStockDO skuStock = ProductSkuStockConvert.INSTANCE.convert(createReqVO);
        skuStockMapper.insert(skuStock);
        // 返回
        return skuStock.getId();
    }

    @Override
    public void updateSkuStock(ProductSkuStockUpdateReqVO updateReqVO) {
        // 校验存在
        validateSkuStockExists(updateReqVO.getId());
        // 更新
        ProductSkuStockDO updateObj = ProductSkuStockConvert.INSTANCE.convert(updateReqVO);
        skuStockMapper.updateById(updateObj);
    }

    @Override
    public void batchUpdateSkuStock2(@Valid List<AppOpenSkuStockUpdateReqVO> updateReqVOs, Long supplierId) {
        updateReqVOs.forEach(reqVO -> {
            skuStockMapper.update(null, Wrappers.lambdaUpdate(ProductSkuStockDO.class)
                    .eq(ProductSkuStockDO::getSupplierId, supplierId)
                    .eq(ProductSkuStockDO::getSkuId, reqVO.getSkuId())
                    .set(ProductSkuStockDO::getStock, reqVO.getStock()));
        });
    }

    @Override
    public void batchUpdateSkuStock(List<ProductSkuStockDO> skuDOs) {
        skuStockMapper.updateBatch(skuDOs, 1000);
    }

    @Override
    public void deleteSkuStock(Long id) {
        // 校验存在
        validateSkuStockExists(id);
        // 删除
        skuStockMapper.deleteById(id);
    }

    private void validateSkuStockExists(Long id) {
        if (skuStockMapper.selectById(id) == null) {
        }
    }

    @Override
    public ProductSkuStockDO getSkuStock(Long skuId) {
        return skuStockMapper.getSkuStock(skuId);
    }

    @Override
    public ProductSkuStockDO createOrUpdateStock(ProductSkuDO sku, ProductSkuCreateOrUpdateReqVO skuVO) {
        ProductSkuStockDO stockDO = skuStockMapper.selectOne(ProductSkuStockDO::getSkuId, sku.getId());
        if (stockDO == null) {
            stockDO = new ProductSkuStockDO();
            stockDO.setSupplierId(sku.getSupplierId());
            stockDO.setSkuId(sku.getId());
            stockDO.setStock(skuVO.getStock());
            stockDO.setWarnStock(skuVO.getWarnStock());
            stockDO.setReserveStock(0);
            skuStockMapper.insert(stockDO);
        } else {
            stockDO.setStock(skuVO.getStock());
            stockDO.setWarnStock(skuVO.getWarnStock());
            skuStockMapper.updateById(stockDO);
        }
        return stockDO;
    }

    @Override
    public List<ProductSkuStockDO> getSkuStockList(Collection<Long> ids) {
        return skuStockMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ProductSkuStockDO> getSkuStockPage(ProductSkuStockPageReqVO pageReqVO) {
        return skuStockMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProductSkuStockDO> getSkuStockList(ProductSkuStockExportReqVO exportReqVO) {
        return skuStockMapper.selectList(exportReqVO);
    }

    @Override
    public List<ProductSkuStockDO> getSkuStocks(List<Long> skuIds) {
        return skuStockMapper.getSkuStocks(skuIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deductInventory(List<Item> items) {
        if (CollectionUtils.isNotEmpty(items)) {
            for (Item item : items) {
                int updateRow = skuStockMapper.deductInventory(item.getSkuId(), item.getCount());
                if (updateRow == 0) {
                    throw ServiceExceptionUtil.exception(ORDER_CREATE_SKU_STOCK_NOT_ENOUGH2, item.getSkuName());
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollbackInventory(List<TradeOrderItemDO> items) {
        if (CollectionUtils.isNotEmpty(items)) {
            for (TradeOrderItemDO item : items) {
                skuStockMapper.restoreInventory(item.getSkuId(), item.getCount());
            }
        }
    }

    @Override
    public List<Stock> getProductSkuStocks(List<Long> skuIds, AreaDTO areaDTO) {
        List<Stock> stockList = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuIds)) {
            return null;
        }
        SupplierDO jdSupplier = supplierService.getSupplierJD();
        List<ProductSkuDO> productSkuDOS = productSkuMapper.selectBatchIds(skuIds);
        Assert.notEmpty(productSkuDOS, "商品不存在");
        Map<String, Long> jdSkuMap = null;
        if(jdSupplier != null) {
            jdSkuMap = productSkuDOS.stream().filter(productSkuDO -> productSkuDO.getSupplierId().equals(jdSupplier.getId()))
                    .collect(Collectors.toMap(ProductSkuDO::getSkuInnerId, ProductSkuDO::getId, (k1, k2) -> k1));
        }
        if (MapUtils.isNotEmpty(jdSkuMap)) {
            // 批量查询库存状态
            GetStockByIdGoodsReq getStockByIdGoodsReq = new GetStockByIdGoodsReq();
            AreaBaseInfoGoodsReq areaBaseInfoGoodsReq = AreaConvert.INSTANCE.convertStockArea(areaDTO);
            getStockByIdGoodsReq.setAreaInfo(areaBaseInfoGoodsReq);
            Collection<String> jdSkuIds = jdSkuMap.keySet();
            List<SkuNumBaseGoodsReq> goodsReqs = jdSkuIds.stream().map(skuId -> {
                SkuNumBaseGoodsReq skuNumBaseGoodsReq = new SkuNumBaseGoodsReq();
                skuNumBaseGoodsReq.setSkuId(Long.valueOf(skuId));
                skuNumBaseGoodsReq.setSkuNumber(1);
                return skuNumBaseGoodsReq;
            }).collect(Collectors.toList());
            getStockByIdGoodsReq.setSkuNumInfoList(goodsReqs);
            List<GetStockByIdGoodsResp> getStockByIdGoodsResps = vopGoodsStockService.getNewStockById(getStockByIdGoodsReq).getOpenRpcResult().getResult();
            List<Stock> stocks = ProductSkuStockConvert.INSTANCE.convertStocks01(getStockByIdGoodsResps, jdSkuMap);
            if (CollectionUtils.isNotEmpty(stocks)) {
                stockList.addAll(stocks);
            }
        }
        List<Long> supplierSkuIds = productSkuDOS.stream().filter(productSkuDO -> jdSupplier == null || !productSkuDO.getSupplierId().equals(jdSupplier.getId()))
                .map(ProductSkuDO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(supplierSkuIds)) {
            List<ProductSkuStockDO> skuStocks = getSkuStocks(supplierSkuIds);
            List<Stock> stocks = ProductSkuStockConvert.INSTANCE.convertStocks(skuStocks);
            if (CollectionUtils.isNotEmpty(stocks)) {
                stockList.addAll(stocks);
            }
        }
        Map<Long, Stock> existsStockMap = stockList.stream().collect(Collectors.toMap(Stock::getSkuId, Function.identity(), (k1, k2) -> k1));
        return skuIds.stream().map(skuId -> {
            Stock stock = existsStockMap.get(skuId);
            if (stock == null) {
                stock = new Stock().setStockStateType(34).setStockStateDesc("无货").setSkuId(skuId).setRemainNumInt(0);
            }
            return stock;
        }).collect(Collectors.toList());
    }

    @Override
    public void saveBatchProductSkuStocks(List<ProductSkuStockDO> productSkuStockDOS) {
        if (CollectionUtils.isEmpty(productSkuStockDOS)) {
            return;
        }

        // 提取 SKU IDs
        List<Long> skuIds = productSkuStockDOS.stream().map(ProductSkuStockDO::getSkuId).collect(Collectors.toList());

        // 获取现有库存数据
        List<ProductSkuStockDO> skuStocks = skuStockMapper.getSkuStocks(skuIds);
        Map<Long, ProductSkuStockDO> skuStockDOMap = convertMap(skuStocks, ProductSkuStockDO::getSkuId);

        // 根据 SKU ID 设置已存在记录的 ID
        for (ProductSkuStockDO productSkuStockDO : productSkuStockDOS) {
            if (skuStockDOMap.containsKey(productSkuStockDO.getSkuId())) {
                productSkuStockDO.setId(skuStockDOMap.get(productSkuStockDO.getSkuId()).getId());
            }
        }

        // 将插入和更新的数据分类
        List<ProductSkuStockDO> insertList = productSkuStockDOS.stream().filter(productSkuStockDO -> productSkuStockDO.getId() == null).collect(Collectors.toList());
        List<ProductSkuStockDO> updateList = productSkuStockDOS.stream().filter(productSkuStockDO -> productSkuStockDO.getId() != null).collect(Collectors.toList());

        // 执行批量插入
        if (CollectionUtils.isNotEmpty(insertList)) {
            skuStockMapper.insertBatch(insertList);
        }

        // 执行批量更新
        if (CollectionUtils.isNotEmpty(updateList)) {
            skuStockMapper.updateBatch(updateList, 1000); // 可根据实际情况调整批量更新的大小
        }
    }

}
