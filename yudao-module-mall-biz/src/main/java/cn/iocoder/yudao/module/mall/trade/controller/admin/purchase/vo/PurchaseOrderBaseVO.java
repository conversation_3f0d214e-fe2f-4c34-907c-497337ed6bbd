package cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo;

import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderBaseVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderItemBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 采购订单 Base VO")
@Data
public class PurchaseOrderBaseVO extends TradeOrderBaseVO {

    @Schema(description = "订单项列表")
    private List<TradeOrderItemBaseVO> items;

}
