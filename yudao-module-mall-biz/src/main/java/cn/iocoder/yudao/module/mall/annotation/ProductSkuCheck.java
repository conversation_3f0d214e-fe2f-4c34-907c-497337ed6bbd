package cn.iocoder.yudao.module.mall.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static cn.iocoder.yudao.module.mall.annotation.ProductSkuCheck.CheckSortType.BEFORE;

/**
 * <AUTHOR>
 * @date 2024/1/30
 */

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ProductSkuCheck {

    String skuId() default "";

    boolean async() default false;

    CheckSortType checkSort() default BEFORE;


    /**
     * 执行顺序
     */
    public static enum CheckSortType {
        BEFORE,
        AFTER;
    }
}

