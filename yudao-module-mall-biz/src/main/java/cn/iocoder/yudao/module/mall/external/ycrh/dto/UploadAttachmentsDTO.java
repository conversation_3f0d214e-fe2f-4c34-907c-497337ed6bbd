package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 上传附件
 * <AUTHOR>
 * @date 2023/7/29
 */
@Data
public class UploadAttachmentsDTO {

    /**
     * 业务流水号
     */
    @JsonProperty("ywlsh")
    private String businessNo;

    /**
     * 模块代码（由审批系统分配）
     */
    @JsonProperty("mkdm")
    private String moduleName;

    /**
     * 业务编号 业务系统根据单据业务自定义
     */
    @JsonProperty("ywbh")
    private String businessCode;

    /**
     * 文件名 附件原始文件名
     */
    @JsonProperty("wjm")
    private String fileName;

    /**
     * 附件内容 二进制文件流byte[]的base64字符串
     */
    @JsonProperty("fjnr")
    private String attachments;

    /**
     * 是否重要附件 0否，1是，重要附件系统会以红色字体突出显示
     */
    @JsonProperty("sfzyfj")
    private String isImportant;

    /**
     * 序号 由1开始自行升序编排，同1单据的附件中序号不得重复
     */
    @JsonProperty("xh")
    private String serialNumber;

}
