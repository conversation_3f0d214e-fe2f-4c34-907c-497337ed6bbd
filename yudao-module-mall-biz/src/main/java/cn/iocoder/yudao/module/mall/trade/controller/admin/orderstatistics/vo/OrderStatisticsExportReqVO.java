package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 订单统计 Excel 导出 Request VO，参数和 OrderStatisticsPageReqVO 是一致的")
@Data
public class OrderStatisticsExportReqVO {

    @Schema(description = "供应商id")
    private Long supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "统计维度类型 1-日 2-月  3-年")
    private Integer type;

    @Schema(description = "商品id")
    private Long skuId;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "订单数量")
    private Integer orderNum;

    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    @Schema(description = "统计时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] staticsTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
