package cn.iocoder.yudao.module.mall.product.controller.app.category.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "用户 APP - 商品分类 Response VO")
public class AppProductCategoryRespVO {

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "分类图标")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String icon;

    @Schema(description = "H5分类图标")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String iconH5;

    @Schema(description = "父级分类ID")
    private Long parentId;

    @Schema(description = "排序")
    private Integer orderSort;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "分类级别")
    private Integer categoryLevel;

    @Schema(description = "下级分类")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<AppProductCategoryRespVO> childCategoryList;

}
