package cn.iocoder.yudao.module.mall.product.controller.app.sku.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品固资检测
 */
@Data
public class AppProductAssetsCheckReqVO {

    @NotNull(message = "参数supplierId不能为空")
    private Long supplierId;
    @NotNull(message = "参数skuId不能为空")
    private Long skuId;
    @NotNull(message = "参数count不能为空")
    private Integer count;
    @NotBlank(message = "参数count不能为空")
    private String categoryCode;

}
