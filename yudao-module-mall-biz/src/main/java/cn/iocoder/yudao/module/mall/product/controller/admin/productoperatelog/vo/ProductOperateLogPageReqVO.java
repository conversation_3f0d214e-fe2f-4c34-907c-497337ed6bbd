package cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品操作日志记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductOperateLogPageReqVO extends PageParam {

    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "skuId")
    private Long spuId;

    @Schema(description = "供应商id")
    private Long supplierId;

    @Schema(description = "用户编号")
    private Long userId;

    @Schema(description = "操作分类")
    private Integer operateType;


    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
