package cn.iocoder.yudao.module.mall.trade.controller.admin.orderoperatelog;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderoperatelog.vo.OrderOperateLogPageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderoperatelog.vo.OrderOperateLogRespVO;
import cn.iocoder.yudao.module.mall.trade.convert.orderoperatelog.OrderOperateLogConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.orderoperatelog.OrderOperateLogDO;
import cn.iocoder.yudao.module.mall.trade.service.orderoperatelog.OrderOperateLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 订单操作日志记录
 */
@Tag(name = "管理后台 - 订单操作日志记录")
@RestController
@RequestMapping("/trade/order-operate-log")
@Validated
public class OrderOperateLogController {

    @Resource
    private OrderOperateLogService orderOperateLogService;


    /**
     * 获得订单操作日志记录
     * @param id
     * @return
     */
    @GetMapping("/get")
    @Operation(summary = "获得订单操作日志记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:order-operate-log:query')")
    public CommonResult<OrderOperateLogRespVO> getOrderOperateLog(@RequestParam("id") Long id) {
        OrderOperateLogDO orderOperateLog = orderOperateLogService.getOrderOperateLog(id);
        return success(OrderOperateLogConvert.INSTANCE.convert(orderOperateLog));
    }


    /**
     * 获得订单操作日志记录分页
     * @param pageVO
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "获得订单操作日志记录分页")
    @PreAuthorize("@ss.hasPermission('trade:order-operate-log:query')")
    public CommonResult<PageResult<OrderOperateLogRespVO>> getOrderOperateLogPage(@Valid OrderOperateLogPageReqVO pageVO) {
        PageResult<OrderOperateLogDO> pageResult = orderOperateLogService.getOrderOperateLogPage(pageVO);
        return success(OrderOperateLogConvert.INSTANCE.convertPage(pageResult));
    }


}
