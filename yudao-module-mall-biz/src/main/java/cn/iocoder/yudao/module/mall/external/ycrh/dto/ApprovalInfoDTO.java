package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 审批实体
 * <AUTHOR>
 * @date 2023/7/24
 */
@Data
public class ApprovalInfoDTO {



    /**
     * 业务流水号 自定义不重复的流水号
     */
    @JsonProperty("ywlsh")
    private String businessNo;

    /**
     * 当前审批级别（从1开始，顺序增加审批级别）
     */
    @JsonProperty("spjb")
    private String approvalLevel;

    /**
     * 审批人姓名
     */
    @JsonProperty("spr")
    private String approvalUserName;

    /**
     * 审批人工号
     */
    @JsonProperty("sprbh")
    private String approvalUserNo;

    /**
     * 审批状态 初始为0，0未处理  1审批通过  2驳回 3 同级审批人已审批
     */
    @JsonProperty("spzt")
    private String approvalStatus;

    /**
     * 审批意见
     */
    @JsonProperty("spsm")
    private String approvalComments;

    /**
     * 是否会签 0 普通，1否，2是
     */
    @JsonProperty("cf2")
    private String isCountersign;

    /**
     * 审批角色名称
     */
    @JsonProperty("spjs")
    private String approvalRoleName;

    /**
     * 是否发送待审批提示短信 1是，0否
     */
    @JsonProperty("tsdx")
    private String isSendMessage;

    /**
     * 	是否签章 0否，1证书签章，2无证书签章
     */
    @JsonProperty("sfqz")
    private String isSignature;

    /**
     * 是否签名 是否签名,0否  1是
     */
    @JsonProperty("sfqm")
    private String isSign;

    /**
     * 	编辑流程	 是否可编辑审批流程 1:能, 0或其他:不能
     */
    @JsonProperty("bjlc")
    private String isUpdateApproval;

    /**
     * 本级别是否可被编辑，1是，0否
     */
    @JsonProperty("cf6")
    private String isUpdateContent;

    /**
     * 是否可删除审批人  1:能, 0或其他:不能
     */
    @JsonProperty("scspr")
    private String isDeleteApprover;

    /**
     * 是否可指定审批人  (1:能, 0或其他:不能)
     */
    @JsonProperty("zdspr")
    private String isDesignatedApprover;

    /**
     * 审批角色编号 根据spjsbh在审批流程设置系统数据库中读取相应的人员作为指定人时候的范围
     */
    @JsonProperty("spjsbh")
    private String approvalRoleNo;

    /**
     * 申请人名称
     */
    @JsonProperty("sqr")
    private String applicantName;

    /**
     * 申请人编号
     */
    @JsonProperty("sqrbh")
    private String applicantNo;

}
