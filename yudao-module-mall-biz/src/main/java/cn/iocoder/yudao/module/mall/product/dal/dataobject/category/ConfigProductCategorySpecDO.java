package cn.iocoder.yudao.module.mall.product.dal.dataobject.category;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 预置商品规格 DO
 * 商品分类内置的规格，在商品维护时，根据所选分类，可以将规格列出由运营专员填写具体值
 *
 * <AUTHOR>
 */
@TableName("config_product_category_spec")
@KeySequence("config_product_category_spec_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigProductCategorySpecDO extends BaseDO {

    /**
     * 规格Id
     */
    @TableId
    private Long id;

    /**
     * 分类ID
     */
    private Long categoryId;
    /**
     * 规格名
     */
    private String name;

    /**
     * 1-spu通用参数类型 2-sku特有参数类型 {@link cn.iocoder.yudao.module.mall.product.enums.spec.SpecTypeEnum}
     */
    private Integer specType;

}
