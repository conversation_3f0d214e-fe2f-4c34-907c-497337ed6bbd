package cn.iocoder.yudao.module.mall.external.assets.util;

import lombok.SneakyThrows;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.Charset;


public class LBJwtUtil {

	private static final String ENCODING = "UTF-8";

	//解密token
	public static String dencrty(String token,String secret,String aeskey,Long issueId) throws UnsupportedEncodingException {
		//获取当前时间
		long now = System.currentTimeMillis();
		//创建JWT实例
		Jwt jwt = new Jwt(secret, aeskey, System.nanoTime(), issueId);
		token = URLDecoder.decode(token,ENCODING);
		//调用解密方法解密token
		String resultJson = jwt.verifyAndDecrypt(token, now);
		return resultJson;
	}
	
	//加密数据
	public static String encrty(String json,String secret,String aeskey,Long issueId) throws UnsupportedEncodingException {
		//获取当前时间
		long now = System.currentTimeMillis();
		//创建JWT实例
		Jwt jwt = new Jwt(secret,aeskey,System.nanoTime(),issueId);
		int bufferSize = jwt.getBufferSize(json) + 1024;
		//创建payload用来装参数
		ByteBuffer payload = ByteBuffer.allocate(bufferSize).order(ByteOrder.BIG_ENDIAN);
        payload.put(json.getBytes(ENCODING)).flip();
		//创建out对象
        ByteBuffer out = ByteBuffer.allocate(bufferSize * 2);
        //调用加密方法，加密参数
        jwt.encryptAndSign(Jwt.Type.SYS, payload, out,now + 60 * 60 * 1000);
        String xjwt = new String(out.array(), out.arrayOffset(), out.remaining(), Charset.forName(ENCODING));
		return URLEncoder.encode(xjwt,ENCODING);
	}

	@SneakyThrows
	public static void main(String[] args) {
		String secret = "yBJeBB";
		String aeskey = "d2YxdU9TelJvV2RETmtrOXJxN3pSNjJYUzFnOW43cU8=";
		Long issueId = 100010L;

		String json = "{\"tmpAcceptNo\":\"\",\"approveStatus\":\"5\",\"acceptNo\":\"\",\"approveMemo\":\"单据删除\",\"assetNo\":[\"2016006674\",\"2016006677\",\"2016006678\",\"2016006679\",\"2016006680\",\"2016006681\",\"2016006682\",\"2016006683\",\"2016006684\",\"2016006685\",\"2016006686\",\"2016006687\",\"2016006688\",\"2016006689\",\"2016006690\",\"2016006691\",\"2016006692\",\"2016006693\",\"2016006943\",\"2016006944\",\"2016006945\",\"2016006946\",\"2016006947\",\"2016006948\",\"2016006949\",\"2016006950\",\"2016006951\",\"2016006952\",\"2016006953\",\"2016006954\",\"2016006955\",\"2016006956\",\"2016006957\",\"2016006958\",\"2016006984\",\"2016006985\",\"2016007671\",\"2016007672\",\"2016007673\",\"2016007674\",\"2016007675\",\"2016007676\",\"2016007677\",\"2016007678\",\"2016007679\",\"2016007680\",\"2016007681\",\"2016007682\",\"2016007683\",\"2016007684\",\"2016007685\",\"2016007686\",\"2016007695\",\"2016007700\",\"2016007705\",\"2016007706\",\"2016007707\",\"2016007708\",\"2016007709\",\"2016007710\",\"2016007711\",\"2016007712\",\"2016007713\",\"2016007714\",\"2016007715\",\"2016007807\",\"2016007897\",\"2016007898\",\"2016007899\",\"2016007900\",\"2016007901\",\"2016007902\",\"2016007903\",\"2016007904\",\"2017000031\",\"2017000032\",\"2017000033\",\"2017000034\",\"2017000035\",\"2017000036\",\"2017000037\",\"2017000038\",\"2017000039\",\"2017000040\",\"2017000041\",\"2017000042\",\"2017000169\",\"2017000170\",\"2017000171\",\"2017000172\",\"2017000173\",\"2017000187\",\"2017000188\",\"2017000189\",\"2017000190\",\"2017000191\",\"2017000192\",\"2017000193\",\"2017000194\",\"2017000195\",\"2017000196\",\"2017000197\",\"2017000198\",\"2017000600\",\"2017000601\",\"2017000602\",\"2017000603\",\"2017000604\",\"2017000605\",\"2017000606\",\"2017000607\",\"2017000608\",\"2017000609\",\"2017000610\",\"2017000611\",\"2017000612\",\"2017000613\",\"2017000614\",\"2017000615\",\"2017000616\",\"2017000617\",\"2017000618\",\"2017000619\",\"2017000620\",\"2017000621\",\"2017000622\",\"2017000623\",\"2017000624\",\"2017000625\",\"2017000930\",\"2017000931\",\"2017000932\",\"2017000933\",\"2017000934\",\"2017000935\",\"2017000936\",\"2017000937\",\"2017000938\",\"2017000939\",\"2017000940\",\"2017000941\",\"2017000942\",\"2017000943\",\"2017000944\",\"2017000945\",\"2017000946\",\"2017000947\",\"2017000948\",\"2017000949\",\"2017000959\",\"2017000960\",\"2017000961\",\"2017000962\",\"2017000963\",\"2017000964\",\"2017000965\",\"2017000966\",\"2017000967\",\"2017000968\",\"2017000969\",\"2017000970\",\"2017000971\",\"2017000972\",\"2017000973\",\"2017000974\",\"2017000975\",\"2017000978\",\"2017000979\",\"2017000980\",\"2017000981\",\"2017000982\",\"2017000983\",\"2017000984\",\"2017000986\",\"2017000989\",\"2017000991\",\"2017000992\",\"2017000993\",\"2017000994\",\"2017000995\",\"2017000996\",\"2017000997\",\"2018000004\",\"2018000005\",\"2018000139\",\"2018000140\",\"2018000291\",\"2018000292\",\"2018000293\",\"2018000294\",\"2018000295\",\"2018000296\",\"2018000297\",\"2018000298\",\"2018000299\",\"2018000300\",\"2018000301\",\"2018000302\",\"2018000303\",\"2018000304\",\"2018000305\",\"2018025906\",\"2018025918\",\"2018030146\",\"2019000001\",\"2019000002\",\"2019000003\",\"2019000004\",\"2019000005\",\"2019000006\",\"2019000007\",\"2019000008\",\"2019000009\",\"2019000010\",\"2019000011\",\"2019000012\",\"2019000013\",\"2019000014\",\"2019000015\",\"2019000016\",\"2019000017\",\"2019000018\",\"2019000019\",\"2019000020\",\"2019000021\",\"2019000022\",\"2019000023\",\"2019000024\",\"2019000025\",\"2019000026\",\"2019000037\",\"2019000053\",\"2019000054\",\"2019000055\",\"2019000076\",\"2019000078\",\"2019000082\",\"2019000083\",\"2019000084\",\"2019000087\",\"2019000088\",\"2019000091\",\"2019000093\",\"2019000244\",\"2019000404\",\"2019000405\",\"2020000003\",\"2020000004\",\"2020000005\",\"2020000006\",\"2020000007\",\"2020000008\",\"2020000009\",\"2020000010\",\"2020000011\",\"2020000012\",\"2020000013\",\"2021000001\",\"2021000002\",\"2021000003\",\"2021000004\",\"2021000005\",\"2021000006\",\"2021000177\",\"2021000178\",\"2021000179\",\"2021000180\",\"2021000186\",\"2021000187\",\"2021000188\",\"2021000189\",\"2021000190\",\"S1980000037\",\"S1980000038\",\"S1983000046\",\"S1986000064\",\"S1986000065\",\"S1986000066\",\"S1986000067\",\"S1986000068\",\"S1986000069\",\"S1986000070\",\"S1986000071\",\"S1986000072\",\"S1986000073\",\"S1986000074\",\"S1986000075\",\"S1986000076\",\"S1986000077\",\"S1986000078\",\"S1986000079\",\"S1986000080\",\"S1986000081\",\"S1986000082\",\"S1986000083\",\"S1986000084\",\"S1988000113\",\"S1990000210\",\"S1994000235\",\"S1994000236\",\"S1994000519\",\"S1995000531\",\"S1995000532\",\"S1995000542\",\"S1995000543\",\"S1995000544\",\"S1995000545\",\"S1995000546\",\"S1995000547\",\"S1995000548\",\"S1995000549\",\"S1995000550\",\"S1995000551\",\"S1995000552\",\"S1995000553\",\"S1995000554\",\"S1995000555\",\"S1995000556\",\"S1995000557\",\"S1995000558\",\"S1995000559\",\"S1995000560\",\"S1995000561\",\"S1995000562\",\"S1995000563\",\"S1995000564\",\"S1995000565\",\"S1995000566\",\"S1996000572\",\"S1996000576\",\"S1996000577\",\"S1996000580\",\"S1996000581\",\"S1996000582\",\"S1996000583\",\"S1996000584\",\"S1996000585\",\"S1996000588\",\"S1996000589\",\"S1996000592\",\"S1996000593\",\"S1996000594\",\"S1996000595\",\"S1997000597\",\"S1998000945\",\"S1998000946\",\"S1998001158\",\"S1998001194\",\"S1999001212\",\"S1999001213\",\"S1999001214\",\"S1999001215\",\"S1999001216\",\"S1999001217\",\"S1999001218\",\"S1999001219\",\"S1999001220\",\"S1999001221\",\"S1999001222\",\"S1999001223\",\"S1999001224\",\"S1999001225\",\"S1999001226\",\"S1999001227\",\"S1999001228\",\"S1999001229\",\"S1999001230\",\"S1999001231\",\"S1999001232\",\"S1999001233\",\"S1999001235\",\"S1999001305\",\"S1999001570\",\"S1999001571\",\"S2000001890\",\"S2000002321\",\"S2000002322\",\"S2000002324\",\"S2000002325\",\"S2000002326\",\"S2000002327\",\"S2000002328\",\"S2000002375\",\"S2000002376\",\"S2000002377\",\"S2000002378\",\"S2000002379\",\"S2000002380\",\"S2000002381\",\"S2000002382\",\"S2000002383\",\"S2000002384\",\"S2000002385\",\"S2000002386\",\"S2000002387\",\"S2000002388\",\"S2001002398\",\"S2001002399\",\"S2001002400\",\"S2001002401\",\"S2001002406\",\"S2001002407\",\"S2001002448\",\"S2001002449\",\"S2001002450\",\"S2001002451\",\"S2001002452\",\"S2001002453\",\"S2001002454\",\"S2001002455\",\"S2001002456\",\"S2001002457\",\"S2001002458\",\"S2001002459\",\"S2001002460\",\"S2001002461\",\"S2001002462\",\"S2001002463\",\"S2001002464\",\"S2001002465\",\"S2001002466\",\"S2001002467\",\"S2001002468\",\"S2001002469\",\"S2002002487\",\"S2002002492\",\"S2002002493\",\"S2002002494\",\"S2002002495\",\"S2002002508\",\"S2002002512\",\"S2002002513\",\"S2002002514\",\"S2002002515\",\"S2002002516\",\"S2002002517\",\"S2002002518\",\"S2002002519\",\"S2002002520\",\"S2002002521\",\"S2002002522\",\"S2002002523\",\"S2002002524\",\"S2002002525\",\"S2003002526\",\"S2003002537\",\"S2003002538\",\"S2003002539\",\"S2003002542\",\"S2003002605\",\"S2003002606\",\"S2003002607\",\"S2003002608\",\"S2003002609\",\"S2003002610\",\"S2003002611\",\"S2003002612\",\"S2003002613\",\"S2003002614\",\"S2003002625\",\"S2004002639\",\"S2004002640\",\"S2004002698\",\"S2004002707\",\"S2004002708\",\"S2005002709\",\"S2005002715\",\"S2005002717\",\"S2005002718\",\"S2005002719\",\"S2005002720\",\"S2005002721\",\"S2005002722\",\"S2005002723\",\"S2005002724\",\"S2005002725\",\"S2005002727\",\"S2005002729\",\"S2005002730\",\"S2005002731\",\"S2005002732\",\"S2005002733\",\"S2005002734\",\"S2005002736\",\"S2005002737\",\"S2005002738\",\"S2005002739\",\"S2005002741\",\"S2005002742\",\"S2005002776\",\"S2005002777\",\"S2005002782\",\"S2005002783\",\"S2005002803\",\"S2005002804\",\"S2005002805\",\"S2005002806\",\"S2005002807\",\"S2005002808\",\"S2005002809\",\"S2005002810\",\"S2005002811\",\"S2005002812\",\"S2005002813\",\"S2005002814\",\"S2005002815\",\"S2005002816\",\"S2005002817\",\"S2006002859\",\"S2006002863\",\"S2006002864\",\"S2006002952\",\"S2006002961\",\"S2006002963\",\"S2006002964\",\"S2006002965\",\"S2006002967\",\"S2007003014\",\"S2007003015\",\"S2007003018\",\"S2007003876\",\"S2007003879\",\"S2007003880\",\"S2007003881\",\"S2007003882\",\"S2007003883\",\"S2007003884\",\"S2007003886\",\"S2007003887\",\"S2007003890\",\"S2007003894\",\"S2007003895\",\"S2007003903\",\"S2007003904\",\"S2007003905\",\"S2007003906\",\"S2007003907\",\"S2008003908\",\"S2008003909\",\"S2008003910\",\"S2008003915\",\"S2008003916\",\"S2008003917\",\"S2008003918\",\"S2008003919\",\"S2008003920\",\"S2008003921\",\"S2008003922\",\"S2008003923\",\"S2008003925\",\"S2008003926\",\"S2008003927\",\"S2008003928\",\"S2008003929\",\"S2008003930\",\"S2008003931\",\"S2008003932\",\"S2008003933\",\"S2008003934\",\"S2008003935\",\"S2008003936\",\"S2008003937\",\"S2008003938\",\"S2008003939\",\"S2008003940\",\"S2008003941\",\"S2008003966\",\"S2008003967\",\"S2008003968\",\"S2008003969\",\"S2008003971\",\"S2008003972\",\"S2009003974\",\"S2009003976\",\"S2009003977\",\"S2009003978\",\"S2009003979\",\"S2009004064\",\"S2009004065\",\"S2009004066\",\"S2009004067\",\"S2009004068\",\"S2009004069\",\"S2009004070\",\"S2009004086\",\"S2009004087\",\"S2009004088\",\"S2009004089\",\"S2009004090\",\"S2009004091\",\"S2009004092\",\"S2009004119\",\"S2009004120\",\"S2009004121\",\"S2009004122\",\"S2009004123\",\"S2009004124\",\"S2009004125\",\"S2009004126\",\"S2009004128\",\"S2009004142\",\"S2009004145\",\"S2009004147\",\"S2009004149\",\"S2009004151\",\"S2009004152\",\"S2010004153\",\"S2010004154\",\"S2010004155\",\"S2010004156\",\"S2010004157\",\"S2010004158\",\"S2010004159\",\"S2010004160\",\"S2010004161\",\"S2010004162\",\"S2010004163\",\"S2010004164\",\"S2010004165\",\"S2010004166\",\"S2010004167\",\"S2010004168\",\"S2010004169\",\"S2010004170\",\"S2010004171\",\"S2010004172\",\"S2010004173\",\"S2010004174\",\"S2010004175\",\"S2010004176\",\"S2010004177\",\"S2010004178\",\"S2010004179\",\"S2010004180\",\"S2010004181\",\"S2010004182\",\"S2010004183\",\"S2010004184\",\"S2010004185\",\"S2010004186\",\"S2010004190\",\"S2010004191\",\"S2010004317\",\"S2010004318\",\"S2010004319\",\"S2010004320\",\"S2010004321\",\"S2010004322\",\"S2010004323\",\"S2010004324\",\"S2010004325\",\"S2010004326\",\"S2010004327\",\"S2010004328\",\"S2010004329\",\"S2010004330\",\"S2010004331\",\"S2010004332\",\"S2010004333\",\"S2010004334\",\"S2010004335\",\"S2010004336\",\"S2010004337\",\"S2010004338\",\"S2010004339\",\"S2010004340\",\"S2010004341\",\"S2010004342\",\"S2010004343\",\"S2010004344\",\"S2010004345\",\"S2010004346\",\"S2010004347\",\"S2010004348\",\"S2010004349\",\"S2010004350\",\"S2010004351\",\"S2010004352\",\"S2010004353\",\"S2010004354\",\"S2010004355\",\"S2010004356\",\"S2010004357\",\"S2010004358\",\"S2010004359\",\"S2010004360\",\"S2010004361\",\"S2010004362\",\"S2010004363\",\"S2010004364\",\"S2010004365\",\"S2010004366\",\"S2010004367\",\"S2010004368\",\"S2010004369\",\"S2010004370\",\"S2010004371\",\"S2010004372\",\"S2010004373\",\"S2010004374\",\"S2010004375\",\"S2010004376\",\"S2010004377\",\"S2010004378\",\"S2010004379\",\"S2010004380\",\"S2010004381\",\"S2010004382\",\"S2010004383\",\"S2010004384\",\"S2010004403\",\"S2010004404\",\"S2010004405\",\"S2010004423\",\"S2010004442\",\"S2010004468\",\"S2010004470\",\"S2010004471\",\"S2010004474\",\"S2010004475\",\"S2010004476\",\"S2010004477\",\"S2010004478\",\"S2010004479\",\"S2010004480\",\"S2011004481\",\"S2011004483\",\"S2011004484\",\"S2011004485\",\"S2011004486\",\"S2011004487\",\"S2011004488\",\"S2011004489\",\"S2011004490\",\"S2011004491\",\"S2011004516\",\"S2011004517\",\"S2011004519\",\"S2011004523\",\"S2011004525\",\"S2011004526\",\"S2011004537\",\"S2012004538\",\"S2012004539\",\"S2012004540\",\"S2012004541\",\"S2012004542\",\"S2012004543\",\"S2012004554\",\"S2012004559\",\"S2012004560\",\"S2012004561\",\"S2012004562\",\"S2012004563\",\"S2012004564\",\"S2012004573\",\"S2012004585\",\"S2012004678\",\"S2012004679\",\"S2012004680\",\"S2012004681\",\"S2013004720\",\"S2013004742\",\"S2013004746\",\"S2013004748\",\"S2013004769\",\"S2013004770\",\"S2013004771\",\"S2013004807\",\"S2013004808\",\"S2013004810\",\"S2013004899\",\"S2013004900\",\"S2013004901\",\"S2013004902\",\"S2013004904\",\"S2013004905\",\"S2013004906\",\"S2013004908\",\"S2013004909\",\"S2013004910\",\"S2013004911\",\"S2013004922\",\"S2013004923\",\"S2013004924\",\"S2013004925\",\"S2013004926\",\"S2013004927\",\"S2013004928\",\"S2013004929\",\"S2013004930\",\"S2013004931\",\"S2013004932\",\"S2013004933\",\"S2013004934\",\"S2013004935\",\"S2013004936\",\"S2013004937\",\"S2013004938\",\"S2013004939\",\"S2013004942\",\"S2013004952\",\"S2013004953\",\"S2013004960\",\"S2013004961\",\"S2013004962\",\"S2013004970\",\"S2014004975\",\"S2014004976\",\"S2014004977\",\"S2014004978\",\"S2014004985\",\"S2014004989\",\"S2014004990\",\"S2014004993\",\"S2014004994\",\"S2014004995\",\"S2014004996\",\"S2014004999\",\"S2014005000\",\"S2014005001\",\"S2014005002\",\"S2014005003\",\"S2014005004\",\"S2014005005\",\"S2014005006\",\"S2014005007\",\"S2014005008\",\"S2014005009\",\"S2014005010\",\"S2014005011\",\"S2014005012\",\"S2014005013\",\"S2014005014\",\"S2014005015\",\"S2014005016\",\"S2014005017\",\"S2014005018\",\"S2014005019\",\"S2014005020\",\"S2014005021\",\"S2014005022\",\"S2014005023\",\"S2014005024\",\"S2014005025\",\"S2014005026\",\"S2014005027\",\"S2014005028\",\"S2014005029\",\"S2014005030\",\"S2014005031\",\"S2014005032\",\"S2014005033\",\"S2014005034\",\"S2014005035\",\"S2014005036\",\"S2014005037\",\"S2014005038\",\"S2014005039\",\"S2014005040\",\"S2014005041\",\"S2014005042\",\"S2014005043\",\"S2014005044\",\"S2014005045\",\"S2014005046\",\"S2014005047\",\"S2014005048\",\"S2014005049\",\"S2014005050\",\"S2014005051\",\"S2014005052\",\"S2014005053\",\"S2014005054\",\"S2014005055\",\"S2014005056\",\"S2014005057\",\"S2014005058\",\"S2014005059\",\"S2014005060\",\"S2014005061\",\"S2014005062\",\"S2014005063\",\"S2014005064\",\"S2014005065\",\"S2014005066\",\"S2014005067\",\"S2014005068\",\"S2014005069\",\"S2014005070\",\"S2014005071\",\"S2014005072\",\"S2014005073\",\"S2014005074\",\"S2014005075\",\"S2014005076\",\"S2014005077\",\"S2014005078\",\"S2014005079\",\"S2014005080\",\"S2014005081\",\"S2014005082\",\"S2014005083\",\"S2014005084\",\"S2014005085\",\"S2014005086\",\"S2014005087\",\"S2014005088\",\"S2014005089\",\"S2014005090\",\"S2014005091\",\"S2014005092\",\"S2014005093\",\"S2014005094\",\"S2014005095\",\"S2014005096\",\"S2014005097\",\"S2014005098\",\"S2014005099\",\"S2014005100\",\"S2014005101\",\"S2014005102\",\"S2014005103\",\"S2014005104\",\"S2014005105\",\"S2014005106\",\"S2014005107\",\"S2014005108\",\"S2014005109\",\"S2014005110\",\"S2014005111\",\"S2014005112\",\"S2014005113\",\"S2014005114\",\"S2014005115\",\"S2014005116\",\"S2014005117\",\"S2014005118\",\"S2014005119\",\"S2014005120\",\"S2014005121\",\"S2014005122\",\"S2014005123\",\"S2014005124\",\"S2014005125\",\"S2014005126\",\"S2014005127\",\"S2014005128\",\"S2014005129\",\"S2014005130\",\"S2014005131\",\"S2014005132\",\"S2014005133\",\"S2014005134\",\"S2014005135\",\"S2014005136\",\"S2014005137\",\"S2014005138\",\"S2014005139\",\"S2014005140\",\"S2014005141\",\"S2014005142\",\"S2014005143\",\"S2014005144\",\"S2014005145\",\"S2014005146\",\"S2014005147\",\"S2014005148\",\"S2014005149\",\"S2014005150\",\"S2014005151\",\"S2014005152\",\"S2014005153\",\"S2014005154\",\"S2014005155\",\"S2014005156\",\"S2014005157\",\"S2014005158\",\"S2014005159\",\"S2014005160\",\"S2014005161\",\"S2014005162\",\"S2014005163\",\"S2014005164\",\"S2014005165\",\"S2014005166\",\"S2014005167\",\"S2014005168\",\"S2014005169\",\"S2014005170\",\"S2014005171\",\"S2014005194\",\"S2014005195\",\"S2014005196\",\"S2014005197\",\"S2014005198\",\"S2014005199\",\"S2014005315\",\"S2014005381\",\"S2014005382\",\"S2014005383\",\"S2014005384\",\"S2014005385\",\"S2014005386\",\"S2014005387\",\"S2014005388\",\"S2014005389\",\"S2014005390\",\"S2014005391\",\"S2014005392\",\"S2014005393\",\"S2014005394\",\"S2014005395\",\"S2014005396\",\"S2014005397\",\"S2014005398\",\"S2014005399\",\"S2014005400\",\"S2014005401\",\"S2014005402\",\"S2014005403\",\"S2014005404\",\"S2014005405\",\"S2014005406\",\"S2014005407\",\"S2014005408\",\"S2014005409\",\"S2014005410\",\"S2014005411\",\"S2014005412\",\"S2014005413\",\"S2014005414\",\"S2014005415\",\"S2014005416\",\"S2014005417\",\"S2014005418\",\"S2014005419\",\"S2014005420\",\"S2014005421\",\"S2014005422\",\"S2014005423\",\"S2014005424\",\"S2014005425\",\"S2014005426\",\"S2014005427\",\"S2014005428\",\"S2014005429\",\"S2014005430\",\"S2014005431\",\"S2014005432\",\"S2014005433\",\"S2014005434\",\"S2014005435\",\"S2014005436\",\"S2014005437\",\"S2014005438\",\"S2014005439\",\"S2014005440\",\"S2014005441\",\"S2014005442\",\"S2014005443\",\"S2014005444\",\"S2014005449\",\"S2014005450\",\"S2014005456\",\"S2014005458\",\"S2014005459\",\"S2014005460\",\"S2014005461\",\"S2014005473\",\"S2014005474\",\"S2014005475\",\"S2014005476\",\"S2014005481\",\"S2014005482\",\"S2014005483\",\"S2014005484\",\"S2014005485\",\"S2014005486\",\"S2014005487\",\"S2014005488\",\"S2014005489\",\"S2014005490\",\"S2014005491\",\"S2014005492\",\"S2014005493\",\"S2014005494\",\"S2014005495\",\"S2014005496\",\"S2014005497\",\"S2014005498\",\"S2014005499\",\"S2014005500\",\"S2014005501\",\"S2014005502\",\"S2014005503\",\"S2014005504\",\"S2014005505\",\"S2014005506\",\"S2014005507\",\"S2014005508\",\"S2014005509\",\"S2014005510\",\"S2014005511\",\"S2014005512\",\"S2014005513\",\"S2014005514\",\"S2014005515\",\"S2014005516\",\"S2014005517\",\"S2014005518\",\"S2014005519\",\"S2014005520\",\"S2014005521\",\"S2014005525\",\"S2014005530\",\"S2014005531\",\"S2014005534\",\"S2014005535\",\"S2014005536\",\"S2014005537\",\"S2014005538\",\"S2014005539\",\"S2014005541\",\"S2014005542\",\"S2014005543\",\"S2014005544\",\"S2014005545\",\"S2014005546\",\"S2014005547\",\"S2014005548\",\"S2014005549\",\"S2014005550\",\"S2014005551\",\"S2014005552\",\"S2014005553\",\"S2014005554\",\"S2014005555\",\"S2014005556\",\"S2014005557\",\"S2014005558\",\"S2014005559\",\"S2014005560\",\"S2014005561\",\"S2014005562\",\"S2014005568\",\"S2014005570\",\"S2014005571\",\"S2014005572\",\"S2014005574\",\"S2014005575\",\"S2014005576\",\"S2014005601\",\"S2014005602\",\"S2014005603\",\"S2014005604\",\"S2014005605\",\"S2014005606\",\"S2014005607\",\"S2014005608\",\"S2014005609\",\"S2014005612\",\"S2014005613\",\"S2014005614\",\"S2014005615\",\"S2014005616\",\"S2014005618\",\"S2014005620\",\"S2014005621\",\"S2014005622\",\"S2014005623\",\"S2014005624\",\"S2014005625\",\"S2014005626\",\"S2014005627\",\"S2014005628\",\"S2014005629\",\"S2014005630\",\"S2014005631\",\"S2014005632\",\"S2014005633\",\"S2014005634\",\"S2014005635\",\"S2014005636\",\"S2014005637\",\"S2014005643\",\"S2014005644\",\"S2014005645\",\"S2014005646\",\"S2014005647\",\"S2014005648\",\"S2014005649\",\"S2014005650\",\"S2014005651\",\"S2014005652\",\"S2014005653\",\"S2014005654\",\"S2014005655\",\"S2014005656\",\"S2014005662\",\"S2014005663\",\"S2014005664\",\"S2014005665\",\"S2014005666\",\"S2014005667\",\"S2014005671\",\"S2014005672\",\"S2014005673\",\"S2014005674\",\"S2014005675\",\"S2014005676\",\"S2014005681\",\"S2014005682\",\"S2014005683\",\"S2014005684\",\"S2014005685\",\"S2014005686\",\"S2014005687\",\"S2014005688\",\"S2014005689\",\"S2014005690\",\"S2015005696\",\"S2015005699\",\"S2015005700\",\"S2015005701\",\"S2015005702\",\"S2015005709\",\"S2015005710\",\"S2015005711\",\"S2015005719\",\"S2015005720\",\"S2015005721\",\"S2015005722\",\"S2015005725\",\"S2015005726\",\"S2015005727\",\"S2015005728\",\"S2015005729\",\"S2015005730\",\"S2015005731\",\"S2015005732\",\"S2015005733\",\"S2015005734\",\"S2015005735\",\"S2015005766\",\"S2015005832\",\"S2015006401\",\"S2015006402\",\"S2015006403\",\"S2015006404\",\"S2015006405\",\"S2015006406\",\"S2015006407\",\"S2015006408\",\"S2015006409\",\"S2015006410\",\"S2015006411\",\"S2015006412\",\"S2015006413\",\"S2015006414\",\"S2015006416\",\"S2015006429\",\"S2015006430\",\"S2015006431\",\"S2015006432\",\"S2015006433\",\"S2015006434\",\"S2015006435\",\"S2015006436\",\"S2015006437\",\"S2015006438\",\"S2015006439\",\"S2015006440\",\"S2015006441\",\"S2015006442\",\"S2015006443\",\"S2015006444\",\"S2015006445\",\"S2015006446\",\"S2015006447\",\"S2015006485\",\"S2015006486\",\"S2015006488\",\"S2015006489\",\"S2015006490\",\"S2015006491\",\"S2015006492\",\"S2015006493\",\"S2015006494\",\"S2015006495\",\"S2015006496\",\"S2015006497\",\"S2015006498\",\"S2015006501\",\"S2015006502\",\"S2015006603\",\"S2015006604\",\"S2015006605\",\"S2015006606\",\"S2015006607\",\"S2015006608\",\"S2015006609\",\"S2015006610\",\"S2015006611\",\"S2015006612\",\"S2015006613\",\"S2015006614\",\"S2015006615\",\"S2015006616\",\"S2015006617\",\"S2015006619\",\"S2015006620\",\"S2015006622\",\"S2015006623\",\"S2015006624\",\"S2015006627\",\"S2015006628\",\"S2015006629\",\"S2015006630\",\"S2016006631\",\"S2016006632\",\"S2016006633\",\"S2016006634\",\"S2016006635\",\"S2016006636\",\"S2016006637\",\"S2016006638\",\"S2016006639\",\"S2016006640\",\"S2016006641\",\"S2016006642\",\"S2016006644\",\"S2016006645\",\"S2016006646\",\"S2016006647\",\"S2016006648\",\"S2016006649\",\"S2016006650\",\"S2016006651\",\"S2016006652\",\"S2016006653\",\"S2016006654\",\"S2016006655\",\"S2016006656\",\"S2016006657\",\"S2016006658\",\"S2016006659\",\"S2016006660\",\"S2016006661\",\"S2016006662\",\"S2016006663\",\"S2016006664\",\"S2016006665\",\"S2016006666\",\"S2016006667\",\"S2016006668\",\"S2016006669\",\"S2016006670\",\"S2016006671\",\"S2016006672\",\"W2001002438\",\"W2001002447\",\"W2002002503\",\"W2003002619\",\"W2003002620\",\"W2003002621\",\"W2003002622\",\"W2003002623\",\"W2004002652\",\"W2008003962\",\"W2008003963\",\"W2008003964\",\"W2008003965\",\"W2009004134\",\"W2009004135\",\"W2014005522\",\"W2014005523\",\"W2014005524\",\"W2014005526\",\"W2014005527\",\"W2014005528\",\"W2014005529\",\"W2014005563\",\"W2014005564\",\"W2014005565\",\"W2014005566\",\"W2014005588\",\"W2014005589\",\"W2014005590\",\"W2014005591\",\"W2014005592\",\"W2014005593\",\"W2014005594\",\"W2014005595\",\"W2014005596\",\"W2014005597\",\"W2015005692\",\"W2015005693\",\"W2015005697\",\"W2015005698\",\"W2015005703\",\"W2015005704\",\"W2015005705\",\"W2015005706\",\"W2015005723\",\"W2015005724\",\"W2015005833\",\"W2015005834\",\"W2015005835\",\"W2015005841\",\"W2015005842\",\"W2015005843\",\"W2015005844\",\"W2015005845\",\"W2015005846\",\"W2015006415\",\"W2015006618\",\"W2016007696\",\"W2016007697\",\"W2016007698\",\"W2016007699\",\"W2017000998\",\"W2017000999\",\"W2017001000\",\"W2017001001\",\"W2017001002\",\"W2017001003\",\"W2017001004\",\"W2017001005\",\"W2017001006\",\"W2017001007\",\"W2017001008\",\"W2017001009\",\"W2017001010\",\"W2017001011\",\"W2017001012\",\"W2017001013\",\"W2017001014\",\"W2017001015\",\"W2017001016\",\"W2017001017\",\"W2017001018\",\"W2017001019\",\"W2017001020\",\"W2017001021\",\"W2017001022\",\"W2017001023\",\"W2017001024\",\"W2017001025\",\"W2017001026\",\"W2017001027\",\"W2017001028\",\"W2017001029\",\"W2017001030\",\"W2017001031\",\"W2017001032\",\"W2017001033\",\"W2017001034\",\"W2017001035\",\"W2017001036\",\"W2017001037\",\"W2017001038\",\"W2017001039\",\"W2017001040\",\"W2017001041\",\"W2017001042\",\"W2017001043\",\"W2017001044\",\"W2017001045\",\"W2017001046\",\"W2017001047\",\"W2017001048\",\"W2017001049\",\"W2017001050\",\"W2017001051\",\"W2017001052\",\"W2017001053\",\"W2017001054\",\"W2017001055\",\"W2017001056\",\"W2017001057\",\"W2017001058\",\"W2017001059\",\"W2017001060\",\"W2017001061\",\"W2017001062\",\"W2017001063\",\"W2017001064\",\"W2017001065\",\"W2017001066\",\"W2017001067\",\"W2017001068\",\"W2017001069\",\"W2017001070\",\"W2017001071\",\"W2017001072\",\"W2017001073\",\"W2017001074\",\"W2017001075\",\"W2017001076\",\"W2017001077\",\"W2017001078\",\"W2017001079\",\"W2017001080\",\"W2017001081\",\"W2017001082\",\"W2017001083\",\"W2017001084\",\"W2017001085\",\"W2017001086\",\"W2017001087\",\"W2017001088\",\"W2017001089\",\"W2017001090\",\"W2017001091\",\"W2017001092\",\"W2017001093\",\"W2017001094\",\"W2017001095\",\"W2017001096\",\"W2017001097\",\"W2017001098\",\"W2017001099\",\"W2017001100\",\"W2017001101\",\"W2017001102\",\"W2017001103\",\"W2017001104\",\"W2019000027\",\"W2019000028\",\"W2019000029\",\"W2019000031\",\"W2019000032\",\"W2019000033\",\"W2019000034\",\"W2019000035\",\"W2019000036\",\"W2019000056\",\"W2019000057\",\"W2019000058\",\"W2019000059\",\"W2019000060\",\"W2019000061\",\"X2013004903\",\"X2014005451\",\"X2014005680\"],\"category16\":\"\",\"approveTime\":1728887266,\"approveBy\":\"张华\",\"category6\":\"\",\"orderDetailNo\":\"1838475235892883458\"}\n";
		String encrpted = encrty(json, secret, aeskey, issueId);
		System.out.println("加密后----------" + encrpted);

		String token ="AAABkpm5aW8CAAAAAAABhqo%3D.x7xXBI%2BxIaf5bqlg7Jp4%2FIHPHZFyIIq2fomQ6tl0tcoONMeL6vgz51bfVczH16x8eoT6qa7lKcIT1AcD2n%2FsPIf9Shpd1l2tR8Sj2yNhOiazk2%2FH1sladWbE7tqSofSkw2%2FpvxE8sYLDVfoMmnN24L8AcdNEXa1rbA6yUVZe90Q%3D.lt%2Fs4QstO5SuPH7qetPeK62BrDLgEoynrxn157uBysI%3D";
		String result = dencrty(encrpted, secret, aeskey, issueId);

		System.out.println("解密后----------" + result);
	}

}
