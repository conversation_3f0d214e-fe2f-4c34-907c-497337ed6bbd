package cn.iocoder.yudao.module.mall.product.dal.mysql.category;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategoryDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ConfigProductCategoryMapper extends BaseMapperX<ConfigProductCategoryDO> {

    default ConfigProductCategoryDO selectByCategoryId(Long categoryId) {
        return selectOne(ConfigProductCategoryDO::getCategoryId, categoryId);
    }
}
