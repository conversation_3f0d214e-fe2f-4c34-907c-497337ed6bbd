package cn.iocoder.yudao.module.mall.product.convert.seocard;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo.ProductSeoCardCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo.ProductSeoCardRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo.ProductSeoCardUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.seocard.vo.AppProductSeoCardRespVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.seocard.ProductSeoCardDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 运营区域 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSeoCardConvert {

    ProductSeoCardConvert INSTANCE = Mappers.getMapper(ProductSeoCardConvert.class);

    ProductSeoCardDO convert(ProductSeoCardCreateReqVO bean);

    ProductSeoCardDO convert(ProductSeoCardUpdateReqVO bean);

    ProductSeoCardRespVO convert(ProductSeoCardDO bean);

    AppProductSeoCardRespVO convert02(ProductSeoCardDO bean);

    List<ProductSeoCardRespVO> convertList(List<ProductSeoCardDO> list);

    List<AppProductSeoCardRespVO> convertList02(List<ProductSeoCardDO> list);

    PageResult<ProductSeoCardRespVO> convertPage(PageResult<ProductSeoCardDO> page);

}
