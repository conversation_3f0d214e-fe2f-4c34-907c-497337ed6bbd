package cn.iocoder.yudao.module.mall.product.controller.app.vopgoods;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppSkuDetailInfo;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.params.GoodsStockInfoReq;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.params.VopStockByIdGoodsReq;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.sku.ProductSkuConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsCategoryService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.req.*;
import com.jd.open.api.sdk.domain.vopdd.QueryPromiseOpenProvider.response.predictSkuPromise.PredictSkuPromiseOpenResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.checkSkuSaleList.CheckSkuSaleGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getCanCodList.GetCanCodGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getGiftInfoSkuList.GetSkuGiftGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSellPrice.GetSellPriceGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSimilarSkuList.GetSimilarSkuGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.GetSkuPoolInfoGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.OpenRpcResult;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuImageList.GetSkuImageGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuStateList.GetSkuStateGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getYanbaoSkuList.GetYanbaoSkuGoodsResp;
import com.jd.open.api.sdk.response.vopdd.VopOrderPredictSkuPromiseResponse;
import com.jd.open.api.sdk.response.vopsp.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * VOP商品相关接口
 * @Description
 * <AUTHOR>
 * @Date 2023/6/9 17:08
 */

@RestController
@RequestMapping("/product/vopgoods")
@Slf4j
@Tag(name = "VOP商品相关接口")
public class AppProductVopGoodsController {

    @Resource
    private VopGoodsService vopGoodsService;
    @Resource
    private VopGoodsBridgeService vopGoodsBridgeService;
    @Resource
    private VopGoodsCategoryService vopGoodsCategoryService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private ProductSkuService productSkuService;

    @Value("${jd.image-path:https://img13.360buyimg.com/n12/}")
    private String jdImagePath;

    /**
     * 查询一级分类数据
     * @param parentCategoryId
     * @return
     */
    @GetMapping("getChildCategoryList")
    @Operation(summary = "查询一级分类数据")
    public CommonResult<List<VopGoodsCategoryItem>> getChildCategoryList(@RequestParam(value = "parentCategoryId", required = false) Long parentCategoryId) {
        return CommonResult.success(vopGoodsCategoryService.getAllLevelOneCategoryList(parentCategoryId));
    }

    /**
     * 查询所有的子类目数据
     * @param parentCategoryId
     * @return
     */
    @GetMapping("getUnderChildCategoryList")
    @Operation(summary = "查询所有的子类目数据")
    public CommonResult<List<VopGoodsCategoryItem>> getUnderChildCategoryList(@RequestParam(value = "parentCategoryId", required = false) Long parentCategoryId) {
        return CommonResult.success(vopGoodsCategoryService.getChildCategoryList(parentCategoryId));
    }

    /**
     * 根据分类id查询分类信息
     * @param categoryIds
     * @return
     */
    @GetMapping("getCategoryInfoList")
    @Operation(summary = "根据分类id查询分类信息")
    public CommonResult<List<com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getCategoryInfoList.GetCategoryInfoGoodsResp>> getCategoryInfoList(@RequestParam(value = "categoryIds", required = false) String categoryIds) {
        VopGoodsGetCategoryInfoListResponse response = vopGoodsService.getCategoryInfoList(categoryIds);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 搜索商品
     *
     * @param searchReq
     * @return
     */
    @GetMapping("goodsSearchPageList")
    @Operation(summary = "搜索商品")
    public CommonResult<VopGoodsSearchPageResultResp> goodsSearchPageList(VopGoodsSearchReq searchReq) {
        VopGoodsSearchPageResultResp resp = vopGoodsBridgeService.goodsSearchPageList(searchReq);
        return CommonResult.success(resp);
    }

    /**
     * 搜索商品
     *
     * @param searchReq
     * @return
     */
    @GetMapping("goodsSearchSimpleList")
    @Operation(summary = "搜索商品")
    public CommonResult<List<VopSkuGoodsPageItem>> goodsSearchSimpleList(VopGoodsSearchReq searchReq) {
        List<VopSkuGoodsPageItem> pageItems = vopGoodsBridgeService.goodsSearchSimpleList(searchReq);
        return CommonResult.success(pageItems);
    }

    /**
     * 查询单个商品的详细信息，包含商品售卖单位、重量、产地、包装清单、主图、规格参数等
     * @param skuDetailReq
     * @return
     */
    @GetMapping("getSkuDetailInfo")
    @Operation(summary = "查询单个商品的详细信息，包含商品售卖单位、重量、产地、包装清单、主图、规格参数等")
    public CommonResult<AppSkuDetailInfo> getSkuDetailInfo(VopGoodsGetSkuDetailReq skuDetailReq) {
        ProductSkuDO skuDO = productSkuService.getSimpleSkuById(skuDetailReq.getSkuId());
        Assert.notNull(skuDO, "商品sku不存在");
        OpenRpcResult openRpcResult = vopGoodsService.getSkuDetailInfo(skuDetailReq).getOpenRpcResult();
        Assert.isTrue(openRpcResult.getSuccess(), String.format("商品sku：%d不存在", skuDetailReq.getSkuId()));
        GetSkuPoolInfoGoodsResp getSkuPoolInfoGoodsResp = openRpcResult.getResult();
        AppSkuDetailInfo skuDetailInfo = ProductSkuConvert.INSTANCE.convertSkuDetailResp(getSkuPoolInfoGoodsResp);
        skuDetailInfo.setSupplierId(skuDO.getSupplierId());
        return CommonResult.success(skuDetailInfo);
    }

    /**
     * 查询单个商品的预计送达时间
     * @param predictSkuPromiseReq
     * @return
     */
    @GetMapping("getSkuPredictPromise")
    @Operation(summary = "查询单个商品的预计送达时间")
    public CommonResult<PredictSkuPromiseOpenResp> getSkuPredictPromise(VopOrderPredictSkuPromiseReq predictSkuPromiseReq) {
        SupplierDO supplier = supplierService.getSupplier(predictSkuPromiseReq.getSupplierId());
        if (supplier != null && supplier.isJd()) {
            VopOrderPredictSkuPromiseResponse response = vopGoodsService.predictSkuPromise(predictSkuPromiseReq);
            return CommonResult.success(response.getOpenRpcResult().getResult());
        }
        return CommonResult.success(null);
    }

    /**
     * 查询商品上架、详情图片、可售、权益等
     *
     * @param skuId
     * @return
     */
    @GetMapping("getAssociationSkuDetailInfo")
    @Operation(summary = "查询商品上架、详情图片、可售、权益等")
    public CommonResult<VopGoodsDetailResp> getAssociationSkuDetailInfo(@RequestParam(value = "skuId", required = true) Long skuId) {
        return CommonResult.success(vopGoodsBridgeService.getAssociationSkuDetailInfo(skuId));
    }

    /**
     * 查询某个商品的全地址库存
     * @param stockInfoReq
     * @return
     */
    @GetMapping("queryGoodsStockInfo")
    @Operation(summary = "查询商品库存状态等")
    public CommonResult<AreaStockInfoResp> queryGoodsStockInfo(@Valid GoodsStockInfoReq stockInfoReq) {
        return CommonResult.success(vopGoodsBridgeService.queryGoodsStockInfo(stockInfoReq));
    }

    /**
     * 批量查询商品库存状态
     * @param vopStockByIdGoodsReq
     * @return
     */
    @PostMapping("getNewStockById")
    @Operation(summary = "批量查询商品库存状态")
    public CommonResult<List<AreaStockInfoResp>> getNewStockById(@Valid @RequestBody VopStockByIdGoodsReq vopStockByIdGoodsReq) {
        return CommonResult.success(vopGoodsBridgeService.getNewStockById(vopStockByIdGoodsReq));
    }

    /**
     * 查询商品图片，包含商品主图、图片路径等
     * @param skuId
     * @return
     */
    @GetMapping("getSkuImageList")
    @Operation(summary = "查询商品图片，包含商品主图、图片路径等")
    public CommonResult<List<GetSkuImageGoodsResp>> getSkuImageList(@RequestParam(value = "skuId", required = true) String skuId) {
        VopGoodsGetSkuImageListResponse response = vopGoodsService.getSkuImageList(skuId);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 验证商品可售性，同时可查询商品是否可开专票、无理由退货类型等
     * @param skuId
     * @return
     */
    @GetMapping("checkSkuSaleList")
    @Operation(summary = "验证商品可售性，同时可查询商品是否可开专票、无理由退货类型等")
    public CommonResult<List<CheckSkuSaleGoodsResp>> checkSkuSaleList(@RequestParam(value = "skuId", required = true) String skuId) {
        VopGoodsCheckSkuSaleListResponse response = vopGoodsService.checkSkuSaleList(skuId);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 查询商品上下架状态
     * @param skuId
     * @return
     */
    @GetMapping("getSkuStateList")
    @Operation(summary = "查询商品上下架状态")
    public CommonResult<List<GetSkuStateGoodsResp>> getSkuStateList(@RequestParam(value = "skuId", required = true) String skuId) {
        VopGoodsGetSkuStateListResponse response = vopGoodsService.getSkuStateList(skuId);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 批量查询商品售卖价
     * @param skuId
     * @return
     */
    @GetMapping("getSellPrice")
    @Operation(summary = "批量查询商品售卖价")
    public CommonResult<List<GetSellPriceGoodsResp>> getSellPrice(@RequestParam(value = "skuId", required = true) String skuId) {
        VopGoodsGetSellPriceResponse response = vopGoodsService.getSellPrice(skuId, null);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 查询相似商品，例如统一款商品的不同颜色
     * @param skuId
     * @return
     */
    @GetMapping("getSimilarSkuList")
    @Operation(summary = "查询相似商品，例如统一款商品的不同颜色")
    public CommonResult<List<GetSimilarSkuGoodsResp>> getSimilarSkuList(@RequestParam(value = "skuId", required = true) Long skuId) {
        VopGoodsGetSimilarSkuListResponse response = vopGoodsService.getSimilarSkuList(skuId);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 查询赠品信息，此内容与企业享受权益相关
     * @param goodsGetGiftInfoSkuListReq
     * @return
     */
    @GetMapping("getGiftInfoSkuList")
    @Operation(summary = "查询赠品信息，此内容与企业享受权益相关")
    public CommonResult<List<GetSkuGiftGoodsResp>> getGiftInfoSkuList(VopGoodsGetGiftInfoSkuListReq goodsGetGiftInfoSkuListReq) {
        VopGoodsGetGiftInfoSkuListResponse response = vopGoodsService.getGiftInfoSkuList(goodsGetGiftInfoSkuListReq);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 查询延保信息
     * @param yanbaoSkuListReq
     * @return
     */
    @GetMapping("getYanbaoSkuList")
    @Operation(summary = "查询延保信息")
    public CommonResult<List<GetYanbaoSkuGoodsResp>> getYanbaoSkuList(VopGoodsGetYanbaoSkuListReq yanbaoSkuListReq) {
        VopGoodsGetYanbaoSkuListResponse response = vopGoodsService.getYanbaoSkuList(yanbaoSkuListReq);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 根据四级地址，查询商品在此地区是否支持货到付款。可进行批量查询
     * @param canCodListReq
     * @return
     */
    @GetMapping("getCanCodList")
    @Operation(summary = "根据四级地址，查询商品在此地区是否支持货到付款。可进行批量查询")
    public CommonResult<List<GetCanCodGoodsResp>> getCanCodList(VopGoodsGetCanCodListReq canCodListReq) {
        VopGoodsGetCanCodListResponse response = vopGoodsService.getCanCodList(canCodListReq);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }
}
