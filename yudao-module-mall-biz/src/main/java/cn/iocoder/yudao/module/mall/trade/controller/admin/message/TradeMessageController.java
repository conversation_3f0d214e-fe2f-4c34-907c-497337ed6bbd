package cn.iocoder.yudao.module.mall.trade.controller.admin.message;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.trade.controller.admin.message.vo.TradeMessagePageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.message.vo.TradeMessageRespVO;
import cn.iocoder.yudao.module.mall.trade.convert.message.TradeMessageConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.message.TradeMessageDO;
import cn.iocoder.yudao.module.mall.trade.service.message.TradeMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 交易消息
 */
@Tag(name = "管理后台 - 交易订单")
@RestController
@RequestMapping("/trade/message")
@Validated
@Slf4j
public class TradeMessageController {

    @Resource
    private TradeMessageService messageService;

    /**
     * 获得交易消息分页
     * @param reqVO
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "获得交易消息分页")
    @PreAuthorize("@ss.hasPermission('trade:message:query')")
    public CommonResult<PageResult<TradeMessageRespVO>> getPage(TradeMessagePageReqVO reqVO) {
        PageResult<TradeMessageDO> pageResult = messageService.getMessagePage(reqVO);
        return success(TradeMessageConvert.INSTANCE.convertPage02(pageResult));
    }

    /**
     * 获得交易消息
     * @param id
     * @return
     */
    @GetMapping("/get")
    @Operation(summary = "获得交易消息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:message:query')")
    public CommonResult<TradeMessageRespVO> getInfo(@RequestParam("id") Long id) {
        TradeMessageDO tradeMessageDO = messageService.getMessage(id);
        return success(TradeMessageConvert.INSTANCE.convert02(tradeMessageDO));
    }

}
