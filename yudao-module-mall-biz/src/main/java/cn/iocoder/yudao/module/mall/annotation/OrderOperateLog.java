package cn.iocoder.yudao.module.mall.annotation;

import cn.iocoder.yudao.module.mall.trade.enums.order.OrderOperateTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2024/1/30
 */

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface OrderOperateLog {

    String content() default "";

    OrderOperateTypeEnum type();

    String orderId() default "";

    String orderNo() default "";

    String userName() default "";
}

