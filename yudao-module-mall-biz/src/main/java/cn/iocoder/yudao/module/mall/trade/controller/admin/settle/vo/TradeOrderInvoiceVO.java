package cn.iocoder.yudao.module.mall.trade.controller.admin.settle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 订单发票相关响应VO")
@Data
public class TradeOrderInvoiceVO {

    /**
     * ID
     */
    private Long id;

    private Long orderId;

    private String invoiceDate;
    private String markId;
    private Integer invoiceOrg;


    private BigDecimal totalBatchInvoiceAmount;
    private BigDecimal invoicePrice;

    private String billToContact;
    private String title;
    private Integer currentBatch;
    private String enterpriseTaxpayer;

    private Integer invoiceType;
    private Integer bizInvoiceContent;
    private String supplierOrder;
    private Integer totalBatch;

    private String settlementId;
    private Integer invoiceNum;


    /**
     * 开票返回结果
     */
    private String invoiceId;
    private String fileUrl;
    private String invoiceCode;

    // 发票状态： 0 未开票 1 已申请 2 开票完成
    private Integer invoiceStatus = 0;
    private String invoiceStatusName;

    /**
     * 凭证文件名称
     */
    private String voucherName;
    /**
     * 凭证上传状态
     */
    private Boolean voucherUploadStatus;
    /**
     * 发票是否验真
     */
    private Boolean invoiceCheckStatus;
    /**
     * 发票验真次数
     */
    private Integer invoiceCheckCount;

}
