package cn.iocoder.yudao.module.mall.product.controller.app.sku.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.mall.product.controller.app.spec.vo.AppProductSpecValueVO;
import cn.iocoder.yudao.module.mall.product.convert.es.ProductSkuESConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.ProductSkuSpecES;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSkuShowStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 商品详情
 *
 * <AUTHOR>
 * @date 2023/12/8
 */
@Data
public class AppProductSkuDetailRespVO {

    /**
     * 商品skuId
     */
    private Long skuId;

    /**
     * 商品供应商id
     */
    private String skuInnerId;


    /**
     * 商品skuName
     */
    private String skuName;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 商品实际销量
     */
    private Integer salesCount;
    /**
     * 商品初始销量
     */
    private Integer initSalesCount;

    /**
     * 最低购买量
     */
    private Integer lowestBuy;

    /**
     * 图片地址
     */
    private String skuPicUrl;

    /**
     * 状态：  {@link cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum}
     */
    private Integer status;

    /**
     * 商品显示状态  {@link ProductSkuShowStatusEnum}
     */
    private Integer showStatus;

    /**
     * 平台上下架状态：  {@link cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum}
     */
    private Integer platformStatus;

    /**
     * sku 属性
     */
    private List<AppProductSpecValueVO> skuSpecValueList;


//    #########################spu信息#########################

    /**
     * 商品 SPU 编号，自增
     */
    private Long spuId;

    /**
     * 供应商内部商品spuId
     */
    private String spuInnerId;

    /**
     * 商品品牌编号
     */
    private Long brandId;

    /**
     * 商品品牌名称
     */
    private String brandName;


    /**
     * 规格类型：0 单规格 1 多规格
     */
    private Integer specType;

    /**
     * 商品名称
     */
    private String spuName;

    /**
     * 卖点
     */
    private String sellPoint;

    /**
     * spu单位
     */
    private String unit;

    /**
     * 描述
     */
    private String description;

    /**
     * 商品封面图片
     */
    private String spuPicUrl;

    /**
     * 商品轮播图片
     */
    private List<String> sliderPicUrls;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 一级分类id
     */
    private Long category1Id;

    /**
     * 二级分类id
     */
    private Long category2Id;

    /**
     * 三级分类id
     */
    private Long category3Id;

    /**
     * 一级分类名称
     */
    private String category1Name;

    /**
     * 二级分类名称
     */
    private String category2Name;

    /**
     * 三级分类名称
     */
    private String category3Name;

    /**
     * 分类路径ID
     */
    private String fullCategoryId;

    /**
     * 分类路径名称
     */
    private String fullCategoryName;

    /**
     * spu 属性
     */
    private List<AppProductSpecValueVO> spuSpecValueList;


    // ###########供应商和租户信息

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商类型
     */
    private Integer supplierType;


    private String logoUrl;

    private Long tenantId;

    private Integer skuState;

    /**
     * 售卖状态
     */
    @Schema(description = "售卖状态")
    private Integer saleStatus;


    public Integer getSkuState() {
        if(this.skuState == null) {
            return this.status;
        }
        return skuState;
    }

    public Integer getSaleStatus() {
        if(this.saleStatus == null) {
            return this.status;
        }
        return saleStatus;
    }

    public boolean isNotOnSale() {
        return ObjectUtil.equal(saleStatus, 0) || ObjectUtil.equal(status, 0);
    }

    public List<String> getSpecValues() {
        if (CollUtil.isEmpty(skuSpecValueList) && CollUtil.isEmpty(spuSpecValueList)) {
            return null;
        }
        return Stream.of(skuSpecValueList, spuSpecValueList)
                .filter(CollUtil::isNotEmpty)
                .flatMap(List::stream)
                .map(AppProductSpecValueVO::getSpecValue)
                .collect(Collectors.toList());
    }

    public List<ProductSkuSpecES> getSpuSpecs() {
        if(CollUtil.isEmpty(spuSpecValueList)) {
            return Collections.singletonList(
                    new ProductSkuSpecES().setSpecName("空").setSpecValue("空"));
        }
        return ProductSkuESConvert.INSTANCE.convertList02(spuSpecValueList);
    }

}
