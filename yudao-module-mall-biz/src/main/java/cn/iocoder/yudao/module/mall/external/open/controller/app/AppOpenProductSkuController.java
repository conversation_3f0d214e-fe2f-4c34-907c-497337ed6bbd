package cn.iocoder.yudao.module.mall.external.open.controller.app;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import cn.iocoder.yudao.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import cn.iocoder.yudao.module.mall.external.open.annotation.OpenAuth;
import cn.iocoder.yudao.module.mall.external.open.context.OpenContextHolder;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.common.AppOpenIdReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenSkuBlackAreaQueryReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenSkuStockUpdateReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenSkuUpdatePriceReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenSkuUpdateStatusReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppAppProductSkuOpenRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo.ProductSkuBlackAreaReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo.ProductSkuBlackAreaRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuBaseRespOpenVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuOpenVO;
import cn.iocoder.yudao.module.mall.product.convert.skublackarea.ProductSkuBlackAreaConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skublackarea.ProductSkuBlackAreaDO;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.skublackarea.ProductSkuBlackAreaService;
import cn.iocoder.yudao.module.mall.product.service.skustock.ProductSkuStockService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.OPEN_APP_BATCH_PRODUCT_OVERLOAD;

/**
 * 开放API - 商品"
 *
 * <AUTHOR>
 * @date 2023/9/12
 */
@Tag(name = "开放API - 商品")
@RestController
@RequestMapping("/mall/open/product")
@Validated
@Slf4j
@OpenAuth
public class AppOpenProductSkuController {

    @Resource
    private ProductSkuBlackAreaService productSkuBlackAreaService;

    @Resource
    private ProductSkuService productSkuService;

    /**
     * 添加商品
     *
     * @param reqVOs
     * @return
     */
    @RateLimiter(count = 30, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class, openSupplier = true)
    @PostMapping("/addProductSku")
    public CommonResult<List<ProductSpuBaseRespOpenVO>> addProductSku(@Valid @RequestBody List<ProductSpuOpenVO> reqVOs) {
        Integer maxSize = 5;
        if(reqVOs != null && reqVOs.size() > maxSize) {
            throw exception(OPEN_APP_BATCH_PRODUCT_OVERLOAD, maxSize);
        }
        Long supplierId = OpenContextHolder.getSupplierId();
        return success(productSkuService.addProductSku(reqVOs, supplierId));
    }

    @Resource
    private ProductSkuStockService productSkuStockService;

    /**
     * 查询商品详情
     * @param appOpenIdReqVO
     * @return
     */
    @PostMapping("/getProductSku")
    @Operation(summary = "查询商品详情")
    public CommonResult<AppAppProductSkuOpenRespVO> getProductSku(@Valid @RequestBody AppOpenIdReqVO appOpenIdReqVO) {
        Long supplierId = OpenContextHolder.getSupplierId();
        AppAppProductSkuOpenRespVO productSkuDetail = productSkuService.getProductSkuDetail(appOpenIdReqVO.getId(), supplierId);
        return success(productSkuDetail);
    }

    /**
     * 设置商品上下架
     *
     * @param reqVOs
     * @return
     */
    @PostMapping("/setProductSkuStatus")
    @Operation(summary = "设置商品上下架")
    public CommonResult<?> setProductSkuStatus(@Valid @RequestBody List<AppOpenSkuUpdateStatusReqVO> reqVOs) {
        Long supplierId = OpenContextHolder.getSupplierId();
        productSkuService.setProductSkuStatus02(reqVOs, supplierId);
        return success(null);
    }

    /**
     * 修改商品价格
     * @param reqVOs
     * @return
     */
    @PostMapping("/updateProductSkuPrice")
    @Operation(summary = "修改商品价格")
    public CommonResult<?> updateProductSkuPrice(@Valid @RequestBody List<AppOpenSkuUpdatePriceReqVO> reqVOs) {
        Long supplierId = OpenContextHolder.getSupplierId();
        productSkuService.updateProductSkuPrice02(reqVOs, supplierId);
        return success(null);
    }

    /**
     * 修改商品库存
     * @param reqVOs
     * @return
     */
    @RateLimiter(count = 20, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class, openSupplier = true)
    @PostMapping("/updateProductSkuStock")
    @Operation(summary = "修改商品库存")
    public CommonResult<?> updateProductSkuStock(@Valid @RequestBody List<AppOpenSkuStockUpdateReqVO> reqVOs) {
        Long supplierId = OpenContextHolder.getSupplierId();
        productSkuStockService.batchUpdateSkuStock2(reqVOs, supplierId);
        return success(null);
    }

    /**
     * 设置商品禁售区域
     *
     * @param reqVOs
     * @return
     */
    @PostMapping("/setProductSkuBlackArea")
    @Operation(summary = "设置商品禁售区域")
    public CommonResult<?> setProductSkuBlackArea(@Valid @RequestBody List<ProductSkuBlackAreaReqVO> reqVOs) {
        Long supplierId = OpenContextHolder.getSupplierId();
        productSkuBlackAreaService.setProductSkuBlackArea(reqVOs, supplierId);
        return success(null);
    }

    /**
     * 查询商品禁售区域
     *
     * @param reqVO
     * @return
     */
    @PostMapping("/getProductSkuBlackArea")
    @Operation(summary = "查询商品禁售区域")
    public CommonResult<List<ProductSkuBlackAreaRespVO>> getProductSkuBlackArea(@Valid @RequestBody AppOpenSkuBlackAreaQueryReqVO reqVO) {
        Long supplierId = OpenContextHolder.getSupplierId();
        List<ProductSkuBlackAreaDO> productSkuBlackArea = productSkuBlackAreaService.getProductSkuBlackArea(reqVO.getSkuIds(), supplierId);
        List<ProductSkuBlackAreaRespVO> productSkuBlackAreaRespVOS = ProductSkuBlackAreaConvert.INSTANCE.convertList(productSkuBlackArea);
        return success(productSkuBlackAreaRespVOS);
    }

}
