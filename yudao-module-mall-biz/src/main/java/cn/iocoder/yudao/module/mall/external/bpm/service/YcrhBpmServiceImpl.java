package cn.iocoder.yudao.module.mall.external.bpm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.object.ObjectUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.infra.api.file.dto.FileRespDTO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.YcrhConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.common.apifail.ApiFailService;
import cn.iocoder.yudao.module.mall.basis.service.integration.YcrhConfigService;
import cn.iocoder.yudao.module.mall.enums.basis.ApiFailMethodEnum;
import cn.iocoder.yudao.module.mall.enums.external.enums.BpmAuditStatusEnum;
import cn.iocoder.yudao.module.mall.enums.external.enums.YcrhBpmCallBackMethodEnum;
import cn.iocoder.yudao.module.mall.external.bpm.BpmClientConfig;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmAuditResultDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmCallbackResultDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmReqDTO;
import cn.iocoder.yudao.module.mall.external.bpm.enums.BpmClientTypeEnum;
import cn.iocoder.yudao.module.mall.external.ycrh.YcrhBpmClient;
import cn.iocoder.yudao.module.mall.external.ycrh.YcrhClient;
import cn.iocoder.yudao.module.mall.external.ycrh.dto.*;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.ApprovalResultReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseValidateReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业财融合审批流实现
 * <AUTHOR>
 * @date 2024/08/22
 */
@Service
@Slf4j
public class YcrhBpmServiceImpl implements BpmService {

    @Resource
    @Lazy
    private TradeOrderService tradeOrderService;
    @Resource
    private YcrhConfigService ycrhConfigService;
    @Resource
    private YcrhBpmClient ycrhBpmClient;
    @Resource
    private BpmClientConfig bpmClientConfig;
    @Resource
    private YcrhClient ycrhClient;
    @Resource
    private FileApi fileApi;
    @Resource
    private ApiFailService apiFailService;

    @Override
    public String getClientType() {
        return BpmClientTypeEnum.YCRH.getType();
    }

    @Override
    public void validateBeforePush(PurchaseValidateReqVO reqVO) {

    }

    @Override
    public String pushBpmInfo(BpmReqDTO bpmReqDTO) {
        PurchaseDO purchaseDO = bpmReqDTO.getPurchaseDO();
        List<Long> orderIds = bpmReqDTO.getOrderIds();
        Assert.notEmpty(bpmReqDTO.getApprovalUserInfos(), "审批人列表不能为空");
        String userNo = SecurityFrameworkUtils.getLoginUser().getUserNo();
        YgInfoRespDTO ygInfo = ycrhClient.getYgInfo(userNo);
        Assert.notNull(ygInfo, "员工id不存在");

        YcrhConfigDO ycrhConfigDO = ycrhConfigService.getYcrhConfig();
        String bpmNo = purchaseDO.getBpmNo();
        LocalDate now = LocalDate.now();
        String applicantDate = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String applicantTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<TradeOrderItemDO> orderItems = tradeOrderService.getOrderItemListByOrderId(orderIds);
        orderItems = orderItems.stream().filter(item -> item.getSkuPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        Assert.notEmpty(orderItems, "订单商品不能为空");

        ApprovalTitleDTO approvalTitleDTO = new ApprovalTitleDTO()
                .setTitle("采购申请")
                .setApprovalNo(bpmNo)
                .setAmount(purchaseDO.getTotalAmount())
                .setRemark("采购平台订单");
        List<ApprovalContentDTO.OrderItemInfo> orderItemInfos = orderItems.stream().map(orderItem -> new ApprovalContentDTO.OrderItemInfo()
            .setSkuName(orderItem.getSkuName())
            .setCount(convert2String(orderItem.getCount()))
            .setPrice(convert2String(orderItem.getSkuPrice()))
            .setTotalPrice(convert2String(orderItem.getSkuTotalPrice())))
            .collect(Collectors.toList());
        String economyClass = "";
        if(StrUtil.isNotBlank(purchaseDO.getEconomicCode())) {
            if(StrUtil.isNotBlank(purchaseDO.getEconomicName())) {
                economyClass = String.format("%s(%s)", purchaseDO.getEconomicName(), purchaseDO.getEconomicCode());
            } else {
                economyClass = String.format("%s", purchaseDO.getEconomicCode());
            }
        }
        ApprovalContentDTO.ApprovalInfoDTO approvalInfoDTO = new ApprovalContentDTO.ApprovalInfoDTO()
                .setApplicantName(ygInfo.getYgName())
                .setPurchaseId(bpmNo)
                .setApplyReason(purchaseDO.getPurchaseReason())
                .setProjectName(purchaseDO.getProjectName())
                .setProjectType(purchaseDO.getProjectType())
                .setProjectTypeName(purchaseDO.getProjectTypeName())
                .setDeptNo(purchaseDO.getProjectDepartmentNo())
                .setProjectChargeName(purchaseDO.getProjectChargeName())
                .setProjectNo(purchaseDO.getProjectNo())
                .setEconomyCategory(economyClass)
                .setAmount(convert2String(purchaseDO.getTotalAmount()))
                .setRemark("采购平台订单");
        ApprovalContentDTO approvalContentDTO = new ApprovalContentDTO()
                .setOrders(orderItemInfos)
                .setApprovalInfos(Arrays.asList(approvalInfoDTO));
        VoucherDTO voucherDTO = new VoucherDTO()
                .setModuleName("YC")
                .setBusinessCode("CG")
                .setBusinessNo(bpmNo)
                .setApplicantName(ygInfo.getYgName())
                .setApplicantNo(ygInfo.getYgNo())
                .setApplicantDate(applicantDate)
                .setApplicantTime(applicantTime)
                .setContentOverview((JSONObject) JSONObject.toJSON(approvalContentDTO))
                .setApprovalLevel("1")
                .setApprovalStatus("0")
                .setApprovalTitle((JSONObject) JSONObject.toJSON(approvalTitleDTO))
                .setNotifyUrl(bpmClientConfig.buildVoucherNotifyUrl());
        List<ApprovalInfoDTO> approvalInfoDTOS = bpmReqDTO.getApprovalUserInfos().stream().map(approvalUserInfo -> new ApprovalInfoDTO()
            .setApprovalLevel(approvalUserInfo.getApprovalLevel())
            .setApprovalUserName(approvalUserInfo.getApprovalUserName())
            .setApprovalUserNo(approvalUserInfo.getApprovalUserNo())
            .setBusinessNo(bpmNo)
            .setIsUpdateApproval(StringUtils.defaultIfBlank(approvalUserInfo.getExtEditProcess(), "1"))
            .setIsDeleteApprover(StringUtils.defaultIfBlank(approvalUserInfo.getExtDeleteApproval(), "0"))
            .setIsDesignatedApprover(StringUtils.defaultIfBlank(approvalUserInfo.getExtAssignApproval(), "0"))
            .setIsSendMessage(StringUtils.defaultIfBlank(approvalUserInfo.getExtSendSms(), "1"))
            .setIsSignature(StringUtils.defaultIfBlank(approvalUserInfo.getExtSeal(), ycrhConfigDO.getParamBpmSfqz2()))
            .setIsSign(StringUtils.defaultIfBlank(approvalUserInfo.getExtSign(), "0"))
            .setApplicantName(ygInfo.getYgName())
            .setApplicantNo(ygInfo.getYgNo())
            .setApprovalStatus("0")
            .setIsCountersign("0")
            .setApprovalRoleName(approvalUserInfo.getApprovalRoleName()))
            .collect(Collectors.toList());

        UploadVoucherDTO reqDTO = new UploadVoucherDTO().setVoucher(voucherDTO).setApprovalInfos(approvalInfoDTOS);
        //上传单据
        ycrhBpmClient.uploadVoucher(reqDTO);
        //上传附件
        uploadAttachments(purchaseDO, true);

        return bpmNo;
    }

    @Override
    public BpmCallbackResultDTO handleBpmCallback(String body, Map<String, String> params) {
        ApprovalResultReqVO paramObject = JsonUtils.parseObject(body, ApprovalResultReqVO.class);
        String bpmNo = paramObject.getBusinessNo();
        JSONObject resultObject = new JSONObject();
        resultObject.put("res", "1");
        resultObject.put("data", bpmNo);

        BpmAuditStatusEnum auditStatus = BpmAuditStatusEnum.PENDING;
        if (YcrhBpmCallBackMethodEnum.AUDIT.getCode().equals(paramObject.getMethod())) {
            if (ObjectUtil.equal(paramObject.getAuditStatus(), BpmAuditStatusEnum.PASS.getCode())) {
                auditStatus = BpmAuditStatusEnum.PASS;
            } else if (ObjectUtil.equal(paramObject.getAuditStatus(), BpmAuditStatusEnum.REJECT.getCode())) {
                auditStatus = BpmAuditStatusEnum.REJECT;
            }

            if(ObjectUtils.equalsAny(paramObject.getAuditStatus(), BpmAuditStatusEnum.PASS.getCode(), BpmAuditStatusEnum.REJECT.getCode())) {
                resultObject.put("completeApproval", "1");
                completeBpm(bpmNo);
            }
        }

        return new BpmCallbackResultDTO()
                .setAuditStatus(auditStatus)
                .setBpmNo(bpmNo)
                .setResult(resultObject);
    }

    @Override
    public BpmAuditResultDTO pullBpmStatus(String bpmNo) {
        List<ApprovalResultDTO> approvalResults = fetchPurchaseAuditResult(bpmNo);
        BpmAuditStatusEnum finalAuditStatus = fetchFinalAuditStatus(bpmNo);

        return new BpmAuditResultDTO()
                .setAuditStatus(finalAuditStatus)
                .setAuditResult(JSON.toJSONString(approvalResults));
    }

    @Override
    public void cancelBpmInfo(String bpmNo, String reason, Long userId) {
        BpmAuditStatusEnum finalAuditStatus = fetchFinalAuditStatus(bpmNo);
        if(!finalAuditStatus.equals(BpmAuditStatusEnum.CANCEL)){
            DeleteVoucherDTO reqDTO2 = new DeleteVoucherDTO()
                    .setModuleName("YC")
                    .setBusinessNo(bpmNo);
            boolean result = ycrhBpmClient.deleteVoucher(reqDTO2);
            log.info("业财取消审批流单据{}调用结果: {}", bpmNo, result);
        } else {
            log.info("业财审批已被删除或不存在: {}", bpmNo);
        }
    }

    /**
     * 获取采购申请单的最终审批状态
     * @param bpmNo
     * @return
     */
    private BpmAuditStatusEnum fetchFinalAuditStatus(String bpmNo) {
        GetVoucherDTO reqDTO = new GetVoucherDTO();
        reqDTO.setBusinessNo(bpmNo);
        YcrhResp ycrhResp = ycrhBpmClient.getFinalVoucher(reqDTO);
        if(ObjectUtil.equal(ycrhResp.getResultCode(), "1")) {
            return BpmAuditStatusEnum.PASS;
        } else if(ObjectUtil.equal(ycrhResp.getResultCode(), "2")) {
            return BpmAuditStatusEnum.REJECT;
        } else if(ObjectUtil.equal(ycrhResp.getResultCode(), "0")) {
            return BpmAuditStatusEnum.PENDING;
        } else if(ObjectUtil.equal(ycrhResp.getResultCode(), "9")) {
            return BpmAuditStatusEnum.CANCEL;
        } else {
            return BpmAuditStatusEnum.PENDING;
        }
    }

    /**
     * 获取采购申请单审批详情
     * @param bpmNo
     */
    private List<ApprovalResultDTO> fetchPurchaseAuditResult(String bpmNo) {
        GetVoucherDTO getVoucherDTO = new GetVoucherDTO().setBusinessNo(bpmNo);
        List<ApprovalResultDTO> approvalResults = ycrhBpmClient.getVoucher(getVoucherDTO);
        // 清除签章图片
        if(CollUtil.isNotEmpty(approvalResults)) {
            approvalResults.forEach(item -> {
                item.setSignImage(null);
                item.setSignatureImage(null);
            });

            return approvalResults;
        }

        return null;
    }

    /**
     * 审批流单据归档处理
     * @param bpmNo
     */
    private void completeBpm(String bpmNo) {
        CompleteVoucherReqDTO reqDTO = new CompleteVoucherReqDTO();
        reqDTO.setBusinessNo(bpmNo);
        YcrhResp resp = ycrhBpmClient.completeVoucher(reqDTO);
        if(!resp.isSuccess()) {
            log.info("审批流单据{}归档处理失败", bpmNo);
        }
    }

    /**
     * 获取审批备注，取最后一个审批节点的审批意见
     * @param approvalResults
     * @return
     */
    private String getAuditMemo(List<ApprovalResultDTO> approvalResults) {
        ApprovalResultDTO approvalResultDTO = Optional.ofNullable(approvalResults)
                .orElse(Lists.newArrayList()).stream()
                .sorted(Comparator.comparing(ApprovalResultDTO::getApprovalLevel).reversed())
                .findFirst().orElse(new ApprovalResultDTO());

        return approvalResultDTO.getAuditResult();
    }

    /**
     * 上传附件
     * @param purchaseDO
     * @param retry
     */
    public boolean uploadAttachments(PurchaseDO purchaseDO, boolean retry) {
        if(StrUtil.isBlank(purchaseDO.getAttachments())) {
            return true;
        }
        List<String> attachments = StrUtil.split(purchaseDO.getAttachments(), ",");
        if(CollUtil.isNotEmpty(attachments)) {
            for(String fileUrl : attachments) {
                try {
                    CommonResult<FileRespDTO> commonResult = fileApi.getFile(fileUrl);
                    commonResult.checkError();
                    FileRespDTO fileResp = commonResult.getData();
                    ResponseEntity<byte[]> fileBytesResp = fileApi.getFileBytes(fileUrl);
                    log.info("fileLength: {}, {}", fileBytesResp.getBody().length, fileResp.getSize());
                    String attachmentBase64 = Base64.encodeBase64String(fileBytesResp.getBody());
                    //上传附件
                    UploadAttachmentsDTO uploadAttachmentsDTO = new UploadAttachmentsDTO()
                            .setAttachments(attachmentBase64)
                            .setBusinessNo(purchaseDO.getBpmNo())
                            .setModuleName("YC")
                            .setIsImportant("0")
                            .setFileName(fileResp.getName())
                            .setBusinessCode("CG")
                            .setSerialNumber("1");
                    ycrhBpmClient.uploadAttachments(uploadAttachmentsDTO);
                } catch (Exception e) {
                    log.error("审批流上传附件失败:{}", fileUrl, e);
                    if(retry) {
                        apiFailService.create(ApiFailMethodEnum.UPLOAD_ATTACHMENTS, purchaseDO.getId(), fileUrl, e.getMessage());
                    }
                    return false;
                }
            }
        }

        return true;
    }

    private String convert2String(Object obj) {
        if(obj == null) {
            return null;
        }
        return obj.toString();
    }

}
