package cn.iocoder.yudao.module.mall.product.service.sku;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenSkuUpdatePriceReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenSkuUpdateStatusReqVO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.ProductSkuUpdateStockReqDTO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SkuCountCategorySummaryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SkuSummaryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SupplierSkuSummaryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.*;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.*;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuStockReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuBaseRespOpenVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuOpenVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.TradeOrderItemBaseReqVO;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsGetSkuDetailReq;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsBrotherSkuReq;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 商品 SKU Service 接口
 *
 * <AUTHOR>
 */
public interface ProductSkuService extends IService<ProductSkuDO> {

    /**
     * 删除商品 SKU
     *
     * @param id 编号
     */
    void deleteSku(Long id);

    /**
     * 转换成京东sku
     * @param skuId
     * @return
     */
    Long convert2JdSkuId(Long skuId);

    /**
     * 终端用户看到商品上下架状态，由本平台和第三方平台控制
     * @param skuId
     * @param thirdStatus
     * @return
     */
    Integer getUnionStatus(Long skuId, Integer thirdStatus);

    /**
     * 获得商品 SKU 信息
     *
     * @param id 编号
     * @return 商品 SKU 信息
     */
    ProductSkuDO getSku(Long id);

    /**
     * 获得商品 SKU 信息
     *
     * @param id 编号
     * @param supplierId 供应商编号
     * @return 商品 SKU 信息
     */
    ProductSkuDO getSku(Long id, Long supplierId);

    /**
     * 获得商品 SKU 信息
     *
     * @param id 编号
     * @return 商品 SKU 信息
     */
    ProductSkuRespVO getSkuDetail(Long id);

    /**
     * 获得商品 SKU 信息
     *
     * @param spuId 商品spu编号
     * @return 商品 SKU 信息
     */
    List<ProductSkuRespVO> getSkuDetailListBySpu(Long spuId);

    /**
     * 根据innerId获取商品SKU信息
     * @param innerId
     * @param supplierId
     * @return
     */
    ProductSkuDO getSimpleSkuByInnerIdAndSupplierId(String innerId, Long supplierId);

    ProductSkuDO getSimpleSkuById(Long skuId);

    /**
     * 根据innerId集合获取商品SKU信息
     * @param innerIds
     * @param supplierId
     * @return
     */
    List<ProductSkuDO> getSkuListByInnerIdAndSupplierId(List<String> innerIds, Long supplierId);

    /**
     * 获得商品 SKU 列表
     *
     * @return 商品sku列表
     */
    List<ProductSkuDO> getSkuList();

    /**
     * 获得商品 SKU 列表
     *
     * @param ids 编号
     * @return 商品sku列表
     */
    List<ProductSkuDO> getSkuList(Collection<Long> ids);

    /**
     * 获取供应商SKU ids
     * @return
     */
    List<String> getInnerIdsBySupplierId(Long supplierId);

    /**
     * 批量修改商品SKU价格
     * @param skus
     */
    void updatePrice(List<ProductSkuPriceReqVO> skus);

    /**
     * 批量调整商品SKU库存
     * @param skus
     */
    void updateStock(List<ProductSkuStockReqVO> skus);

    /**
     * 批量修改商品SEO字段
     * @param skus
     */
    void updateSeoInfo(List<ProductSkuSeoUpdateReqVO> skus);

    /**
     * 对 sku 的组合的属性等进行合法性校验
     *
     * @param list sku组合的集合
     */
    void validateSkuList(List<ProductSkuCreateOrUpdateReqVO> list, Integer specType);

    /**
     * 批量创建 SKU
     *
     * @param spu 商品 SPU
     * @param list SKU 对象集合
     */
    void createSkuList(ProductSpuDO spu, List<ProductSkuCreateOrUpdateReqVO> list);

    /**
     * 根据 SPU 编号，批量更新它的 SKU 信息
     *
     * @param spu 商品 SPU
     * @param list SKU 的集合
     */
    void updateSkuList(ProductSpuDO spu, List<ProductSkuCreateOrUpdateReqVO> list);

    /**
     * 根据 SPU 编号，批量更新它的 SKU 状态
     * @param spuId
     * @param status
     */
    void updateSkuStatusBySpu(Long spuId, Integer status);

    /**
     * 根据 SKU 编号，批量更新它的 SKU 状态
     * @param skuId
     * @param status
     */
    void updateSkuStatus(Long skuId, Integer status);

    /**
     * 根据 SPU 编号，批量更新它的 SKU 显示状态
     * @param spuId
     * @param showStatus
     */
    void updateSkuShowStatusBySpu(Long spuId, Integer showStatus);

    /**
     * 根据 SKU 编号，批量更新它的 SKU 显示状态
     * @param skuId
     * @param showStatus
     */
    void updateSkuShowStatus(Long skuId, Integer showStatus);

    /**
     * 根据 SPU 编号，批量更新它的平台上下架状态
     * @param spuId
     * @param status
     */
    void updateSkuPlatformStatusBySpu(Long spuId, Integer status);

    /**
     * 根据 SKU 编号，批量更新它的 SKU 平台上下架状态
     * @param skuId
     * @param showStatus
     */
    void updateSkuPlatformStatus(Long skuId, Integer showStatus);

    /**
     * 更新 SKU 库存（增量）
     *
     * 如果更新的库存不足，会抛出异常
     *
     * @param updateStockReqDTO 更行请求
     */
    void updateSkuStock(ProductSkuUpdateStockReqDTO updateStockReqDTO);

    /**
     * 获得商品 SKU 集合
     *
     * @param spuId spu 编号
     * @return 商品sku 集合
     */
    List<ProductSkuDO> getSkuListBySpuId(Long spuId);

    /**
     * 基于 SPU 编号和状态，获得商品 SKU 集合
     *
     * @param spuId SPU 编号
     * @param status 状态
     * @return 商品 SKU 集合
     */
    List<ProductSkuDO> getSkuListBySpuIdAndStatus(Long spuId,
                                                  @Nullable Integer status);

    /**
     * 获得 spu 对应的 SKU 集合
     *
     * @param spuIds spu 编码集合
     * @return  商品 sku 集合
     */
    List<ProductSkuDO> getSkuListBySpuId(List<Long> spuIds);

    /**
     * 通过 spuId 删除 sku 信息
     *
     * @param spuId spu 编码
     */
    void deleteSkuBySpuId(Long spuId);

    /**
     * 通过 spuId 禁用 sku 信息
     *
     * @param spuId spu 编码
     */
    void disableSkuBySpuId(Long spuId);

    /**
     * 获得库存预警的 SKU 数组
     *
     * @return SKU 数组
     */
    List<ProductSkuDO> getSkuListByAlarmStock();


    /**
     * 设置商品上下架
     * @param reqVOs
     * @param supplierId
     */
    void setProductSkuStatus(List<ProductSkuUpdateStatusVO> reqVOs, Long supplierId);

    /**
     * 设置商品上下架
     * @param reqVOs
     * @param supplierId
     */
    void setProductSkuStatus02(List<AppOpenSkuUpdateStatusReqVO> reqVOs, Long supplierId);

    /**
     * 修改商品价格
     * @param reqVOs
     * @param supplierId
     */
    void updateProductSkuPrice(List<ProductSkuUpdatePriceVO> reqVOs, Long supplierId);

    /**
     * 修改商品价格
     * @param reqVOs
     * @param supplierId
     */
    void updateProductSkuPrice02(List<AppOpenSkuUpdatePriceReqVO> reqVOs, Long supplierId);

    /**
     * 根据innerId设置上下架状态
     */
    void updateStatusByInnerId(String innerId, Integer status, Long supplierId);

    /**
     * 添加供应商商品
     * @param reqVOs
     * @param supplierId
     */
    List<ProductSpuBaseRespOpenVO> addProductSku(List<ProductSpuOpenVO> reqVOs, Long supplierId);

    /**
     * 校验商品规则
     * @param skuInnerId
     * @param fullCategoryId
     * @param salePrice
     * @param keywords
     */
    void validProductRule(String skuInnerId, String fullCategoryId, BigDecimal salePrice, List<String> keywords, int status);
    void validProductRule(String skuInnerId, String fullCategoryId);
    void validProductRule(String skuInnerId, BigDecimal salePrice);
    void validProductRule(String skuInnerId, List<String> keywords);

    /**
     * 更新供应商商品
     * @param reqVO
     * @param supplierId
     */
    void saveOrUpdateProductSku(ProductSpuOpenVO reqVO, Long supplierId, Long tenantId);

    /**
     * 获取商品sku详情
     * @param skuId
     * @return
     */
    AppAppProductSkuOpenRespVO getProductSkuDetail(Long skuId, Long supplierId);

    /**
     * 获取商品详情
     * @param skuDetailReq
     * @return
     */
    AppSkuDetailInfo getSkuDetailInfo(VopGoodsGetSkuDetailReq skuDetailReq);

    /**
     * 获取兄弟商品
     * @param brotherSkuReq
     * @return
     */
    List<BrotherSku> getBrotherSkus(VopGoodsBrotherSkuReq brotherSkuReq);

    /**
     * 查询京东商品库存状态价格及轮播商品
     * @param productSkuStockReqVO
     * @return
     */
    SkuStockInfo getSkuStockInfo(AppProductSkuStockReqVO productSkuStockReqVO);

    /**
     * 获取商品品牌销量排行列表
     * @param skuDetailReq
     * @return
     */
    List<SkuGoodsPageItem> getSkuSuggestList(VopGoodsGetSkuDetailReq skuDetailReq);

    /**
     * 批量获取商品详情
     * @param skuIds
     * @return
     */
    List<AppProductSkuDetailRespVO> getProductSkuDetailList(List<Long> skuIds);

    List<AppProductSkuDetailRespVO> getProductSkuDetailListNoSort(List<Long> skuIds);

    /**
     * 同步该租户所有商品到ES
     */
    void syncAllProductSkuIndex2ES();

    /**
     * 批量同步附近天数的商品到ES
     * @param days
     */
    void syncProductSkuIndex2ESRecently(int days);

    /**
     * 批量同步指定商品ES
     * @param skuIds
     */
    void syncProductSkuIndex2ES(List<Long> skuIds);

    /**
     * 通过id或者innerId查询数据
     * @param skuId
     * @return
     */
    ProductSkuDO getSkuByIdOrInnerId(String skuId);

    /**
     * 通过id或者innerId批量查询数据
     * @param skuIds
     * @return
     */
    List<ProductSkuDO> getSkuByIdOrInnerIds(List<String> skuIds);

    /**
     * 查询SKU ID列表
     * @param reqVO
     * @return
     */
    List<Long> getSkuIdList(ProductSkuPageReqVO reqVO);

    /**
     * 商品比价
     * @param skuIds
     * @return
     */
    List<SkuPriceCompareRespVO> skuPriceCompare(List<Long> skuIds);

    /**
     * 统计当前sku总数
     * @return
     */
    SkuSummaryRespVO getSkuTotal();

    /**
     * 统计供应商当前sku总数
     * @return
     */
    List<SupplierSkuSummaryRespVO> getSupplierSkuTotal();

    /**
    List<SkuPriceCompareRespVO> skuPriceCompare(List<SkuPriceCompareReq> skuPriceCompareReqs);


    /**
     * 查询商品上下架数量
     * @param tenantId
     * @param supplierId
     * @return
     */
    List<ProductSkuStatusResult> getProductSkuStatusResult(Long tenantId, Long supplierId);

    /**
     * 获取SPU分类, 以分号分隔
     * @param skuInnerId
     * @param supplierId
     * @return
     */
    String getSpuCategory(String skuInnerId, Long supplierId);

    /**
     * 供应商商品下架处理
     */
    void updateOffJdSku(String skuInnerId, Long supplierId);

    void increaseSkuSaleCountAndAmount(List<TradeOrderItemBaseReqVO> items);

    void decreaseSaleCountAndAmount(List<TradeOrderItemBaseReqVO> items);

    /**
     * 统计商品数量分类排行
     * @return
     */
    List<SkuCountCategorySummaryRespVO> getProductCountByCategory();

    PageResult<ProductSkuPageRespVO> selectPage2(ProductSkuPageReqVO reqVO);

    ProductSkuDetailRespVO selectSkuDetail2(ProductSkuReqVO reqVO);

    void importSeoList(List<ProductSeoSkuImportExcelVO> list);

    void importTagList(List<ProductSkuTagImportExcelVO> list);

    void resetSkuSeoStatus(List<Long> skuIdList);

    void addSkuSeo(List<Long> skuIdList);

    void setSalePriceIfNotLoggedIn(SkuStockInfo skuStockInfo);

    void setSalePriceIfNotLoggedIn(AppSkuDetailInfo appSkuDetailInfo);

    void setSalePriceIfNotLoggedIn(List<?> items);

    void setSalePriceIfNotLoggedIn(PageResult<?> pageResult);

    /**
     * 根据供应商状态更新SKU索引
     * @param supplier
     */
    void updateSkuIndexBySupplierStatus(SupplierDO supplier);

    /**
     * 根据分类状态更新SKU索引
     * @param categoryDO
     */
    void updateSkuIndexByCategoryStatus(ProductCategoryDO categoryDO);

    /**
     * 导出商品SKU
     * @param exportReqVO
     * @return
     */
    String exportSku(ProductSkuExportReqVO exportReqVO);

    /**
     * 导出运营商品SKU
     * @param exportReqVO
     * @return
     */
    String exportSeoSku(ProductSkuExportReqVO exportReqVO);

    /**
     * 批量查询SKU索引状态
     * @param skuIds SKU ID列表
     * @return SKU索引状态列表
     */
    List<ProductSkuIndexStatusRespVO> batchQuerySkuIndexStatus(List<Long> skuIds);

    /**
     * 查询本地已经存在的京东商品
     * @param jdSkuIds
     * @return
     */
    List<String> existJdSkuIds(List<String> jdSkuIds);

    Boolean isExistJdSkuId(String jdSkuId);
}
