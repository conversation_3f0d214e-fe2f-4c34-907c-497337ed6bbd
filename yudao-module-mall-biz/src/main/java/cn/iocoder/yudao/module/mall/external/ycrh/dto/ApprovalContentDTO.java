package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/9
 */
@Data
public class ApprovalContentDTO {

    /**
     * 订单信息
     */
    @JSONField(name = "订单信息")
    private List<OrderItemInfo> orders;

    /**
     * 申请信息
     */
    @JSONField(name = "申请信息")
    private List<ApprovalInfoDTO> approvalInfos;


    @Data
    public static class OrderItemInfo {
        /**
         * 名称
         */
        @JSONField(name = "名称", ordinal = 0)
        private String skuName;

        /**
         * 数量
         */
        @JSONField(name = "数量", ordinal = 1)
        private String count;

        /**
         * 价格
         */
        @JSONField(name = "价格", ordinal = 2)
        private String price;

        /**
         * 总金额
         */
        @JSONField(name = "总金额", ordinal = 3)
        private String totalPrice;
    }

    @Data
    public static class ApprovalInfoDTO {
        /**
         * 申请人
         */
        @JSONField(name = "申请人")
        private String applicantName;

        /**
         * 批次号
         */
        @JSONField(name = "批次号")
        private String purchaseId;

        /**
         * 申请原因
         */
        @JSONField(name = "申请原因")
        private String applyReason;

        /**
         * 项目名称
         */
        @JSONField(name = "使用项目")
        private String projectName;

        /**
         * 部门编号
         */
        @JSONField(name = "部门编号")
        private String deptNo;

        /**
         * 项目编号
         */
        @JSONField(name = "项目编号")
        private String projectNo;

        /**
         * 项目类型
         */
        @JSONField(name = "项目类型")
        private String projectType;

        /**
         * 项目类型
         */
        @JSONField(name = "项目类型名称")
        private String projectTypeName;

        /**
         * 项目负责人
         */
        @JSONField(name = "项目负责人")
        private String projectChargeName;

        /**
         * 经济分类
         */
        @JSONField(name = "经济分类")
        private String economyCategory;

        /**
         * 报销金额
         */
        @JSONField(name = "报销金额")
        private String amount;

        /**
         * 备注
         */
        @JSONField(name = "备注")
        private String remark;
    }


}
