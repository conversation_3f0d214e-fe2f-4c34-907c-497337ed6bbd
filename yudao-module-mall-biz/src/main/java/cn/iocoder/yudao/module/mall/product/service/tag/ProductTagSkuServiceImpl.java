package cn.iocoder.yudao.module.mall.product.service.tag;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.mq.producer.product.ProductSkuProducer;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSeoSkuExportRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuExportRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuPageRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagSimpleVO;
import cn.iocoder.yudao.module.mall.product.convert.tag.ProductTagConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.tag.ProductTagSkuMapper;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMultiMap;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.TAG_ID_EXITS_INVALID;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.TAG_SKU_ID_EXITS_INVALID;

/**
 * 标签SKU关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProductTagSkuServiceImpl extends ServiceImpl<ProductTagSkuMapper, ProductTagSkuDO>  implements ProductTagSkuService {

    @Resource
    @Lazy
    private ProductTagService tagService;
    @Resource
    @Lazy
    private ProductSkuService skuService;
    @Resource
    private ProductSkuProducer productSkuProducer;

    @Override
    public void saveTagSku(List<Long> tagIdList, List<Long> skuIdList) {
        if(CollUtil.isEmpty(tagIdList) || CollUtil.isEmpty(skuIdList)) {
            return;
        }
        validateTagId(tagIdList);
        validateSkuId(skuIdList);

        Set<Long> usedTagIds = new TreeSet<>(tagIdList);
        Set<Long> usedSkuIds = new TreeSet<>(skuIdList);

        List<ProductTagSkuDO> tagSkuList = new ArrayList<>();
        usedSkuIds.forEach(skuId -> {
            usedTagIds.forEach(tagId -> {
                tagSkuList.add(new ProductTagSkuDO().setSkuId(skuId).setTagId(tagId));
            });
        });

        // 移除sku所有的标签
        baseMapper.delete(Wrappers.lambdaQuery(ProductTagSkuDO.class).in(ProductTagSkuDO::getSkuId, skuIdList));
        // 批量保存
        baseMapper.insertBatch(tagSkuList);
        // 更新商品索引
        syncProductIndex(skuIdList);
    }

    @Override
    public void saveTagSkuBatch(Map<Long, List<Long>> skuTagIdsMap) {
        if(CollUtil.isEmpty(skuTagIdsMap)) {
            return;
        }

        Set<Long> skuIds = skuTagIdsMap.keySet();
        List<ProductTagSkuDO> tagSkuList = new ArrayList<>();
        skuTagIdsMap.forEach((skuId, tagIds) -> {
            // 标签只做追加处理
            if(tagIds != null) {
                List<ProductTagSkuDO> persistList = baseMapper.selectList(Wrappers.lambdaQuery(ProductTagSkuDO.class)
                        .eq(ProductTagSkuDO::getSkuId, skuId)
                        .in(ProductTagSkuDO::getTagId, tagIds));
                if(CollUtil.isNotEmpty(persistList)) {
                    Set<Long> persistTagIds = persistList.stream().map(ProductTagSkuDO::getTagId).collect(Collectors.toSet());
                    tagIds = tagIds.stream().filter(tagId -> !persistTagIds.contains(tagId)).collect(Collectors.toList());
                }
                tagIds.forEach(tagId -> {
                    tagSkuList.add(new ProductTagSkuDO().setSkuId(skuId).setTagId(tagId));
                });
            }
        });
        // 批量保存
        baseMapper.insertBatch(tagSkuList);
        // 更新商品索引
        syncProductIndex(new ArrayList<>(skuIds));
    }

    private void syncProductIndex(List<Long> skuIds) {
        if(CollUtil.isNotEmpty(skuIds)) {
            productSkuProducer.sendProductSkuIndexSync(skuIds);
        }
    }

    @Override
    public void deleteTagSku(List<Long> tagIdList, List<Long> skuIdList) {
        validateTagId(tagIdList);
        validateSkuId(skuIdList);

        baseMapper.delete(Wrappers.lambdaQuery(ProductTagSkuDO.class)
                .in(ProductTagSkuDO::getTagId, tagIdList)
                .in(ProductTagSkuDO::getSkuId, skuIdList));

        // 更新商品索引
        syncProductIndex(skuIdList);
    }

    @Override
    public void deleteTagSkuByTag(List<Long> tagIdList) {
        validateTagId(tagIdList);

        List<ProductTagSkuDO> tagSkuList = baseMapper.selectList(Wrappers.lambdaQuery(ProductTagSkuDO.class)
                .in(ProductTagSkuDO::getTagId, tagIdList)
                .select(ProductTagSkuDO::getSkuId));
        List<Long> skuIdList = convertList(tagSkuList, ProductTagSkuDO::getSkuId);
        baseMapper.delete(Wrappers.lambdaQuery(ProductTagSkuDO.class)
                .in(ProductTagSkuDO::getTagId, tagIdList));

        // 更新商品索引
        syncProductIndex(skuIdList);
    }

    @Override
    public void deleteTagSkuBySku(List<Long> skuIdList) {
        validateSkuId(skuIdList);

        baseMapper.delete(Wrappers.lambdaQuery(ProductTagSkuDO.class)
                .in(ProductTagSkuDO::getSkuId, skuIdList));

        // 更新商品索引
        syncProductIndex(skuIdList);
    }

    @Override
    public void deleteTagSkuBySkuV2(List<Long> skuIdList) {
        baseMapper.delete(Wrappers.lambdaQuery(ProductTagSkuDO.class)
                .in(ProductTagSkuDO::getSkuId, skuIdList));
    }

    private void validateTagId(List<Long> tagIdList) {
        if(CollUtil.isEmpty(tagIdList)) {
            return;
        }
        long count = tagService.count(Wrappers.lambdaQuery(ProductTagDO.class).in(ProductTagDO::getId, tagIdList));
        if(count != tagIdList.size()) {
            throw exception(TAG_ID_EXITS_INVALID);
        }
    }

    private void validateSkuId(List<Long> skuIdList) {
        if(CollUtil.isEmpty(skuIdList)) {
            return;
        }
        long count = skuService.count(Wrappers.lambdaQuery(ProductSkuDO.class).in(ProductSkuDO::getId, skuIdList));
        if(count != skuIdList.size()) {
            throw exception(TAG_SKU_ID_EXITS_INVALID);
        }
    }

    @Override
    public void deleteTagSkuByTag(Long tagId) {
        deleteTagSkuByTag(Arrays.asList(tagId));
    }

    @Override
    public List<ProductTagDO> getTagListBySku(Long skuId) {
        return getTagListBySku(Arrays.asList(skuId));
    }

    @Override
    public List<ProductTagDO> getTagListBySku(List<Long> skuIdList) {
        List<ProductTagSkuDO> tagSkuList = getBySku(skuIdList);
        List<Long> tagIds = convertList(tagSkuList, ProductTagSkuDO::getTagId);

        return tagService.getTagList(tagIds);
    }

    @Override
    public Map<Long, List<ProductTagDO>> getTagListMap(List<Long> skuIdList) {
        if(CollUtil.isEmpty(skuIdList)) {
            return null;
        }
        Map<Long, List<ProductTagDO>> map = new HashMap<>();
        List<ProductTagSkuDO> tagSkuList = getBySku(skuIdList);
        if(CollUtil.isEmpty(tagSkuList)) {
            return null;
        }

        List<ProductTagDO> tagList = tagService.getTagList(tagSkuList.stream().map(ProductTagSkuDO::getTagId).collect(Collectors.toList()));
        Map<Long, List<ProductTagSkuDO>> tagSkuMap = convertMultiMap(tagSkuList, ProductTagSkuDO::getSkuId);

        tagSkuMap.forEach((skuId, tagSku) -> {
            Set<Long> tagIds = tagSku.stream().map(ProductTagSkuDO::getTagId).collect(Collectors.toSet());
            map.put(skuId, tagList.stream().filter(tag -> tagIds.contains(tag.getId())).collect(Collectors.toList()));
        });

        return map;
    }

    @Override
    public Map<Long, List<Long>> getTagIdListMap(List<Long> skuIdList) {
        Map<Long, List<ProductTagSkuDO>> tagSkuMap = getTagSkuMap(skuIdList);
        if(tagSkuMap == null) {
            return null;
        }
        Map<Long, List<Long>> tagIdMap = new HashMap<>();
        tagSkuMap.forEach((key, val) -> {
            if(val != null && CollUtil.isNotEmpty(val)) {
                tagIdMap.put(key, convertList(val, ProductTagSkuDO::getTagId));
            }
        });

        return tagIdMap;
    }

    private Map<Long, List<ProductTagSkuDO>> getTagSkuMap(List<Long> skuIdList) {
        if(CollUtil.isEmpty(skuIdList)) {
            return null;
        }
        List<ProductTagSkuDO> tagSkuList = getBySku(skuIdList);
        if(CollUtil.isEmpty(tagSkuList)) {
            return null;
        }

        return convertMultiMap(tagSkuList, ProductTagSkuDO::getSkuId);
    }

    @Override
    public void fillTag(PageResult<ProductSkuPageRespVO> pageResult) {
        if(CollUtil.isEmpty(pageResult.getList())) {
            return;
        }

        List<Long> skuIds = pageResult.getList().stream().map(ProductSkuPageRespVO::getId).collect(Collectors.toList());
        Map<Long, List<ProductTagDO>> skuTagMap = getTagListMap(skuIds);
        if(CollUtil.isEmpty(skuTagMap)) {
            return;
        }

        pageResult.getList().forEach(sku -> {
            List<ProductTagDO> tags = skuTagMap.get(sku.getId());
            sku.setTagList(ProductTagConvert.INSTANCE.convertList02(tags));
        });
    }

    @Override
    public void fillTag2(List<ProductSeoSkuExportRespVO> seoSkuExportRespVOS) {
        if(CollUtil.isEmpty(seoSkuExportRespVOS)) {
            return;
        }

        List<Long> skuIds = seoSkuExportRespVOS.stream().map(ProductSeoSkuExportRespVO::getSkuId).collect(Collectors.toList());
        Map<Long, List<ProductTagDO>> skuTagMap = getTagListMap(skuIds);
        if(CollUtil.isEmpty(skuTagMap)) {
            return;
        }

        seoSkuExportRespVOS.forEach(sku -> {
            List<ProductTagDO> tags = skuTagMap.get(sku.getSkuId());
            List<Long> tagIdList = tags.stream().map(ProductTagDO::getId).collect(Collectors.toList());
            sku.setTagIdStr(StrUtil.join("\n", tagIdList));
            List<String> tagNameList = tags.stream().map(ProductTagDO::getName).collect(Collectors.toList());
            sku.setTagNameStr(StrUtil.join("\n", tagNameList));
        });
    }


    @Override
    public Map<Long, List<ProductTagSimpleVO>> getSkuTagMap(List<Long> skuIds) {
        Map<Long, List<ProductTagSimpleVO>> map = new HashMap<>();
        Map<Long, List<ProductTagDO>> skuTagMap = getTagListMap(skuIds);
        if(CollUtil.isEmpty(skuTagMap)) {
            return map;
        }

        skuIds.forEach(skuId -> {
            if(skuTagMap.containsKey(skuId) && CollUtil.isNotEmpty(skuTagMap.get(skuId))) {
                List<ProductTagDO> tags = skuTagMap.get(skuId);
                map.put(skuId, ProductTagConvert.INSTANCE.convertList02(tags));
            }
        });

        return map;
    }

    private ProductTagSkuDO getByTagAndSku(Long tagId, Long skuId) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(ProductTagSkuDO.class)
                .eq(ProductTagSkuDO::getTagId, tagId)
                .eq(ProductTagSkuDO::getSkuId, skuId));
    }

    private List<ProductTagSkuDO> getBySku(Long skuId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(ProductTagSkuDO.class)
                .eq(ProductTagSkuDO::getSkuId, skuId));
    }

    private List<ProductTagSkuDO> getBySku(List<Long> skuIdList) {
        return baseMapper.selectList(Wrappers.lambdaQuery(ProductTagSkuDO.class)
                .in(ProductTagSkuDO::getSkuId, skuIdList));
    }


}
