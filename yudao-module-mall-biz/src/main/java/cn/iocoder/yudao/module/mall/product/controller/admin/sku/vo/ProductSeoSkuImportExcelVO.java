package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 商品SEO运营SKU导入Excel模板字段
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class ProductSeoSkuImportExcelVO {

    @ExcelProperty("*供应商ID")
    @ColumnWidth(15)
    private Long supplierId;

    @ExcelProperty("*平台SKU")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(25)
    private Long skuId;

    @ExcelProperty("*三方SKU")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(25)
    private String skuInnerId;

    @ExcelProperty("*初始销量")
    @ColumnWidth(15)
    private Integer initSalesCount;

}
