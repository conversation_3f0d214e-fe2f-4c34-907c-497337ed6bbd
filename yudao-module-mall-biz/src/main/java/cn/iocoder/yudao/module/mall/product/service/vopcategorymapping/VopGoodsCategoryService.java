package cn.iocoder.yudao.module.mall.product.service.vopcategorymapping;

import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsCategoryItem;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopGoodsCategoryDO;

import java.util.List;

public interface VopGoodsCategoryService {

    List<VopGoodsCategoryDO> getVopGoodsPrimaryCategoryList();

    List<VopGoodsCategoryItem> getChildCategoryList(Long parentCategoryId);
}
