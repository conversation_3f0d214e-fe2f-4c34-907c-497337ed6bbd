package cn.iocoder.yudao.module.mall.product.dal.mysql.seocard;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo.ProductSeoCardPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.seocard.ProductSeoCardDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 运营区域 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSeoCardMapper extends BaseMapperX<ProductSeoCardDO> {

    default PageResult<ProductSeoCardDO> selectPage(ProductSeoCardPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductSeoCardDO>()
                .likeIfPresent(ProductSeoCardDO::getName, reqVO.getName())
                .likeIfPresent(ProductSeoCardDO::getTitle, reqVO.getTitle())
                .eqIfPresent(ProductSeoCardDO::getType, reqVO.getType())
                .eqIfPresent(ProductSeoCardDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ProductSeoCardDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductSeoCardDO::getSort, ProductSeoCardDO::getId));
    }

}
