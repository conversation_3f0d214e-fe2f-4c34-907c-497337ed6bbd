package cn.iocoder.yudao.module.mall.external.sso.cas.dynamic;

import org.jasig.cas.client.validation.Assertion;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

/**
 * @author: HF
 * @description: TODO
 * @date: 2024/4/25 15:55
 */
@Configuration
public class DynamicCasConfig {

    @Bean
    public RedisTemplate<String, Assertion> casRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Assertion> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(RedisSerializer.java(this.getClass().getClassLoader()));
        return template;
    }
}
