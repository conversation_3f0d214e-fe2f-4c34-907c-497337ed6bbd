package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.validation.constraints.*;

/**
 * 订单统计 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class OrderStatisticsBaseVO {

    @Schema(description = "供应商id")
    private Long supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "统计维度类型 1-日 2-月  3-年")
    private Integer type;


    @Schema(description = "订单数量")
    private Integer orderNum;

    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    @Schema(description = "统计时间")
    private String staticsTime;

}
