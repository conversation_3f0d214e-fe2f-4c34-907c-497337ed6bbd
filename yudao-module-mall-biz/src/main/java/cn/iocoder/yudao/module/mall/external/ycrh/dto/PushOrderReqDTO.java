package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class PushOrderReqDTO {

    /**
     * 业务类型 "01":采购 "02":费用缴纳 "03":大型仪器
     */
    private String businessType;

    /**
     * 订单信息(业务类型为"01"时必填)
     */
    private ShopOrderDTO wbShopOrder;

    /**
     * 缴费信息(业务类型为"02"或"03"时必填)
     */
    private PayOrderDTO	wbPayOrder;
}
