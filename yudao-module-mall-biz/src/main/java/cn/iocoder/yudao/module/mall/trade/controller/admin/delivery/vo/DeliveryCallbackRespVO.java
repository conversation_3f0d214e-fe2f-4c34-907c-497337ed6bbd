package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.jd.open.api.sdk.internal.JSON.JSON;
import lombok.Data;

import java.io.Serializable;

@Data
public class DeliveryCallbackRespVO implements Serializable {
    /**
     * true表示成功，false表示失败。如果提交回调接口的地址失败，30分钟后重新回调，3次仍旧失败的，自动放弃
     */
    private Boolean result;
    /**
     *200: 提交成功 500: 服务器错误 其他错误请自行定义
     */
    private String returnCode;
    /**
     * 返回的提示
     */
    private String message;

    public static DeliveryCallbackRespVO success() {
        DeliveryCallbackRespVO result = new DeliveryCallbackRespVO();
        result.returnCode = "200";
        result.result = true;
        result.message = "成功";
        return result;
    }

    public static DeliveryCallbackRespVO error() {
        DeliveryCallbackRespVO result = new DeliveryCallbackRespVO();
        result.returnCode = "500";
        result.result = false;
        result.message = "失败";
        return result;
    }
}
