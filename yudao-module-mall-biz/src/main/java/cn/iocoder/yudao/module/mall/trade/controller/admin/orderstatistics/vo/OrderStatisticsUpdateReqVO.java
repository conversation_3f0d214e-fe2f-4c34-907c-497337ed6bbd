package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 订单统计更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderStatisticsUpdateReqVO extends OrderStatisticsBaseVO {

    @Schema(description = "编号，主键自增", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "编号，主键自增不能为空")
    private Long id;

}
