package cn.iocoder.yudao.module.mall.external.sso.cas.dynamic;

import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.SsoConfigDO;
import cn.iocoder.yudao.module.mall.external.sso.cas.CasContextHolder;
import cn.iocoder.yudao.module.mall.external.sso.cas.util.CasUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: HF
 * @description: TODO
 * @date: 2024/4/12 14:32
 */
@Slf4j
public abstract class AbstractDynamicCasFilter implements Filter {

    protected Map<Long, Filter> targetCasFilterMap = new HashMap<>();
    protected FilterConfig initConfig = null;
    protected CasConfigService casConfigService;

    public AbstractDynamicCasFilter(CasConfigService casConfigService) {
        this.casConfigService = casConfigService;
    }


    protected Filter getTargetFilter(Long tId) {
        Filter result = null;
        if (tId != null) {
            if(!casConfigService.isLoginByCas()) {
                return null;
            }
            Map<String, String> tenantCasConfig = casConfigService.getCasConfig(tId);
            result = targetCasFilterMap.get(tId);
            if (result == null) {
                try {
                    result = genTargetFilter(initConfig, tenantCasConfig);
                } catch (ServletException e) {
                    throw new RuntimeException(e);
                }
                targetCasFilterMap.put(tId, result);
            }
        }
        return result;
    }

    protected abstract Filter genTargetFilter(FilterConfig config, Map<String, String> casConfigMap) throws ServletException;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        this.initConfig = filterConfig;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        String servletPath = httpServletRequest.getServletPath();
        log.info("cas-abstract-filter entry: {}", servletPath);
        if(!StringUtils.containsIgnoreCase(servletPath, "app-api")) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }

        Long tenantId = casConfigService.getTenantId(httpServletRequest);
        HashMap<String, Object> jsonObj = null;
        if(httpServletRequest.getMethod().equalsIgnoreCase("post") && StringUtils.containsIgnoreCase(servletPath, "sso-login")) {
            jsonObj = casConfigService.parseJsonFromBody(httpServletRequest);
            tenantId = casConfigService.parseTenantId(jsonObj);
        }
        if(tenantId == null) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }

        Filter targetCasFilter = this.getTargetFilter(tenantId);
        if (targetCasFilter != null) {
            doBefore(servletRequest, tenantId, jsonObj);
            targetCasFilter.doFilter(servletRequest, servletResponse, filterChain);
            String uid = CasContextHolder.getUserId();
            if(StringUtils.isNotBlank(uid)) {
                casConfigService.removeAssertionInRedis(uid);
            }
        } else {
            filterChain.doFilter(servletRequest, servletResponse);
        }
    }

    protected void doBefore(ServletRequest servletRequest,  Long tenantId, Map<String, Object> bodyJson) {

    }

    protected void handleCasException(ServletRequest servletRequest, ServletResponse servletResponse) throws IOException {
        if (TenantContextHolder.getTenantId() == null) {
            return;
        }
        SsoConfigDO ssoConfigDO = casConfigService.getSsoConfig();
        if(ssoConfigDO != null) {
            HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;
            HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
            String userAgent = httpRequest.getHeader("User-Agent");
            log.info("userAgent: {}", userAgent);
            if(ServletUtils.isMobileRequest(userAgent) && StringUtils.isNotBlank(ssoConfigDO.getCasClientHostUrl2())) {
                httpResponse.sendRedirect(CasUtils.buildCasErrorUrl(ssoConfigDO.getCasClientHostUrl2()));
            } else {
                httpResponse.sendRedirect(CasUtils.buildCasErrorUrl(ssoConfigDO.getCasClientHostUrl()));
            }
        }
    }


}
