package cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo;

import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuCreateOrUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecValueReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 商品 SPU 更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSpuUpdateReqVO extends ProductSpuBaseVO {

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品编号不能为空")
    private Long id;

    @Schema(description = "商品描述详情", example = "")
    private String description;

    @Schema(description = "H5端商品描述详情", example = "")
    private String descriptionH5;

    /**
     * 通用属性数组
     */
    @Valid
    private List<ProductSpecValueReqVO> spuSpecValueList;

    /**
     * SKU 数组
     */
    @Valid
    private List<ProductSkuCreateOrUpdateReqVO> skus;

}
