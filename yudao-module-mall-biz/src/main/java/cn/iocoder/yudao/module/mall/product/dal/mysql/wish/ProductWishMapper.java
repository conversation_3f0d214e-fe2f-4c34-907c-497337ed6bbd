package cn.iocoder.yudao.module.mall.product.dal.mysql.wish;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo.ProductWishExportReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo.ProductWishPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.wish.ProductWishDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 心愿单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductWishMapper extends BaseMapperX<ProductWishDO> {

    default PageResult<ProductWishDO> selectPage(ProductWishPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductWishDO>()
                .likeIfPresent(ProductWishDO::getBrand, reqVO.getBrand())
                .likeIfPresent(ProductWishDO::getContact, reqVO.getContact())
                .likeIfPresent(ProductWishDO::getPhone, reqVO.getPhone())
                .eqIfPresent(ProductWishDO::getUserId, reqVO.getUserId())
                .eqIfPresent(ProductWishDO::getStatus, reqVO.getStatus())
                .likeIfPresent(ProductWishDO::getUserName, reqVO.getUserName())
                .betweenIfPresent(ProductWishDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductWishDO::getId));
    }

    default List<ProductWishDO> selectList(ProductWishExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ProductWishDO>()
                .likeIfPresent(ProductWishDO::getBrand, reqVO.getBrand())
                .likeIfPresent(ProductWishDO::getContact, reqVO.getContact())
                .likeIfPresent(ProductWishDO::getPhone, reqVO.getPhone())
                .likeIfPresent(ProductWishDO::getUserName, reqVO.getUserName())
                .betweenIfPresent(ProductWishDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductWishDO::getId));
    }

}
