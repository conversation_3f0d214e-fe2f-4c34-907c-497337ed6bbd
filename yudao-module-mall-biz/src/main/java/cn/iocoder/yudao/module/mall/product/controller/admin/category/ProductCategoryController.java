package cn.iocoder.yudao.module.mall.product.controller.admin.category;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.category.ProductCategoryConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.category.ProductCategoryService;
import cn.iocoder.yudao.module.mall.trade.controller.admin.billorder.vo.BillOrderExcelVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.billorder.vo.BillOrderExportReqVO;
import cn.iocoder.yudao.module.mall.trade.convert.billorder.BillOrderConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.billorder.BillOrderDO;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTask;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 商品分类")
@RestController
@RequestMapping("/product/category")
@Validated
public class ProductCategoryController {

    @Resource
    private ProductCategoryService categoryService;

    @PostMapping("/create")
    @Operation(summary = "创建商品分类")
    @PreAuthorize("@ss.hasPermission('product:category:create')")
    public CommonResult<Long> createCategory(@Valid @RequestBody ProductCategoryCreateReqVO createReqVO) {
        return success(categoryService.createCategory(createReqVO));
    }

    @PostMapping("/clone")
    @Operation(summary = "克隆平台配置商品分类")
    @PreAuthorize("@ss.hasPermission('product:category:create')")
    public CommonResult<Boolean> cloneCategory(@Valid @RequestBody ProductCategoryCloneReqVO cloneReqVO) {
        categoryService.cloneCategory(cloneReqVO);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品分类")
    @PreAuthorize("@ss.hasPermission('product:category:update')")
    public CommonResult<Boolean> updateCategory(@Valid @RequestBody ProductCategoryUpdateReqVO updateReqVO) {
        categoryService.updateCategory(updateReqVO);
        return success(true);
    }

    /**
     * 获得导入模板
     * @param response
     * @throws IOException
     */
    @GetMapping("/get-import-template")
    @PreAuthorize("@ss.hasPermission('product:category:create')")
    @Operation(summary = "获得商品分类导入模板")
    public void seoImportTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<ProductCategoryImportExcelVO> list = Arrays.asList(
                ProductCategoryImportExcelVO.builder()
                        .code("6010000")
                        .name("办公用品")
                        .iconUrl("https://zcfl.whu.edu.cn/fcdn/picon/1.png")
                        .h5IconUrl("https://zcfl.whu.edu.cn/fcdn/picon/1-m.png")
                        .economyClass("300102")
                        .sort(1)
                        .build()
        );
        // 输出
        ExcelUtils.write(response, "商品分类导入模板.xls", "分类列表", ProductCategoryImportExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "导入商品分类")
    @PreAuthorize("@ss.hasPermission('product:category:create')")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.PRODUCT_CATEGORY_IMPORT)
    public CommonResult<String> seoImportExcel(@RequestParam("file") MultipartFile file) throws Exception {
        List<ProductCategoryImportExcelVO> list = ExcelUtils.read(file, ProductCategoryImportExcelVO.class);
        categoryService.importList(list);
        return success(AsyncFrontTaskContext.getTaskId());
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新商品分类状态")
    @PreAuthorize("@ss.hasPermission('product:category:update')")
    public CommonResult<Boolean> updateCategoryStatus(@RequestBody ProductCategoryUpdateReqVO updateReqVO) {
        categoryService.updateCategoryStatus(updateReqVO.getId(), ProductCategoryStatusEnum.fromStatus(updateReqVO.getStatus()));
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:category:delete')")
    public CommonResult<Boolean> deleteCategory(@RequestParam("categoryId") Long categoryId) {
        categoryService.deleteCategoryWithChildren(categoryId);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<ProductCategoryRespVO> getCategory(@RequestParam("id") Long id) {
        ProductCategoryDO category = categoryService.getCategory(id);
        return success(ProductCategoryConvert.INSTANCE.convert(category));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商品分类列表")
    @PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<List<ProductCategoryRespVO>> getCategoryList(@Valid ProductCategoryListReqVO treeListReqVO) {
        List<ProductCategoryDO> list = categoryService.getAllCategoryList(treeListReqVO);
        return success(ProductCategoryConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-by-parent")
    @Operation(summary = "根据上级分类ID获得下级商品分类列表")
	@PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<List<ProductCategoryRespVO>> getCategoryListByParent(Long parentCategoryId) {
        List<ProductCategoryDO> list = categoryService.getChildCategoryList(parentCategoryId, ProductCategoryStatusEnum.ENABLE);
        return success(ProductCategoryConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-root")
    @Operation(summary = "获得一级商品分类列表")
	@PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<List<ProductCategoryRespVO>> getRootCategoryList(ProductCategoryStatusEnum status) {
        List<ProductCategoryDO> list = categoryService.getRootCategoryList(status);
        return success(ProductCategoryConvert.INSTANCE.convertList05(list));
    }

    @GetMapping("/list-children-tree")
    @Operation(summary = "获得下级商品分类树形列表")
	@PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<List<ProductCategoryTreeRespVO>> getChildCategoryTreeList(@RequestParam(value = "parentCategoryId", required = true) Long parentCategoryId, ProductCategoryStatusEnum status) {
        List<ProductCategoryDO> list = categoryService.getChildCategoryTreeList(parentCategoryId, status);
        return success(ProductCategoryConvert.INSTANCE.convertList06(list));
    }

    @GetMapping("/get-total-count")
    @Operation(summary = "查询分数总数量")
	@PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<Integer> getTotalCount() {
        return success(categoryService.getCategoryTotalCount());
    }

    @PostMapping("/init")
    @Operation(summary = "新租户实始化商品分类")
    @PreAuthorize("@ss.hasPermission('product:category:update')")
    public CommonResult<Boolean> initCategory4Tenant(@RequestParam Long tenantId) {
        categoryService.initCategory4Tenant(tenantId);
        return success(true);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品分类 Excel")
    @PreAuthorize("@ss.hasPermission('product:category:query')")
    @OperateLog(type = EXPORT)
    public void exportCategoryExcel(@Valid ProductCategoryListReqVO treeListReqVO,
                                    HttpServletResponse response) throws IOException {
        List<ProductCategoryDO> list = categoryService.getAllCategoryList(treeListReqVO);
        // 导出 Excel
        List<ProductCategoryExcelVO> datas = ProductCategoryConvert.INSTANCE.convertList10(list);
        ExcelUtils.write(response, "商品分类.xls", "数据", ProductCategoryExcelVO.class, datas);
    }

}
