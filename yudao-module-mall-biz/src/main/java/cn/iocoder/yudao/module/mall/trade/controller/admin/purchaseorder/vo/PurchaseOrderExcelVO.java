package cn.iocoder.yudao.module.mall.trade.controller.admin.purchaseorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 采购订单 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PurchaseOrderExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("用户id")
    private Long userId;

    @ExcelProperty("采购id")
    private Long purchaseId;

    @ExcelProperty("采购id")
    private Long orderId;

    @ExcelProperty("创建时间")
    private Date createTime;

}
