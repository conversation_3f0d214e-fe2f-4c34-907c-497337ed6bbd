package cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 心愿单 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ProductWishExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("商品分类")
    private String categoryNames;

    @ExcelProperty("品牌")
    private String brand;

    @ExcelProperty("型号")
    private String model;

    @ExcelProperty("数量")
    private Integer quantity;

    @ExcelProperty("预计下单时间")
    private Date orderTime;

    @ExcelProperty("产品链接")
    private String productLink;

    @ExcelProperty("参数描述")
    private String productMemo;

    @ExcelProperty("联系人")
    private String contact;

    @ExcelProperty("联系电话")
    private String phone;

    @ExcelProperty("状态")
    private String statusInfo;

    @ExcelIgnore
    private Integer status;

    @ExcelProperty("会员用户姓名")
    private String userName;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
