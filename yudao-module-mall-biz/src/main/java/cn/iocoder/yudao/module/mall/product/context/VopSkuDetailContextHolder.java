package cn.iocoder.yudao.module.mall.product.context;

import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import com.alibaba.ttl.TransmittableThreadLocal;
import org.apache.commons.lang3.BooleanUtils;

/**
 * <AUTHOR>
 * @date 2024/1/30
 */
public class VopSkuDetailContextHolder {

    /**
     * skuId
     */
    private static final ThreadLocal<Long> SKU_ID = new TransmittableThreadLocal<>();

    /**
     * skuInnerId
     */
    private static final ThreadLocal<String> SKU_INNER_ID = new TransmittableThreadLocal<>();

    private static final ThreadLocal<ProductSkuDO> PRODUCT_SKU = new TransmittableThreadLocal<>();

    private static final ThreadLocal<Boolean> IS_JD_SKU = new TransmittableThreadLocal<>();


    /**
     * @param skuId
     * @param skuInnerId
     */
    public static void set(Long skuId, String skuInnerId) {
        SKU_ID.set(skuId);
        SKU_INNER_ID.set(skuInnerId);
    }

    public static void setIsJdSku(Boolean isJdSku) {
        IS_JD_SKU.set(isJdSku);
    }
    /**
     * 设置sku
     *
     * @param productSkuDO
     */
    public static void set(ProductSkuDO productSkuDO) {
        SKU_ID.set(productSkuDO.getId());
        SKU_INNER_ID.set(productSkuDO.getSkuInnerId());
        PRODUCT_SKU.set(productSkuDO);
        //判断商品是否京东品
        IS_JD_SKU.set(productSkuDO.isJd());
    }


    public static Long getSkuId() {
        return SKU_ID.get();
    }

    public static String getSkuInnerId() {
        return SKU_INNER_ID.get();
    }

    public static ProductSkuDO getProductSku() {
        return PRODUCT_SKU.get();
    }

    public static boolean isJdSku() {
        return BooleanUtils.toBoolean(IS_JD_SKU.get());
    }


    public static void clear() {
        SKU_ID.remove();
        SKU_INNER_ID.remove();
        PRODUCT_SKU.remove();
        IS_JD_SKU.remove();
    }

}
