package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade;

import cn.iocoder.yudao.module.mall.trade.controller.admin.settle.vo.OrderInvoiceUpdateReq;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Data
public class AppTradeOrderSendInvoiceReqVO extends OrderInvoiceUpdateReq {

}
