package cn.iocoder.yudao.module.mall.framework.datapermission.config;

import cn.iocoder.yudao.framework.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.aftersale.TradeAfterSaleDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * mall 模块的数据权限 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class DataPermissionConfiguration {

    @Bean
    public DeptDataPermissionRuleCustomizer sysDeptDataPermissionRuleCustomizer() {
        return rule -> {
            // dept
            rule.addDeptColumn(MemberUserDO.class, "dept_id");
            rule.addDeptColumn(TradeOrderDO.class, "dept_id");
            rule.addDeptColumn(TradeAfterSaleDO.class, "dept_id");
            
        };
    }

}