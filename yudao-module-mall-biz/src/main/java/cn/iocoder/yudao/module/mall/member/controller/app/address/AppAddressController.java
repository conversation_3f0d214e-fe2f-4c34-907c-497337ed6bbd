package cn.iocoder.yudao.module.mall.member.controller.app.address;

import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.ip.core.utils.IPUtils;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import cn.iocoder.yudao.framework.ratelimiter.core.keyresolver.impl.UserRateLimiterKeyResolver;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.*;
import cn.iocoder.yudao.module.mall.member.convert.address.AddressConvert;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AddressDO;
import cn.iocoder.yudao.module.mall.member.service.address.AddressService;
import cn.iocoder.yudao.module.mall.member.service.address.AreaService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 用户 APP - 用户收件地址
 */
@Tag(name = "用户 APP - 用户收件地址")
@RestController
@RequestMapping("/member/address")
@Validated
@Slf4j
public class AppAddressController {

    @Resource
    private AddressService addressService;
    @Resource
    private AreaService areaService;

    /**
     * 创建用户收件地址
     * @param createReqVO
     * @return
     */
    @PostMapping("/create")
    @Operation(summary = "创建用户收件地址")
    @PreAuthenticated
    @Idempotent(timeout = 30, message = "正在创建中，请勿重复提交")
    public CommonResult<Long> createAddress(@Valid @RequestBody AppAddressCreateReqVO createReqVO) {
        return success(addressService.createAddress(getLoginUserId(), createReqVO));
    }

    /**
     * 更新用户收件地址
     * @param updateReqVO
     * @return
     */
    @PostMapping("/update")
    @Operation(summary = "更新用户收件地址")
    @PreAuthenticated
    public CommonResult<Boolean> updateAddress(@Valid @RequestBody AppAddressUpdateReqVO updateReqVO) {
        addressService.updateAddress(getLoginUserId(), updateReqVO);
        return success(true);
    }

    /**
     * 删除用户收件地址
     * @param id
     * @return
     */
    @PostMapping("/delete")
    @Operation(summary = "删除用户收件地址")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthenticated
    public CommonResult<Boolean> deleteAddress(@RequestParam("id") Long id) {
        addressService.deleteAddress(getLoginUserId(), id);
        return success(true);
    }

    /**
     * 获得用户收件地址
     * @param id
     * @return
     */
    @GetMapping("/get")
    @Operation(summary = "获得用户收件地址")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthenticated
    public CommonResult<AppAddressRespVO> getAddress(@RequestParam("id") Long id) {
        AddressDO address = addressService.getAddress(getLoginUserId(), id);
        return success(AddressConvert.INSTANCE.convert(address));
    }

    /**
     * 获得默认的用户收件地址
     * @return
     */
    @GetMapping("/get-default")
    @Operation(summary = "获得默认的用户收件地址")
    public CommonResult<AppAddressRespVO> getDefaultUserAddress() {
        AddressDO address = null;
        Long userId = getLoginUserId();
        if (null != userId) {
            address = addressService.getDefaultUserAddress(userId);
        }
        if (address == null) {
            address = getIpParseAreaV2();
        }

        if (address == null) {
            address = getDefaultArea();
        }
        return success(AddressConvert.INSTANCE.convert(address));
    }

    /**
     * 解析文本地址
     * @return
     */
    @PostMapping("/parse-address")
    @Operation(summary = "解析文本地址并输出区域ID")
    @PreAuthenticated
    public CommonResult<AppAddressRespVO> parseAddress(@RequestBody AppAddressParseReqVO reqVO) {
        AddressDO address = areaService.parseFullName(reqVO.getContent());
        return success(AddressConvert.INSTANCE.convert(address));
    }

    private AddressDO getDefaultArea() {
        AddressDO addressDO = new AddressDO();
        addressDO.setProvinceId(VopGoodsBridgeService.DEFAULT_PROVINCE_ID);
        addressDO.setProvinceName("湖北");
        addressDO.setCityId(VopGoodsBridgeService.DEFAULT_CITY_ID);
        addressDO.setCityName("武汉市");
        addressDO.setCountyId(VopGoodsBridgeService.DEFAULT_COUNTY_ID);
        addressDO.setCountyName("洪山区");
        addressDO.setTownId(VopGoodsBridgeService.DEFAULT_TOWN_ID);
        addressDO.setTownName("狮子山街道");

        return addressDO;
    }

    private AddressDO getIpParseAreaV2() {
        try {
            String clientIp = ServletUtils.getClientIP();
            String fullAreaName = IPUtils.getFullAreaName(clientIp);
            if(StringUtils.isBlank(fullAreaName)) {
                return null;
            }

            return areaService.parseFullName(fullAreaName);
        } catch (Exception exception) {
            return null;
        }
    }

    private AddressDO getIpParseArea() {
        try {
            String clientIp = ServletUtils.getClientIP();
            String url = "http://whois.pconline.com.cn/?ip=" + clientIp;
            String result = HttpUtil.get(url);
            Document document = Jsoup.parse(result);
            Elements form = document.getElementsByTag("p");
            String areaSource = form.get(1).text().split("：")[1];
            String[] areaArr = areaSource.split(" ");
            if (areaArr.length == 0) {
                return null;
            }

            return areaService.parseFullName(areaArr[0]);
        } catch (Exception e) {
            log.error("IP解析地址失败", e);
            return null;
        }
    }

    /**
     * 获得用户收件地址列表
     * @return
     */
    @GetMapping("/list")
    @Operation(summary = "获得用户收件地址列表")
    @PreAuthenticated
    public CommonResult<List<AppAddressRespVO>> getAddressList() {
        List<AddressDO> list = addressService.getAddressList(getLoginUserId());
        return success(AddressConvert.INSTANCE.convertList(list));
    }

    /**
     * 获取省市区地址
     * @param param
     * @return
     */
    @PostMapping("/getAreas")
    @Operation(summary = "获取省市区地址")
    public CommonResult<List<AddressAreaRespVO>> getAreas(@RequestBody AppAddressQueryVO param) {
        try {
            return success(areaService.getAreas(param));
        } catch (Exception e) {
            log.error("获取地址异常...", e);
        }

        return success(new ArrayList<>());
    }

}
