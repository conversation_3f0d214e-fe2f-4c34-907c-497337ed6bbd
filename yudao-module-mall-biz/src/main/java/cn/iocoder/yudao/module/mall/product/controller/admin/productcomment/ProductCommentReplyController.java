package cn.iocoder.yudao.module.mall.product.controller.admin.productcomment;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.productcomment.ProductCommentReplyConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentReplyDO;
import cn.iocoder.yudao.module.mall.product.service.productcomment.ProductCommentReplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 商品评价回复")
@RestController
@RequestMapping("/mall/product-comment-reply")
@Validated
public class ProductCommentReplyController {

    @Resource
    private ProductCommentReplyService productCommentReplyService;

    @PostMapping("/create")
    @Operation(summary = "创建商品评价回复")
    @PreAuthorize("@ss.hasPermission('product:comment-reply:create')")
    public CommonResult<Long> createProductCommentReply(@Valid @RequestBody ProductCommentReplyCreateReqVO createReqVO) {
        return success(productCommentReplyService.createProductCommentReply(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品评价回复")
    @PreAuthorize("@ss.hasPermission('product:comment-reply:update')")
    public CommonResult<Boolean> updateProductCommentReply(@Valid @RequestBody ProductCommentReplyUpdateReqVO updateReqVO) {
        productCommentReplyService.updateProductCommentReply(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品评价回复")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:comment-reply:delete')")
    public CommonResult<Boolean> deleteProductCommentReply(@RequestParam("id") Long id) {
        productCommentReplyService.deleteProductCommentReply(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品评价回复")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:comment-reply:query')")
    public CommonResult<ProductCommentReplyRespVO> getProductCommentReply(@RequestParam("id") Long id) {
        ProductCommentReplyDO productCommentReply = productCommentReplyService.getProductCommentReply(id);
        return success(ProductCommentReplyConvert.INSTANCE.convert(productCommentReply));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商品评价回复列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('product:comment-reply:query')")
    public CommonResult<List<ProductCommentReplyRespVO>> getProductCommentReplyList(@RequestParam("ids") Collection<Long> ids) {
        List<ProductCommentReplyDO> list = productCommentReplyService.getProductCommentReplyList(ids);
        return success(ProductCommentReplyConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品评价回复分页")
    @PreAuthorize("@ss.hasPermission('product:comment-reply:query')")
    public CommonResult<PageResult<ProductCommentReplyRespVO>> getProductCommentReplyPage(@Valid ProductCommentReplyPageReqVO pageVO) {
        PageResult<ProductCommentReplyDO> pageResult = productCommentReplyService.getProductCommentReplyPage(pageVO);
        return success(ProductCommentReplyConvert.INSTANCE.convertPage(pageResult));
    }

}
