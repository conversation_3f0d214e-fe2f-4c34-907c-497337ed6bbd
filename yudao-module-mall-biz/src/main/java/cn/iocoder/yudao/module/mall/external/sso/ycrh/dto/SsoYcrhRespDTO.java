package cn.iocoder.yudao.module.mall.external.sso.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SsoYcrhRespDTO {

    /**
     * 员工编号
     */
    @JsonProperty("ygbh")
    private String userNo;

    /**
     * 员工名称
     */
    @JsonProperty("ygmc")
    private String userName;

    /**
     * 部门编号
     */
    @JsonProperty("bmbh")
    private String deptCode;

    /**
     * 部门名称
     */
    @JsonProperty("bmmc")
    private String deptName;

    /**
     * 人员类型
     */
    @JsonProperty("rylx")
    private String userType;

    /**
     * 职位名称
     */
    @JsonProperty("zwmc")
    private String jobTitle;

    /**
     * 是否可用(1:是 0:否)
     */
    @JsonProperty("isok")
    private String status;


}
