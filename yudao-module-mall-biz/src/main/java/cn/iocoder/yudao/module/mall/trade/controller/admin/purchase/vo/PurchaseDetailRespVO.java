package cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo;

import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderPageItemRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.ApprovalResultRespVO;
import cn.iocoder.yudao.module.mall.trade.enums.purchase.TradePurchaseStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理后台 - 采购 Response VO
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 采购 Response VO")
@Data
public class PurchaseDetailRespVO {

    /**
     * 采购单id
     */
    private Long id;

    /**
     * 状态，见 {@link TradePurchaseStatusEnum}
     */
    private Integer status;

    /**
     * 采购人用户ID
     */
    private Long userId;

    /**
     * 采购人用户昵称
     */
    private String userName;

    /**
     * 审批流单据号
     */
    private String bpmNo;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 项目负责人编号
     */
    private String projectChargeNo;

    /**
     * 项目负责人姓名
     */
    private String projectChargeName;

    /**
     * 项目所在部门名称
     */
    private String projectDepartmentName;

    /**
     * 项目所在部门编号
     */
    private String projectDepartmentNo;

    /**
     * 采购原因
     */
    private String purchaseReason;

    /**
     * 审批结果 1通过，2驳回，0撤销，-1审批中
     */
    private Integer auditStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;

    /**
     *  验收人姓名
     */
    private String accepterName;

    /**
     * 验收人手机号
     */
    private String accepterMobile;

    /**
     * 验收人邮箱
     */
    private String accepterEmail;

    /**
     * 附件，附件URL并且逗号分隔
     */
    @Schema(description = "附件")
    private String attachments;

    /**
     * 订单信息
     */
    private List<TradeOrderPageItemRespVO> orders;

    /**
     * 审批流节点信息
     */
    private List<ApprovalResultRespVO> bpmResult;

}
