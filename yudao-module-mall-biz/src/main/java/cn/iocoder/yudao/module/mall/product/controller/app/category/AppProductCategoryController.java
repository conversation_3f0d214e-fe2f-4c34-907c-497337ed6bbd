package cn.iocoder.yudao.module.mall.product.controller.app.category;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.product.controller.app.category.vo.AppProductCategoryRespVO;
import cn.iocoder.yudao.module.mall.product.convert.category.ProductCategoryConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.category.ProductCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 商品分类")
@RestController
@RequestMapping("/product/category")
@Validated
public class AppProductCategoryController {

    @Resource
    private ProductCategoryService categoryService;

    @GetMapping("/getRootList")
    @Operation(summary = "获得一级商品分类列表")
    public CommonResult<List<AppProductCategoryRespVO>> getRootCategoryList() {
        List<ProductCategoryDO> list = categoryService.getRootCategoryList(ProductCategoryStatusEnum.ENABLE);
        return success(ProductCategoryConvert.INSTANCE.convertList03(list));
    }

    @GetMapping("/getNamePath")
    @Operation(summary = "获得分类名称路径")
    public CommonResult<String> getCategoryNamePath(@RequestParam Long[] ids) {
        if(ids.length == 0) {
            return success(null);
        }

        String namePath = categoryService.getNamePath(Arrays.asList(ids), "/");
        return success(namePath);
    }

    @GetMapping("/getEconomyClass")
    @Operation(summary = "获得分类经济分类")
    public CommonResult<Map<Long, String>> getCategoryEconomyClass(@RequestParam Long[] ids) {
        if(ids.length == 0) {
            return success(null);
        }

        return success(categoryService.getEconomyClassMapByCategoryId(Arrays.asList(ids)));
    }

    @GetMapping("/getChildTreeList")
    @Operation(summary = "获得下级商品分类树形列表")
    public CommonResult<List<AppProductCategoryRespVO>> getChildCategoryTreeList(@RequestParam(value = "parentCategoryId", required = true) Long parentCategoryId) {
        List<ProductCategoryDO> list = categoryService.getChildCategoryTreeList(parentCategoryId, ProductCategoryStatusEnum.ENABLE);
        return success(ProductCategoryConvert.INSTANCE.convertList03(list));
    }

    @GetMapping("/getChildList")
    @Operation(summary = "获得下级商品分类列表")
    public CommonResult<List<AppProductCategoryRespVO>> getChildCategoryList(@RequestParam(value = "parentCategoryId", required = true) Long parentCategoryId) {
        List<ProductCategoryDO> list = categoryService.getChildCategoryList(parentCategoryId, ProductCategoryStatusEnum.ENABLE);
        return success(ProductCategoryConvert.INSTANCE.convertList03(list));
    }

}
