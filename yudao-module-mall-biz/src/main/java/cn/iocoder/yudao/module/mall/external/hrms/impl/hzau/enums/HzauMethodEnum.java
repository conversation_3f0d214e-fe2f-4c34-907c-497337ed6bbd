package cn.iocoder.yudao.module.mall.external.hrms.impl.hzau.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Getter
public enum HzauMethodEnum {

    // 武大相关接口
    GET_ACCESS_TOKEN("/open_api/authentication/get_access_token","获取接口访问Token"),
    GET_STAFF_LIST("/open_api/customization/view_charlie_alpha/full","获取教职工基本信息"),
    GET_DEPT_LIST("/open_api/customization/view_golf/full","获取部门信息");

    private String methodName;

    private String desc;

    HzauMethodEnum(String methodName, String desc) {
        this.methodName = methodName;
        this.desc = desc;
    }
}
