package cn.iocoder.yudao.module.mall.trade.service.delivery.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.tenant.TenantIdUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.framework.config.MallProperties;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.external.open.context.OpenContextHolder;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.delivery.AppOpenDeliveryBindReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo.*;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderDeliveryReqVO;
import cn.iocoder.yudao.module.mall.trade.convert.delivery.DeliveryConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryCompanyDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryProductSkuDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryTrackDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.dal.mysql.delivery.DeliveryMapper;
import cn.iocoder.yudao.module.mall.trade.enums.delivery.DeliveryTypeEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderStatusEnum;
import cn.iocoder.yudao.module.mall.trade.framework.delivery.config.DeliveryProperties;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryCompanyService;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryProductSkuService;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryService;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryTrackService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskUtils;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportRespExcelVO;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportTask;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TtlRunnable;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.kuaidi100.sdk.api.QueryTrack;
import com.kuaidi100.sdk.api.Subscribe;
import com.kuaidi100.sdk.contant.ApiInfoConstant;
import com.kuaidi100.sdk.request.*;
import com.kuaidi100.sdk.response.*;
import com.kuaidi100.sdk.utils.SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.trade.enums.ErrorCodeConstants.DELIVERY_NOT_EXISTS;

/**
 * 物流信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Lazy
@Slf4j
public class DeliveryServiceImpl extends ServiceImpl<DeliveryMapper, DeliveryDO> implements DeliveryService {

    public static final String  SUBSCRIBE_UPDATE_CALLBACK_URL = "%s/app-api/trade/delivery/notify/callback?tcode=%s";

    @Resource
    private DeliveryProperties deliveryProperties;
    @Resource
    private MallProperties mallProperties;
    @Resource
    private DeliveryMapper deliveryMapper;
    @Resource
    private DeliveryTrackService deliveryTrackService;
    @Resource
    private DeliveryProductSkuService deliveryProductSkuService;
    @Resource
    private DeliveryCompanyService deliveryCompanyService;
    @Resource
    @Lazy
    private TradeOrderService tradeOrderService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private AsyncFrontTaskUtils asyncFrontTaskUtils;

    @Override
    public Long createDelivery(DeliveryCreateReqVO createReqVO) {
        // 插入
        DeliveryDO delivery = DeliveryConvert.INSTANCE.convert(createReqVO);
        deliveryMapper.insert(delivery);
        // 返回
        return delivery.getId();
    }

    @Override
    public void updateDelivery(DeliveryUpdateReqVO updateReqVO) {
        // 校验存在
        validateDeliveryExists(updateReqVO.getId());
        // 更新
        DeliveryDO updateObj = DeliveryConvert.INSTANCE.convert(updateReqVO);
        deliveryMapper.updateById(updateObj);
    }

    @Override
    public void deleteDelivery(Long id) {
        // 校验存在
        validateDeliveryExists(id);
        // 删除
        deliveryMapper.deleteById(id);
    }

    private void validateDeliveryExists(Long id) {
        if (deliveryMapper.selectById(id) == null) {
            throw exception(DELIVERY_NOT_EXISTS);
        }
    }

    @Override
    public DeliveryDO getDelivery(Long id) {
        return deliveryMapper.selectById(id);
    }

    @Override
    public List<DeliveryDO> getDeliveryList(Collection<Long> ids) {
        return deliveryMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DeliveryDO> getDeliveryPage(DeliveryPageReqVO pageReqVO) {
        return deliveryMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DeliveryDO> getDeliveryList(DeliveryExportReqVO exportReqVO) {
        return deliveryMapper.selectList(exportReqVO);
    }

    /**
     * 自建订单更新物流信息
     * @param deliveryBindReqVO
     * @return
     */
    @Override
    public Boolean productSend(DeliveryBindReqVO deliveryBindReqVO, AsyncImportTask importTask, List<AsyncImportRespExcelVO> importRespList) {
        //查询物流单号是否已经存在
        List<DeliveryDO> deliveryDOS = deliveryMapper.selectList(
                new LambdaUpdateWrapper<DeliveryDO>().eq(DeliveryDO::getNum, deliveryBindReqVO.getNum()));

        //检查订单是否存在
        String[] orderNoArray = deliveryBindReqVO.getOrderNo().split(",");
        List<String> orderNos = Arrays.asList(orderNoArray);
        List<TradeOrderDO> tradeOrderDOS = tradeOrderService.getOrdersByOrderNo(orderNos);
        Assert.equals(orderNos.size(), tradeOrderDOS.size(), "订单号不正确");
        List<Long> supplierIds = tradeOrderDOS.stream().map(TradeOrderDO::getSupplierId).distinct().collect(Collectors.toList());
        Assert.isTrue(supplierIds.size() == 1, "同一个物流单号不能绑定多个供应商订单");

        tradeOrderDOS.forEach(tradeOrderDO -> {
            if(ObjectUtil.notEqual(TradeOrderStatusEnum.HAS_CONFIRM.getStatus(), tradeOrderDO.getStatus())
                    && ObjectUtil.notEqual(TradeOrderStatusEnum.DO_DELIVERING.getStatus(), tradeOrderDO.getStatus())
                    && ObjectUtil.notEqual(TradeOrderStatusEnum.DELIVER_DONE.getStatus(), tradeOrderDO.getStatus())
                    && ObjectUtil.notEqual(TradeOrderStatusEnum.CHECK_AND_ACCEPT.getStatus(), tradeOrderDO.getStatus()) && ObjectUtil.notEqual(TradeOrderStatusEnum.AFTER_SALES_DOING.getStatus(), tradeOrderDO.getStatus())){
                Assert.notNull(tradeOrderDO, "订单状态不正确，不允许发货");
            }
        });

        //检查商品sku是否存在
        Map<String, List<Long>> skuIdMap = new HashMap<>();
        List<Long> skuIdList = null;
        List<Long> allSkuIdList = new ArrayList<>();
        tradeOrderDOS.forEach(tradeOrderDO -> {
            List<TradeOrderItemDO> tradeOrderItemDOS = tradeOrderService.getOrderItemListByOrderId(tradeOrderDO.getId());
            List<Long> skuIds = tradeOrderItemDOS.stream().map(TradeOrderItemDO -> TradeOrderItemDO.getSkuId()).collect(Collectors.toList());
            allSkuIdList.addAll(skuIds);
            skuIdMap.put(tradeOrderDO.getNo(), skuIds);
        });
        if(StrUtil.isNotBlank(deliveryBindReqVO.getSkuIds())
                && CollUtil.isNotEmpty(Arrays.asList(deliveryBindReqVO.getSkuIds().split(",")))){
            List<Long> skuIdList2 = Arrays.stream(deliveryBindReqVO.getSkuIds().split(",")).map(skuId -> Long.valueOf(skuId)).collect(Collectors.toList());
            Assert.isTrue(allSkuIdList.containsAll(skuIdList2), "商品sku不正确");
            skuIdList = skuIdList2;
        }
        else {
            skuIdList = allSkuIdList;
        }

        // 通过物流100查询物流
        QueryTrackReq queryTrackReq = new QueryTrackReq();
        QueryTrackParam queryTrackParam = new QueryTrackParam();
        queryTrackParam.setCom(deliveryBindReqVO.getCompanyCode());
        queryTrackParam.setNum(deliveryBindReqVO.getNum());
        queryTrackParam.setResultv2("4");
        String param = JSONObject.toJSONString(queryTrackParam);
        queryTrackReq.setParam(param);
        queryTrackReq.setCustomer(deliveryProperties.getCustomer());
        queryTrackReq.setSign(SignUtils.querySign(param, deliveryProperties.getAuthKey(), deliveryProperties.getCustomer()));
        QueryTrack queryTrack = new QueryTrack();
        QueryTrackResp queryTrackResp = null;
        try {
            queryTrackResp = queryTrack.queryTrack(queryTrackReq);
            Assert.equals(queryTrackResp.getStatus(), "200", String.format("物流查询失败, 错误详情：%s, 物流错误码:%s", queryTrackResp.getMessage(), queryTrackResp.getReturnCode()));
        }
        catch (Exception ex){
            Assert.isNull(ex, String.format("%s", ex.getMessage()));
        }

        // 查询商品信息
        Map<Long, ProductSkuDO> productSkuDOMap = new HashMap<>();
        for (int i = 0; i < skuIdList.size(); i++) {
            Long skuId = skuIdList.get(i);
            ProductSkuDO productSkuDO = productSkuService.getSku(Long.valueOf(skuId));
            Assert.notNull(productSkuDO, "商品skuId不存在");
            productSkuDOMap.put(skuId, productSkuDO);
        }

        // 查询快递公司名称
        List<DeliveryCompanyDO> deliveryCompanyDOS = deliveryCompanyService.list(
                new LambdaQueryWrapperX<DeliveryCompanyDO>().eq(DeliveryCompanyDO::getCom, queryTrackResp.getCom()));
        if(deliveryCompanyDOS.isEmpty()){
            //自动将物流公司添加到租户物流公司列表中
            Assert.isTrue(deliveryCompanyService.add(queryTrackResp.getCom()), "物流公司查不到，请联系管理员同步物流公司");
            //添加后再查询一次
            deliveryCompanyDOS = deliveryCompanyService.list(
                    new LambdaQueryWrapperX<DeliveryCompanyDO>().eq(DeliveryCompanyDO::getCom, queryTrackResp.getCom()));
        }

        // 更新物流信息表
        List<DeliveryDO> deliveryList = new ArrayList<>();
        QueryTrackResp finalQueryTrackResp = queryTrackResp;
        List<DeliveryCompanyDO> finalDeliveryCompanyDOS = deliveryCompanyDOS;
        List<Long> finalSkuIdList = skuIdList;
        tradeOrderDOS.forEach(tradeOrderDO -> {
            DeliveryDO deliveryDO = new DeliveryDO();
            deliveryDO.setCom(finalQueryTrackResp.getCom());
            deliveryDO.setName(finalDeliveryCompanyDOS.get(0).getName());
            deliveryDO.setNum(finalQueryTrackResp.getNu());
            deliveryDO.setIsCheck(Integer.valueOf(finalQueryTrackResp.getIscheck()));
            deliveryDO.setState(Integer.valueOf(finalQueryTrackResp.getState()));
            deliveryDO.setSource(DeliveryCompanyService.KUAIDI100_SOURCE_NAME);
            if(finalQueryTrackResp.getRouteInfo() != null){
                if(finalQueryTrackResp.getRouteInfo().getFrom() != null){
                    deliveryDO.setFromName(finalQueryTrackResp.getRouteInfo().getFrom().getName());
                    deliveryDO.setFromNumber(finalQueryTrackResp.getRouteInfo().getFrom().getNumber());
                }
                if(finalQueryTrackResp.getRouteInfo().getTo() != null){
                    deliveryDO.setToName(finalQueryTrackResp.getRouteInfo().getTo().getName());
                    deliveryDO.setToNumber(finalQueryTrackResp.getRouteInfo().getTo().getNumber());
                }
                if(finalQueryTrackResp.getRouteInfo().getCur() != null){
                    deliveryDO.setCurName(finalQueryTrackResp.getRouteInfo().getCur().getName());
                    deliveryDO.setCurNumber(finalQueryTrackResp.getRouteInfo().getCur().getNumber());
                }
            }
            deliveryDO.setOrderNo(tradeOrderDO.getNo());
            deliveryDO.setSupplierId(tradeOrderDO.getSupplierId());
            deliveryDO.setSupplierName(tradeOrderDO.getSupplierName());
            this.saveOrUpdate(deliveryDO, new LambdaQueryWrapperX<DeliveryDO>()
                    .eq(DeliveryDO::getNum, finalQueryTrackResp.getNu()).eq(DeliveryDO::getOrderNo, tradeOrderDO.getNo()));
            DeliveryDO deliveryDO1 = this.getOne(new LambdaQueryWrapperX<DeliveryDO>()
                    .eq(DeliveryDO::getNum, finalQueryTrackResp.getNu())
                    .eq(DeliveryDO::getOrderNo, tradeOrderDO.getNo()));

            // 更新物流sku关系表
            List<DeliveryProductSkuDO> deliveryProductSkuDOS = new ArrayList<>();
            List<Long> orderSkuIdList = skuIdMap.get(tradeOrderDO.getNo());
            Set<Long> skuIdSet = finalSkuIdList.stream().collect(Collectors.toSet());
            List<Long> filterSkuIds = orderSkuIdList.stream()
                    .filter(skuIdSet::contains)
                    .collect(Collectors.toList());

            for (int i = 0; i < filterSkuIds.size(); i++) {
                Long skuId = filterSkuIds.get(i);
                DeliveryProductSkuDO deliveryProductSkuDO = new DeliveryProductSkuDO();
                deliveryProductSkuDO.setDeliveryId(deliveryDO1.getId());
                deliveryProductSkuDO.setSkuId(skuId);
                deliveryProductSkuDO.setSkuName(productSkuDOMap.get(skuId).getSkuName());
                deliveryProductSkuDO.setPicUrl(productSkuDOMap.get(skuId).getPicUrl());
                deliveryProductSkuDO.setSupplierId(tradeOrderDO.getSupplierId());
                deliveryProductSkuDOS.add(deliveryProductSkuDO);
            }
            deliveryProductSkuService.sync(deliveryProductSkuDOS,
                    new LambdaQueryWrapperX<DeliveryProductSkuDO>().eq(DeliveryProductSkuDO::getDeliveryId, deliveryDO1.getId()));

            deliveryList.add(deliveryDO1);
        });

        List<DeliveryTrackDO> deliveryTrackDOS = new ArrayList<>();
        queryTrackResp.getData().forEach(item -> {
            DeliveryTrackDO deliveryTrackDO = new DeliveryTrackDO();
            deliveryTrackDO.setNum(deliveryBindReqVO.getNum());
            deliveryTrackDO.setSupplierId(tradeOrderDOS.get(0).getSupplierId());
            deliveryTrackDO.setSupplierName(tradeOrderDOS.get(0).getSupplierName());
            deliveryTrackDO.setContent(item.getContext());
            deliveryTrackDO.setDeliveryTime(item.getTime());
            if(item.getStatus() != null){
                deliveryTrackDO.setStatus(item.getStatus());
            }
            if(item.getStatusCode() != null){
                deliveryTrackDO.setStatusCode(item.getStatusCode());
            }
            if(item.getAreaCenter() != null){
                deliveryTrackDO.setAreaCenter(item.getAreaCenter());
            }
            if(item.getAreaCode() != null){
                deliveryTrackDO.setAreaCode(item.getAreaCode());
            }
            if(item.getAreaName() != null){
                deliveryTrackDO.setAreaName(item.getAreaName());
            }
            if(item.getAreaPinYin() != null){
                deliveryTrackDO.setAreaPinyin(item.getAreaPinYin());
            }
            deliveryTrackDOS.add(deliveryTrackDO);
        });

        // 更新物流轨迹
        deliveryTrackService.sync(deliveryTrackDOS,
                new LambdaQueryWrapperX<DeliveryTrackDO>().eq(DeliveryTrackDO::getNum, deliveryBindReqVO.getNum()));

        // 开启订阅
        DeliveryDO deliveryDO1 = deliveryList.get(0);
        if(deliveryDO1.getSubscribe() != 1){
            boolean res = subscribeUpdate(deliveryDO1.getCom(), deliveryDO1.getNum());
            if(res){
                for (DeliveryDO deliveryDO : deliveryList) {
                    deliveryDO.setSubscribe(1);
                    this.saveOrUpdate(deliveryDO);
                }
            }
        }

        //获取目前最新的一条物流轨迹
        DeliveryTrackDO lastDelivery = deliveryTrackService.getLatestByDelivery(new LambdaQueryWrapperX<DeliveryTrackDO>()
                .eq(DeliveryTrackDO::getNum, deliveryDO1.getNum())
                .orderByDesc(DeliveryTrackDO::getDeliveryTime)
                .last("limit 1"));

        //修改订单状态，只有状态是已确认的订单，则修改为已发货，其他状态不做修改
        tradeOrderDOS.forEach(tradeOrderDO -> {
            tradeOrderService.deliveryOrder(new TradeOrderDeliveryReqVO()
                    .setId(tradeOrderDO.getId())
                    .setDeliveryNum(deliveryBindReqVO.getNum()));
            //修改订单tip提示为最新的物流轨迹
            if (lastDelivery != null && StrUtil.isNotBlank(lastDelivery.getContent())) {
                tradeOrderService.updateOrderDeliveryTip(lastDelivery.getContent(), tradeOrderDO.getNo());
            }
        });

        if(importTask != null && importRespList != null) {
            importRespList.add(importTask.plusCreated(deliveryBindReqVO.getOrderNo()));
        }

        return true;
    }

    @Override
    public Boolean serviceSend(DeliveryServiceReqVO deliveryServiceReqVO, AsyncImportTask importTask, List<AsyncImportRespExcelVO> importRespList) {
        //检查订单是否存在
        String[] orderNoArray = deliveryServiceReqVO.getOrderNo().split(",");
        List<String> orderNos = Arrays.asList(orderNoArray);
        List<TradeOrderDO> tradeOrderDOS = tradeOrderService.getOrdersByOrderNo(orderNos);
        Assert.equals(orderNos.size(), tradeOrderDOS.size(), "订单号不正确");

        // 虚拟、服务类发货
        tradeOrderDOS.forEach(tradeOrderDO -> {
            TradeOrderDeliveryReqVO deliveryReqVO = new TradeOrderDeliveryReqVO();
            deliveryReqVO.setId(tradeOrderDO.getId());
            tradeOrderService.deliveryOrder(deliveryReqVO);
        });

        if(importTask != null && importRespList != null) {
            importRespList.add(importTask.plusCreated(deliveryServiceReqVO.getOrderNo()));
        }

        return true;
    }

    @Override
    public Boolean selfSend(DeliverySelfReqVO deliverySelfReqVO, AsyncImportTask importTask, List<AsyncImportRespExcelVO> importRespList) {
        //检查订单是否存在
        String[] orderNoArray = deliverySelfReqVO.getOrderNo().split(",");
        List<String> orderNos = Arrays.asList(orderNoArray);
        List<TradeOrderDO> tradeOrderDOS = tradeOrderService.getOrdersByOrderNo(orderNos);
        Assert.equals(orderNos.size(), tradeOrderDOS.size(), "订单号不正确");
        List<Long> supplierIds = tradeOrderDOS.stream().map(TradeOrderDO::getSupplierId).distinct().collect(Collectors.toList());
        Assert.isTrue(supplierIds.size() == 1, "不能同时操作多个供应商订单");

        tradeOrderDOS.forEach(tradeOrderDO -> {
            SupplierDO supplierDO = supplierService.getSupplier(tradeOrderDO.getSupplierId());
            String companyName = String.format("%s自配送", supplierDO.getName());
            String companyCode = companyName;
            String num = IdUtil.getSnowflakeNextIdStr();

            // 绑定自配送物流
            AppOpenDeliveryBindReqVO appOpenDeliveryBindReqVO = new AppOpenDeliveryBindReqVO();
            appOpenDeliveryBindReqVO.setOrderId(tradeOrderDO.getId())
                    .setSkuIds(deliverySelfReqVO.getSkuIds())
                    .setCompanyName(companyName)
                    .setCompanyCode(companyCode)
                    .setNum(num);
            thirdBindOrder(appOpenDeliveryBindReqVO);

            // 更新自配送物流信息
            SubscribePushData subscribePushData = new SubscribePushData();
            subscribePushData.setStatus("在途");
            subscribePushData.setContext(deliverySelfReqVO.getDetail());
            subscribePushData.setTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            List<SubscribePushData> subscribePushDataList = new ArrayList<>();
            subscribePushDataList.add(subscribePushData);
            SubscribePushResult subscribePushResult = new SubscribePushResult();
            subscribePushResult.setNu(num);
            subscribePushResult.setCom(companyCode);
            subscribePushResult.setState("0");
            subscribePushResult.setIscheck("0");
            subscribePushResult.setData(subscribePushDataList);
            thirdUpdate(subscribePushResult);
        });

        if(importTask != null && importRespList != null) {
            importRespList.add(importTask.plusCreated(deliverySelfReqVO.getOrderNo()));
        }

        return true;
    }

    @Override
    public Boolean deleteByOrder(String orderNo) {
        //检查订单是否存在
        String[] orderNoArray = orderNo.split(",");
        List<String> orderNos = Arrays.asList(orderNoArray);
        List<TradeOrderDO> tradeOrderDOS = tradeOrderService.getOrdersByOrderNo(orderNos);
        Assert.equals(orderNos.size(), tradeOrderDOS.size(), "订单号不正确");

        List<Long> deliveryIds = deliveryMapper.selectList(new LambdaQueryWrapper<DeliveryDO>().in(DeliveryDO::getOrderNo, orderNos))
                .stream().map(DeliveryDO::getId).collect(Collectors.toList());
        deliveryProductSkuService.remove(new LambdaQueryWrapper<DeliveryProductSkuDO>().in(DeliveryProductSkuDO::getDeliveryId, deliveryIds));
        deliveryMapper.delete(new LambdaUpdateWrapper<DeliveryDO>().in(DeliveryDO::getOrderNo, orderNos));
        tradeOrderService.update(new LambdaUpdateWrapper<TradeOrderDO>().in(TradeOrderDO::getNo, orderNos).set(TradeOrderDO::getTip, null).set(TradeOrderDO::getDeliveryTime, null));

        return true;
    }

    /**
     * 订阅物流回调
     * @param param
     * @return
     */
    @Override
    public Boolean deliveryCallback(String param) {
        SubscribePushParamResp subscribePushParamResp = JSONObject.parseObject(param, SubscribePushParamResp.class);
        if(subscribePushParamResp == null){
            log.error("物流回调结果异常");
            return false;
        }
        return thirdUpdate(subscribePushParamResp.getLastResult());
    }

    /**
     * 第三方订单更新物流信息
     * @param appOpenDeliveryBindReqVO
     * @return
     */
    @Override
    public Boolean thirdBindOrder(AppOpenDeliveryBindReqVO appOpenDeliveryBindReqVO) {
        //查询物流单号是否已经存在
        List<DeliveryDO> deliveryDOS = deliveryMapper.selectList(
                new LambdaUpdateWrapper<DeliveryDO>().eq(DeliveryDO::getNum, appOpenDeliveryBindReqVO.getNum()));
        //Assert.equals(deliveryDOS.size(), 0, "物流单号已绑定到其他订单");

        Long orderId = appOpenDeliveryBindReqVO.getOrderId();
        //查询订单对应平台信息
        TradeOrderDO tradeOrderDO = null;
        if(OpenContextHolder.getSupplierId() != null){
            log.info("供应商id:{}", OpenContextHolder.getSupplierId());
            tradeOrderDO = tradeOrderService.getOrder4Supplier(OpenContextHolder.getSupplierId(), orderId);
        }
        else {
            tradeOrderDO = tradeOrderService.getOrder(orderId);
        }

        //检查商品sku是否存在
        List<TradeOrderItemDO> tradeOrderItemDOS = tradeOrderService.getOrderItemListByOrderId(tradeOrderDO.getId());
        List<Long> skuIds = tradeOrderItemDOS.stream().map(TradeOrderItemDO -> TradeOrderItemDO.getSkuId()).collect(Collectors.toList());
        List<Long> skuIdList = null;
        if(StrUtil.isNotBlank(appOpenDeliveryBindReqVO.getSkuIds())
                && CollUtil.isNotEmpty(Arrays.asList(appOpenDeliveryBindReqVO.getSkuIds().split(",")))){
            skuIdList = Arrays.stream(appOpenDeliveryBindReqVO.getSkuIds().split(",")).map(skuId -> Long.valueOf(skuId)).collect(Collectors.toList());
            Assert.isTrue(skuIds.containsAll(skuIdList), "商品sku不正确");
        }
        else {
            skuIdList = skuIds;
        }

        // 查询商品信息
        Map<Long, ProductSkuDO> productSkuDOMap = new HashMap<>();
        for (int i = 0; i < skuIdList.size(); i++) {
            Long skuId = skuIdList.get(i);
            ProductSkuDO productSkuDO = productSkuService.getSku(skuId);
            Assert.notNull(productSkuDO, "商品skuId不存在");
            productSkuDOMap.put(skuId, productSkuDO);
        }
        // 更新订单状态
        tradeOrderService.updateOrderDelivery(tradeOrderDO.getSupplierId(), orderId);

        // 更新物流信息表
        DeliveryDO deliveryDO2 = new DeliveryDO();
        deliveryDO2.setCom(appOpenDeliveryBindReqVO.getCompanyCode());
        deliveryDO2.setName(appOpenDeliveryBindReqVO.getCompanyName());
        deliveryDO2.setNum(appOpenDeliveryBindReqVO.getNum());
        deliveryDO2.setOrderNo(tradeOrderDO.getNo());
        deliveryDO2.setSupplierId(tradeOrderDO.getSupplierId());
        deliveryDO2.setSupplierName(tradeOrderDO.getSupplierName());
        deliveryDO2.setSource(tradeOrderDO.getSupplierName());
        this.saveOrUpdate(deliveryDO2, new LambdaUpdateWrapper<DeliveryDO>()
                .eq(DeliveryDO::getNum, appOpenDeliveryBindReqVO.getNum()).eq(DeliveryDO::getOrderNo, tradeOrderDO.getNo()));
        DeliveryDO deliveryDO3 = this.getOne(
                new LambdaQueryWrapperX<DeliveryDO>()
                        .eq(DeliveryDO::getNum, appOpenDeliveryBindReqVO.getNum())
                        .eq(DeliveryDO::getOrderNo, tradeOrderDO.getNo()));

        // 更新物流sku关系表
        List<DeliveryProductSkuDO> deliveryProductSkuDOS = new ArrayList<>();
        for (int i = 0; i < skuIdList.size(); i++) {
            Long skuId = skuIdList.get(i);
            DeliveryProductSkuDO deliveryProductSkuDO = new DeliveryProductSkuDO();
            deliveryProductSkuDO.setDeliveryId(deliveryDO3.getId());
            deliveryProductSkuDO.setSkuId(skuId);
            deliveryProductSkuDO.setSkuName(productSkuDOMap.get(skuId).getSkuName());
            deliveryProductSkuDO.setPicUrl(productSkuDOMap.get(skuId).getPicUrl());
            deliveryProductSkuDO.setSupplierId(productSkuDOMap.get(skuId).getSupplierId());
            deliveryProductSkuDO.setSupplierName(productSkuDOMap.get(skuId).getSupplierName());
            deliveryProductSkuDOS.add(deliveryProductSkuDO);
        }

        deliveryProductSkuService.sync(deliveryProductSkuDOS,
                new LambdaQueryWrapperX<DeliveryProductSkuDO>().eq(DeliveryProductSkuDO::getDeliveryId, deliveryDO3.getId()));

        // 第三方商品绑定时默认开启订阅
        // 开放平台的物流由第三方供应商推送更新
//        if(deliveryDO3.getSubscribe() != 1){
//            boolean res = subscribeUpdate(deliveryDO3.getCom(), deliveryDO3.getNum());
//            if(res){
//                deliveryDO3.setSubscribe(1);
//                this.saveOrUpdate(deliveryDO3);
//            }
//        }

        return true;
    }

    /**
     * 第三方平台物流更新
     * @param subscribePushResult
     * @return
     */
    @Override
    public Boolean thirdUpdate(SubscribePushResult subscribePushResult){
        Assert.notNull(subscribePushResult, "物流回调结果异常");

        List<DeliveryDO> deliveryDOS = deliveryMapper.selectList(new LambdaQueryWrapperX<DeliveryDO>()
                .eq(DeliveryDO::getNum, subscribePushResult.getNu()));
        //1个物流单号查询多个物流信息/多个订单流水号
        Assert.notEmpty(deliveryDOS, "物流回调查询不到物流单号：{}", subscribePushResult.getNu());

        // 检查是否是其他供应商的物流单号
        if(OpenContextHolder.getSupplierId() != null) {
            List<String> orderNos = deliveryDOS.stream().map(DeliveryDO::getOrderNo).collect(Collectors.toList());
            List<TradeOrderDO> tradeOrderDOS = tradeOrderService.list(new LambdaQueryWrapperX<TradeOrderDO>().in(TradeOrderDO::getNo, orderNos));
            List<Long> supplyIds = tradeOrderDOS.stream().map(TradeOrderDO::getSupplierId).collect(Collectors.toList());
            Set<Long> supplierIds = new HashSet<>(supplyIds);
            Assert.isTrue(supplierIds.size() == 1, "物流单号不正确：{}", subscribePushResult.getNu());
            Assert.isTrue(supplierIds.contains(OpenContextHolder.getSupplierId()), "物流单号不正确：{}", subscribePushResult.getNu());
        }

        //1个物流单号对应多个物流详情信息
        List<DeliveryTrackDO> deliveryTrackDOS = new ArrayList<>();
        for (SubscribePushData item : subscribePushResult.getData()) {
            DeliveryTrackDO deliveryTrackDO = new DeliveryTrackDO()
                    .setNum(subscribePushResult.getNu())
                    .setSupplierId(deliveryDOS.get(0).getSupplierId())
                    .setSupplierName(deliveryDOS.get(0).getSupplierName())
                    .setContent(item.getContext())
                    .setDeliveryTime(item.getTime());
            if(item.getStatus() != null){
                deliveryTrackDO.setStatus(item.getStatus());
            }
            if(item.getStatusCode() != null){
                deliveryTrackDO.setStatus(item.getStatusCode());
            }
            if(item.getAreaCenter() != null){
                deliveryTrackDO.setAreaCenter(item.getAreaCenter());
            }
            if(item.getAreaCode() != null){
                deliveryTrackDO.setAreaCode(item.getAreaCode());
            }
            if(item.getAreaName() != null){
                deliveryTrackDO.setAreaName(item.getAreaName());
            }
            if(item.getAreaPinYin() != null){
                deliveryTrackDO.setAreaPinyin(item.getAreaPinYin());
            }
            deliveryTrackDOS.add(deliveryTrackDO);
        }

        // 更新物流轨迹
        deliveryTrackService.sync(deliveryTrackDOS,
                new LambdaQueryWrapperX<DeliveryTrackDO>().eq(DeliveryTrackDO::getNum, subscribePushResult.getNu()));

        //更新完后获取目前最新的一条物流轨迹
        DeliveryTrackDO lastDelivery = deliveryTrackService.getLatestByDelivery(new LambdaQueryWrapperX<DeliveryTrackDO>()
                .eq(DeliveryTrackDO::getNum, subscribePushResult.getNu())
                .orderByDesc(DeliveryTrackDO::getDeliveryTime)
                .last("limit 1"));

        for(DeliveryDO deliveryDO : deliveryDOS) {
            deliveryDO.setCom(subscribePushResult.getCom());
            deliveryDO.setNum(subscribePushResult.getNu());
            deliveryDO.setIsCheck(Integer.valueOf(subscribePushResult.getIscheck()));
            deliveryDO.setState(Integer.valueOf(subscribePushResult.getState()));
            //修改订单tip提示为最新的物流轨迹

            if (lastDelivery != null && StrUtil.isNotBlank(lastDelivery.getContent())) {
                tradeOrderService.updateOrderDeliveryTip(lastDelivery.getContent(), deliveryDO.getOrderNo());
            }
        }
        deliveryMapper.updateBatch(deliveryDOS, 10);

        return true;
    }

    /**
     * 根据物流单号查询物流
     * @param deliveryNum
     * @return
     */
    @Override
    public DeliveryRespVO getByDeliveryNum(String deliveryNum) {
        DeliveryDO deliveryDO = this.getOne(Wrappers.lambdaQuery(DeliveryDO.class).eq(DeliveryDO::getNum, deliveryNum));
        List<DeliveryProductSkuDO> skuList = deliveryProductSkuService.getListByDelivery(deliveryDO.getId());
        List<DeliveryTrackDO> trackList = deliveryTrackService.getListByDelivery(deliveryDO.getNum());

        return DeliveryConvert.INSTANCE.convert02(deliveryDO, skuList, trackList);
    }

    @Override
    public DeliveryRespVO getByDeliveryNumAndOrderNo(String deliveryNum, String orderNo) {
        DeliveryDO deliveryDO = this.getOne(Wrappers.lambdaQuery(DeliveryDO.class).eq(DeliveryDO::getNum, deliveryNum).eq(DeliveryDO::getOrderNo, orderNo));
        List<DeliveryProductSkuDO> skuList = deliveryProductSkuService.getListByDelivery(deliveryDO.getId());
        List<DeliveryTrackDO> trackList = deliveryTrackService.getListByDelivery(deliveryDO.getNum());

        return DeliveryConvert.INSTANCE.convert02(deliveryDO, skuList, trackList);
    }

    /**
     * 根据订单号查询物流
     * @param orderNo
     * @return
     */
    @Override
    public List<DeliveryRespVO> getByOrderNo(String orderNo) {
        List<DeliveryDO> deliveryDOList = this.list(Wrappers.lambdaQuery(DeliveryDO.class).eq(DeliveryDO::getOrderNo, orderNo));
        List<DeliveryRespVO> respList = new ArrayList<>();
        deliveryDOList.forEach(deliveryDO -> {
            List<DeliveryProductSkuDO> skuList = deliveryProductSkuService.getListByDelivery(deliveryDO.getId());
            List<DeliveryTrackDO> trackList = deliveryTrackService.getListByDelivery(deliveryDO.getNum());

            respList.add(DeliveryConvert.INSTANCE.convert02(deliveryDO, skuList, trackList));
        });

        return respList;
    }

    @Override
    public void importExcel(List<DeliveryImportExcelVO> list) {
        asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), AsyncTaskTypeEnum.ORDER_DELIVERY_IMPORT, TtlRunnable.get(() -> {
            doImportExcel(list);
        }));
    }

    public void doImportExcel(List<DeliveryImportExcelVO> list) {
        String taskId = AsyncFrontTaskContext.getTaskId();
        AsyncImportTask importTask = asyncFrontTaskUtils.getAsyncImportTask(taskId);

        if(CollUtil.isEmpty(list)) {
            asyncFrontTaskUtils.taskDone(importTask);
            return;
        }

        try {
            importTask.setTotal(list.size());
            List<AsyncImportRespExcelVO> importRespList = new ArrayList<>();

            AtomicInteger counter = new AtomicInteger(1);
            asyncFrontTaskUtils.updateTask(importTask);

            list.stream().forEach(deliveryImportExcelVO -> {
                counter.incrementAndGet();
                if(counter.get() % 10 == 0) {
                    asyncFrontTaskUtils.updateTask(importTask);
                }

                Boolean importColTypeError = true;
                try {
                    if(DeliveryTypeEnum.PRODUCT_SEND.getName().equals(deliveryImportExcelVO.getDeliveryType())){
                        DeliveryBindReqVO deliveryBindReqVO = new DeliveryBindReqVO();
                        deliveryBindReqVO.setOrderNo(deliveryImportExcelVO.getOrderNo());
                        deliveryBindReqVO.setNum(deliveryImportExcelVO.getDeliveryNum());
                        deliveryBindReqVO.setCompanyName(deliveryImportExcelVO.getDeliveryName());
                        importColTypeError = false;
                        productSend(deliveryBindReqVO, importTask, importRespList);
                    }
                    else if(DeliveryTypeEnum.SELF_SEND.getName().equals(deliveryImportExcelVO.getDeliveryType())){
                        DeliverySelfReqVO deliverySelfReqVO = new DeliverySelfReqVO();
                        deliverySelfReqVO.setOrderNo(deliveryImportExcelVO.getOrderNo());
                        deliverySelfReqVO.setDetail(deliveryImportExcelVO.getDeliveryContent());
                        importColTypeError = false;
                        selfSend(deliverySelfReqVO, importTask, importRespList);
                    }
                    else if(DeliveryTypeEnum.SERVICE_SEND.getName().equals(deliveryImportExcelVO.getDeliveryType())){
                        DeliveryServiceReqVO deliveryServiceReqVO = new DeliveryServiceReqVO();
                        deliveryServiceReqVO.setOrderNo(deliveryImportExcelVO.getOrderNo());
                        importColTypeError = false;
                        serviceSend(deliveryServiceReqVO, importTask, importRespList);
                    }
                    else {
                        importRespList.add(importTask.plusFailed(deliveryImportExcelVO.getOrderNo(), "订单发货方式错误"));
                    }
                } catch (Exception ex) {
                    if(importColTypeError){
                        importRespList.add(importTask.plusFailed(deliveryImportExcelVO.getOrderNo(), "导出Excel数据类型错误"));
                    }
                    else {
                        importRespList.add(importTask.plusFailed(deliveryImportExcelVO.getOrderNo(), ex.getMessage()));
                    }
                }
            });

            asyncFrontTaskUtils.updateTask(importTask);
            asyncFrontTaskUtils.importDone(taskId, importRespList);
        } catch (Exception e) {
            log.error("订单物流导入失败", e);
            asyncFrontTaskUtils.importFail();
        }
    }

    /**
     * 订阅更新
     * @param com
     * @param num
     * @return
     */
    public Boolean subscribeUpdate(String com, String num) {
        try {
            SubscribeParameters subscribeParameters = new SubscribeParameters();
            subscribeParameters.setCallbackurl(this.generateCallbackUrl());

            SubscribeParam subscribeParam = new SubscribeParam();
            subscribeParam.setParameters(subscribeParameters);
            subscribeParam.setCompany(com);
            subscribeParam.setNumber(num);
            subscribeParam.setKey(deliveryProperties.getAuthKey());

            SubscribeReq subscribeReq = new SubscribeReq();
            subscribeReq.setSchema(ApiInfoConstant.SUBSCRIBE_SCHEMA);
            subscribeReq.setParam(new Gson().toJson(subscribeParam));

            Subscribe subscribe = new Subscribe();
            SubscribeResp subscribeResp = subscribe.subscribe(subscribeReq);
            // 提交成功或重复订阅
            if(subscribeResp.getReturnCode().equals("200") || subscribeResp.getReturnCode().equals("501")){
                return true;
            }
        } catch (Exception e) {
            log.error("订单更新错误", e);
        }
        return false;
    }

    public String generateCallbackUrl() {
        String tcode = TenantIdUtils.encryptTenantId(TenantContextHolder.getTenantId());
        return String.format(SUBSCRIBE_UPDATE_CALLBACK_URL, mallProperties.getHostDomain(), tcode);
    }
}
