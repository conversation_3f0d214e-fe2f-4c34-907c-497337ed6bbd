package cn.iocoder.yudao.module.mall.trade.controller.admin.orderassets.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 订单明细固资信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TradeOrderItemAssetsPageReqVO extends PageParam {

    @Schema(description = "订单明细ID")
    private Long orderItemId;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "员工工号")
    private String userNo;

    /**
     * {@link cn.iocoder.yudao.module.mall.trade.enums.order.AssetStatusEnum}
     */
    @Schema(description = "审批状态：0-建档待确认")
    private Integer assetStatus;

    @Schema(description = "审批状态")
    private List<Integer> assetStatusList;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "外部系统编码")
    private String sysCode;

}
