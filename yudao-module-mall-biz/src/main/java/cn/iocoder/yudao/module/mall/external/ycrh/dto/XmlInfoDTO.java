package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class XmlInfoDTO {

    /**
     * 项目分类 1-商品 2-运费 3-费用缴纳 4-仪器使用费
     */
    @JsonProperty("xmType")
    private String projectType;

    /**
     * 部门编号
     */
    @JsonProperty("bmbh")
    private String departmentNo;

    /**
     * 售后接口中：商品编号(xmType为"1"时必填)
     */
    @JsonProperty("skuId")
    private String skuId;

    /**
     * 项目编号
     */
    @JsonProperty("xmbh")
    private String projectNo;

    /**
     * 项目负责人编号
     */
    @JsonProperty("xmfzrbh")
    private String chargeNo;

    /**
     * 使用金额
     */
    @JsonProperty("JE")
    private BigDecimal amount;

}
