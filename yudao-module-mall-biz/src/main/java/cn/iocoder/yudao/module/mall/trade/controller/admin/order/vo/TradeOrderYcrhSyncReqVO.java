package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Schema(description = "管理后台 - 交易订单的分页 Request VO")
@Data
public class TradeOrderYcrhSyncReqVO {

    @Schema(description = "订单提交时间开始")
    @NotNull
    private Date submitTimeBegin;

    @Schema(description = "订单提交时间结束")
    @NotNull
    private Date submitTimeEnd;

}
