package cn.iocoder.yudao.module.mall.framework;

import cn.iocoder.yudao.framework.jackson.core.databind.LocalDateTimeDeserializer;
import cn.iocoder.yudao.framework.jackson.core.databind.LocalDateTimeSerializer;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/7/15
 */
@Configuration
@Slf4j
public class JacksonConfiguration {
//    @Bean
//    public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter() {
//        Jackson2ObjectMapperBuilder builder = new Jackson2ObjectMapperBuilder()
//                .serializerByType(Long.class,ToStringSerializer.instance)
//                .serializerByType(Long.class,ToStringSerializer.instance)
//                .serializerByType(LocalDateTime.class,LocalDateTimeSerializer.INSTANCE)
//                .deserializerByType(LocalDateTime.class,LocalDateTimeDeserializer.INSTANCE);
//        return new MappingJackson2HttpMessageConverter(builder.build());
//    }

}
