package cn.iocoder.yudao.module.mall.product.controller.app.spu.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 商品spu信息
 * <AUTHOR>
 * @date 2023/9/21
 */
@Data
public class ProductSpuBaseOpenVO {

    /**
     * 商品 SPU 编号，自增
     */
    private Long spuId;
    /**
     * 供应商内部商品spuId
     */
    @NotBlank(message = "spuInnerId不能为空")
    @Size(min = 1, max = 50, message = "spuInnerId只允许为1至50个字符")
    private String spuInnerId;
    /**
     * 商品品牌编号
     */
    private Long brandId;
    /**
     * 商品品牌名称
     */
    @NotBlank(message = "brandName不能为空")
    @Size(min = 1, max = 50, message = "brandName只允许为1至50个字符")
    private String brandName;
    /**
     * 完整分类id -分隔
     */
    private String fullCategoryId;
    /**
     * 完整分类id /分隔
     */
    private String fullCategoryName;
    /**
     * 规格类型：0 单规格 1 多规格
     */
    @NotNull(message = "specType不能为空")
    private Integer specType;
    /**
     * 商品名称
     */
    @NotBlank(message = "spuName不能为空")
    @Size(max = 150, message = "spuName不能超过150个字符")
    private String spuName;

    /**
     * spu单位
     */
    @Size(max = 20, message = "unit不能超过20个字符")
    private String unit;
    /**
     * 描述
     */
    @NotBlank(message = "description不能为空")
    private String description;
    /**
     * H5端描述
     */
    private String descriptionH5;
    /**
     * 商品封面图片
     */
    @NotBlank(message = "picUrl不能为空")
    @Size(min = 1, max = 200, message = "picUrl不能超过200个字符")
    private String picUrl;
    /**
     * 商品轮播图片
     */
    @NotNull(message = "sliderPicUrls不能为空")
    private List<String> sliderPicUrls;
    /**
     * 一级分类id
     */
    @NotNull(message = "category1Id不能为空")
    private Long category1Id;
    /**
     * 二级分类id
     */
    @NotNull(message = "category2Id不能为空")
    private Long category2Id;
    /**
     * 三级分类id
     */
    private Long category3Id;
    /**
     * 一级分类名称
     */
    private String category1Name;
    /**
     * 二级分类名称
     */
    private String category2Name;
    /**
     * 三级分类名称
     */
    private String category3Name;
    /**
     * 状态：1：上架，0：下架
     */
    @NotNull(message = "status不能为空")
    private Integer status;

}
