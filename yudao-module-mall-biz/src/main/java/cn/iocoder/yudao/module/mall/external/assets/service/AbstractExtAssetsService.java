package cn.iocoder.yudao.module.mall.external.assets.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.AssetsConfigDO;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsCompleteDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsCreateDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsResp;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsStatusUpdateReq;
import cn.iocoder.yudao.module.mall.external.assets.enums.AssetsMethodEnum;
import cn.iocoder.yudao.module.mall.external.assets.util.LBJwtUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Slf4j
public abstract class AbstractExtAssetsService {

    private static final int API_TIME_OUT = 20 * 1000;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 客户端编码
     * @return
     */
    public abstract String getClientCode();

    /**
     * 是否需要轮询状态
     * @return
     */
    public boolean needPollStatus() {
        return false;
    }

    /**
     * 查询资产状态
     * @param orderNo
     * @param orderItemId
     * @return
     */
    public ExtAssetsStatusUpdateReq doPollStatus(String orderNo, Long orderItemId) {
        return null;
    }

    /**
     * 推送资产建档信息
     * @param data
     * @return
     */
    public abstract ExtAssetsResp<String> pushAssets(AssetsConfigDO config, ExtAssetsCreateDTO data);

    /**
     * 完成资产建档及相关信息填充
     * @param data
     * @return
     */
    public abstract boolean completeAssets(AssetsConfigDO config, ExtAssetsCompleteDTO data);

    /**
     * 推送固资建档信息
     * @param data
     * @return
     */
    protected ExtAssetsResp<String> doPushAssets(Object data, AssetsMethodEnum method, AssetsConfigDO config) {
        TypeReference<ExtAssetsResp<String>> typeReference = new TypeReference<ExtAssetsResp<String>>() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        return sendRequest(config, method, data, javaType, true);
    }

    /**
     * 完成固资建档信息
     * @param data
     * @return
     */
    protected boolean doCompleteAssets(ExtAssetsCompleteDTO data, AssetsMethodEnum method, AssetsConfigDO config) {
        TypeReference<ExtAssetsResp<Void>> typeReference = new TypeReference<ExtAssetsResp<Void>>() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        ExtAssetsResp<Void> resp = sendRequest(config, method, data, javaType, true);
        return resp.isSuccess();
    }

    private <T> ExtAssetsResp<T> sendRequest(AssetsConfigDO config, AssetsMethodEnum assetsMethodEnum, Object param, JavaType javaType, boolean checkFail) {
        ExtAssetsResp<T> resp = sendRequest(config, assetsMethodEnum, param, javaType);
        if (checkFail && !resp.isSuccess()) {
            throw exception(ErrorCodeConstants.ASSETS_FAIL, resp.getMsg());
        }

        return resp;
    }

    /**
     * 发送业财融合接口请求，通用逻辑封装
     *
     * @param config 配置信息
     * @param assetsMethodEnum 业务方法
     * @param param          业务数据
     * @param javaType
     * @return
     */
    private <T> ExtAssetsResp<T> sendRequest(AssetsConfigDO config, AssetsMethodEnum assetsMethodEnum, Object param, JavaType javaType) {
        try {
            String paramStr = JSON.toJSONString(param);
            String data = LBJwtUtil.encrty(paramStr, config.getKey1(), config.getKey2(), Long.valueOf(config.getKey3()));

            StringBuilder comUrl = new StringBuilder(config.getApiHost());
            comUrl.append(assetsMethodEnum.getMethodName());

            HttpRequest req = null;
            if(ObjectUtil.equal("get", assetsMethodEnum.getHttpMethod())) {
                comUrl.append("?token=");
                comUrl.append(data);
                req = HttpUtil.createGet(comUrl.toString()).timeout(API_TIME_OUT);
            } else {
                req = HttpUtil.createPost(comUrl.toString()).form("token", data).timeout(API_TIME_OUT);
            }
            int maxStrLength = 1000;

            log.info("ext-assets url: {}", comUrl);
            log.info("ext-assets {} param: {}", assetsMethodEnum.getMethodName(), StringUtils.abbreviate(paramStr, maxStrLength));
            log.info("ext-assets {} token: {}", assetsMethodEnum.getMethodName(), StringUtils.abbreviate(data, maxStrLength));
            HttpResponse response = req.execute();
            if (response.isOk()) {
                String body = response.body();
                log.info("ext-assets {} resp: {}", assetsMethodEnum.getMethodName(), StringUtils.abbreviate(body, maxStrLength));
                JSONObject respObj = JSONObject.parseObject(body);
                if (respObj.containsKey("code")) {
                    return objectMapper.readValue(body, javaType);
                } else {
                    log.error("ext-assets {} resp no code...", assetsMethodEnum.getMethodName());
                    throw exception(ErrorCodeConstants.ASSETS_FAIL,"返回格式不正确");
                }
            } else {
                log.error("ext-assets {} error: {}", assetsMethodEnum.getMethodName(), response.getStatus());
                throw exception(ErrorCodeConstants.ASSETS_FAIL,"结果异常");
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("ext-assets {} error", assetsMethodEnum.getMethodName(), e);
            throw exception(ErrorCodeConstants.ASSETS_FAIL,"系统异常");
        }
    }

}
