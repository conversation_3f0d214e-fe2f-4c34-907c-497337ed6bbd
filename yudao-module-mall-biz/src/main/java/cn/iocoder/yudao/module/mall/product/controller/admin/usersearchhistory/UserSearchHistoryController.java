package cn.iocoder.yudao.module.mall.product.controller.admin.usersearchhistory;


import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.usersearchhistory.vo.UserSearchHistoryPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.usersearchhistory.vo.UserSearchHistoryRespVO;
import cn.iocoder.yudao.module.mall.product.convert.searchhistory.ProductSearchHistoryConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.searchhistory.ProductSearchHistoryDO;
import cn.iocoder.yudao.module.mall.product.service.searchhistory.ProductSearchHistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

;

@Tag(name = "管理后台 - 用户搜索历史")
@RestController
@RequestMapping("/product/search-history")
@Validated
public class UserSearchHistoryController {

    @Resource
    private ProductSearchHistoryService productSearchHistoryService;


    /**
     *搜索历史列表
     *
     * @param reqVO
     * @return
     */
    @PostMapping("/page")
    @Operation(summary = "会员历史搜索列表")
    @PreAuthorize("@ss.hasPermission('product:search-history:query')")
    public CommonResult<PageResult<UserSearchHistoryRespVO>> getBillPage(@RequestBody @Valid UserSearchHistoryPageReqVO reqVO) {
        PageResult<ProductSearchHistoryDO> userSearchHistoryPage = productSearchHistoryService.getUserSearchHistoryPage(reqVO);
        return success(ProductSearchHistoryConvert.INSTANCE.convert(userSearchHistoryPage));
    }


    /**
     *删除单条记录
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除单条历史搜索记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:search-history:delete')")
    public CommonResult<Boolean> deleteUserSearchHistory(@RequestParam("id") Long id) {
        productSearchHistoryService.deleteById(id);
        return success(true);
    }




}
