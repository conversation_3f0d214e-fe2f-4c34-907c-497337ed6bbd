package cn.iocoder.yudao.module.mall.external.xy2.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class Xy2OrderDataDTO {
    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 三方订单编号
     */
    private String thirdOrderId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 订单总价
     */
    private String totalPrice;

    /**
     * 收货人
     */
    private String receiverName;

    /**
     * 收货电话
     */
    private String receiverMobile;

    /**
     * 收货地址
     */
    private String receiverAddress;

    /**
     * 供应商编号
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商品信息
     */
    private List<Xy2SkuDataDTO> skuData;
}
