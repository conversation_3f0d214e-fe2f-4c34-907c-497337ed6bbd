package cn.iocoder.yudao.module.mall.product.convert.brand;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.brand.AppOpenProductBrandDetailRespVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.brand.AppOpenProductBrandRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.brand.vo.ProductBrandCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.brand.vo.ProductBrandRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.brand.vo.ProductBrandUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.BrandSearchAggGoodsResp;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.brand.ProductBrandDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 品牌 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductBrandConvert {

    ProductBrandConvert INSTANCE = Mappers.getMapper(ProductBrandConvert.class);

    ProductBrandDO convert(ProductBrandCreateReqVO bean);

    ProductBrandDO convert(ProductBrandUpdateReqVO bean);

    ProductBrandRespVO convert(ProductBrandDO bean);

    AppOpenProductBrandDetailRespVO convert2(ProductBrandDO bean);

    List<ProductBrandRespVO> convertList(List<ProductBrandDO> list);

    PageResult<ProductBrandRespVO> convertPage(PageResult<ProductBrandDO> page);

    PageResult<AppOpenProductBrandRespVO> convertPage2(PageResult<ProductBrandDO> page);

    @Mappings(value = {
            @Mapping(source = "id", target = "brandId")
    })
    BrandSearchAggGoodsResp convertAggResp(ProductBrandDO productBrandDO);


    List<BrandSearchAggGoodsResp> convertAggResps(List<ProductBrandDO> productBrandDOs);

}
