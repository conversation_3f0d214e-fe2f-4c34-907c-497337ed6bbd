package cn.iocoder.yudao.module.mall.product.dal.mysql.productcomment;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentReplyDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商品评价回复 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductCommentReplyMapper extends BaseMapperX<ProductCommentReplyDO> {

    default PageResult<ProductCommentReplyDO> selectPage(ProductCommentReplyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductCommentReplyDO>()
                .eqIfPresent(ProductCommentReplyDO::getCommentId, reqVO.getCommentId())
                .eqIfPresent(ProductCommentReplyDO::getSupplierId, reqVO.getSupplierId())
                .likeIfPresent(ProductCommentReplyDO::getSupplierName, reqVO.getSupplierName())
                .eqIfPresent(ProductCommentReplyDO::getMemberId, reqVO.getMemberId())
                .likeIfPresent(ProductCommentReplyDO::getNickName, reqVO.getNickName())
                .eqIfPresent(ProductCommentReplyDO::getAvatar, reqVO.getAvatar())
                .eqIfPresent(ProductCommentReplyDO::getContent, reqVO.getContent())
                .eqIfPresent(ProductCommentReplyDO::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(ProductCommentReplyDO::getClientIp, reqVO.getClientIp())
                .eqIfPresent(ProductCommentReplyDO::getClientArea, reqVO.getClientArea())
                .eqIfPresent(ProductCommentReplyDO::getReportCount, reqVO.getReportCount())
                .betweenIfPresent(ProductCommentReplyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductCommentReplyDO::getId));
    }

}
