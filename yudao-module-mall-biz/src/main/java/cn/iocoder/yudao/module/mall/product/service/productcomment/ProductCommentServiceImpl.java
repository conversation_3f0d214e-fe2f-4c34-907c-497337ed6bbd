package cn.iocoder.yudao.module.mall.product.service.productcomment;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.ip.core.Area;
import cn.iocoder.yudao.framework.ip.core.utils.IPUtils;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.ProductCommentAuditReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.ProductCommentPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.productcomment.ProductCommentConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.productcomment.ProductCommentMapper;
import cn.iocoder.yudao.module.mall.product.enums.comment.ProductCommentAuditStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.mysql.order.TradeOrderItemMapper;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.COMMENT_AUDIT_STATUS_INVALID;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.COMMENT_NOT_EXISTS;

/**
 * 商品评价 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductCommentServiceImpl implements ProductCommentService {

    @Resource
    private ProductCommentMapper commentMapper;

    @Resource
    private MemberUserService memberUserService;

    @Resource
    private SupplierService supplierService;

    @Resource
    private TradeOrderItemMapper tradeOrderItemMapper;

    @Resource
    private ProductSkuService productSkuService;

    @Resource
    private TradeOrderService tradeOrderService;


    @Override
    public Long createComment(AppProductCommentCreateReqVO createReqVO, Long memberId) {
        MemberUserDO memberUser = memberUserService.getUser(memberId);
        Assert.notNull(memberUser,"会员不存在");
        TradeOrderDO tradeOrderDO = tradeOrderService.getOrderByNo(createReqVO.getOrderNo());
        Assert.notNull(memberUser,"订单不存在");
        ProductSkuDO productSkuDO = productSkuService.getSku(createReqVO.getSkuId());
        Assert.notNull(productSkuDO,"商品不存在");
        ProductCommentDO comment = ProductCommentConvert.INSTANCE.convert(createReqVO, memberUser, productSkuDO, tradeOrderDO);

        comment.setAuditStatus(ProductCommentAuditStatusEnum.NONE.getStatus());

        String clientIp = ServletUtils.getClientIP();
        if(StringUtils.isNotBlank(clientIp)) {
            comment.setClientIp(clientIp);
        }
        if(!clientIp.equals("127.0.0.1")
                && !clientIp.startsWith("192.168.")
                && !clientIp.startsWith("10.")){
            Area area = IPUtils.getArea(clientIp);
            if(area != null && StringUtils.isNotBlank(area.getName())) {
                comment.setClientArea(area.getName());
            }
        }

        commentMapper.insert(comment);
        tradeOrderItemMapper.completeComment(comment.getOrderId(), comment.getSkuId());
        return comment.getId();
    }

    @Override
    public void updateComment(AppProductCommentUpdateReqVO updateReqVO) {
        // 校验存在
        validateCommentExists(updateReqVO.getId());
        // 更新
        ProductCommentDO updateObj = ProductCommentConvert.INSTANCE.convert(updateReqVO);
        commentMapper.updateById(updateObj);
    }

    @Override
    public void deleteComment(Long id) {
        // 校验存在
        validateCommentExists(id);
        // 删除
        commentMapper.deleteById(id);
    }

    private void validateCommentExists(Long id) {
        if (commentMapper.selectById(id) == null) {
            throw exception(COMMENT_NOT_EXISTS);
        }
    }

    @Override
    public ProductCommentDO getComment(Long id) {
        return commentMapper.selectById(id);
    }

    @Override
    public List<ProductCommentDO> getCommentList(Collection<Long> ids) {
        return commentMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ProductCommentDO> getCommentPage(AppProductCommentPageReqVO pageReqVO) {
        return commentMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<ProductCommentDO> getProductCommentPage(AppProductCommentOrderPageReqVO pageReqVO) {
        return commentMapper.getProductCommentPage(pageReqVO);
    }

    @Override
    public PageResult<ProductCommentDO> getCommentPage(ProductCommentPageReqVO pageReqVO) {
        return commentMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProductCommentDO> getCommentList(AppProductCommentExportReqVO exportReqVO) {
        return commentMapper.selectList(exportReqVO);
    }

    @Override
    public void updateAuditStatus(ProductCommentAuditReqVO reqVO) {
        ProductCommentDO productComment = validateCommentAuditStatus(reqVO.getId());
        commentMapper.updateAuditStatus(productComment.getId(), productComment.getAuditStatus(),
                new ProductCommentDO().setAuditStatus(reqVO.getAuditStatus()));
    }

    @Override
    public void like(Long id) {
        ProductCommentDO productCommentDO = commentMapper.selectById(id);
        productCommentDO.setLikeCount(productCommentDO.getLikeCount() + 1);
        commentMapper.updateById(productCommentDO);
    }

    private ProductCommentDO validateCommentAuditStatus(Long id) {
        ProductCommentDO productCommentDO = commentMapper.selectById(id);
        Assert.notNull(productCommentDO,"评论不存在");
        Integer auditStatus = productCommentDO.getAuditStatus();
        // 校验订单是否是待发货状态
        if (ProductCommentAuditStatusEnum.NONE.equals(auditStatus)) {
            throw exception(COMMENT_AUDIT_STATUS_INVALID);
        }
        return productCommentDO;
    }

}
