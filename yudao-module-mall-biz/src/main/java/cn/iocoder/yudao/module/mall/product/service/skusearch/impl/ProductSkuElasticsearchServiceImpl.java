package cn.iocoder.yudao.module.mall.product.service.skusearch.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.convert.supplier.SupplierConvert;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.product.api.sku.ProductSkuApi;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.AreaDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.CanSaleRespDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.SkuBaseReqDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.Stock;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.area.AreaConvert;
import cn.iocoder.yudao.module.mall.product.convert.brand.ProductBrandConvert;
import cn.iocoder.yudao.module.mall.product.convert.category.ProductCategoryConvert;
import cn.iocoder.yudao.module.mall.product.convert.es.SkuGoodsPageItemConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.brand.ProductBrandDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.ProductSkuES;
import cn.iocoder.yudao.module.mall.product.dal.es.ProductSkuESRepository;
import cn.iocoder.yudao.module.mall.product.enums.es.SortTypeEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.brand.ProductBrandService;
import cn.iocoder.yudao.module.mall.product.service.category.ProductCategoryService;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.skusearch.ProductSkuElasticsearchService;
import cn.iocoder.yudao.module.mall.product.service.skustock.ProductSkuStockService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.dto.SkuPriceRespDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.*;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedNested;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.ScriptSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * <AUTHOR>
 * @date 2023/12/7
 */
@Service
@Slf4j
public class ProductSkuElasticsearchServiceImpl implements ProductSkuElasticsearchService {

    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;
    @Resource
    private ProductSkuESRepository productSkuESRepository;
    @Resource
    private ProductCategoryService productCategoryService;
    @Resource
    @Lazy
    private ProductSkuService productSkuService;
    @Resource
    private ProductSkuStockService productSkuStockService;
    @Resource
    @Lazy
    private ProductSkuApi productSkuApi;
    @Resource
    private SupplierService supplierService;
    @Resource
    @Lazy
    private VopGoodsService vopGoodsService;
    @Resource
    private ProductBrandService productBrandService;
    @Override
    public void save(ProductSkuES productSkuES) {
        productSkuESRepository.save(productSkuES);
    }
    @Override
    public void saveAll(List<ProductSkuES> productSkuESList) {
        productSkuESRepository.saveAll(productSkuESList);
    }

    @Override
    public GoodsSearchPageResultResp goodsSearchPageList(GoodsSearchReq req) {
        // 创建响应对象
        GoodsSearchPageResultResp resp = new GoodsSearchPageResultResp();

        // 创建 Elasticsearch 查询构建器
        NativeSearchQueryBuilder searchQueryBuilder = new NativeSearchQueryBuilder();
        searchQueryBuilder.withTrackTotalHits(true);

        // 构建ES查询条件
        buildESQueryByReq(searchQueryBuilder, req);

        // 设置分页
        PageResult<SkuGoodsPageItem> pageResult = new PageResult<>();
        pageResult.setPageSize(req.getPageSize());
        pageResult.setPageNum(req.getPageIndex());
        pageResult.setTotal(0L);
        // 分页从0开始
        PageRequest pageReq = PageRequest.of(req.getPageIndex() - 1, req.getPageSize());
        searchQueryBuilder.withPageable(pageReq);

        // 打印查询语句
//        log.debug("es-sort======{}", searchQueryBuilder.build().getElasticsearchSorts());
//        log.debug("es-query======{}", searchQueryBuilder.build().getQuery().toString());
//        log.debug("es-agg======{}", searchQueryBuilder.build().getAggregations().toString());

        // 执行查询
        SearchHits<ProductSkuES> searchHits = elasticsearchTemplate.search(searchQueryBuilder.build(), ProductSkuES.class);
        List<ProductSkuES> productSkuES = searchHits.getSearchHits().stream().map(SearchHit::getContent).collect(Collectors.toList());
        List<Long> skuIds = productSkuES.stream().map(item -> Long.valueOf(item.getSkuId())).collect(Collectors.toList());

        pageResult.setTotal(searchHits.getTotalHits());
        resp.setPageResult(pageResult);
        if(CollUtil.isNotEmpty(skuIds)) {
            List<AppProductSkuDetailRespVO> skuDetails = productSkuService.getProductSkuDetailListNoSort(skuIds);
            if(CollUtil.isNotEmpty(skuDetails)) {
                List<Stock> productSkuStocks = null;
                List<AppProductSkuDetailRespVO> enableSkuDetails = skuDetails.stream()
                        .filter(skuDetailRespVO -> ProductSpuStatusEnum.isEnable(skuDetailRespVO.getStatus()))
                        .collect(Collectors.toList());
                if(CollUtil.isNotEmpty(enableSkuDetails)) {
                    AreaDTO areaDTO = AreaConvert.INSTANCE.convertArea(req.getAreaIds());
                    // 查询京东商品可售状态及价格
                    fillStatusAndPrice4JD(enableSkuDetails, areaDTO, req);
                    Set<Long> enableSkuIds = enableSkuDetails.stream()
                            .filter(skuItem -> !skuItem.isNotOnSale())
                            .map(AppProductSkuDetailRespVO::getSkuId).collect(Collectors.toSet());

                    productSkuStocks = productSkuStockService.getProductSkuStocks(new ArrayList<>(enableSkuIds), areaDTO);
                }

                List<SkuGoodsPageItem> skuGoodsPageItems = SkuGoodsPageItemConvert.INSTANCE.convertList(skuDetails, productSkuStocks);
                pageResult.setList(skuGoodsPageItems);
            }
        }

        // 这里的聚合计划搜索结果的分组后得 分类，供应商，品牌，规格，方便进一步筛选
        AggregationsContainer<?> aggContainer = searchHits.getAggregations();
        if(aggContainer != null) {
            Aggregations aggResult = (Aggregations)aggContainer.aggregations();
            // 解析商品分类
            resp.setCategoryAggList(parseAggCategory(aggResult, req));
            // 解析品牌
            resp.setBrandAggList(parseAggBrand(aggResult, req));
            // 解析供应商
            resp.setSupplierAggList(parseAggSupplier(aggResult, req));
            // 解析规格
            resp.setSpecAggList(parseAggSpuSpecs(aggResult, req));
        } else {
            resp.setCategoryAggList(Collections.emptyList());
        }
        resp.setPageResult(pageResult);
        return resp;
    }

    private void fillStatusAndPrice4JD(List<AppProductSkuDetailRespVO> enableSkuDetails, AreaDTO areaDTO, GoodsSearchReq req) {
        SupplierDO jdSupplier = supplierService.getSupplierJD();
        if(jdSupplier == null || jdSupplier.getId() == null) {
            return;
        }

        // 查询京东商品可售状态以及价格
        List<AppProductSkuDetailRespVO> jdProductSkus = enableSkuDetails.stream()
                .filter(skuDetailRespVO -> jdSupplier.getId().equals(skuDetailRespVO.getSupplierId()))
                .collect(Collectors.toList());
        Set<String> enableJdSkuInnerIds = jdProductSkus.stream().map(AppProductSkuDetailRespVO::getSkuInnerId).collect(Collectors.toSet());

        //京东商品需要再次判断是否可购买
        if(CollUtil.isNotEmpty(jdProductSkus)) {
            Map<String, AppProductSkuDetailRespVO> skuDetailMap = convertMap(enableSkuDetails, AppProductSkuDetailRespVO::getSkuInnerId);
            List<SkuBaseReqDTO> skuBaseReqDTOS = jdProductSkus.stream().map(skuDetailRespVO -> new SkuBaseReqDTO()
                    .setSkuId(Long.valueOf(skuDetailRespVO.getSkuInnerId()))
                    .setSkuNumber(1)).collect(Collectors.toList());
            List<CanSaleRespDTO> skusAllSaleState = productSkuApi.getJdSkusAllSaleState(skuBaseReqDTOS, areaDTO);
            List<CanSaleRespDTO> noCanSaleRespDTOS = Optional.ofNullable(skusAllSaleState).orElse(Lists.newArrayList()).stream()
                    .filter(canSaleRespDTO -> !canSaleRespDTO.getCanPurchase()).collect(Collectors.toList());
            for (CanSaleRespDTO noCanSaleRespDTO : noCanSaleRespDTOS) {
                String noCanSaleSkuId = noCanSaleRespDTO.getSkuId().toString();
                enableJdSkuInnerIds.remove(noCanSaleSkuId);
                AppProductSkuDetailRespVO productSkuDetailRespVO = skuDetailMap.get(noCanSaleSkuId);
                if(productSkuDetailRespVO == null) {
                    continue;
                }
                productSkuDetailRespVO.setSaleStatus(0);
            }
        }

        // 实时查询京东商品价格
        if(CollUtil.isNotEmpty(enableJdSkuInnerIds)) {
            List<Long> enableSkuInnerIdList = enableJdSkuInnerIds.stream().map(Long::valueOf).collect(Collectors.toList());
            List<SkuPriceRespDTO> vopGoodsPriceList = vopGoodsService.getVopSellPrice(enableSkuInnerIdList);
            Map<Long, SkuPriceRespDTO> priceMap = convertMap(vopGoodsPriceList, SkuPriceRespDTO::getSkuId);
            jdProductSkus.forEach(productSkuDetail -> {
                if(NumberUtil.isNumber(productSkuDetail.getSkuInnerId())) {
                    Long innerId = Long.valueOf(productSkuDetail.getSkuInnerId());
                    if(priceMap.containsKey(innerId)) {
                        productSkuDetail.setSalePrice(priceMap.get(innerId).getSalePrice());
                        productSkuDetail.setMarketPrice(priceMap.get(innerId).getMarketPrice());
                    }
                }
            });
        }
    }

    private List<CategorySearchAggGoodsResp> parseAggCategory(Aggregations aggResult, GoodsSearchReq req) {
        if(!ObjectUtil.defaultIfNull(req.getWithAggCate(), true)) {
            return null;
        }

        ParsedLongTerms categoryIdTerms = (ParsedLongTerms)aggResult.asMap().get("categoryIds");
        List<? extends Terms.Bucket> buckets = categoryIdTerms.getBuckets();
        List<Long> categoryIds = buckets.stream().map(item -> (Long)item.getKey()).collect(Collectors.toList());
        // 去除无效分类ID
        categoryIds = categoryIds.stream().filter(cateId -> cateId > 0).collect(Collectors.toList());
        log.info("商品搜索推荐分类ID： {}", categoryIds);
        if(CollUtil.isEmpty(categoryIds)) {
            if(req.getCategoryId1() != null) {
                categoryIds.add(req.getCategoryId1());
            }
            if(req.getCategoryId2() != null) {
                categoryIds.add(req.getCategoryId2());
            }
            if(req.getCategoryId3() != null) {
                categoryIds.add(req.getCategoryId3());
            }
        }
        if(CollUtil.isNotEmpty(categoryIds)) {
            List<ProductCategoryDO> parentCategoryList = productCategoryService.getParentsByCategoryIds(categoryIds);
            return ProductCategoryConvert.INSTANCE.convertResps(parentCategoryList);
        }

        return null;
    }

    private List<BrandSearchAggGoodsResp> parseAggBrand(Aggregations aggResult, GoodsSearchReq req) {
        if(!ObjectUtil.defaultIfNull(req.getWithAggBrand(), true)) {
            return null;
        }

        ParsedLongTerms brandIdTerms = (ParsedLongTerms) aggResult.asMap().get("brandIds");
        List<? extends Terms.Bucket> brandBuckets = brandIdTerms.getBuckets();
        List<Long> brandIds = brandBuckets.stream().map(bucket -> (Long) bucket.getKey()).collect(Collectors.toList());
        log.info("商品搜索推荐品牌ID： {}", brandIds);
        if (CollUtil.isNotEmpty(brandIds)) {
            List<ProductBrandDO> brandList = productBrandService.getBrandList(brandIds);
            return ProductBrandConvert.INSTANCE.convertAggResps(brandList);
        }

        return null;
    }

    private List<SupplierSearchAggGoodsResp> parseAggSupplier(Aggregations aggResult, GoodsSearchReq req) {
        if(!ObjectUtil.defaultIfNull(req.getWithAggSup(), true)) {
            return null;
        }

        ParsedLongTerms supplierIdTerms = (ParsedLongTerms) aggResult.asMap().get("supplierIds");
        List<? extends Terms.Bucket> supplierBuckets = supplierIdTerms.getBuckets();
        List<Long> supplierIds = supplierBuckets.stream().map(item -> (Long) item.getKey()).collect(Collectors.toList());
        log.info("商品搜索推荐供应商ID： {}", supplierIds);
        if (CollUtil.isNotEmpty(supplierIds)) {
            List<SupplierDO> supplierList = supplierService.getSupplierList(supplierIds);
            return SupplierConvert.INSTANCE.convertRsps(supplierList);
        }

        return null;
    }

    private List<GoodsSearchSpecRespVO> parseAggSpuSpecs(Aggregations aggResult, GoodsSearchReq req) {
        if(!ObjectUtil.defaultIfNull(req.getWithAggSpec(), false)) {
            return null;
        }

        ParsedNested specAgg = aggResult.get("spec_agg");
        if (specAgg == null) return null;

        ParsedStringTerms specNames = specAgg.getAggregations().get("spec_names");
        List<Terms.Bucket> list = specNames.getBuckets().stream().filter(nameBucket -> ObjectUtil.notEqual(nameBucket.getKeyAsString(), "空"))
                .collect(Collectors.toList());

        return list.stream()
                .map(nameBucket -> {
                GoodsSearchSpecRespVO specRespVO = new GoodsSearchSpecRespVO();
                specRespVO.setSpecName(nameBucket.getKeyAsString());

                ParsedStringTerms specValues = nameBucket.getAggregations().get("spec_values");
                List<String> valueBuckets = specValues.getBuckets().stream()
                        .map(MultiBucketsAggregation.Bucket::getKeyAsString)
                        .collect(Collectors.toList());
                specRespVO.setSpecValues(valueBuckets);

                return specRespVO;
            }).collect(Collectors.toList());
    }

    /**
     * 针对请求参数构建ES的查询条件
     * @param searchQueryBuilder
     * @param req
     */
    private void buildESQueryByReq(NativeSearchQueryBuilder searchQueryBuilder, GoodsSearchReq req) {
        // 主布尔查询（包含查询和过滤逻辑）
        BoolQueryBuilder mainBoolQuery = QueryBuilders.boolQuery();

        // 独立过滤条件构建器（不参与评分）
        BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();

        // 租户ID过滤（必须生效）
        Long tenantId = TenantContextHolder.getTenantId();
        if (tenantId != null) {
            filterBuilder.must(QueryBuilders.termQuery("tenantId", tenantId));
        }

        // 添加过滤条件（品牌/供应商/分类等）
        addFilterConditions(filterBuilder, req);

        // 价格区间过滤（使用filter提升性能）
        handlePriceRange(filterBuilder, req);

        // 关键词搜索（参与评分）
        handleKeywordSearch(mainBoolQuery, req);

        // 整合过滤条件到主查询
        mainBoolQuery.filter(filterBuilder);

        // 设置主查询
        searchQueryBuilder.withQuery(mainBoolQuery);

        // 后续聚合和排序
        setupAggregations(searchQueryBuilder, req);
        setupSorting(searchQueryBuilder, req);
    }

    private void addFilterConditions(BoolQueryBuilder queryBuilder, GoodsSearchReq req) {
        // 添加品牌、供应商、分类等过滤条件
        if (req.getBrandId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("brandId", req.getBrandId()));
        }
        if (req.getSupplierId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("supplierId", req.getSupplierId()));
        }
        if (req.getCategoryId1() != null) {
            queryBuilder.must(QueryBuilders.termQuery("categoryId1", req.getCategoryId1()));
        }
        if (req.getCategoryId2() != null) {
            queryBuilder.must(QueryBuilders.termQuery("categoryId2", req.getCategoryId2()));
        }
        if (req.getCategoryId3() != null) {
            queryBuilder.must(QueryBuilders.termQuery("categoryId3", req.getCategoryId3()));
        }
        if (CollUtil.isNotEmpty(req.getTagIds())) {
            queryBuilder.must(QueryBuilders.termsQuery("tagIds", req.getTagIds()));
        }

        // 针对经费卡内嵌规则的搜索条件处理...
        // 添加SKU直接过滤条件
        if (CollUtil.isNotEmpty(req.getSkuIds())) {
            queryBuilder.must(QueryBuilders.termsQuery("skuId", req.getSkuIds()));
        } else if (CollUtil.isNotEmpty(req.getOrderedSkus())) {
            queryBuilder.must(QueryBuilders.termsQuery("skuId", req.getOrderedSkus()));
        }

        BoolQueryBuilder categoryOrQuery = QueryBuilders.boolQuery();
        // 添加分类ID数组过滤条件
        if (CollUtil.isNotEmpty(req.getCategoryId1s())) {
            categoryOrQuery.should(QueryBuilders.termsQuery("categoryId1", req.getCategoryId1s()));
        }
        if (CollUtil.isNotEmpty(req.getCategoryId2s())) {
            categoryOrQuery.should(QueryBuilders.termsQuery("categoryId2", req.getCategoryId2s()));
        }
        if (CollUtil.isNotEmpty(req.getCategoryId3s())) {
            categoryOrQuery.should(QueryBuilders.termsQuery("categoryId3", req.getCategoryId3s()));
        }

        // 只有至少有一个分类条件存在时才添加OR查询
        if (categoryOrQuery.hasClauses()) {
            queryBuilder.must(categoryOrQuery);
        }

        // 商品规格
        if(ObjectUtil.defaultIfNull(req.getWithAggSpec(), false) && CollUtil.isNotEmpty(req.getSpecKVS())) {
            BoolQueryBuilder specFilter = QueryBuilders.boolQuery();
            for(String kv : req.getSpecKVS()) {
                if(kv.contains(":")) {
                    String[] secs = kv.split(":");
                    String[] parts = kv.split(":", 2); // 防止值中带 : 被误切
                    String specName = parts[0].trim();
                    String specValue = parts[1].trim();

                    // 每个规格构建一个独立的 nested 查询
                    BoolQueryBuilder singleSpecQuery = QueryBuilders.boolQuery()
                            .must(QueryBuilders.termQuery("spuSpecs.specName", specName))
                            .must(QueryBuilders.termQuery("spuSpecs.specValue", specValue));

                    // 添加到外层 must 中
                    specFilter.must(QueryBuilders.nestedQuery("spuSpecs", singleSpecQuery, ScoreMode.None));
                }
            }

            queryBuilder.must(specFilter);
        }
    }

    private void handleKeywordSearch(BoolQueryBuilder queryBuilder, GoodsSearchReq req) {
        String keyword = req.getKeyword();
        if (StrUtil.isNotBlank(keyword)) {
            // 处理数字搜索（ID精确匹配）
            if (StrUtil.isNumeric(keyword)) {
                // 数字策略：优先ID精确匹配
                String[] idFields = {"skuId", "skuInnerId"};
                MultiMatchQueryBuilder idQuery = QueryBuilders.multiMatchQuery(keyword, idFields)
                        .operator(Operator.AND)
                        .minimumShouldMatch("100%");

                // 短数字补充模糊匹配
                if (keyword.length() <= 10) {
                    BoolQueryBuilder fallbackQuery = QueryBuilders.boolQuery()
                            .should(idQuery)
                            .should(buildTextQuery(keyword))
                            .minimumShouldMatch(1);
                    queryBuilder.must(fallbackQuery);
                } else {
                    queryBuilder.must(idQuery);
                }
            } else {
                queryBuilder.must(buildTextQuery(keyword));
            }
        }
    }

    private QueryBuilder buildTextQuery(String keyword) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 主搜索：多字段带权重匹配
        Map<String, Float> weightedFields = new LinkedHashMap<>();
        weightedFields.put("skuName", 4.0f);
        weightedFields.put("skuInnerId", 4.0f);
        weightedFields.put("sellPoint", 5.0f);
        weightedFields.put("brandName", 2.0f);
        weightedFields.put("categoryName1", 1.8f);
        weightedFields.put("categoryName2", 1.5f);
        weightedFields.put("categoryName3", 1.2f);
        weightedFields.put("supplierName", 1.0f);
        weightedFields.put("specValues", 0.8f);

        MultiMatchQueryBuilder mmqb = new MultiMatchQueryBuilder(keyword);
        weightedFields.forEach(mmqb::field);
        mmqb.type(MultiMatchQueryBuilder.Type.BEST_FIELDS)
                .minimumShouldMatch("75%");

        boolQuery.must(mmqb);
        // 短词补充：N-Gram匹配（需索引预处理）
        if (keyword.length() <= 4) {
            boolQuery.should(QueryBuilders.matchQuery("skuName.ngram", keyword).boost(0.3f));
        }

        return boolQuery;
    }

    private void handlePriceRange(BoolQueryBuilder queryBuilder, GoodsSearchReq req) {
        if (req.getMinPrice() != null || req.getMaxPrice() != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("salePrice");
            if (req.getMinPrice() != null) {
                rangeQueryBuilder.gte(req.getMinPrice());
            }
            if (req.getMaxPrice() != null) {
                rangeQueryBuilder.lte(req.getMaxPrice());
            }
            queryBuilder.must(rangeQueryBuilder);
        }
    }

    private void setupAggregations(NativeSearchQueryBuilder searchQueryBuilder, GoodsSearchReq req) {
        if(ObjectUtil.defaultIfNull(req.getWithAggCate(), true)) {
            TermsAggregationBuilder categoryBuilder = AggregationBuilders.terms("categoryIds")
                    .field(req.categoryIsAllNull() ? "categoryId1" : (req.getCategoryId1() != null ? "categoryId2" : "categoryId3"))
                    .order(BucketOrder.count(false))
                    .size(req.categoryIsAllNull() ? 30 : (50));
            searchQueryBuilder.withAggregations(categoryBuilder);
        }

        if(ObjectUtil.defaultIfNull(req.getWithAggBrand(), true)) {
            TermsAggregationBuilder brandAggregationBuilder = AggregationBuilders.terms("brandIds").field("brandId").order(BucketOrder.count(false)).size(30);
            searchQueryBuilder.withAggregations(brandAggregationBuilder);
        }

        if(ObjectUtil.defaultIfNull(req.getWithAggSup(), true)) {
            TermsAggregationBuilder supplierAggregationBuilder = AggregationBuilders.terms("supplierIds").field("supplierId").order(BucketOrder.count(false)).size(30);
            searchQueryBuilder.withAggregations(supplierAggregationBuilder);
        }


        if(ObjectUtil.defaultIfNull(req.getWithAggSpec(), false)) {
            searchQueryBuilder.withAggregations(AggregationBuilders.nested("spec_agg", "spuSpecs")
                    .subAggregation(AggregationBuilders.terms("spec_names").field("spuSpecs.specName").order(BucketOrder.count(false)).size(10)
                            .subAggregation(AggregationBuilders.terms("spec_values").field("spuSpecs.specValue").order(BucketOrder.count(false)).size(20))
                    ));
        }
    }

    private void setupSorting(NativeSearchQueryBuilder searchQueryBuilder, GoodsSearchReq req) {
        // （按给定的 skuId 数组顺序排序）
        if(CollUtil.isNotEmpty(req.getOrderedSkus())) {
            // 使用 ScriptSort，按 skuId 在给定数组中的顺序排序
            Script script = new Script(org.elasticsearch.script.ScriptType.INLINE, "painless",
                    "def idArray = params.idArray;def skuIdStr = doc['skuId'].value;def skuId = skuIdStr;return idArray.indexOf(skuId);",
                    Collections.singletonMap("idArray", req.getOrderedSkus()));

            ScriptSortBuilder scriptSortBuilder = SortBuilders.scriptSort(script, ScriptSortBuilder.ScriptSortType.NUMBER)
                    .order(SortOrder.ASC); // 索引越小越靠前
            searchQueryBuilder.withSorts(scriptSortBuilder);
        } else {
            // 1，相关度排序
            searchQueryBuilder.withSorts(SortBuilders.scoreSort().order(SortOrder.DESC));
            // 2，业务字段排序
            if (req.getSortType() != null) {
                SortTypeEnum sortTypeEnum = SortTypeEnum.fromCode(req.getSortType());
                if (sortTypeEnum != null) {
                    FieldSortBuilder fieldSortBuilder = SortBuilders.fieldSort(sortTypeEnum.getField()).order(SortOrder.fromString(sortTypeEnum.getSort()));
                    searchQueryBuilder.withSorts(fieldSortBuilder);
                }
            }
            // 3，供应商权重
            if(req.getSupplierId() == null) {
                searchQueryBuilder.withSorts(SortBuilders.fieldSort("supplierWeight").order(SortOrder.DESC));
            }
            // 4,避免结果全是一个供应商或都同一批商品，再加一个随机数排序
            searchQueryBuilder.withSorts(SortBuilders.fieldSort("randSeq"));
        }
    }

    @Override
    public void deleteAll(List<Long> skuIds) {
        if(CollectionUtils.isNotEmpty(skuIds)) {
            try {
                productSkuESRepository.deleteAllById(skuIds);
            } catch(Exception e) {
                log.info("商品索引删除失败，skuId:");
            }
        }
    }

    @Override
    public void deleteBySupplierId(Long supplierId) {
        if(supplierId != null) {
            try {
                Criteria criteria = new Criteria("supplierId").is(supplierId);
                CriteriaQuery query = new CriteriaQuery(criteria);
                elasticsearchTemplate.delete(query, ProductSkuES.class);
            } catch(Exception e) {
                log.info("商品索引删除失败，supplierId: {}", supplierId);
            }
        }
    }

    @Override
    public void deleteAllOfTenant(Long tenantId) {
        if(tenantId != null) {
            try {
                Criteria criteria = new Criteria("tenantId").is(tenantId);
                CriteriaQuery query = new CriteriaQuery(criteria);
                elasticsearchTemplate.delete(query, ProductSkuES.class);
            } catch(Exception e) {
                log.info("商品索引删除失败，tenantId: {}", tenantId);
            }
        }
    }

}
