package cn.iocoder.yudao.module.mall.product.controller.app.wish;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo.ProductWishPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.wish.vo.AppProductWishCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.wish.vo.AppProductWishRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.wish.vo.AppProductWishUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.convert.wish.ProductWishConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.wish.ProductWishDO;
import cn.iocoder.yudao.module.mall.product.service.wish.ProductWishService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.WISH_NOT_EXISTS;

@Tag(name = "用户 APP - 心愿单")
@RestController
@RequestMapping("/product/wish")
@Validated
public class AppProductWishController {

    @Resource
    private ProductWishService wishService;
    @Resource
    private MemberUserService memberUserService;

    @PostMapping("/create")
    @Operation(summary = "创建心愿单")
    @PreAuthenticated
    public CommonResult<Long> createWish(@Valid @RequestBody AppProductWishCreateReqVO createReqVO) {
        Long userId = getLoginUserId();
        MemberUserDO user = memberUserService.getUser(userId);
        createReqVO.setUserId(userId);
        createReqVO.setUserName(user.getNickname());

        return success(wishService.createWish(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新心愿单")
    @PreAuthenticated
    public CommonResult<Boolean> updateWish(@Valid @RequestBody AppProductWishUpdateReqVO updateReqVO) {
        validatePermission(updateReqVO.getId());
        wishService.updateWish(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除心愿单")
    @PreAuthenticated
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteWish(@RequestParam("id") Long id) {
        validatePermission(id);
        wishService.deleteWish(id);
        return success(true);
    }

    private void validatePermission(Long id) {
        ProductWishDO productWishDO = wishService.getWish(id);
        if(productWishDO == null) {
            throw exception(WISH_NOT_EXISTS);
        }
        Long userId = getLoginUserId();
        if(!userId.equals(productWishDO.getUserId())) {
            throw exception(WISH_NOT_EXISTS);
        }
    }

    @GetMapping("/get")
    @Operation(summary = "获得心愿单")
    @PreAuthenticated
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppProductWishRespVO> getWish(@RequestParam("id") Long id) {
        validatePermission(id);
        ProductWishDO wish = wishService.getWish(id);
        return success(ProductWishConvert.INSTANCE.convert02(wish));
    }

    @GetMapping("/page")
    @Operation(summary = "获得心愿单分页")
    @PreAuthenticated
    public CommonResult<PageResult<AppProductWishRespVO>> getWishPage(@Valid ProductWishPageReqVO pageVO) {
        Long userId = getLoginUserId();
        pageVO.setUserId(userId);
        PageResult<ProductWishDO> pageResult = wishService.getWishPage(pageVO);
        return success(ProductWishConvert.INSTANCE.convertPage02(pageResult));
    }


}
