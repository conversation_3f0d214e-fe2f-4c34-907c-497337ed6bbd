package cn.iocoder.yudao.module.mall.member.service.address;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.redis.util.RedisUtils;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.common.AppOpenAreaReqVO;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.AddressAreaRespVO;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.AppAddressQueryVO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AddressDO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AreaDO;
import cn.iocoder.yudao.module.mall.member.dal.mysql.address.AreaMapper;
import cn.iocoder.yudao.module.mall.member.enums.AddressLevelEnum;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopAddressService;
import cn.iocoder.yudao.module.mall.vop.service.VopConfigService;
import com.google.common.collect.Lists;
import com.jd.open.api.sdk.domain.vopdz.ConvertAddressOpenProvider.response.convertFourAreaByDetailStr.QueryAreaFourIdOpenResp;
import com.jd.open.api.sdk.domain.vopdz.QueryAddressOpenProvider.response.queryJdAreaIdList.QueryJdAreaIdListOpenResp;
import com.jd.open.api.sdk.response.vopdz.VopAddressConvertFourAreaByDetailStrResponse;
import com.jd.open.api.sdk.response.vopdz.VopAddressQueryJdAreaIdListResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.yudao.module.mall.basis.dal.redis.RedisKeyConstants.MALL_AREA;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/9/18 13:48
 */
@Service
@Validated
@Slf4j
public class AreaServiceImpl implements AreaService {

    @Resource
    private AreaMapper areaMapper;
    @Resource
    private VopConfigService vopConfigService;
    @Resource
    private VopAddressService vopAddressService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private RedissonClient redissonClient;

    private volatile List<AreaDO> cacheList = null;
    private final Object lock = new Object();

    private List<AreaDO> getCacheList() {
        if (cacheList == null) {
            synchronized (lock) {
                if (cacheList == null) {
                    log.info("初始化区域缓存...");
                    cacheList = areaMapper.selectList();
                    log.info("区域缓存初始化完成，共{}条记录", getCacheList().size());
                }
            }
        }
        return cacheList;
    }

    // 清除缓存的方法
    public void clearCache() {
        synchronized (lock) {
            cacheList = null;
        }
    }

    @Override
    public List<AreaDO> getAreas(AppOpenAreaReqVO param) {
        List<AreaDO> allAreaList = getCacheList();
        List<AreaDO> areaList = null;
        if (param.getAreaId() == null) {
            areaList = allAreaList.stream().filter(item -> Objects.equals(param.getLevel(),
                            item.getLevel())).collect(Collectors.toList());
        } else {
            areaList = allAreaList.stream().filter(item -> Objects.equals(param.getLevel(), item.getLevel())
                            && Objects.equals(param.getAreaId(), item.getParentId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(areaList)) {
            return Collections.emptyList();
        }

        return areaList;
    }

    @Override
    public AddressDO parseFullName(String fullAreaName) {
        // 判断京东VOP是否打开，如果打开直接通过京东接口查询
        if(vopConfigService.isVopEnabled()) {
            return parseAddressByJd(fullAreaName);
        }

        List<AreaDO> allAreaList = getCacheList();
        List<AreaDO> hitAreaList = new ArrayList<>();
        allAreaList.forEach(area -> {
            if(fullAreaName.contains(area.getName())) {
                hitAreaList.add(area);
            }
        });
        if(CollUtil.isEmpty(hitAreaList)) {
            return null;
        }
        hitAreaList.sort(Comparator.comparingInt(AreaDO::getLevel));
        Map<Integer, AreaDO> hitAreaMap = convertMap(hitAreaList, AreaDO::getLevel);

        AddressDO addressDO = new AddressDO();
        Integer level1 = AddressLevelEnum.PROVINCE.getCode();
        Integer level2 = AddressLevelEnum.CITY.getCode();
        Integer level3 = AddressLevelEnum.COUNTRY.getCode();
        Integer level4 = AddressLevelEnum.TOWN.getCode();
        if(hitAreaMap.containsKey(level1)) {
            addressDO.setProvinceId(hitAreaMap.get(level1).getId());
            addressDO.setProvinceName(hitAreaMap.get(level1).getName());
        }
        if(hitAreaMap.containsKey(level2)) {
            addressDO.setCityId(hitAreaMap.get(level2).getId());
            addressDO.setCityName(hitAreaMap.get(level2).getName());
        }
        if(hitAreaMap.containsKey(level3)) {
            addressDO.setTownId(hitAreaMap.get(level3).getId());
            addressDO.setTownName(hitAreaMap.get(level3).getName());
        }
        if(hitAreaMap.containsKey(level4)) {
            addressDO.setCountyId(hitAreaMap.get(level4).getId());
            addressDO.setCountyName(hitAreaMap.get(level4).getName());
        }

        return addressDO;
    }

    private AddressDO parseAddressByJd(String fullAreaName) {
        try {
            VopAddressConvertFourAreaByDetailStrResponse response = vopAddressService.convertFourAreaByDetailStr(fullAreaName);
            QueryAreaFourIdOpenResp fourIdOpenResp = response.getOpenRpcResult().getResult();
            if (fourIdOpenResp == null) {
                return null;
            }
            AddressDO addressDO = new AddressDO();
            addressDO.setCityId(fourIdOpenResp.getCityId());
            addressDO.setCityName(fourIdOpenResp.getCityName());
            addressDO.setTownId(fourIdOpenResp.getTownId());
            addressDO.setTownName(fourIdOpenResp.getTownName());
            addressDO.setCountyId(fourIdOpenResp.getCountyId());
            addressDO.setCountyName(fourIdOpenResp.getCountyName());
            addressDO.setProvinceId(fourIdOpenResp.getProvinceId());
            addressDO.setProvinceName(fourIdOpenResp.getProvinceName());
            return addressDO;
        } catch (Exception e) {
            log.error("parseAddressByJd error: ", e.getMessage());
        }

        return null;
    }

    @Override
    public List<AddressAreaRespVO> getAreas(AppAddressQueryVO queryVO) {
        // 判断京东VOP是否打开，如果打开直接通过京东接口查询
        if(vopConfigService.isVopEnabled()) {
            return getJdAreas(queryVO);
        }

        List<AreaDO> allAreaList = getCacheList();
        List<AreaDO> areaDOS = null;
        if(ObjectUtil.equal(queryVO.getAddressLevel(), AddressLevelEnum.PROVINCE)) {
            areaDOS = allAreaList.stream().filter(area -> area.getLevel().equals(AddressLevelEnum.PROVINCE.getCode())).collect(Collectors.toList());
        } else if(queryVO.getAreaId() != null) {
            areaDOS = allAreaList.stream().filter(area -> queryVO.getAreaId().equals(area.getParentId())).collect(Collectors.toList());
        }
        if(areaDOS == null) {
            return null;
        }
        areaDOS.sort(Comparator.comparingLong(AreaDO::getId));
        return areaDOS.stream().map(areaInfo -> new AddressAreaRespVO().setAreaId(areaInfo.getId()).setAreaName(areaInfo.getName())).collect(Collectors.toList());
    }

    private List<AddressAreaRespVO> getJdAreas(AppAddressQueryVO queryVO) {
        String lockKey = String.format("lock:area:%d:%d", queryVO.getAddressLevel().getCode(), queryVO.getAreaId());
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        try {
            String key = String.format("%s:%d:%d", MALL_AREA, queryVO.getAddressLevel().getCode(), queryVO.getAreaId());
            List<AddressAreaRespVO> list = redisUtils.get(key);
            if(list == null) {
                VopAddressQueryJdAreaIdListResponse response = vopAddressService.queryJdAreaIdList(queryVO.getAddressLevel().getCode(), queryVO.getAreaId());
                if (response != null && response.getOpenRpcResult() != null && response.getOpenRpcResult().getResult() != null) {
                    QueryJdAreaIdListOpenResp result = response.getOpenRpcResult().getResult();
                    list = Optional.ofNullable(result.getAreaInfoList()).orElse(Lists.newArrayList())
                            .stream().map(areaInfo -> new AddressAreaRespVO().setAreaId(areaInfo.getAreaId()).setAreaName(areaInfo.getAreaName()))
                            .collect(Collectors.toList());

                    redisUtils.set(key, list, 7, TimeUnit.DAYS);
                    return list;
                }
            } else {
                return list;
            }
        } finally {
            if(lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return Lists.newArrayList();
    }

    @Override
    public String validateAreaFullLevel(Long provinceId, Long cityId, Long countyId, Long townId) {
        List<AreaDO> allAreaList = getCacheList();
        Map<Long, AreaDO> areaMap = convertMap(allAreaList, AreaDO::getId);
        if(areaMap == null) {
            return null;
        }
        if(!areaMap.containsKey(provinceId)) {
            return "省级地址无效";
        }
        if(!areaMap.containsKey(cityId)) {
            return "市级地址无效";
        }
        if(!areaMap.containsKey(countyId)) {
            return "区级地址无效";
        }
        if(townId != null && !areaMap.containsKey(townId)) {
            return "乡镇地址无效";
        }
        return null;
    }

}
