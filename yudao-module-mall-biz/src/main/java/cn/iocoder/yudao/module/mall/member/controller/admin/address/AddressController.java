package cn.iocoder.yudao.module.mall.member.controller.admin.address;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.AddressAreaRespVO;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.AppAddressQueryVO;
import cn.iocoder.yudao.module.mall.member.service.address.AddressService;
import cn.iocoder.yudao.module.mall.member.service.address.AreaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 用户收货地址")
@RestController
@RequestMapping("/member/address")
@Validated
@Slf4j
public class AddressController {

    @Resource
    private AddressService addressService;
    @Resource
    private AreaService areaService;

    /**
     * 获取省市区地址
     * @param param
     * @return
     */
    @PostMapping("/getAreas")
    @Operation(summary = "获取省市区地址")
    public CommonResult<List<AddressAreaRespVO>> getAreas(@RequestBody AppAddressQueryVO param) {
        try {
            return success(areaService.getAreas(param));
        } catch (Exception e) {
            log.error("获取地址异常...", e);
        }

        return success(new ArrayList<>());
    }
}
