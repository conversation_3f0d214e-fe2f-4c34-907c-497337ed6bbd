package cn.iocoder.yudao.module.mall.trade.controller.admin.orderassets.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.iocoder.yudao.module.mall.trade.enums.order.AssetStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单明细固资信息 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class TradeOrderAssetsDetailVO {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单明细ID")
    private Long orderItemId;

    @Schema(description = "数量")
    @Excel(name = "商品数量", orderNum = "1", width = 15)
    private Integer quantity;

    @Schema(description = "总金额")
    @Excel(name = "总金额", orderNum = "2", width = 15)
    private BigDecimal totalPrice;

    @Schema(description = "验收单号")
    @Excel(name = "验收单号", orderNum = "3", width = 25)
    private String assetAcceptNo;

    @Schema(description = "资产编号")
    @Excel(name = "资产编号", orderNum = "4", width = 25)
    private String assetNo;

    @Schema(description = "审批人")
    @Excel(name = "审批人", orderNum = "5", width = 15)
    private String approveBy;

    /**
     * 参考 {@link AssetStatusEnum}
     */
    @Schema(description = "审批状态")
    @Excel(name = "审批状态", orderNum = "6", replace = {"建档待处理_0", "建档已提交_1", "无须建档_2", "建档中_3", "建档成功_4", "建档失败_5"}, width = 15)
    private Integer approveStatus;

    @Schema(description = "审批备注")
    @Excel(name = "审批备注", orderNum = "7", width = 25)
    private String approveMemo;

    @Schema(description = "审批时间")
    @Excel(name = "审批时间", orderNum = "8", width = 25)
    private LocalDateTime approveTime;

    @Schema(description = "6大类资产类别代码")
    @Excel(name = "6大类资产类别代码", orderNum = "9", width = 20)
    private String category6;

    @Schema(description = "16大类资产类别代码")
    @Excel(name = "16大类资产类别代码", orderNum = "10", width = 20)
    private String category16;

    @Schema(description = "资产业务分类")
    @Excel(name = "资产业务分类", orderNum = "11", width = 20)
    private String businessClass;

}
