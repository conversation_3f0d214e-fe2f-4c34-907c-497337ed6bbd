package cn.iocoder.yudao.module.mall.external.sso.ycrh.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.HttpRequest;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.SsoConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.integration.SsoConfigService;
import cn.iocoder.yudao.module.mall.external.sso.ycrh.dto.SsoYcrhRespDTO;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 业财融合SSO统一认证处理
 */
@Service
@Slf4j
public class SsoYcrhService {

    @Resource
    private SsoConfigService ssoConfigService;

    /**
     * 校验token并获取员工编号
     * @param token
     * @return
     */
    public SsoYcrhRespDTO validateToken(String token) {
        Assert.notBlank(token, "token不能为空");
        SsoConfigDO ssoConfigDO = ssoConfigService.getSsoConfig();
        String url = ssoConfigDO.getTokenCheckUrl();
        Assert.notBlank(url, "token校验URL不能为空");

        Map<String, Object> params = new HashMap<>();
        params.put("token", token);
        String response = HttpRequest.get(url).form(params).timeout(4000).execute().body();
        log.info("SSO-YCRH登录结果: {}", response);
        JSONObject object = JSONObject.parseObject(response);
        String code1 = object.getString("code");
        if(!code1.equals("00")) {
            log.error("SSO-YCRH接口状态异常, {}", object.getString("message"));
            return null;
        }
        JSONObject result = object.getJSONObject("result");
        if(!result.getString("resultCode").equals("1")) {
            log.error("SSO-YCRH登录失败, {}", result.getString("resultMsg"));
            return null;
        }

        SsoYcrhRespDTO respDTO = JsonUtils.parseObject(result.getString("data"), SsoYcrhRespDTO.class);
        return respDTO;
    }

}
