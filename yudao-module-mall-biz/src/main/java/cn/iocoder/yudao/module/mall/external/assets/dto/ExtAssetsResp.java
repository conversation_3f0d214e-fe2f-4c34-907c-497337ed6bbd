package cn.iocoder.yudao.module.mall.external.assets.dto;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 资产接口响应DTO
 */
@Data
public class ExtAssetsResp<T> {

    /**
     * 1:成功；-1:失败
     */
    private String code;
    /**
     * 文字描述
     */
    private String msg;
    /**
     * 成功时返回数据
     */
    private T data;

    @JsonIgnore
    public boolean isSuccess() {
        return ObjectUtil.equal("1", code);
    }

    public ExtAssetsResp success() {
        this.code = "1";
        this.data = null;
        return this;
    }

    /**
     * 如果失败 抛出异常
     *
     * @param checkFail
     * @return
     */
    public T getData(boolean checkFail) {
        if (!isSuccess()) {
            throw exception(ErrorCodeConstants.ASSETS_FAIL, this.msg);
        }
        return data;
    }

}
