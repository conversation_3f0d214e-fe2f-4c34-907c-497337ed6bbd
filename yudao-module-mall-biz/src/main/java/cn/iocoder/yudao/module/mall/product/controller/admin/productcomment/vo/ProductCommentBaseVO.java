package cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
 * 商品评价 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ProductCommentBaseVO {

    @Schema(description = "订单id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单id不能为空")
    private Long orderId;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "skuId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "skuId不能为空")
    private Long skuId;

    @Schema(description = "spuId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "spuId不能为空")
    private Long spuId;

    @Schema(description = "供应商id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "供应商id不能为空")
    private Long supplierId;

    @Schema(description = "供应商名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String supplierName;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "会员id不能为空")
    private Long memberId;

    @Schema(description = "会员昵称")
    private String nickName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "评价星数 1->5")
    private Integer score;

    @Schema(description = "评价内容")
    private String content;

    @Schema(description = "上传图片地址，以逗号隔开")
    private String pics;

    @Schema(description = "是否匿名 1-是 0 否")
    private Integer anonymousFlag;

    @Schema(description = "审核状态")
    private Integer auditStatus;

    @Schema(description = "评价者ip")
    private String clientIp;

    @Schema(description = "评价者地区")
    private String clientArea;

    @Schema(description = "评价者点赞数")
    private Integer likeCount;

    @Schema(description = "评价回复数")
    private Integer replyCount;

    @Schema(description = "举报次数")
    private Integer reportCount;

}
