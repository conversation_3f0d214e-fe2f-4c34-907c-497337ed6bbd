package cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品操作日志记录 Excel 导出 Request VO，参数和 ProductOperateLogPageReqVO 是一致的")
@Data
public class ProductOperateLogExportReqVO {

    @Schema(description = "订单id")
    private Long skuId;

    @Schema(description = "供应商id")
    private Long supplierId;

    @Schema(description = "用户编号")
    private Long userId;

    @Schema(description = "操作分类")
    private Integer operateType;

    @Schema(description = "操作内容")
    private String content;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
