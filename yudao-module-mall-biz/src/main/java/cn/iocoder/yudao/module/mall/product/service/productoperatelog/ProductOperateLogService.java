package cn.iocoder.yudao.module.mall.product.service.productoperatelog;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productoperatelog.ProductOperateLogDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductOperateTypeEnum;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.orderoperatelog.OrderOperateLogDO;

/**
 * 商品操作日志记录 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductOperateLogService {

    /**
     * 创建商品操作日志记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createOperateLog(@Valid ProductOperateLogCreateReqVO createReqVO);

    void saveCreateOperateSpuLog(ProductSpuDO productSpuDO);

    void saveCreateOperateSkuLog(ProductSkuDO productSkuDO);

    void saveUpdateOperateSpuLog(ProductSpuDO sourceSpu, ProductSpuDO targetSpu);

    void saveUpdateOperateSkuLog(ProductSkuDO sourceSku, ProductSkuDO targetSku);

    void saveProductOperateLogDO(Long skuId, Long spuId, Long supplierId, String content);



    /**
     * 更新商品操作日志记录
     *
     * @param updateReqVO 更新信息
     */
    void updateOperateLog(@Valid ProductOperateLogUpdateReqVO updateReqVO);

    /**
     * 删除商品操作日志记录
     *
     * @param id 编号
     */
    void deleteOperateLog(Long id);

    /**
     * 获得商品操作日志记录
     *
     * @param id 编号
     * @return 商品操作日志记录
     */
    ProductOperateLogDO getOperateLog(Long id);

    /**
     * 获得商品操作日志记录列表
     *
     * @param ids 编号
     * @return 商品操作日志记录列表
     */
    List<ProductOperateLogDO> getOperateLogList(Collection<Long> ids);

    /**
     * 获得商品操作日志记录分页
     *
     * @param pageReqVO 分页查询
     * @return 商品操作日志记录分页
     */
    PageResult<ProductOperateLogDO> getOperateLogPage(ProductOperateLogPageReqVO pageReqVO);

    /**
     * 获得商品操作日志记录列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 商品操作日志记录列表
     */
    List<ProductOperateLogDO> getOperateLogList(ProductOperateLogExportReqVO exportReqVO);

    void saveProductOperateLog(ProductOperateLogDO productOperateLogDO);

}
