package cn.iocoder.yudao.module.mall.trade.controller.admin.orderassets.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import cn.iocoder.yudao.module.mall.trade.enums.order.AssetStatusEnum;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 订单明细固资信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class TradeOrderItemAssetsExcelVO {

    @Excel(needMerge = true, name = "用户工号", orderNum = "1",width = 15)
    private String userNo;

    @Excel(needMerge = true, name = "用户名", orderNum = "2",width = 15)
    private String userName;

    @Excel(needMerge = true, name = "部门编号", orderNum = "3",width = 15)
    private String deptNo;

    @Excel(needMerge = true, name = "部门名称", orderNum = "4",width = 20)
    private String deptName;

    @Excel(needMerge = true, name = "订单号", orderNum = "5",width = 30)
    private String orderNo;

    @Excel(needMerge = true, name = "供应商", orderNum = "6",width = 25)
    private String supplier;

    @Excel(needMerge = true, name = "商品名称", orderNum = "7",width = 45)
    private String skuName;

    @Excel(needMerge = true, name = "单价", orderNum = "8",width = 15)
    private BigDecimal price;

    @Excel(needMerge = true, name = "数量", orderNum = "9",width = 15)
    private Integer quantity;

    @Excel(needMerge = true, name = "联系人手机号", orderNum = "10",width = 20)
    private String phone;

    @Excel(needMerge = true, name = "购买日期", orderNum = "11",width = 20)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime buyDate;

    @ExcelProperty("订单明细ID")
    @Excel(needMerge = true, name = "订单明细ID", orderNum = "12",width = 25)
    private Long orderItemId;

    /**
     * 参考 {@link AssetStatusEnum}
     */
    @Excel(name = "建档状态", orderNum = "13", replace = {"建档待处理_0", "建档已提交_1", "无须建档_2", "建档中_3", "建档成功_4", "建档失败_5"}, width = 15)
    private Integer assetStatus;

    @ExcelProperty("建档完成时间")
    @Excel(needMerge = true, name = "建档完成时间", orderNum = "14",width = 20)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime assetFinishTime;

    @ExcelCollection(name = "固资明细", orderNum = "15")
    private List<TradeOrderAssetsDetailVO> detailList;

}
