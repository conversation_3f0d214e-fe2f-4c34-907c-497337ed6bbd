package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AppOrderSplitSkuReqVO {

    @Schema(description = "商品sku")
    @NotNull(message = "子订单商品sku不能为空")
    private Long skuId;

    @Schema(description = "商品个数")
    @NotNull(message = "子订单商品数不能为空")
    private Integer count;
}
