package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AppOrderSplitOrderReqVO {

    @Schema(description = "子订单三方订单id")
    @NotNull(message = "子订单三方订单id不能为空")
    private String thirdOrderId;

    @Schema(description = "子订单sku信息")
    @NotEmpty(message = "子订单sku不能为空")
    private List<AppOrderSplitSkuReqVO> skuReqVOList;

}
