package cn.iocoder.yudao.module.mall.trade.controller.admin.settle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/27 14:40
 */
@Schema(description = "管理后台 - 发票申请请求参数")
@Data
public class OrderInvoiceReq {

   /**
    * 订单Id
    */
   private List<Long> orderIdList;

   /**
    * 开票日期
    */
   private String invoiceDate;
}
