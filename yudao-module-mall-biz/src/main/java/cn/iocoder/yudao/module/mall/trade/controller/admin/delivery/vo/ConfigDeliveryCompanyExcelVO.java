package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 平台配置物流公司 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ConfigDeliveryCompanyExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("物流公司编码")
    private String com;

    @ExcelProperty("物流公司名称")
    private String name;

    @ExcelProperty("物流公司类型")
    private String type;

    @ExcelProperty("创建时间")
    private Date createTime;

}
