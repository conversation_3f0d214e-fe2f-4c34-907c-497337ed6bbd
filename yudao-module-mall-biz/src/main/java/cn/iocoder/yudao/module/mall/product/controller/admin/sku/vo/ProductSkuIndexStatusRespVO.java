package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 商品SKU索引状态响应VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "商品SKU索引状态响应VO")
public class ProductSkuIndexStatusRespVO {

    @Schema(description = "SKU ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long skuId;

    @Schema(description = "SKU内部ID", example = "SKU001")
    private String skuInnerId;

    @Schema(description = "SKU名称", example = "iPhone 15 Pro 256GB 深空黑色")
    private String skuName;

    @Schema(description = "是否需要索引", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean needIndex;

    @Schema(description = "不需要索引的原因（当needIndex为false时）", example = "商品已下架")
    private String reason;

    @Schema(description = "商品状态", example = "1")
    private Integer status;

    @Schema(description = "商品显示状态", example = "1")
    private Integer showStatus;

    @Schema(description = "商品平台状态", example = "1")
    private Integer platformStatus;

    @Schema(description = "供应商ID", example = "100")
    private Long supplierId;

    @Schema(description = "供应商名称", example = "京东供应商")
    private String supplierName;

    @Schema(description = "供应商状态", example = "1")
    private Integer supplierStatus;

    @Schema(description = "分类路径ID", example = "1-2-3")
    private String fullCategoryId;

    @Schema(description = "分类路径名称", example = "数码产品-手机-智能手机")
    private String fullCategoryName;
}
