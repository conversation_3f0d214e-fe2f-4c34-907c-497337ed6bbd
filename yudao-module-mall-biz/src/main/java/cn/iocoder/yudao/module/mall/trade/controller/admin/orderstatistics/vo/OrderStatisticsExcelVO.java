package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 订单统计 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class OrderStatisticsExcelVO {

    @ExcelProperty("编号，主键自增")
    private Long id;

    @ExcelProperty("供应商id")
    private Long supplierId;

    @ExcelProperty("供应商名称")
    private String supplierName;

    @ExcelProperty("统计维度类型 1-日 2-月  3-年")
    private Integer type;

    @ExcelProperty("商品id")
    private Long skuId;

    @ExcelProperty("商品名称")
    private String skuName;

    @ExcelProperty("订单数量")
    private Integer orderNum;

    @ExcelProperty("订单金额")
    private BigDecimal orderAmount;

    @ExcelProperty("统计时间")
    private String staticsTime;

    @ExcelProperty("创建时间")
    private Date createTime;

}
