package cn.iocoder.yudao.module.mall.product.controller.app.sku.vo;

import cn.iocoder.yudao.module.mall.product.controller.app.spec.vo.AppProductSpecValueVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SkuPriceCompareRespVO {
    /**
     * 商品skuId
     */
    private Long skuId;

    /**
     * 商品供应商id
     */
    private String skuInnerId;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;


    /**
     * 商品skuName
     */
    private String skuName;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 图片地址
     */
    private String picUrl;

    /**
     * 商品品牌编号
     */
    private Long brandId;

    /**
     * 商品品牌名称
     */
    private String brandName;

    /**
     * spu单位
     */
    private String unit;

    /**
     * 状态：  {@link cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum}
     */
    private Integer status;

    /**
     * sku 属性
     */
    private List<AppProductSpecValueVO> specValueList;
}