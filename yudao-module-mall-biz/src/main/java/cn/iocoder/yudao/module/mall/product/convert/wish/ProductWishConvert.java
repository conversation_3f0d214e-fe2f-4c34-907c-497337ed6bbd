package cn.iocoder.yudao.module.mall.product.convert.wish;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo.ProductWishExcelVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo.ProductWishRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo.ProductWishStausReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.wish.vo.AppProductWishCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.wish.vo.AppProductWishRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.wish.vo.AppProductWishUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.wish.ProductWishDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 心愿单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductWishConvert {

    ProductWishConvert INSTANCE = Mappers.getMapper(ProductWishConvert.class);

    ProductWishDO convert(AppProductWishCreateReqVO bean);

    ProductWishDO convert(AppProductWishUpdateReqVO bean);

    ProductWishDO convert(ProductWishStausReqVO bean);

    ProductWishRespVO convert(ProductWishDO bean);

    AppProductWishRespVO convert02(ProductWishDO bean);

    List<ProductWishRespVO> convertList(List<ProductWishDO> list);

    List<AppProductWishRespVO> convertList02(List<ProductWishDO> list);

    List<ProductWishExcelVO> convertList03(List<ProductWishDO> list);

    PageResult<ProductWishRespVO> convertPage(PageResult<ProductWishDO> page);

    PageResult<AppProductWishRespVO> convertPage02(PageResult<ProductWishDO> page);


}
