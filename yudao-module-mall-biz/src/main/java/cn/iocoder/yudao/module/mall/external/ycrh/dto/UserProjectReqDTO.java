package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class UserProjectReqDTO {
    /**
     * 员工编号
     */
    @JsonProperty("ygbh")
    private String ygNo;

    /**
     * 类型 1-自己名下所有项目 2-自己为项目主负责人的项目 3-自己为项目被授权人的项目
     */
    private String type;

    /**
     * 经济分类科目编号
     */
    @JsonProperty("jjflkmbh")
    private String subjectNo;

    /**
     * 是否包含未发布项目 0：否（默认）1：是
     */
    private String isRelease;

    /**
     * 多个分类以逗号分隔
     */
    @JsonProperty("skuTypeID")
    private String categoryIds;
}
