package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品SEO运营SKU标签导入Excel模板字段
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class ProductSkuTagImportExcelVO {

    @ExcelProperty("*供应商ID")
    @ColumnWidth(15)
    private Long supplierId;

    @ExcelProperty("*平台SKU")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(25)
    private Long skuId;

    @ExcelProperty("*三方SKU")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(25)
    private String skuInnerId;

    @ExcelProperty("*标签ID,多个中划线-分隔")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(35)
    private String tagIds;

    @ExcelProperty("初始销量")
    @ColumnWidth(15)
    private Integer initSalesCount;

    @JsonIgnore
    public List<Long> getTagIdList() {
        if(StringUtils.isNotBlank(tagIds)) {
            String[] ids = StringUtils.split(tagIds, "-");
            return Arrays.stream(ids)
                    .filter(StrUtil::isNumeric)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }
        return null;
    }

}
