package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class DeliveryImportExcelVO {

    @ExcelProperty("订单编号")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(25)
    private String orderNo;

    @ExcelProperty("发货方式")
    @ColumnWidth(25)
    private String deliveryType;

    @ExcelProperty("快递单号")
    @ColumnWidth(25)
    private String deliveryNum;

    @ExcelProperty("快递名称")
    @ColumnWidth(25)
    private String deliveryName;

    @ExcelProperty("自配送物流详情")
    @ColumnWidth(200)
    private String deliveryContent;
}