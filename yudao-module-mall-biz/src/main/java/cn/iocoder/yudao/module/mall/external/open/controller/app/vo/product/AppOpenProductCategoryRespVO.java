package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class AppOpenProductCategoryRespVO implements Serializable {

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String categoryName;
    /**
     * 父级分类ID
     */
    @Schema(description = "父级分类ID")
    private Long parentId;
    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    private Long categoryId;
    /**
     * 分类级别
     */
    @Schema(description = "分类级别")
    private Integer categoryLevel;

}
