package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 * @date 2024/5/19
 */
@Data
public class OrderStatisticsQueryVO {

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 统计维度类型 1-日 2-月  3-年
     */
    private Integer type;

    /**
     * 统计开始时间
     */
    @Schema(description = "统计开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String staticsStartTime;

    /**
     * 统计结束时间
     */
    @Schema(description = "统计结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String staticsEndTime;
}
