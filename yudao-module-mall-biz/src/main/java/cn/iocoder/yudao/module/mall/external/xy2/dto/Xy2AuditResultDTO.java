package cn.iocoder.yudao.module.mall.external.xy2.dto;

import lombok.Data;

@Data
public class Xy2AuditResultDTO {

    /**
     * 审批日期
     */
    private String approvalDate;

    /**
     * 审批级别
     */
    private String approvalLevel;

    /**
     * 审批时间
     */
    private String approvalTime;

    /**
     * 审批人名称
     */
    private String approvalUserName;

    /**
     * 审批人编号
     */
    private String approvalUserNo;

    /**
     * 审批角色名称
     */
    private String approvalRoleName;

    /**
     * 审批状态，审批状态 0-未审批、 1-审批通过、2-审批驳回
     */
    private String auditStatus;

    /**
     * 审批意见
     */
    private String auditResult;
}
