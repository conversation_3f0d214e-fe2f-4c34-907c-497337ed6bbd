package cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuSpecTypeEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
* 商品 SPU Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ProductSpuBaseVO {

    @Schema(description = "商品品牌编号", example = "1")
    @NotNull(message = "商品品牌编号不能为空")
    private Long brandId;

    @Schema(description = "商品品牌名称", example = "小米")
    private String brandName;

    @Schema(description = "供应商编号", example = "1")
    @NotNull(message = "供应商编号不能为空")
    private Long supplierId;

    @Schema(description = "供应商类型", example = "1")
    private Integer supplierType;

    @Schema(description = "供应商名称", example = "小米")
    private String supplierName;

    @Schema(description = "商品分类编号, 以-分隔")
    @NotNull(message = "商品分类编号不能为空")
    private String fullCategoryId;

    @Schema(description = "商品分类名称, 以/分隔")
    @NotNull(message = "商品分类名称不能为空")
    private String fullCategoryName;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    @NotEmpty(message = "商品名称不能为空")
    private String spuName;

    @Schema(description = "促销语", example = "好吃！")
    private String sellPoint;

    @Schema(description = "商品封面图片", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品封面图片")
    private String picUrl;

    @Schema(description = "商品轮播图片", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品轮播图片")
    private List<String> sliderPicUrls;

    @Schema(description = "排序字段", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "排序字段不能为空")
    private Integer sort;

    @Schema(description = "单位", example = "台")
    private String unit;

    @Schema(description = "商品状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品状态不能为空")
    @InEnum(ProductSpuStatusEnum.class)
    private Integer status;

    @Schema(description = "销售价", example = "1024")
    private BigDecimal salePrice;

    @Schema(description = "规格类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规格类型不能为空")
    @InEnum(ProductSpuSpecTypeEnum.class)
    private Integer specType;

    @Schema(description = "实际销量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Integer salesCount;

    @Schema(description = "初始销量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Integer initSalesCount;

    @Schema(description = "一级商品分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "一级商品分类编号不能为空")
    private Long category1Id;

    @Schema(description = "二级商品分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "二级商品分类编号不能为空")
    private Long category2Id;

    @Schema(description = "三级商品分类编号", example = "1")
    private Long category3Id;

    @Schema(description = "一级商品分类名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "通讯")
    @NotNull(message = "一级商品分类名称不能为空")
    private String category1Name;

    @Schema(description = "二级商品分类名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "手机")
    @NotNull(message = "二级商品分类名称不能为空")
    private String category2Name;

    @Schema(description = "三级商品分类名称", example = "手机配件")
    private String category3Name;

    @Schema(description = "商品显示状态 1-显示 0-不显示", example = "1")
    private Integer showStatus;

    @Schema(description = "商品平台上下架状态 1-上架 0-下架", example = "1")
    private Integer platformStatus;

}
