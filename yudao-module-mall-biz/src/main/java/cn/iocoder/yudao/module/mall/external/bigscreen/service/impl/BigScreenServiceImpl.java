package cn.iocoder.yudao.module.mall.external.bigscreen.service.impl;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.external.bigscreen.dal.mysql.bigscreen.BigScreenMapper;
import cn.iocoder.yudao.module.mall.external.bigscreen.service.BigScreenService;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.*;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.dal.mysql.order.TradeOrderItemAssetsMapper;
import cn.iocoder.yudao.module.mall.trade.dal.mysql.order.TradeOrderItemMapper;
import cn.iocoder.yudao.module.mall.trade.enums.order.SettleStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeAuditStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderStatusEnum;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Service
@Validated
public class BigScreenServiceImpl implements BigScreenService {
    @Resource
    private MemberUserService memberUserService;

    @Resource
    private ProductSkuService productSkuService;

    @Resource
    private TradeOrderItemMapper tradeOrderItemMapper;

    @Resource
    private TradeOrderService tradeOrderService;

    @Resource
    private TradeOrderItemAssetsMapper tradeOrderItemAssetsMapper;

    @Resource
    private BigScreenMapper bigScreenMapper;

    @Override
    public BigScreenUserSummaryRespVO getUserSummary() {
        Long tenantId = TenantContextHolder.getTenantId();
        BigScreenUserSummaryRespVO bigScreenUserSummaryRespVO = new BigScreenUserSummaryRespVO();
        bigScreenUserSummaryRespVO.setLoginedUserCount(bigScreenMapper.getLoginedUserCount(tenantId));
        bigScreenUserSummaryRespVO.setUserTotal(7468L);
        return bigScreenUserSummaryRespVO;
    }

    @Override
    public BigScreenOrderSummaryRespVO getOrderSummary() {
        Long tenantId = TenantContextHolder.getTenantId();

        BigScreenOrderSummaryRespVO bigScreenOrderSummaryRespVO = new BigScreenOrderSummaryRespVO();
        // 今日订单数量
        Long todayOrderCount = bigScreenMapper.getTodayOrderCount(tenantId);
        if(todayOrderCount == null){
            todayOrderCount = 0L;
        }
        bigScreenOrderSummaryRespVO.setTodayOrderCount(todayOrderCount);

        // 今日订单金额
        BigDecimal todayOrderAmount = bigScreenMapper.getTodayOrderAmount(tenantId);
        if(todayOrderAmount == null){
            todayOrderAmount = BigDecimal.ZERO;
        }
        bigScreenOrderSummaryRespVO.setTodayOrderAmount(todayOrderAmount);

        // 总订单数量
        Long orderCount = bigScreenMapper.getOrderCount(tenantId);
        if(orderCount == null){
            orderCount = 0L;
        }
        bigScreenOrderSummaryRespVO.setTotalOrderCount(orderCount);

        // 总订单金额
        BigDecimal orderAmount = bigScreenMapper.getOrderAmount(tenantId);
        if(orderAmount == null){
            orderAmount = BigDecimal.ZERO;
        }
        bigScreenOrderSummaryRespVO.setTotalOrderAmount(orderAmount);

        return bigScreenOrderSummaryRespVO;
    }

    @Override
    public List<BigScreenRealTimeOrderRespVO> getRealTimeOrderList() {
        Long tenantId = TenantContextHolder.getTenantId();
        return bigScreenMapper.getRealTimeOrderList(tenantId);
    }

    @Override
    public BigScreenProductSummaryRespVO getProductSummary() {
        Long tenantId = TenantContextHolder.getTenantId();

        BigScreenProductSummaryRespVO bigScreenProductSummaryRespVO = new BigScreenProductSummaryRespVO();
        bigScreenProductSummaryRespVO.setSkuTotal(productSkuService.count(
                new LambdaQueryWrapperX<ProductSkuDO>().eq(ProductSkuDO::getStatus, ProductSpuStatusEnum.ENABLE.getStatus())));
        bigScreenProductSummaryRespVO.setSellTotal(bigScreenMapper.getSellTotal(tenantId));

        Long todaySellCount = bigScreenMapper.getTodaySellCount(tenantId);
        if(todaySellCount == null){
            todaySellCount = 0L;
        }
        bigScreenProductSummaryRespVO.setTodaySellCount(todaySellCount);

        return bigScreenProductSummaryRespVO;
    }

    @Override
    public List<BigScreenSupplierProductSummaryRespVO> getSupplierProductSummary() {
        Long tenantId = TenantContextHolder.getTenantId();
        return bigScreenMapper.getSupplierProductSummary(tenantId);
    }

    @Override
    public List<BigScreenSupplierProductProportionRespVO> getSupplierProductProportion() {
        Long tenantId = TenantContextHolder.getTenantId();
        return bigScreenMapper.getSupplierProductProportion(tenantId);
    }

    @Override
    public List<BigScreenProductCategoryProportionRespVO> getProductCategoryProportion() {
        Long tenantId = TenantContextHolder.getTenantId();
        return bigScreenMapper.getProductCategoryProportion(tenantId);
    }

    @Override
    public List<BigScreenSellProductCategorySummaryRespVO> getSellProductCategorySummary() {
        Long tenantId = TenantContextHolder.getTenantId();
        return bigScreenMapper.getSellProductCategorySummary(tenantId);
    }

    @Override
    public BigScreenApprovalOrderSummaryRespVO getApprovalOrderSummary() {
        Long tenantId = TenantContextHolder.getTenantId();

        BigScreenApprovalOrderSummaryRespVO bigScreenApprovalOrderSummaryRespVO = new BigScreenApprovalOrderSummaryRespVO();

        // 待审批订单数
        Long pendingApprovalOrderCount = bigScreenMapper.getPendingApprovalOrderCount(tenantId);
        if(pendingApprovalOrderCount == null){
            pendingApprovalOrderCount = 0L;
        }
        bigScreenApprovalOrderSummaryRespVO.setPendingApprovalOrderCount(pendingApprovalOrderCount);

        // 审批通过订单数
        Long approvalOrderCount = bigScreenMapper.getApprovalOrderCount(tenantId);
        if(approvalOrderCount == null){
            approvalOrderCount = 0L;
        }
        bigScreenApprovalOrderSummaryRespVO.setApprovedOrderCount(approvalOrderCount);

        // 审批驳回订单数
        Long rejectOrderCount = bigScreenMapper.getRejectOrderCount(tenantId);
        if(rejectOrderCount == null){
            rejectOrderCount = 0L;
        }
        bigScreenApprovalOrderSummaryRespVO.setRejectedOrderCount(rejectOrderCount);

        // 待结算订单数
        Long pendingSettlementOrderCount = bigScreenMapper.getPendingSettlementOrderCount(tenantId);
        if(pendingSettlementOrderCount == null){
            pendingSettlementOrderCount = 0L;
        }
        bigScreenApprovalOrderSummaryRespVO.setPendingSettlementOrderCount(pendingSettlementOrderCount);

        // 待结算金额
        BigDecimal pendingSettlementAmount = bigScreenMapper.getPendingSettlementAmount(tenantId);
        if(pendingSettlementAmount == null){
            pendingSettlementAmount = BigDecimal.ZERO;
        }
        bigScreenApprovalOrderSummaryRespVO.setPendingSettlementAmount(pendingSettlementAmount);

        return bigScreenApprovalOrderSummaryRespVO;
    }

    @Override
    public BigScreenAssetArchiveSummaryRespVO getAssetArchiveSummary() {
        Long tenantId = TenantContextHolder.getTenantId();

        BigScreenAssetArchiveSummaryRespVO bigScreenAssetArchiveSummaryRespVO = new BigScreenAssetArchiveSummaryRespVO();
        Long archiveOrderCount = bigScreenMapper.getAssetArchiveOrderSummary(tenantId);
        if(archiveOrderCount == null){
            archiveOrderCount = 0L;
        }

        bigScreenAssetArchiveSummaryRespVO.setAssetArchiveOrderTotal(archiveOrderCount);
        Long archiveSkuCount = bigScreenMapper.getAssetArchiveSkuSummary(tenantId);
        if(archiveSkuCount == null){
            archiveSkuCount = 0L;
        }
        bigScreenAssetArchiveSummaryRespVO.setAssetArchiveSkuTotal(archiveSkuCount);

        return bigScreenAssetArchiveSummaryRespVO;
    }
}
