package cn.iocoder.yudao.module.mall.product.convert.category;

import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenProductCategoryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryTreeRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.category.vo.AppProductCategoryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.CategorySearchAggGoodsResp;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商品分类 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductCategoryConvert {

    ProductCategoryConvert INSTANCE = Mappers.getMapper(ProductCategoryConvert.class);

    ProductCategoryDO convert(ProductCategoryCreateReqVO bean);

    ConfigProductCategoryDO convert2(ProductCategoryCreateReqVO bean);

    ProductCategoryDO convert(ProductCategoryUpdateReqVO bean);

    ConfigProductCategoryDO convert2(ProductCategoryUpdateReqVO bean);

    ProductCategoryRespVO convert(ProductCategoryDO bean);

    ProductCategoryRespVO convert2(ConfigProductCategoryDO bean);

    AppProductCategoryRespVO convertVO(ProductCategoryDO bean);

    List<ProductCategoryRespVO> convertList(List<ProductCategoryDO> list);

    List<ProductCategoryRespVO> convertList02(List<ConfigProductCategoryDO> list);

    List<AppProductCategoryRespVO> convertList03(List<ProductCategoryDO> list);

    List<AppOpenProductCategoryRespVO> convertList04(List<ProductCategoryDO> list);

    List<ProductCategoryRespVO> convertList05(List<ProductCategoryDO> list);

    List<ProductCategoryRespVO> convertList07(List<ConfigProductCategoryDO> list);

    List<ProductCategoryTreeRespVO> convertList06(List<ProductCategoryDO> list);

    List<ProductCategoryTreeRespVO> convertList08(List<ConfigProductCategoryDO> list);

    List<ProductCategoryDO> convertList09(List<ConfigProductCategoryDO> list);


    @Mappings(value = {
            @Mapping(source = "categoryName", target = "cateName"),
            @Mapping(source = "parentId",target = "parentCategoryId")
    })
    CategorySearchAggGoodsResp convertResp(ProductCategoryDO productCategoryDO);


    List<CategorySearchAggGoodsResp> convertResps(List<ProductCategoryDO> productCategoryDOs);
}
