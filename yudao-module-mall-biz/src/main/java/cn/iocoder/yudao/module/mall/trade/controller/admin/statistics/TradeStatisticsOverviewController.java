package cn.iocoder.yudao.module.mall.trade.controller.admin.statistics;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.*;
import cn.iocoder.yudao.module.mall.trade.service.orderstatistics.OrderStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 订单概览统计")
@RestController
@RequestMapping("/trade/statistics/overview")
@Validated
public class TradeStatisticsOverviewController {

    @Resource
    private OrderStatisticsService orderStatisticsService;
    @Resource
    private MemberUserService memberUserService;

    /**
     * 登录用户数
     * @return
     */
    @GetMapping("/getUserCount")
    @Operation(summary = "登录用户数")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<Long> getUserCount(){
        return success(memberUserService.getUserCount());
    }

    @GetMapping("/getSupplierSellProportion")
    @Operation(summary = "供应商销售额占比")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<List<SupplierSellProportionRespVO>> getSupplierSellProportion() {
        List<SupplierSellProportionRespVO> supplierSellProportionRespVOS = orderStatisticsService.getSupplierSellProportion();
        return success(supplierSellProportionRespVOS);
    }

    @GetMapping("/getSellCount")
    @Operation(summary = "总销量")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<Long> getSellCount() {
        Long sellCount = orderStatisticsService.getSellCount();
        return success(sellCount);
    }

    @GetMapping("/getSellAmount")
    @Operation(summary = "总销售额")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<BigDecimal> getSellAmount() {
        BigDecimal sellAmount = orderStatisticsService.getSellAmount();
        return success(sellAmount);
    }

    @PostMapping("/dailySaleSummary")
    @Operation(summary = "按天统计销售额")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<List<OrderSaleDaySummaryRespVO>> dailySaleSummary(@Valid @RequestBody OrderSaleSummaryReqVO orderSaleSummaryReqVO) {
        List<OrderSaleDaySummaryRespVO> orderSaleDaySummaryRespVOS = orderStatisticsService.dailySaleSummary(orderSaleSummaryReqVO);
        return success(orderSaleDaySummaryRespVOS);
    }

    @PostMapping("/monthlySalesSummary")
    @Operation(summary = "按月统计销售额")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderSaleMonthSummaryRespVO>> monthlySalesSummary(@Valid @RequestBody OrderSaleMonthSummaryReqVO orderSaleMonthSummaryReqVO) {
        List<OrderSaleMonthSummaryRespVO> orderSaleMonthSummaryRespVOS = orderStatisticsService.monthlySalesSummary(orderSaleMonthSummaryReqVO);
        return success(orderSaleMonthSummaryRespVOS);
    }

}
