package cn.iocoder.yudao.module.mall.member.dal.dataobject.address;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 用户收件地址 DO
 *
 * <AUTHOR>
 */
@TableName("member_address")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 收件人名称
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 省编号
     */
    private Long provinceId;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编号
     */
    private Long cityId;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区县编号
     */
    private Long countyId;

    /**
     * 区县名称
     */
    private String countyName;
    /**
     * 乡镇编号
     */
    private Long townId;

    /**
     * 乡镇名称
     */
    private String townName;
    /**
     * 邮编
     */
    private String consigneeZip;

    /**
     * 收件详细地址
     */
    private String consigneeAddress;

    /**
     * 是否默认
     *
     * true - 默认收件地址
     */
    private Boolean defaulted;

    /**
     * 别名
     */
    private String alias;

    /**
     * 固定电话
     */
    private String telephone;


    /**
     * 邮箱
     */
    private String email;

}
