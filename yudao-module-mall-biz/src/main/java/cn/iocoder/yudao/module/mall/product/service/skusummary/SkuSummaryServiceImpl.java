package cn.iocoder.yudao.module.mall.product.service.skusummary;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.sku.ProductSkuMapper;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.mall.product.controller.admin.skusummary.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusummary.SkuSummaryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.module.mall.product.convert.skusummary.SkuSummaryConvert;
import cn.iocoder.yudao.module.mall.product.dal.mysql.skusummary.SkuSummaryMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.*;

/**
 * 供应商上架商品数统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SkuSummaryServiceImpl extends ServiceImpl<SkuSummaryMapper, SkuSummaryDO> implements SkuSummaryService {

    @Resource
    private SkuSummaryMapper skuSummaryMapper;

    @Resource
    private ProductSkuMapper productSkuMapper;

    @Resource
    private SupplierService supplierService;

    @Resource
    private ProductSkuService productSkuService;

    @Override
    public Long createSkuSummary(SkuSummaryCreateReqVO createReqVO) {
        // 插入
        SkuSummaryDO skuSummary = SkuSummaryConvert.INSTANCE.convert(createReqVO);
        skuSummaryMapper.insert(skuSummary);
        // 返回
        return skuSummary.getId();
    }

    @Override
    public void updateSkuSummary(SkuSummaryUpdateReqVO updateReqVO) {
        // 校验存在
        validateSkuSummaryExists(updateReqVO.getId());
        // 更新
        SkuSummaryDO updateObj = SkuSummaryConvert.INSTANCE.convert(updateReqVO);
        skuSummaryMapper.updateById(updateObj);
    }

    @Override
    public void deleteSkuSummary(Long id) {
        // 校验存在
        validateSkuSummaryExists(id);
        // 删除
        skuSummaryMapper.deleteById(id);
    }

    private void validateSkuSummaryExists(Long id) {
        if (skuSummaryMapper.selectById(id) == null) {
            throw exception(SKU_SUMMARY_NOT_EXISTS);
        }
    }

    @Override
    public SkuSummaryDO getSkuSummary(Long id) {
        return skuSummaryMapper.selectById(id);
    }

    @Override
    public List<SkuSummaryDO> getSkuSummaryList(Collection<Long> ids) {
        return skuSummaryMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<SkuSummaryDO> getSkuSummaryPage(SkuSummaryPageReqVO pageReqVO) {
        return skuSummaryMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SkuSummaryDO> getSkuSummaryList(SkuSummaryExportReqVO exportReqVO) {
        return skuSummaryMapper.selectList(exportReqVO);
    }

    @Override
    public List<SkuSummaryDO> saveSupplierSkuSummary() {
        List<SkuSummaryDO> skuSummaryDOS = new ArrayList<>();
        List<SupplierDO> supplierDOS = supplierService.getAllEnabledSupplierList();
        LocalDateTime startOfDay = LocalDateTime.of(LocalDateTime.now().toLocalDate(), LocalTime.MIN);
        Date startOfDayDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        for (SupplierDO supplierDO : supplierDOS) {
            Long listingTotal = productSkuMapper.selectCount(
                    new LambdaQueryWrapperX<ProductSkuDO>().eq(ProductSkuDO::getStatus, 1)
                            .eq(ProductSkuDO::getSupplierId, supplierDO.getId())
                            .le(ProductSkuDO::getUpdateTime, startOfDay));
            Long delistingTotal = productSkuMapper.selectCount(
                    new LambdaQueryWrapperX<ProductSkuDO>()
                            .eq(ProductSkuDO::getStatus, 2).eq(ProductSkuDO::getSupplierId, supplierDO.getId())
                            .le(ProductSkuDO::getUpdateTime, startOfDay));
            skuSummaryDOS.add(new SkuSummaryDO()
                    .setSupplierId(supplierDO.getId())
                    .setSupplierName(supplierDO.getName())
                    .setSupplierType(supplierDO.getType())
                    .setListingTotal(listingTotal)
                    .setDelistingTotal(delistingTotal)
                    .setDeadline(startOfDayDate));
        }

        saveOrUpdateBatch(skuSummaryDOS);
        return skuSummaryDOS;
    }

    @Override
    public List<SkuSummaryDaysRespVO> supplierSkuSummary(SkuSummaryDaysReqVO skuSummaryDaysReqVO) {
        List<SkuSummaryDO> skuSummaryDOS = skuSummaryMapper.selectList(new LambdaQueryWrapperX<SkuSummaryDO>()
                .eq(SkuSummaryDO::getSupplierId, skuSummaryDaysReqVO.getSupplierId())
                .between(SkuSummaryDO::getDeadline, skuSummaryDaysReqVO.getStartTime(), skuSummaryDaysReqVO.getEndTime()));
        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
        return skuSummaryDOS.stream()
                .map(skuSummaryDO -> {
                    SkuSummaryDaysRespVO skuSummaryDaysRespVO = new SkuSummaryDaysRespVO();
                    skuSummaryDaysRespVO.setSupplierId(skuSummaryDO.getId());
                    skuSummaryDaysRespVO.setSupplierName(skuSummaryDO.getSupplierName());
                    skuSummaryDaysRespVO.setSupplierType(skuSummaryDO.getSupplierType());
                    skuSummaryDaysRespVO.setListingTotal(skuSummaryDO.getListingTotal());
                    skuSummaryDaysRespVO.setDelistingTotal(skuSummaryDO.getDelistingTotal());
                    skuSummaryDaysRespVO.setDeadline(skuSummaryDO.getDeadline());
                    LocalDateTime localDeadLine = Instant.ofEpochMilli(skuSummaryDO.getDeadline().getTime())
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
                    skuSummaryDaysRespVO.setDate(localDeadLine.format(formatter));
                    return skuSummaryDaysRespVO;
                })
                .collect(Collectors.toList());
    }
}
