package cn.iocoder.yudao.module.mall.member.dal.dataobject.user;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.time.LocalDateTime;

/**
 * 会员用户 DO
 *
 * uk_mobile 索引：基于 {@link #mobile} 字段
 *
 * <AUTHOR>
 */
@TableName(value = "member_user", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberUserDO extends TenantBaseDO {

    /**
     * 用户ID
     */
    @TableId
    private Long id;

    /**
     * 员工编号 三方系统唯一标识
     */
    private String userNo;
    /**
     * 员工姓名
     */
    private String name;
    /**
     * 部门编号
     */
    private String deptCode;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 职务名称
     */
    private String jobTitle;
    /**
     * 人员类型
     */
    private String userType;

    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 帐号状态  0-开启 1-关闭
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 手机
     */
    private String mobile;
    /**
     * 加密后的密码
     *
     * 因为目前使用 {@link BCryptPasswordEncoder} 加密器，所以无需自己处理 salt 盐
     */
    private String password;
    /**
     * 注册 IP
     */
    private String registerIp;
    /**
     * 最后登录IP
     */
    private String loginIp;
    /**
     * 最后登录时间
     */
    private LocalDateTime loginDate;

    // TODO 芋艿：name 真实名字；
    // TODO 芋艿：email 邮箱；
    // TODO 芋艿：gender 性别；
    // TODO 芋艿：score 积分；
    // TODO 芋艿：payPassword 支付密码；

    @JsonIgnore
    public String getNameOrNickname() {
        if(StringUtils.isNotBlank(name)) {
            return name;
        }
        return nickname;
    }
}
