package cn.iocoder.yudao.module.mall.product.service.tag;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupSimpleVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.convert.tag.ProductTagGroupConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagGroupDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.tag.ProductTagGroupMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.TAG_GROUP_DELETE_WITH_TAG;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.TAG_GROUP_NOT_EXISTS;

/**
 * 标签分组 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductTagGroupServiceImpl extends ServiceImpl<ProductTagGroupMapper, ProductTagGroupDO>  implements ProductTagGroupService {

    @Resource
    private ProductTagGroupMapper tagGroupMapper;
    @Resource
    @Lazy
    private ProductTagService tagService;

    @Override
    public Long createTagGroup(ProductTagGroupCreateReqVO createReqVO) {
        // 插入
        ProductTagGroupDO tagGroup = ProductTagGroupConvert.INSTANCE.convert(createReqVO);
        tagGroupMapper.insert(tagGroup);
        // 返回
        return tagGroup.getId();
    }

    @Override
    public void updateTagGroup(ProductTagGroupUpdateReqVO updateReqVO) {
        // 校验存在
        validateTagGroupExists(updateReqVO.getId());
        // 更新
        ProductTagGroupDO updateObj = ProductTagGroupConvert.INSTANCE.convert(updateReqVO);
        tagGroupMapper.updateById(updateObj);
    }

    @Override
    public void deleteTagGroup(Long id) {
        // 校验存在
        validateTagGroupExists(id);
        // 校验是否包含标签
        long tagCount = tagService.countByGroup(id);
        if(tagCount > 0) {
            throw exception(TAG_GROUP_DELETE_WITH_TAG);
        }
        // 删除
        tagGroupMapper.deleteById(id);
    }

    private void validateTagGroupExists(Long id) {
        if (tagGroupMapper.selectById(id) == null) {
            throw exception(TAG_GROUP_NOT_EXISTS);
        }
    }

    @Override
    public ProductTagGroupDO getTagGroup(Long id) {
        return tagGroupMapper.selectById(id);
    }

    @Override
    public List<ProductTagGroupDO> getTagGroupList(Collection<Long> ids) {
        return tagGroupMapper.selectBatchIds(ids);
    }

    @Override
    public List<ProductTagGroupSimpleVO> getAllSimpleList() {
        List<ProductTagDO> tagDOList = tagService.list(Wrappers.lambdaQuery(ProductTagDO.class)
                .eq(ProductTagDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .orderByDesc(ProductTagDO::getUpdateTime));
        Set<Long> groupIds = tagDOList.stream().map(ProductTagDO::getGroupId).collect(Collectors.toSet());
        if(CollUtil.isEmpty(groupIds)) {
            return null;
        }
        List<ProductTagGroupDO> groupDOList = tagGroupMapper.selectList(Wrappers.lambdaQuery(ProductTagGroupDO.class)
                .eq(ProductTagGroupDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .in(ProductTagGroupDO::getId, groupIds)
                .orderByDesc(ProductTagGroupDO::getUpdateTime));

        return ProductTagGroupConvert.INSTANCE.convertList(groupDOList, tagDOList);
    }

    @Override
    public PageResult<ProductTagGroupDO> getTagGroupPage(ProductTagGroupPageReqVO pageReqVO) {
        return tagGroupMapper.selectPage(pageReqVO);
    }

}
