package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class OrderInfoDTO {

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 订单实际金额
     */
    private BigDecimal orderAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 商品信息
     */
    private List<SkuInfoDTO> skuInfo;

    /**
     * 发票凭证附件类型: "01":下载链接   02":凭证附件影像化文件 (参考 凭证附件上传)
     */
    private String invoiceType;

    /**
     * 发票在线预览链接 (发票凭证附件类型为"01"时必填)
     */
    private String[] invoiceUrl;

    /**
     * 凭证附件列表 发票凭证附件类型为"02"时必填)
     */
    private List<VoucherInfoDTO> voucherInfo;

    /**
     * 运费 (业务类型为"02"时必填)
     */
    private BigDecimal freight;

    /**
     * 下单时间 (yyyy-MM-dd HH:mm:ss) 业务类型为"02"时必填)
     */
    private String orderTime;

    /**
     * 下单人工号 (业务类型为"02"时必填)
     */
    private String ygbh;

    /**
     * 使用项目信息列表 (业务类型为"02"时必填，privatePaymentList不为空时必填)
     */
    private List<OrderXmInfoDTO> orderXmInfo;
    /**
     * 经济分类(默认传"30218")  (业务类型为"02"时必填)
     */
    private String jjfl;

    /**
     * 备注摘要 (业务类型为"02"时必填)
     */
    private String bz;
}
