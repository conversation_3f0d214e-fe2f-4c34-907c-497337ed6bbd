package cn.iocoder.yudao.module.mall.external.sso.cas.dynamic;

import org.jasig.cas.client.boot.configuration.CasClientConfigurer;
import org.jasig.cas.client.boot.configuration.EnableCasClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.Filter;

/**
 * @author: HF
 * @description: TODO
 * @date: 2024/4/13 17:50
 */

@ConditionalOnProperty(value = "yudaocas.enabled", havingValue = "true")
@Component
@EnableCasClient
public class DynamicCasClientConfigurer implements CasClientConfigurer {

    @Resource
    private CasConfigService casConfigService;

    @Override
    public void configureAuthenticationFilter(FilterRegistrationBean authenticationFilter) {
        authenticationFilter.setOrder(Ordered.HIGHEST_PRECEDENCE + 601);
        authenticationFilter.addUrlPatterns("/app-api/mall/open/sso/cas/auth");
        Filter dynamicFilter = new CasAuthenticationDynamicFilter(casConfigService);
        authenticationFilter.setFilter(dynamicFilter);
    }

    @Override
    public void configureValidationFilter(FilterRegistrationBean validationFilter) {
        validationFilter.setOrder(Ordered.HIGHEST_PRECEDENCE + 600);
        validationFilter.addUrlPatterns("/app-api/mall/open/sso/cas/auth");
        Filter dynamicFilter = new CasValidationDynamicFilter(casConfigService);
        validationFilter.setFilter(dynamicFilter);
    }

    @Override
    public void configureHttpServletRequestWrapperFilter(FilterRegistrationBean httpServletRequestWrapperFilter) {
        httpServletRequestWrapperFilter.setOrder(Ordered.HIGHEST_PRECEDENCE + 602);
        httpServletRequestWrapperFilter.addUrlPatterns("/app-api/mall/open/sso/cas/*", "/app-api/member/auth/sso-login/*");
        Filter dynamicFilter = new HttpServletRequestWrapperDynamicFilter(casConfigService);
        httpServletRequestWrapperFilter.setFilter(dynamicFilter);
    }

    @Override
    public void configureAssertionThreadLocalFilter(FilterRegistrationBean assertionThreadLocalFilter) {
        assertionThreadLocalFilter.setOrder(Ordered.HIGHEST_PRECEDENCE + 603);
        assertionThreadLocalFilter.addUrlPatterns("/app-api/mall/open/sso/cas/*", "/app-api/member/auth/sso-login/*");
        Filter dynamicFilter = new AssertionThreadLocalDynamicFilter(casConfigService);
        assertionThreadLocalFilter.setFilter(dynamicFilter);
    }

}
