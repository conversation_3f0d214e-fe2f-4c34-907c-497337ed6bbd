package cn.iocoder.yudao.module.mall.product.controller.app.wish.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

/**
 * 心愿单 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class AppProductWishBaseVO {

    @Schema(description = "商品分类ID,逗号分隔", requiredMode = Schema.RequiredMode.REQUIRED)
    private String categoryIds;

    @Schema(description = "商品分类名称,/分隔", requiredMode = Schema.RequiredMode.REQUIRED)
    private String categoryNames;

    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer quantity;

    @Schema(description = "预计下单时间")
    private Date orderTime;

    @Schema(description = "产品链接")
    private String productLink;

    @Schema(description = "需求描述")
    @Length(max = 300, message = "参数描述不能超过300个字符")
    private String productMemo;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "回复内容")
    private String replyContent;

}
