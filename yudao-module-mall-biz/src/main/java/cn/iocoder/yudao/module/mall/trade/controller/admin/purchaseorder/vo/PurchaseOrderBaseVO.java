package cn.iocoder.yudao.module.mall.trade.controller.admin.purchaseorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
 * 采购订单 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class PurchaseOrderBaseVO {

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "采购id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "采购id不能为空")
    private Long purchaseId;

    @Schema(description = "采购id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "采购id不能为空")
    private Long orderId;

}
