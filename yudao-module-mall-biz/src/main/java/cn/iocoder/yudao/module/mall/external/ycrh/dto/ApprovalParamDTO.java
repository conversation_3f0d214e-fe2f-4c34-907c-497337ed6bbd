package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/24
 */
@Data
public class ApprovalParamDTO {

    /**
     * 审批方式参数 1:项目大类 2:业务类型
     */
    @JsonProperty("spfscs")
    private String approvalType;


    /**
     * 业务类型
     */
    @JsonProperty("ywlx")
    private String businessType;

    /**
     * 项目的部门编号
     */
    @JsonProperty("bmid")
    private String departmentNo;

    /**
     * 项目编号
     */
    @JsonProperty("xmid")
    private String projectNo;

    /**
     * 职工编号
     */
    @JsonProperty("zgbh")
    private String ygNo;

    /**
     * 职工姓名
     */
    @JsonProperty("zgxm")
    private String ygName;

    /**
     * 职工所在部门
     */
    @JsonProperty("zgszbm")
    private String ygDepartmentNo;

    /**
     * 报销总金额
     */
    @JsonProperty("je")
    private String amount;

    /**
     * 经济分类科目编号
     */
    @JsonProperty("jjflkmbh")
    private String economyClass;

    /**
     * 借款责任人
     */
    @JsonProperty("jkzer")
    private String borrower;
}
