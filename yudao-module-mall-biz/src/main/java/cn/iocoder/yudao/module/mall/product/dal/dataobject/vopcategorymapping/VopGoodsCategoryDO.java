package cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@TableName("vop_goods_category")
@KeySequence("vop_goods_category_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VopGoodsCategoryDO extends TenantBaseDO {

    private Long id;
    /**
     * 分类ID
     */
    private Long categoryId;
    /**
     * 分类层级
     */
    private Integer categoryLevel;
    /**
     * 分类名称
     */
    private String categoryName;
    /**
     * 父级分类ID, 对应的是categoryId字段
     */
    private Long parentId;
    /**
     * 排序, 值越小，排序越靠前
     */
    private Integer orderSort;
    /**
     * 状态，是否展示 {@link cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum}
     */
    private Integer status;




}
