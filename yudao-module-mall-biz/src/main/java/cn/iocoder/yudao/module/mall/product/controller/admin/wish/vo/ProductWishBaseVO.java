package cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 心愿单 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ProductWishBaseVO {

    @Schema(description = "商品分类ID,逗号分隔", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品分类ID不能为空")
    private String categoryIds;

    @Schema(description = "商品分类名称,/分隔", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品分类名称不能为空")
    private String categoryNames;

    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数量不能为空")
    private Integer quantity;

    @Schema(description = "预计下单时间")
    private LocalDateTime orderTime;

    @Schema(description = "产品链接")
    private String productLink;

    @Schema(description = "参数描述")
    private String productMemo;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "会员用户姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "会员用户姓名不能为空")
    private String userName;

    @Schema(description = "会员用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "会员用户id不能为空")
    private Long userId;

    /**
     * 状态: 0-待回复 1-已回复
     */
    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "回复内容")
    private String replyContent;

    @Schema(description = "回复扩展参数")
    private String extParams;

}
