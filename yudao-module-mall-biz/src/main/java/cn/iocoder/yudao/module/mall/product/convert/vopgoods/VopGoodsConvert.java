package cn.iocoder.yudao.module.mall.product.convert.vopgoods;

import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsCategoryItem;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopSkuGoodsPageItem;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategoryDO;
import com.jd.open.api.sdk.domain.vopsp.SearchGoodsProvider.response.searchSku.SkuHitSearchGoodsResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface VopGoodsConvert {

    VopGoodsConvert INSTANCE = Mappers.getMapper(VopGoodsConvert.class);

    @Mapping(source = "needShow", target = "status")
    VopGoodsCategoryItem convertCategory(com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getChildCategoryList.GetCategoryInfoGoodsResp bean);

    @Mapping(source = "needShow", target = "status")
    ConfigProductCategoryDO convertCategoryDO(com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getChildCategoryList.GetCategoryInfoGoodsResp bean);

    @Mapping(source = "needShow", target = "status")
    ConfigProductCategoryDO convertCategoryDO(com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getCategoryInfoList.GetCategoryInfoGoodsResp bean);

    VopSkuGoodsPageItem convertVopGoodsItem(SkuHitSearchGoodsResp bean);

}
