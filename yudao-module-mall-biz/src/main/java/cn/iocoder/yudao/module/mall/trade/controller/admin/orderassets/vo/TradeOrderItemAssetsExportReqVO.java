package cn.iocoder.yudao.module.mall.trade.controller.admin.orderassets.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 订单明细固资信息 Excel 导出 Request VO，参数和 TradeOrderItemAssetsPageReqVO 是一致的")
@Data
public class TradeOrderItemAssetsExportReqVO {

    @Schema(description = "订单明细ID")
    private Long orderItemId;

    @Schema(description = "验收单号")
    private String assetAcceptNo;

    @Schema(description = "资产编号")
    private String assetNo;

    @Schema(description = "审批人")
    private String approveBy;

    @Schema(description = "审批状态：0-无须建档")
    private Integer approveStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
