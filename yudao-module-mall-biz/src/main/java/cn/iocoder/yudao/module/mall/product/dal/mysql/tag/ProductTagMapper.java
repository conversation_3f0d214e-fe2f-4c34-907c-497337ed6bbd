package cn.iocoder.yudao.module.mall.product.dal.mysql.tag;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 标签 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductTagMapper extends BaseMapperX<ProductTagDO> {

    default PageResult<ProductTagDO> selectPage(ProductTagPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductTagDO>()
                .likeIfPresent(ProductTagDO::getName, reqVO.getName())
                .eqIfPresent(ProductTagDO::getGroupId, reqVO.getGroupId())
                .eqIfPresent(ProductTagDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ProductTagDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductTagDO::getId));
    }

}
