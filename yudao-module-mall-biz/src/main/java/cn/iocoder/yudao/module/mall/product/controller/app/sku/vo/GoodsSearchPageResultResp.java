package cn.iocoder.yudao.module.mall.product.controller.app.sku.vo;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/8
 */
@Data
public class GoodsSearchPageResultResp {

    /**
     * 商品分页数据
     */
    @Schema(description = "商品分页数据")
    private PageResult<SkuGoodsPageItem> pageResult;

    /**
     * 相关分类汇总信息
     */
    @Schema(description = "相关分类汇总信息")
    private List<CategorySearchAggGoodsResp> categoryAggList;

    /**
     * 品牌汇总信息
     */
    @Schema(description = "品牌汇总信息")
    private List<BrandSearchAggGoodsResp> brandAggList;

    /**
     * 供应商汇总信息
     */
    @Schema(description = "供应商汇总信息")
    private List<SupplierSearchAggGoodsResp> supplierAggList;

    /**
     * SPU规格汇总信息
     */
    private List<GoodsSearchSpecRespVO> specAggList;

}
