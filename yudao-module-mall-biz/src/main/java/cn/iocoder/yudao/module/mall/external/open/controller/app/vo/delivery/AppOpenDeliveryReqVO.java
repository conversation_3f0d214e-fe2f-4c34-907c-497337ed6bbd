package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.delivery;

import com.kuaidi100.sdk.response.SubscribePushParamResp;
import com.kuaidi100.sdk.response.SubscribePushResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AppOpenDeliveryReqVO extends SubscribePushResult {

    /**
     * 物流公司名称
     */
    @Schema(description = "物流公司名称")
    @NotNull(message = "物流公司名称不能为空")
    private String companyName;

    /**
     * 物流公司编码
     */
    @Schema(description = "物流公司编码")
    @NotNull(message = "物流公司编码不能为空")
    private String companyCode;

    /**
     * 物流单号
     */
    @Schema(description = "物流单号")
    @NotNull(message = "物流单号不能为空")
    private String num;
}
