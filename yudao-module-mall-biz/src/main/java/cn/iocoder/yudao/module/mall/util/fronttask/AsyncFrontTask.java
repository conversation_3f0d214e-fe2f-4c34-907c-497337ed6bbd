package cn.iocoder.yudao.module.mall.util.fronttask;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 前端异步任务，主要是耗时的导入导出任务；
 * 异步处理并能返回任务进度和结果
 * <AUTHOR>
 * @date 2024/8/16
 */

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface AsyncFrontTask {

    AsyncTaskTypeEnum taskType();

    /**
     * 是否内嵌执行，如异步导入时，应该会先进行基础校验，再执行异步导入；
     * 如果非内嵌执行，会自动将执行逻辑封装到子线程
     * @return
     */
    boolean isNested() default true;
}

