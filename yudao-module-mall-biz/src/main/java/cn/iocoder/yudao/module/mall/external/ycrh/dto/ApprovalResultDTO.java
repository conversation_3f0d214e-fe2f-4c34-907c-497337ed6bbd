package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/8/16
 */
@Data
public class ApprovalResultDTO {


    /**
     * 审批级别
     */
    @JsonProperty("spjb")
    private String approvalLevel;

    /**
     * 审批时间格式"2022-06-23 15:06:45"
     */
    @JsonProperty("spsj")
    private String approvalTime;

    /**
     * 审批日期格式 20220623
     */
    @JsonProperty("sprq")
    private String approvalDate;

    /**
     * 审批状态 0 未审批 1审批通过 2审批驳回
     */
    @JsonProperty("spzt")
    private String auditStatus;

    /**
     * 审批意见
     */
    @JsonProperty("spsm")
    private String auditResult;

    /**
     * 是否签章 0 不签章 1 证书签章 2无证书签章
     */
    @JsonProperty("sfqz")
    private String isSignature;

    /**
     * 签章图片	clob	签章图片（sfqz为0时该字段为空）	可空
     */
    @JsonProperty("signimage")
    private String signatureImage;

    /**
     * 审批人编号
     */
    @JsonProperty("sprbh")
    private String approvalUserNo;

    /**
     * 审批角色名称
     */
    @JsonProperty("spjs")
    private String approvalRoleName;

    /**
     * 审批人名称
     */
    @JsonProperty("spr")
    private String approvalUserName;

    /**
     * 是否签名1是  0否
     */
    @JsonProperty("sfqm")
    private String isSign;

    /**
     * 	签名图片	Clob	签名图片（base64格式图片）
     */
    @JsonProperty("handwritingjson")
    private String signImage;

    /**
     * 是否财务审批
     */
    @JsonProperty("cwsp")
    private String isFinancialAudit;
}
