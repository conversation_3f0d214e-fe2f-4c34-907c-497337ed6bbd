package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
public class SplitShopOrderDTO {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单总金额
     */
    private BigDecimal totalPrice;

    /**
     * 运费
     */
    private BigDecimal freight;

    /**
     * 下单时间(yyyy-MM-dd HH:mm:ss)
      */
    private String orderTime;

    /**
     * 保管人工号(商品分类包含固定资产时必填)
     */
    private String assetsKeeper;

    /**
     * 保管地点(商品分类包含固定资产时必填)
     */
    private String assetsPalce;

    /**
     * 订单拆分原因
     */
    @JsonProperty("bz")
    private String remark;

    /**
     * 商品列表
     */
    private List<SkuInfoDTO> wbSkuInfo;
}
