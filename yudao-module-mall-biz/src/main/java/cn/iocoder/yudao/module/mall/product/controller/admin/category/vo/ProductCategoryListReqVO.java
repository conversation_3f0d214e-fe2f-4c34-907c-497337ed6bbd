package cn.iocoder.yudao.module.mall.product.controller.admin.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 商品分类列表查询 Request VO")
@Data
public class ProductCategoryListReqVO {

    @Schema(description = "分类名称", example = "办公文具")
    private String name;

    @Schema(description = "分类ID", example = "123")
    private Long categoryId;

    @Schema(description = "分类状态", example = "1")
    private Integer status;

    @Schema(description = "分类类型", example = "1")
    private Integer type;

    @Schema(description = "经济分类编码", example = "1")
    private String economyClass;

    @Schema(description = "有无经济分类编码", example = "true")
    private Boolean haveEconomyClass;

}
