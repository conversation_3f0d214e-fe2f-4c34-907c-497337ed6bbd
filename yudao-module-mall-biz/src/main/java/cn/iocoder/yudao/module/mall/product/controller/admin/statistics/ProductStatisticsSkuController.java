package cn.iocoder.yudao.module.mall.product.controller.admin.statistics;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.external.bigscreen.service.BigScreenService;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenProductCategoryProportionRespVO;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenSellProductCategorySummaryRespVO;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenSupplierProductProportionRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SkuCountCategorySummaryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SkuSummaryRespVO;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.ProductSellAmountSummaryRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.ProductSellCountSummaryRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.SupplierSellCountSummaryRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.SupplierSellProportionRespVO;
import cn.iocoder.yudao.module.mall.trade.service.orderstatistics.OrderStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 商品统计")
@RestController
@RequestMapping("/product/statistics/sku")
@Validated
public class ProductStatisticsSkuController {
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private BigScreenService bigScreenService;
    @Resource
    private OrderStatisticsService orderStatisticsService;

    /**
     * 统计当前sku总数
     * @return
     */
    @GetMapping("/getSkuTotal")
    @Operation(summary = "统计当前sku总数")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:query')")
    public CommonResult<SkuSummaryRespVO> getSkuTotal() {
        SkuSummaryRespVO skuSummaryRespVO = productSkuService.getSkuTotal();
        return success(skuSummaryRespVO);
    }

    /**
     * 统计商品数量分类排行
     * @return
     */
    @GetMapping("/getProductCountByCategory")
    @Operation(summary = "统计供应商当前sku总数")
    @PreAuthorize("@ss.hasAnyPermissions('product:sku-statistics:query')")
    public CommonResult<List<SkuCountCategorySummaryRespVO>> getProductCountByCategory() {
        List<SkuCountCategorySummaryRespVO> skuCountCategorySummaryRespVOS = productSkuService.getProductCountByCategory();
        return success(skuCountCategorySummaryRespVOS);
    }

    /**
     * 采购商品品类排行
     * @return
     */
    @GetMapping("getSellProductCategorySummary")
    @Operation(summary = "采购商品品类排行")
    @PreAuthorize("@ss.hasAnyPermissions('product:sku-statistics:query')")
    public CommonResult<List<BigScreenSellProductCategorySummaryRespVO>> getSellProductCategorySummary() {
        List<BigScreenSellProductCategorySummaryRespVO> bigScreenSellProductCategorySummaryRespVOS = bigScreenService.getSellProductCategorySummary();
        return CommonResult.success(bigScreenSellProductCategorySummaryRespVOS);
    }

    @GetMapping("/getSupplierSellProductCountSummary")
    @Operation(summary = "商品销售数量供应商排行")
    @PreAuthorize("@ss.hasAnyPermissions('product:sku-statistics:query')")
    public CommonResult<List<SupplierSellCountSummaryRespVO>> getSupplierSellProductCountSummary() {
        List<SupplierSellCountSummaryRespVO> supplierSellCountSummaryRespVOS = orderStatisticsService.getSupplierSellProductCountSummary();
        return success(supplierSellCountSummaryRespVOS);
    }

    @GetMapping("/getSellProductAmountSummary")
    @Operation(summary = "商品销售额排行")
    @PreAuthorize("@ss.hasAnyPermissions('product:sku-statistics:query')")
    public CommonResult<List<ProductSellAmountSummaryRespVO>> getSellProductAmountSummary() {
        List<ProductSellAmountSummaryRespVO> productSellAmountSummaryRespVOS = orderStatisticsService.getSellProductAmountSummary();
        return success(productSellAmountSummaryRespVOS);
    }

    @GetMapping("/getSellProductTotalSummary")
    @Operation(summary = "商品销量排行")
    @PreAuthorize("@ss.hasAnyPermissions('product:sku-statistics:query')")
    public CommonResult<List<ProductSellCountSummaryRespVO>> getSellProductTotalSummary() {
        List<ProductSellCountSummaryRespVO> productSellCountSummaryRespVOS = orderStatisticsService.getSellProductTotalSummary();
        return success(productSellCountSummaryRespVOS);
    }
}
