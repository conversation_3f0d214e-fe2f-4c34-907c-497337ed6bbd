package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.external.bigscreen.service.BigScreenService;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenSellProductCategorySummaryRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.*;
import cn.iocoder.yudao.module.mall.trade.convert.orderstatistics.OrderStatisticsConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.orderstatistics.OrderStatisticsDO;
import cn.iocoder.yudao.module.mall.trade.service.orderstatistics.OrderStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 订单统计")
@RestController
@RequestMapping("/trade/order-statistics")
@Validated
public class OrderStatisticsController {

    @Resource
    private OrderStatisticsService orderStatisticsService;
    @Resource
    private BigScreenService bigScreenService;


    @PostMapping("/create")
    @Operation(summary = "创建订单统计")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:create')")
    public CommonResult<Long> createOrderStatistics(@Valid @RequestBody OrderStatisticsCreateReqVO createReqVO) {
        return success(orderStatisticsService.createOrderStatistics(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新订单统计")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:update')")
    public CommonResult<Boolean> updateOrderStatistics(@Valid @RequestBody OrderStatisticsUpdateReqVO updateReqVO) {
        orderStatisticsService.updateOrderStatistics(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除订单统计")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:delete')")
    public CommonResult<Boolean> deleteOrderStatistics(@RequestParam("id") Long id) {
        orderStatisticsService.deleteOrderStatistics(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得订单统计")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<OrderStatisticsRespVO> getOrderStatistics(@RequestParam("id") Long id) {
        OrderStatisticsDO orderStatistics = orderStatisticsService.getOrderStatistics(id);
        return success(OrderStatisticsConvert.INSTANCE.convert(orderStatistics));
    }

    @GetMapping("/list")
    @Operation(summary = "获得订单统计列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderStatisticsRespVO>> getOrderStatisticsList(@RequestParam("ids") Collection<Long> ids) {
        List<OrderStatisticsDO> list = orderStatisticsService.getOrderStatisticsList(ids);
        return success(OrderStatisticsConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得订单统计分页")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<PageResult<OrderStatisticsRespVO>> getOrderStatisticsPage(@Valid OrderStatisticsPageReqVO pageVO) {
        PageResult<OrderStatisticsDO> pageResult = orderStatisticsService.getOrderStatisticsPage(pageVO);
        return success(OrderStatisticsConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出订单统计 Excel")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:export')")
    @OperateLog(type = EXPORT)
    public void exportOrderStatisticsExcel(@Valid OrderStatisticsExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<OrderStatisticsDO> list = orderStatisticsService.getOrderStatisticsList(exportReqVO);
        // 导出 Excel
        List<OrderStatisticsExcelVO> datas = OrderStatisticsConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "订单统计.xls", "数据", OrderStatisticsExcelVO.class, datas);
    }

//    OrderStatisticsVO

    /**
     * 获得首页订单数量和金额统计
     * @param queryVO
     * @return
     */
    @PostMapping("/queryOrderStatistics")
    @Operation(summary = "获得首页订单数量和金额统计")
    //@Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderStatisticsVO>> queryOrderStatistics(@RequestBody  OrderStatisticsQueryVO queryVO) {
        List<OrderStatisticsVO> orderStatisticsVOS = orderStatisticsService.queryOrderStatistics(queryVO);
        return success(orderStatisticsVOS);
    }

    /**
     * 获取首页订单状态统计
     * @param supplierId
     * @return
     */
    @GetMapping("/queryOrderStatisticsTotalVO")
	@Operation(summary = "获取首页订单状态统计")
	@PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<OrderStatisticsTotalResultVO> queryOrderStatisticsTotalVO(@RequestParam(value = "supplierId", required = false) Long supplierId) {
        return success(orderStatisticsService.queryOrderStatisticsTotalVO(supplierId));
    }

    /**
     * 查询近多少天内的商品销量和销售额
     * @param days 最近多少天
     * @return
     */
    @GetMapping("/getSalesAndRevenueInLastDays")
    @Operation(summary = "查询近多少天内的商品销量和销售额")
    @Parameter(name = "days", description = "最近多少天", required = true, example = "30")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<SalesAndRevenueSummaryRespVO>> getSalesAndRevenueInLastDays(Long days) {
        List<SalesAndRevenueSummaryRespVO> result = orderStatisticsService.getSalesAndRevenueInLastDays(days);
        return success(result);
    }

    /**
     * 查询近多少天内商品分类销量及销售额排行
     * @param categoryLevel 商品分类级别，一级 1， 二级 2， 三级 3
     * @return
     */
    @GetMapping("/getSalesAndRevenueInLastDaysByProductCategory")
    @Operation(summary = "查询近多少天内商品分类销量及销售额排行")
    @Parameter(name = "days", description = "最近多少天", required = true, example = "30")
    @Parameter(name = "categoryLevel", description = "商品分类级别，一级 1， 二级 2， 三级 3", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<ProductCategorySalesAndRevenueSummaryRespVO>> getSalesAndRevenueInLastDaysByProductCategory(Long days, Long categoryLevel) {
        Assert.checkBetween(categoryLevel, 1, 3, "商品分类级别数值不正确");
        List<ProductCategorySalesAndRevenueSummaryRespVO> result = orderStatisticsService.getSalesAndRevenueInLastDaysByProductCategory(days, categoryLevel, TenantContextHolder.getRequiredTenantId());
        return success(result);
    }

    /**
     * 查询日期范围内具体供应商的销售订单数、销售总金额、结算订单数和结算总金额
     * @return
     */
    @GetMapping("/getSupplierSalesSettleSummary")
    @Operation(summary = "查询日期范围内具体供应商的销售订单数、销售总金额、结算订单数和结算总金额")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<SupplierSalesSettleSummaryRespVO> getSupplierSalesSettleSummary(@Valid SupplierSalesSettleSummaryReqVO supplierSalesSettleSummaryReqVO) {
        SupplierSalesSettleSummaryRespVO result = orderStatisticsService.getSupplierSalesSettleSummary(supplierSalesSettleSummaryReqVO);
        return success(result);
    }

    /**
     * 查询日期范围内每天新增的订单数量及金额、每天确认的订单数量及金额、每天发货的订单数及金额和每天完成的订单数及金额
     * @return
     */
    @GetMapping("/getSummarizeOrderStatsByDays")
    @Operation(summary = "查询日期范围内每天新增的订单数量及金额、每天确认的订单数量及金额、每天发货的订单数及金额和每天完成的订单数及金额")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<SummarizeOrderStatsByDaysRespVO>> getSummarizeOrderStatsByDays(@Valid SummarizeOrderStatsByDaysReqVO summarizeOrderStatsByDaysReqVO) {
        Assert.checkBetween(summarizeOrderStatsByDaysReqVO.getType(), 1, 4, "订单统计类型不正确");
        List<SummarizeOrderStatsByDaysRespVO> result = orderStatisticsService.getSummarizeOrderStatsByDays(summarizeOrderStatsByDaysReqVO, TenantContextHolder.getTenantId());
        return success(result);
    }

    @GetMapping("/getSupplierSellProportion")
    @Operation(summary = "供应商销售额占比")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<SupplierSellProportionRespVO>> getSupplierSellProportion() {
        List<SupplierSellProportionRespVO> supplierSellProportionRespVOS = orderStatisticsService.getSupplierSellProportion();
        return success(supplierSellProportionRespVOS);
    }

    @GetMapping("/getSellProductCategorySummary")
    @Operation(summary = "商品销售品类排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<BigScreenSellProductCategorySummaryRespVO>> getSellProductCategorySummary() {
        List<BigScreenSellProductCategorySummaryRespVO> bigScreenSellProductCategorySummaryRespVOS = bigScreenService.getSellProductCategorySummary();
        return success(bigScreenSellProductCategorySummaryRespVOS);
    }

    @GetMapping("/getSupplierSellProductCountSummary")
    @Operation(summary = "商品销售数量供应商排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<SupplierSellCountSummaryRespVO>> getSupplierSellProductCountSummary() {
        List<SupplierSellCountSummaryRespVO> supplierSellCountSummaryRespVOS = orderStatisticsService.getSupplierSellProductCountSummary();
        return success(supplierSellCountSummaryRespVOS);
    }

    @GetMapping("/getSellProductTotalSummary")
    @Operation(summary = "商品销量排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<ProductSellCountSummaryRespVO>> getSellProductTotalSummary() {
        List<ProductSellCountSummaryRespVO> productSellCountSummaryRespVOS = orderStatisticsService.getSellProductTotalSummary();
        return success(productSellCountSummaryRespVOS);
    }

    @GetMapping("/getSellProductAmountSummary")
    @Operation(summary = "商品销售额排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<ProductSellAmountSummaryRespVO>> getSellProductAmountSummary() {
        List<ProductSellAmountSummaryRespVO> productSellAmountSummaryRespVOS = orderStatisticsService.getSellProductAmountSummary();
        return success(productSellAmountSummaryRespVOS);
    }

    @GetMapping("/getSellCount")
    @Operation(summary = "总销量")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<Long> getSellCount() {
        Long sellCount = orderStatisticsService.getSellCount();
        return success(sellCount);
    }

    @GetMapping("/getSellAmount")
    @Operation(summary = "总销售额")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<BigDecimal> getSellAmount() {
        BigDecimal sellAmount = orderStatisticsService.getSellAmount();
        return success(sellAmount);
    }

    @GetMapping("/getNotSettleAmount")
    @Operation(summary = "待结算金额")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<BigDecimal> getNotSettleAmount() {
        BigDecimal notSettleAmount = orderStatisticsService.getNotSettleAmount();
        return success(notSettleAmount);
    }

    @GetMapping("/getOrderSummary")
    @Operation(summary = "订单统计：待确认订单、待发货订单、售后订单、已完成订单、待审批订单、已审批订单、待结算订单、已结算订单、已取消订单")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<OrderSummaryRespVO> getOrderSummary() {
        OrderSummaryRespVO orderSummary = orderStatisticsService.getOrderSummary();
        return success(orderSummary);
    }

    @GetMapping("/getOrderSummaryByDept")
    @Operation(summary = "部门销售额排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderDeptSummaryRespVO>> getOrderSummaryByDept(Integer limit) {
        List<OrderDeptSummaryRespVO> orderDeptSummaryRespVOS = orderStatisticsService.getOrderSummaryByDept(limit);
        return success(orderDeptSummaryRespVOS);
    }

    @GetMapping("/getOrderSummaryByProject")
    @Operation(summary = "项目销售额排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderProjectSummaryRespVO>> getOrderSummaryByProject() {
        List<OrderProjectSummaryRespVO> orderProjectSummaryRespVOS = orderStatisticsService.getOrderSummaryByProject();
        return success(orderProjectSummaryRespVOS);
    }

    @GetMapping("/getOrderSummaryBySupplier")
    @Operation(summary = "供应商销售额排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderSupplierSummaryRespVO>> getOrderSummaryBySupplier() {
        List<OrderSupplierSummaryRespVO> orderSupplierSummaryRespVOS = orderStatisticsService.getOrderSummaryBySupplier();
        return success(orderSupplierSummaryRespVOS);
    }

    @GetMapping("/getAfterSaleOrderSummary")
    @Operation(summary = "供应商售后订单量排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderAfterSaleSummaryRespVO>> getAfterSaleOrderSummary() {
        List<OrderAfterSaleSummaryRespVO> orderAfterSaleSummaryRespVOS = orderStatisticsService.getAfterSaleOrderSummary();
        return success(orderAfterSaleSummaryRespVOS);
    }

    @PostMapping("/dailySaleSummary")
    @Operation(summary = "按天统计销售额")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderSaleDaySummaryRespVO>> dailySaleSummary(@Valid @RequestBody OrderSaleSummaryReqVO orderSaleSummaryReqVO) {
        List<OrderSaleDaySummaryRespVO> orderSaleDaySummaryRespVOS = orderStatisticsService.dailySaleSummary(orderSaleSummaryReqVO);
        return success(orderSaleDaySummaryRespVOS);
    }

    @PostMapping("/weeklySalesSummary")
    @Operation(summary = "按周统计销售额")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderSaleWeekSummaryRespVO>> weeklySalesSummary(@Valid @RequestBody OrderSaleSummaryReqVO orderSaleSummaryReqVO) {
        List<OrderSaleWeekSummaryRespVO> orderSaleWeekSummaryRespVOS = orderStatisticsService.weeklySalesSummary(orderSaleSummaryReqVO);
        return success(orderSaleWeekSummaryRespVOS);
    }

    @PostMapping("/monthlySalesSummary")
    @Operation(summary = "按月统计销售额")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderSaleMonthSummaryRespVO>> monthlySalesSummary(@Valid @RequestBody OrderSaleMonthSummaryReqVO orderSaleMonthSummaryReqVO) {
        List<OrderSaleMonthSummaryRespVO> orderSaleMonthSummaryRespVOS = orderStatisticsService.monthlySalesSummary(orderSaleMonthSummaryReqVO);
        return success(orderSaleMonthSummaryRespVOS);
    }

}
