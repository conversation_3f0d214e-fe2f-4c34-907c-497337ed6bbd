package cn.iocoder.yudao.module.mall.product.dal.mysql.category;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryListReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 商品分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductCategoryMapper extends BaseMapperX<ProductCategoryDO> {

    default List<ProductCategoryDO> selectList(ProductCategoryListReqVO listReqVO) {
        return selectList(new LambdaQueryWrapperX<ProductCategoryDO>()
                .likeIfPresent(ProductCategoryDO::getCategoryName, listReqVO.getName())
                .orderByAsc(ProductCategoryDO::getCategoryLevel, ProductCategoryDO::getOrderSort));
    }

    default Long selectCountByParentId(Long parentId) {
        return selectCount(ProductCategoryDO::getParentId, parentId);
    }

    default List<ProductCategoryDO> selectListByStatus(Integer status) {
        return selectList(ProductCategoryDO::getStatus, status);
    }

    default ProductCategoryDO selectByCategoryId(Long categoryId) {
        return selectOne(ProductCategoryDO::getCategoryId, categoryId);
    }

    default List<ProductCategoryDO> getByCategoryIds(Collection<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return null;
        }
        return selectList(ProductCategoryDO::getCategoryId, categoryIds);

    }

}
