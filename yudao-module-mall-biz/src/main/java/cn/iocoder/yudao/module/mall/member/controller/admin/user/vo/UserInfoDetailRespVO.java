package cn.iocoder.yudao.module.mall.member.controller.admin.user.vo;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 管理后台 - 用户个人信息 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 用户个人信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserInfoDetailRespVO extends UserInfoRespVO {

    /**
     * 员工状态 1为可用，0：不可用
     */
    private String ygStatus;

}
