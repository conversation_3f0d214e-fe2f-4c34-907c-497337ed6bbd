package cn.iocoder.yudao.module.mall.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/5/19
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RedissonLock {

    String key();

    /**
     * 等待时间
     * @return
     */
    long waitTime() default 10;

    /**
     * 锁的持有时间
     * @return
     */
    long leaseTime() default 5;

    /**
     * 时间单位
     * @return
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;
}
