package cn.iocoder.yudao.module.mall.external.bigscreen.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BigScreenApprovalOrderSummaryRespVO {
    @Schema(description = "待审批订单数量")
    private Long pendingApprovalOrderCount;

    @Schema(description = "审批通过订单数量")
    private Long approvedOrderCount;

    @Schema(description = "审核驳回订单数量")
    private Long rejectedOrderCount;

    @Schema(description = "待结算订单数")
    private Long pendingSettlementOrderCount;

    @Schema(description = "待结算金额")
    private BigDecimal pendingSettlementAmount;
}
