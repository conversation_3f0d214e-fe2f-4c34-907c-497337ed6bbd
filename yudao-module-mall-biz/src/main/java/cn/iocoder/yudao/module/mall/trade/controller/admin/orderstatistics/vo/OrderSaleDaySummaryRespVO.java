package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderSaleDaySummaryRespVO {

    @Schema(description = "时间字符串")
    private String timeString;

    @Schema(description = "订单个数")
    private Long count;

    @Schema(description = "订单金额")
    private BigDecimal amount;
}
