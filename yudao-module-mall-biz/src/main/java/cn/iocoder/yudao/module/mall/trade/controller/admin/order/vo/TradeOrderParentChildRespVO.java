package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 交易订单的父订单信息 Response VO")
@Data
public class TradeOrderParentChildRespVO {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "订单号")
    private String no;

    @Schema(description = "订单号")
    private String thirdOrderId;

    @Schema(description = "拆单状态")
    private Integer splitStatus;

    @Schema(description = "下单时间")
    private LocalDateTime submitTime;

    @Schema(description = "拆单时间")
    private LocalDateTime splitTime;

    @Schema(description = "订单金额")
    private BigDecimal orderPrice;

    @Schema(description = "商品金额")
    private BigDecimal productPrice;

    @Schema(description = "商品金额")
    private BigDecimal deliveryPrice;

    @Schema(description = "退款金额")
    private BigDecimal refundPrice;

    @Schema(description = "供应商id", example = "123")
    private Long supplierId;

    @Schema(description = "供应商类型", example = "1")
    private Integer supplierType;

    @Schema(description = "供应商名称", example = "京东")
    private String supplierName;

    @Schema(description = "订单状态", example = "1")
    private Integer status;

    /**
     * 子订单列表
     */
    @Schema(description = "管理后台 - 子订单列表")
    private List<TradeOrderParentChildRespVO> childOrders;

}
