package cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment;

import cn.iocoder.yudao.module.mall.product.enums.comment.ProductCommentAuditStatusEnum;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 商品评价 DO
 *
 * <AUTHOR>
 */
@TableName("product_comment")
@KeySequence("product_comment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCommentDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * spuId
     */
    private Long spuId;
    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 会员id
     */
    private Long memberId;
    /**
     * 会员昵称
     */
    private String nickName;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 评价星数 1->5
     */
    private Integer score;
    /**
     * 评价内容
     */
    private String content;
    /**
     * 上传图片地址，以逗号隔开
     */
    private String pics;
    /**
     * 是否匿名 1-是 0 否
     */
    private Integer anonymousFlag;
    /**
     * 审核状态  0-待审核 1-审核通过 2-审核拒绝
     * {@link ProductCommentAuditStatusEnum}
     */
    private Integer auditStatus;
    /**
     * 评价者ip
     */
    private String clientIp;
    /**
     * 评价者地区
     */
    private String clientArea;
    /**
     * 评价者点赞数
     */
    private Integer likeCount;
    /**
     * 评价回复数
     */
    private Integer replyCount;
    /**
     * 举报次数
     */
    private Integer reportCount;

}
