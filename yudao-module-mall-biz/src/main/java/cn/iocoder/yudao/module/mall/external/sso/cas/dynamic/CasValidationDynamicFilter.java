package cn.iocoder.yudao.module.mall.external.sso.cas.dynamic;

import cn.iocoder.yudao.module.mall.external.sso.cas.CasContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.jasig.cas.client.validation.Cas20ProxyReceivingTicketValidationFilter;
import org.jasig.cas.client.validation.Cas30ProxyReceivingTicketValidationFilter;
import org.jasig.cas.client.validation.Saml11TicketValidationFilter;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

/**
 * @author: HF
 * @description: TODO
 * @date: 2024/4/13 18:41
 */
@Slf4j
public class CasValidationDynamicFilter extends AbstractDynamicCasFilter {

    public CasValidationDynamicFilter(CasConfigService casConfigService) {
        super(casConfigService);
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException {
        try {
            CasContextHolder.clear();
            super.doFilter(servletRequest, servletResponse, filterChain);
        } catch(Exception e) {
            HttpServletRequest httpRequest = (HttpServletRequest)servletRequest;
            log.error("CasSso ticket validate exception： {}, {}", httpRequest.getRequestURL(), e.getMessage());
            handleCasException(servletRequest, servletResponse);
        }
    }

    @Override
    protected Filter genTargetFilter(FilterConfig config, Map<String, String> casConfigMap) throws ServletException {
        DummyFilterConfig newConfig = new DummyFilterConfig("", config.getServletContext(), casConfigMap);

        String casType = (String) casConfigMap.get("validation-type");
        if (casType != null) {
            casType = casType.toUpperCase();
        }
        Filter targetCasValidationFilter;
        switch (casType) {
            case "CAS":
                targetCasValidationFilter = new Cas20ProxyReceivingTicketValidationFilter();
                break;
            case "SAML":
                targetCasValidationFilter = new Saml11TicketValidationFilter();
                break;
            default:
                targetCasValidationFilter = new Cas30ProxyReceivingTicketValidationFilter();
        }
        targetCasValidationFilter.init(newConfig);
        return targetCasValidationFilter;
    }
}
