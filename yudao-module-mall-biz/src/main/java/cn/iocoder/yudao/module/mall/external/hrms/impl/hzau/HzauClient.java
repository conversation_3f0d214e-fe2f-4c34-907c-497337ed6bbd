package cn.iocoder.yudao.module.mall.external.hrms.impl.hzau;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.HrmsConfigDO;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.external.hrms.impl.hzau.dto.*;
import cn.iocoder.yudao.module.mall.external.hrms.impl.hzau.enums.HzauMethodEnum;
import cn.iocoder.yudao.module.mall.external.hrms.impl.hzau.vo.HzauAccessTokenVO;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Component
@Slf4j
public class HzauClient {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private ObjectMapper objectMapper;

    private HzauAccessTokenVO getAccessToken(HrmsConfigDO configDO) {
        Long tenantId = TenantContextHolder.getTenantId();
        if(tenantId == null) {
            log.error("租户ID不能为空");
            return null;
        }

        String lockKey = "lock:external:hzau:open:" + tenantId;
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        try {
            String valueKey = "external:hzau:open:token:" + tenantId;
            ValueOperations<String, HzauAccessTokenVO> valueOpt = redisTemplate.opsForValue();
            HzauAccessTokenVO accessTokenVO = valueOpt.get(valueKey);
            if(accessTokenVO == null || !accessTokenVO.checkValidTime()) {
                HzauAccessTokenRespDTO respDTO = queryHzauAccessToken(configDO);
                accessTokenVO = new HzauAccessTokenVO()
                        .setAccessToken(respDTO.getAccessToken()).setExpiresIn(respDTO.getExpiresIn()).setCreateTime(LocalDateTime.now());
                valueOpt.set(valueKey, accessTokenVO, 1, TimeUnit.DAYS);
            }

            return accessTokenVO;
        } catch (Exception e) {
            log.error("获取HzauAccessToken异常", e);
        } finally {
            if(lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }

        return null;
    }

    private HzauAccessTokenRespDTO queryHzauAccessToken(HrmsConfigDO configDO) {
        TypeReference<HzauResp<HzauAccessTokenRespDTO>> typeReference = new TypeReference<HzauResp<HzauAccessTokenRespDTO>>() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        Map<String, Object> param = new HashMap<>();
        param.put("key", configDO.getKey1());
        param.put("secret", configDO.getKey2());
        HzauResp<HzauAccessTokenRespDTO> resp = sendRequest(null, configDO.getServerUrl(), HzauMethodEnum.GET_ACCESS_TOKEN, param, javaType, true);
        return resp.getResult();
    }

    public HzauPageRespDTO<HzauStaffDTO> getStaffPage(HrmsConfigDO configDO, HzauStaffPageReqDTO reqDTO) {
        HzauAccessTokenVO accessTokenVO = getAccessToken(configDO);
        if(accessTokenVO == null) {
            return null;
        }
        Map<String, Object> params = BeanUtil.beanToMap(reqDTO, new HashMap<>(), false, true);
        TypeReference<HzauResp<HzauPageRespDTO<HzauStaffDTO>> > typeReference = new TypeReference<HzauResp<HzauPageRespDTO<HzauStaffDTO>> >() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        HzauResp<HzauPageRespDTO<HzauStaffDTO>> resp = sendRequest(accessTokenVO.getAccessToken(), configDO.getServerUrl(), HzauMethodEnum.GET_STAFF_LIST, params, javaType, false);
        return resp.getResult();
    }

    public HzauStaffDTO getStaffInfo(HrmsConfigDO configDO, String userNo) {
        HzauStaffPageReqDTO reqDTO = new HzauStaffPageReqDTO().setGH(userNo);
        HzauPageRespDTO<HzauStaffDTO> resp = getStaffPage(configDO, reqDTO);
        if(resp != null && CollUtil.isNotEmpty(resp.getList())) {
            return resp.getList().get(0);
        }

        return null;
    }

    public HzauPageRespDTO<HzauDeptDTO> getDeptPage(HrmsConfigDO configDO, HzauDeptPageReqDTO reqDTO) {
        HzauAccessTokenVO accessTokenVO = getAccessToken(configDO);
        if(accessTokenVO == null) {
            return null;
        }
        Map<String, Object> params = BeanUtil.beanToMap(reqDTO, new HashMap<>(), false, true);
        TypeReference<HzauResp<HzauPageRespDTO<HzauDeptDTO>> > typeReference = new TypeReference<HzauResp<HzauPageRespDTO<HzauDeptDTO>> >() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        HzauResp<HzauPageRespDTO<HzauDeptDTO>> resp = sendRequest(accessTokenVO.getAccessToken(), configDO.getServerUrl(), HzauMethodEnum.GET_DEPT_LIST, params, javaType, true);
        return resp.getResult();
    }

    private <T> HzauResp<T> sendRequest(String accessToken, String serverUrl, HzauMethodEnum methodEnum, Map<String, Object> param, JavaType javaType, boolean checkFail) {
        HzauResp<T> resp = sendRequest(accessToken, serverUrl, methodEnum, param, javaType);
        if (checkFail && !resp.isSuccess()) {
            throw exception(ErrorCodeConstants.HRMS_HZAU_FAIL, resp.getMessage());
        }

        return resp;
    }

    private <T> HzauResp<T> sendRequest(String accessToken, String serverUrl, HzauMethodEnum methodEnum, Map<String, Object> param, JavaType javaType) {
        try {
            StringBuilder comUrl = new StringBuilder(serverUrl);
            comUrl.append(methodEnum.getMethodName());
            if(accessToken != null) {
                comUrl.append("?access_token=");
                comUrl.append(accessToken);
            }

            HttpRequest req = HttpRequest.get(comUrl.toString());
            req.form(param);

            log.info("{} param: {}", methodEnum.getMethodName(), param);
            HttpResponse response = req.timeout(5000).execute();
            log.info("response: {}", response);
            if (response.isOk()) {
                String body = response.body();
                log.info("{} resp: {}", methodEnum.getMethodName(), body);
                JSONObject respObj = JSONObject.parseObject(body);
                if (respObj.containsKey("code")) {
                    return objectMapper.readValue(body, javaType);
                } else {
                    log.error("{} resp no code...", methodEnum.getMethodName());
                    throw exception(ErrorCodeConstants.HRMS_HZAU_FAIL,"返回格式不正确");
                }
            } else {
                log.error("{} error: {}", methodEnum.getMethodName(), response.getStatus());
                throw exception(ErrorCodeConstants.HRMS_HZAU_FAIL,"结果异常");
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("{} error: ", methodEnum.getMethodName(), e);
            throw exception(ErrorCodeConstants.HRMS_HZAU_FAIL,"系统异常");
        }
    }

}
