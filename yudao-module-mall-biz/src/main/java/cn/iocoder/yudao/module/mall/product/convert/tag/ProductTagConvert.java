package cn.iocoder.yudao.module.mall.product.convert.tag;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagSimpleVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 标签 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductTagConvert {

    ProductTagConvert INSTANCE = Mappers.getMapper(ProductTagConvert.class);

    ProductTagDO convert(ProductTagCreateReqVO bean);

    ProductTagDO convert(ProductTagUpdateReqVO bean);

    ProductTagRespVO convert(ProductTagDO bean);

    List<ProductTagRespVO> convertList(List<ProductTagDO> list);

    PageResult<ProductTagRespVO> convertPage(PageResult<ProductTagDO> page);

    ProductTagSimpleVO convert02(ProductTagDO bean);

    List<ProductTagSimpleVO> convertList02(List<ProductTagDO> tagList);

}
