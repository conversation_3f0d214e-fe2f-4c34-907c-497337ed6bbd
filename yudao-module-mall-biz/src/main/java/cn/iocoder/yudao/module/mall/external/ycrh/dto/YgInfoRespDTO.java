package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 员工信息查询结果DTO
 */
@Data
public class YgInfoRespDTO {

    /**
     * 员工姓名
     */
    @JsonProperty("ygmc")
    private String ygName;
    /**
     * 员工编号
     */
    @JsonProperty("ygbh")
    private String ygNo;
    /**
     * 银行账号
     */
    @JsonProperty("yhzh")
    private String bankAccount;
    /**
     * 部门编号
     */
    @JsonProperty("bmbh")
    private String departmentNo;
    /**
     * 部门名称
     */
    @JsonProperty("bmmc")
    private String departmentName;
    /**
     * 员工状态：1为可用，0：不可用
     */
    @JsonProperty("isok")
    private String status;
    /**
     * 人员类型
     */
    @JsonProperty("rylx")
    private String ygType;
    /**
     * 人员类型名称
     */
    @JsonProperty("rylxmc")
    private String ygTypeName;
    /**
     * 人员类别代码
     */
    @JsonProperty("rylbdm")
    private String ygCategoryCode;
    /**
     * 人员类别名称
     */
    @JsonProperty("rylbmc")
    private String ygCategoryName;
    /**
     * 公务卡号
     */
    @JsonProperty("gwkh")
    private String cardNo;
    /**
     * 职务名称
     */
    @JsonProperty("zwmc")
    private String jobTitle;
    /**
     * 联系电话
     */
    @JsonProperty("lxdh")
    private String mobile;


}
