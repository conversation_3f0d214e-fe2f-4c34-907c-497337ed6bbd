package cn.iocoder.yudao.module.mall.product.controller.app.wish.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "商城前台 - 心愿单创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppProductWishCreateReqVO extends AppProductWishBaseVO {

    private String userName;

    private Long userId;

}
