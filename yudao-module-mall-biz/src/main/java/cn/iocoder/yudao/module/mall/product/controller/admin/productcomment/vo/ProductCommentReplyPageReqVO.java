package cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo;

import lombok.*;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品评价回复分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductCommentReplyPageReqVO extends PageParam {

    @Schema(description = "评价id")
    private Long commentId;

    @Schema(description = "供应商id")
    private Long supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "会员id")
    private Long memberId;

    @Schema(description = "会员昵称")
    private String nickName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "评价内容")
    private String content;

    @Schema(description = "审核状态 0-待审核 1-审核通过 2-审核驳回")
    private Integer auditStatus;

    @Schema(description = "回复者ip")
    private String clientIp;

    @Schema(description = "回复者地区")
    private String clientArea;

    @Schema(description = "举报次数")
    private Integer reportCount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
