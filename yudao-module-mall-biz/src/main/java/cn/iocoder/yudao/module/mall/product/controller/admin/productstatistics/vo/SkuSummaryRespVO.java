package cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SkuSummaryRespVO {
    @Schema(description = "统计sku总数")
    private Long total;

    @Schema(description = "上架sku总数")
    private Long listingTotal;

    @Schema(description = "下架sku总数")
    private Long delistingTotal;

    @Schema(description = "分类个数")
    private Long categoryCount;
}
