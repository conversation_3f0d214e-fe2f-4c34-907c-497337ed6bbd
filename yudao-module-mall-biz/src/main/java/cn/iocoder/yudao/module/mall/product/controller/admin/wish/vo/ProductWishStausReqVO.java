package cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 心愿单 状态修改 VO
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ProductWishStausReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 状态: 0-待回复 1-已回复
     */
    @Schema(description = "状态")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "回复内容")
    @NotEmpty(message = "回复内容不能为空")
    private String replyContent;

    @Schema(description = "回复扩展参数")
    private String extParams;

}
