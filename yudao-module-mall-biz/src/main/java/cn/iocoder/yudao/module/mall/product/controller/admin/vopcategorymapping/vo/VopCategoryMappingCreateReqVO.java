package cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 京东分类映射创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VopCategoryMappingCreateReqVO extends VopCategoryMappingBaseVO {

}
