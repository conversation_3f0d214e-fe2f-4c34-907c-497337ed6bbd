package cn.iocoder.yudao.module.mall.product.controller.app.skusearch.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "APP前台 - 商品搜索分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SkuContentDemoPageReqVO extends PageParam {

    @Schema(description = "关键字")
    private String keyword;

    @Schema(description = "分类ID路径，以逗号分隔")
    private String categoryIdPath;

    @Schema(description = "品牌ID")
    private Long brandId;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "规格值")
    private SpecValueVo specValueVo;

    @Data
    @ToString(callSuper = true)
    @AllArgsConstructor
    public static class SpecValueVo {
        private Long specId;
        private String specValue;
    }
}
