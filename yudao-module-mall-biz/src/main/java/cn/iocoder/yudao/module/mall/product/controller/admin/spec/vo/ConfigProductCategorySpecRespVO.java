package cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

@Schema(description = "管理后台 - 商品规格 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ConfigProductCategorySpecRespVO extends ConfigProductCategorySpecBaseVO {

    @Schema(description = "规格Id", required = true)
    private Long id;

    @Schema(description = "创建时间")
    private Date createTime;

}
