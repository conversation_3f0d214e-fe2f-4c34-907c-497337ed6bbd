package cn.iocoder.yudao.module.mall.external.xy2;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.BpmConfigDO;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.external.bpm.BpmClientConfig;
import cn.iocoder.yudao.module.mall.external.xy2.dto.Xy2PullBpmInfoRespDTO;
import cn.iocoder.yudao.module.mall.external.xy2.dto.Xy2PushBpmInfoReqDTO;
import cn.iocoder.yudao.module.mall.external.xy2.dto.Xy2Resp;
import cn.iocoder.yudao.module.mall.external.xy2.enums.Xy2MethodEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 湘雅2客户端
 */
@Service
@Slf4j
public class Xy2Client {

    @Resource
    private BpmClientConfig bpmClientConfig;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 获取湘雅二审批流配置
     * @return
     */
    private BpmConfigDO getConfig() {
        BpmConfigDO bpmConfigDO = bpmClientConfig.getBpmConfigDO();
        if(bpmConfigDO != null) {
            if(StringUtils.isBlank(bpmConfigDO.getParamExt1())) {
                throw new ServiceException(ErrorCodeConstants.XY2_CONFIG_NULL);
            }
            if(StringUtils.isBlank(bpmConfigDO.getParamExt2())) {
                throw new ServiceException(ErrorCodeConstants.XY2_CONFIG_NULL);
            }
            if(StringUtils.isBlank(bpmConfigDO.getParamExt3())) {
                throw new ServiceException(ErrorCodeConstants.XY2_CONFIG_NULL);
            }
            return bpmConfigDO;
        } else {
            log.error("湘雅审批流配置为空");
            throw new ServiceException(ErrorCodeConstants.XY2_CONFIG_FAIL);
        }
    }

    /**
     * 推送审批流信息
     * @param xy2PushBpmInfoReqDTO
     * @return
     */
    public String pushBpmInfo(Xy2PushBpmInfoReqDTO xy2PushBpmInfoReqDTO) {
        Map<String, Object> params = objectMapper.convertValue(xy2PushBpmInfoReqDTO, new TypeReference<Map<String, Object>>() {});
        TypeReference<Xy2Resp<String>> typeReference = new TypeReference<Xy2Resp<String>>() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        Xy2Resp<String> resp = sendRequest(Xy2MethodEnum.PUSH_BPM_INFO, params, javaType, false);
        return resp.getData();
    }

    /**
     * 拉取审批流信息
     * @param bpmNo
     * @return
     */
    public Xy2PullBpmInfoRespDTO pullBpmStatus(String bpmNo) {
        Map<String, Object> data = new Tree<>();
        data.put("bpmNo", bpmNo);
        Map<String, Object> params = objectMapper.convertValue(data, new TypeReference<Map<String, Object>>() {});
        TypeReference<Xy2Resp<Xy2PullBpmInfoRespDTO>> typeReference = new TypeReference<Xy2Resp<Xy2PullBpmInfoRespDTO>>() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        Xy2Resp<Xy2PullBpmInfoRespDTO> resp = sendRequest(Xy2MethodEnum.PULL_BPM_STATUS, params, javaType, false);
        return resp.getData();
    }

    /**
     * 取消审批流信息
     * @param bpmNo
     * @param reason
     * @return
     */
    public String cancelBpmInfo(String bpmNo, String reason) {
        Map<String, Object> data = new Tree<>();
        data.put("bpmNo", bpmNo);
        data.put("reason", reason);
        Map<String, Object> params = objectMapper.convertValue(data, new TypeReference<Map<String, Object>>() {});
        TypeReference<Xy2Resp<String>> typeReference = new TypeReference<Xy2Resp<String>>() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        Xy2Resp<String> resp = sendRequest(Xy2MethodEnum.CANCEL_BPM_INFO, params, javaType, false);
        return resp.getData();
    }

    private <T> Xy2Resp<T> sendRequest(Xy2MethodEnum methodEnum, Map<String, Object> param, JavaType javaType, boolean checkFail) {
        Xy2Resp<T> resp = sendRequest(methodEnum, param, javaType);
        if (checkFail && !resp.isSuccess()) {
            throw exception(ErrorCodeConstants.XY2_FAIL, resp.getMsg());
        }

        return resp;
    }

    private <T> Xy2Resp<T> sendRequest(Xy2MethodEnum methodEnum, Map<String, Object> param, JavaType javaType) {
        BpmConfigDO config = getConfig();
        try {
            StringBuilder requestUrl = new StringBuilder(config.getParamExt1());
            requestUrl.append(methodEnum.getMethodName());

            String appKey = config.getParamExt2();
            String appSecret = config.getParamExt3();
            String nonce = RandomUtil.randomString(20);
            String timestamp = String.valueOf(System.currentTimeMillis());
            String body = JSON.toJSONString(param);
            String signature = DigestUtil.sha256Hex(appKey + nonce + timestamp + body + appSecret);

            HttpRequest req = HttpUtil.createPost(requestUrl.toString())
                    .header("Content-Type", "application/json")
                    .header("app-token", appKey)
                    .header("app-nonce", nonce)
                    .header("app-timestamp", timestamp)
                    .header("app-sign", signature)
                    .body(body);
            HttpResponse response = req.timeout(20000).execute();
            log.info("xy2 {} body: {} response：{}", methodEnum.getMethodName(), body, response);
            if (response.isOk()) {
                String responseBody = response.body();
                JSONObject respObj = JSONObject.parseObject(responseBody);
                if (respObj.containsKey("code")) {
                    Xy2Resp<T> resp = objectMapper.readValue(responseBody, javaType);
                    if(resp.isSuccess()) {
                        return resp;
                    } else {
                        throw exception(ErrorCodeConstants.XY2_FAIL,resp.getMsg());
                    }
                } else {
                    throw exception(ErrorCodeConstants.XY2_FAIL,"返回格式不正确");
                }
            } else {
                log.info("xy2 {} fail, response：{}", methodEnum.getMethodName(), response);
                throw exception(ErrorCodeConstants.XY2_FAIL,"结果异常");
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("xy2 {} body: {} error: ", methodEnum.getMethodName(), param, e);
            throw exception(ErrorCodeConstants.XY2_FAIL,"系统异常");
        }
    }
}
