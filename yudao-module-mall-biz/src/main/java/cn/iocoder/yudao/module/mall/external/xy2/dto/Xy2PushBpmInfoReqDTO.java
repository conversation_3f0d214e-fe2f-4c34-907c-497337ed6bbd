package cn.iocoder.yudao.module.mall.external.xy2.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class Xy2PushBpmInfoReqDTO {
    /**
     * 审批单号
     */
    private String bpmNo;

    /**
     * 审批原因
     */
    private String reason;

    /**
     * 申请人名称
     */
    private String applicantName;

    /**
     * 申请人编号
     */
    private String applicantNo;

    /**
     * 采购单金额
     */
    private String purchaseTotalPrice;

    /**
     * 审批状态变化通知回调url
     */
    private String callbackUrl;

    /**
     * 附件列表
     */
    private List<String> files;

    /**
     * 经济分类名称
     */
    private String economyClassName;

    /**
     * 经济分类
     */
    private String economyClass;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 订单明细
     */
    private List<Xy2OrderDataDTO> ordersData;
}
