package cn.iocoder.yudao.module.mall.external.assets.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 资产建档申请DTO
 */
@Data
public class ExtAssetsCreateDTO {

    /**
     * 建账人 工号
     */
    private String user;
    /**
     * 建账人 姓名
     */
    private String userName;
    /**
     * 建账部门 编号
     */
    private String dept;
    /**
     * 建账部门 名称
     */
    private String deptName;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单明细编号
     */
    private String orderDetailNo;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 型号
     */
    private String model;
    /**
     * 规格
     */
    private String format;
    /**
     * 销售单位
     */
    private String supplier;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 发票号
     */
    private String invoice;
    /**
     * 发票图片链接, 洛比特有
     */
    private String[] invoiceImg;
    /**
     * 发票图片链接
     */
    private String invoiceUrl;
    /**
     * 购置日期
     */
    private String buyDate;
    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 商品链接
     */
    private String skuUrl;
    /**
     * 扩展字段，供应商联系人
     */
    private String extSupUser;
    /**
     * 扩展字段，供应商联系人电话
     */
    private String extSupPhone;


}
