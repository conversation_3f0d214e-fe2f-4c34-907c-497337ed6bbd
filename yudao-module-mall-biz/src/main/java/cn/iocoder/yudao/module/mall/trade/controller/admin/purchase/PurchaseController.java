package cn.iocoder.yudao.module.mall.trade.controller.admin.purchase;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.basisconfig.BasisConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.basisconfig.BasisConfigService;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo.PurchaseDetailRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo.PurchasePageItemRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo.PurchasePageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.ApprovalResultRespVO;
import cn.iocoder.yudao.module.mall.trade.convert.order.TradeOrderConvert;
import cn.iocoder.yudao.module.mall.trade.convert.purchase.PurchaseConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import cn.iocoder.yudao.module.mall.trade.enums.purchase.TradePurchaseStatusEnum;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.trade.service.purchase.PurchaseService;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.trade.enums.ErrorCodeConstants.PURCHASE_NOT_EXISTS;

@Tag(name = "管理后台 - 采购单")
@RestController
@RequestMapping("/trade/purchase")
@Validated
@Slf4j
public class PurchaseController {

    @Resource
    private PurchaseService purchaseService;
    @Resource
    private TradeOrderService tradeOrderService;
    @Resource
    private BasisConfigService basisConfigService;
    @Resource
    private MemberUserService memberUserService;

    @GetMapping("/get-basis-config")
    @Operation(summary = "获得基础配置")
    @PreAuthorize("@ss.hasPermission('trade:purchase:query')")
    public CommonResult<Map<String, Boolean>> getBasisConfig() {
        Map<String, Boolean> result = new HashMap<>();
        BasisConfigDO basisConfigDO = basisConfigService.getBasisConfig();
        if(basisConfigDO != null) {
            result.put("isBpm", basisConfigDO.needApprove());
            result.put("isProject", basisConfigDO.needProject());
        }

        return CommonResult.success(result);
    }

    @GetMapping("/page")
    @Operation(summary = "获得采购单分页")
    @PreAuthorize("@ss.hasPermission('trade:purchase:query')")
    public CommonResult<PageResult<PurchasePageItemRespVO>> getPurchasePage(PurchasePageReqVO reqVO) {
        // 查询分页
        PageResult<PurchaseDO> pageResult = purchaseService.getPurchasePage(reqVO);
        return CommonResult.success(PurchaseConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/page/v2")
    @Operation(summary = "获得采购单分页")
    @PreAuthorize("@ss.hasPermission('trade:purchase:query')")
    public CommonResult<PageResult<PurchasePageItemRespVO>> getPurchasePageV2(PurchasePageReqVO reqVO) {
        // 查询分页
        PageResult<PurchaseDO> pageResult = purchaseService.getPurchasePage(reqVO);
        PageResult<PurchasePageItemRespVO> voResult = PurchaseConvert.INSTANCE.convertPage(pageResult);

        if(CollUtil.isNotEmpty(voResult.getList())) {
            List<Long> purchaseIds = voResult.getList().stream().map(PurchasePageItemRespVO::getId).collect(Collectors.toList());
            List<TradeOrderDO> orders = tradeOrderService.getOrderListByPurchaseId(purchaseIds);
            List<Long> orderIds = orders.stream().map(TradeOrderDO::getId).collect(Collectors.toList());
            List<TradeOrderItemDO> orderItems = tradeOrderService.getOrderItemListByOrderId(orderIds);
            Map<Long, List<TradeOrderDO>> purchaseMap = CollectionUtils.convertMultiMap(orders, TradeOrderDO::getPurchaseId);

            if(CollUtil.isNotEmpty(purchaseMap)) {
                voResult.getList().forEach(voPurchase -> {
                    List<TradeOrderDO> pOrders = purchaseMap.get(voPurchase.getId());
                    if(CollUtil.isEmpty(pOrders)) {
                        return;
                    }
                    List<Long> pOrderIds = pOrders.stream().map(TradeOrderDO::getId).collect(Collectors.toList());
                    List<TradeOrderItemDO> pOrderItems = orderItems.stream().filter(oi -> pOrderIds.contains(oi.getOrderId())).collect(Collectors.toList());

                    voPurchase.setOrders(TradeOrderConvert.INSTANCE.convertOrders(pOrders, pOrderItems));
                });
            }
        }

        return CommonResult.success(voResult);
    }

    @GetMapping("/detail")
    @Operation(summary = "获得采购单详情")
    @PreAuthorize("@ss.hasPermission('trade:purchase:query')")
    public CommonResult<PurchaseDetailRespVO> getPurchaseDetail(@RequestParam("id") Long purchaseId) {
        PurchaseDO purchaseDO = purchaseService.getPurchaseById(purchaseId);
        if(purchaseDO == null) {
            return CommonResult.error(exception(PURCHASE_NOT_EXISTS, purchaseId));
        }

        MemberUserDO memberUserDO = memberUserService.getUser(purchaseDO.getUserId());
        if(memberUserDO != null && StringUtils.isNotBlank(memberUserDO.getName())) {
            purchaseDO.setUserName(memberUserDO.getName());
        }

        // 查询采购单
        PurchaseDetailRespVO result = PurchaseConvert.INSTANCE.convert2PurchaseDetailRespVO(purchaseDO);
        List<TradeOrderDO> orders = tradeOrderService.getOrderListByPurchaseId(purchaseId);
        if(CollUtil.isNotEmpty(orders)) {
            List<Long> orderIds = orders.stream().map(TradeOrderDO::getId).collect(Collectors.toList());
            List<TradeOrderItemDO> orderItems = tradeOrderService.getOrderItemListByOrderId(orderIds);
            result.setOrders(TradeOrderConvert.INSTANCE.convertOrders(orders, orderItems));
        }

        // 审批流结果
        String auditResult = purchaseDO.getAuditResult();
        if (StringUtils.isNotBlank(auditResult)) {
            result.setBpmResult(JSON.parseArray(auditResult, ApprovalResultRespVO.class));
        }

        return CommonResult.success(result);
    }

    @PutMapping("/cancel")
    @Operation(summary = "取消采购单")
    @PreAuthorize("@ss.hasPermission('trade:purchase:update')")
    public CommonResult<Boolean> cancelPurchase(@RequestParam("id") Long purchaseId) {
        // 取消采购单
        purchaseService.cancelPurchase(purchaseId, TradePurchaseStatusEnum.ADMIN_CANCEL);
        return CommonResult.success(true);
    }

    @PostMapping("/pull-bpm-status")
    @Operation(summary = "更新审批流状态")
    @PreAuthorize("@ss.hasPermission('trade:purchase:update')")
    public CommonResult<Boolean> pullBpmStatus(@RequestParam("id") Long purchaseId) {
        // 取消采购单
        purchaseService.pullPurchaseAuditStatus(purchaseId);
        return CommonResult.success(true);
    }

}