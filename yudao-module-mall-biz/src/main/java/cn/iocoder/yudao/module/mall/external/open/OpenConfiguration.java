package cn.iocoder.yudao.module.mall.external.open;

import cn.iocoder.yudao.module.mall.external.open.filter.OpenRequestFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenConfiguration {

    @Bean
    public FilterRegistrationBean<OpenRequestFilter> openRequestFilterRegistration() {
        FilterRegistrationBean<OpenRequestFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new OpenRequestFilter());
        registration.addUrlPatterns("/app-api/mall/open/*");
        registration.setOrder(1);
        return registration;
    }

}
