package cn.iocoder.yudao.module.mall.external.ycrh.enums;

import lombok.Getter;

/**
 * 业财融合，上传凭证文件类型
 * <AUTHOR>
 * @date 2025/4/8
 */
@Getter
public enum YcrhFileTypeEnum {
    /**
     * 附件类型
     */
    RECEIPT("00","凭证信息"),
    BIZ_SUBMIT("01","业务报销单"),
    INCOME_APPLY("02","收入申报税表"),
    ELE_INVOICE("03","电子发票"),
    PAPER_INVOICE("04","纸质发票"),
    CONTRACT("05","合同信息"),
    BANK_REPLY("06","银行回单"),
    SUBMIT_APPLY("07","报销申请单"),
    BUDGET_APPLY("08","项目预算申请书"),
    BPM_PROOF("09","审批证明类"),
    RESEARCH_PROJECT("10","科研项目入账单"),
    RESEARCH_END("11","科研结题结转单"),
    BPM_DETAIL("98","审批过程影像"),
    OTHER("99","其他附件");


    private String type;

    private String desc;

    YcrhFileTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static YcrhFileTypeEnum getByType(String type) {
        for (YcrhFileTypeEnum item : YcrhFileTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item;
            }
        }
        return null;
    }
}
