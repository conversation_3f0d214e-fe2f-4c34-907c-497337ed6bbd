package cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.math.BigDecimal;
import javax.validation.constraints.*;

/**
 * 采购 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class PurchaseBaseVO {

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "订单数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单数不能为空")
    private Integer orderCount;

    @Schema(description = "采购总金额")
    private BigDecimal totalAmount;

    @Schema(description = "项目编号")
    private String projectNo;

    @Schema(description = "采购原因")
    private String purchaseReason;

    @Schema(description = "审批附件")
    private String attachments;

}
