package cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo;

import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.checkSkuSaleList.CheckSkuSaleGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSellPrice.GetSellPriceGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSimilarSkuList.GetSimilarSkuGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuImageList.SkuImageItemGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuStateList.GetSkuStateGoodsResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class VopGoodsDetailResp {

    /**
     * 详细图片信息
     */
    @Schema(description = "详细图片信息")
    List<SkuImageItemGoodsResp> skuImageList;

    /**
     * 商品可售性
     */
    @Schema(description = "商品可售性")
    List<CheckSkuSaleGoodsResp> skuSaleList;

    /**
     * 商家状态信息
     */
    @Schema(description = "商家状态信息")
    List<GetSkuStateGoodsResp> skuStateList;

    /**
     * 商品价格信息
     */
    @Schema(description = "商品价格信息")
    List<GetSellPriceGoodsResp> priceGoodsList;

    /**
     * 同类商品推荐信息
     */
    @Schema(description = "同类商品推荐信息")
    List<GetSimilarSkuGoodsResp> similarSkuGoodsList;


    /**
     * 自定义的商品推荐信息
     */
    @Schema(description = "自定义的商品推荐信息")
    List<VopSkuGoodsPageItem> suggestGoodsList;

}
