package cn.iocoder.yudao.module.mall.product.controller.app.skusearch;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.app.skusearch.vo.SkuContentDemoPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.skusearch.vo.SkuContentDemoRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.params.GoodsStockInfoReq;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.AreaStockInfoResp;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsCategoryItem;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsDetailResp;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsSearchPageResultResp;
import cn.iocoder.yudao.module.mall.product.convert.skusearch.SkuContentDemoConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.SkuContentDemoDO;
import cn.iocoder.yudao.module.mall.product.service.skusearch.SkuContentDemoService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsCategoryService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.req.*;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.checkSkuSaleList.CheckSkuSaleGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getCanCodList.GetCanCodGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getGiftInfoSkuList.GetSkuGiftGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSellPrice.GetSellPriceGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSimilarSkuList.GetSimilarSkuGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.GetSkuPoolInfoGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuImageList.GetSkuImageGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuStateList.GetSkuStateGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getYanbaoSkuList.GetYanbaoSkuGoodsResp;
import com.jd.open.api.sdk.response.vopsp.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;

/**
 * 商品SKU搜索 Demo
 * @Description
 * <AUTHOR>
 * @Date 2023/6/9 17:08
 */

@RestController
@RequestMapping("/product/sku_content_demo")
@Slf4j
@Tag(name = "商品SKU搜索Demo接口")
public class AppSkuContentDemoController {

    @Resource
    SkuContentDemoService skuContentDemoService;

    /**
     *  商品搜索
     * @param pageReqVO
     * @return
     */
    @GetMapping("search")
    @Operation(summary = "商品搜索")
    @PermitAll
    public CommonResult<PageResult<SkuContentDemoRespVO>> getChildCategoryList(SkuContentDemoPageReqVO pageReqVO) {
        PageResult<SkuContentDemoDO> pageResult = skuContentDemoService.seachByPage(pageReqVO);
        return CommonResult.success(SkuContentDemoConvert.INSTANCE.convertPage(pageResult));
    }

}
