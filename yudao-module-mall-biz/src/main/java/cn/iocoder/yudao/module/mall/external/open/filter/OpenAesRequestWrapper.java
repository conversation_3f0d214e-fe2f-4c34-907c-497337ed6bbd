package cn.iocoder.yudao.module.mall.external.open.filter;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import com.baomidou.mybatisplus.core.toolkit.AES;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 请求体数据通过AES进行解密
 * <AUTHOR>
 * @date 2025-08-06
 */
@Slf4j
public class OpenAesRequestWrapper extends HttpServletRequestWrapper {

    private String decryptedBody;

    public OpenAesRequestWrapper(HttpServletRequest request) {
        super(request);
    }

    public void decryptRequestBody(String encodingKey) {
        HttpServletRequest request = (HttpServletRequest) getRequest();
        String sourceBody = null;
        try {
            sourceBody = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("获取请求体数据异常: {}", request.getRequestURI(), e);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_DATA_PARSE_FAIL);
        }

        if(StrUtil.isNotBlank(encodingKey)) {
            try {
                this.decryptedBody = AES.decrypt(sourceBody, encodingKey);
            } catch (Exception ee) {
                log.error("请求体解密失败: {}, {}, {}", request.getRequestURI(), sourceBody, ee.getMessage());
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPEN_APP_DATA_DECRYPT_FAIL);
            }
        } else {
            this.decryptedBody = sourceBody;
        }
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if(StrUtil.isBlank(decryptedBody)) {
            return super.getInputStream();
        }

        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(decryptedBody.getBytes());
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return byteArrayInputStream.available() == 0;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
                throw new UnsupportedOperationException();
            }

            @Override
            public int read() throws IOException {
                return byteArrayInputStream.read();
            }
        };
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(this.getInputStream()));
    }

    public static void main(String[] args) {
        String source = "{}";
//        String key = RandomUtil.randomString(16);
        String key = "a8mlxf4faru9vzga";
        String encryptStr = AES.encrypt(source, key);
        String decryptStr = AES.decrypt(encryptStr, key);
        System.out.println("aeskey====:" + key);
        System.out.println("encryptStr=====" + encryptStr);
        System.out.println("decryptStr=====" + decryptStr);
    }

}
