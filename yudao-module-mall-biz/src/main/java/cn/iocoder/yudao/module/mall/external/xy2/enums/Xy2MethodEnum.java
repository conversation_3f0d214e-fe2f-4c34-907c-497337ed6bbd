package cn.iocoder.yudao.module.mall.external.xy2.enums;

import lombok.Getter;

/**
 * 湘雅二接口枚举
 */
@Getter
public enum Xy2MethodEnum {
    /**
     * 湘雅二审批接口
     */
    PUSH_BPM_INFO("pushBpmInfo","推送审批信息"),
    PULL_BPM_STATUS("pullBpmStatus","拉取审批状态"),
    CANCEL_BPM_INFO("cancelBpmInfo","取消审批");

    private String methodName;

    private String desc;

    Xy2MethodEnum(String methodName, String desc) {
        this.methodName = methodName;
        this.desc = desc;
    }

    public static Xy2MethodEnum getByMethodName(String methodName) {
        for (Xy2MethodEnum item : Xy2MethodEnum.values()) {
            if (item.getMethodName().equals(methodName)) {
                return item;
            }
        }
        return null;
    }
}
