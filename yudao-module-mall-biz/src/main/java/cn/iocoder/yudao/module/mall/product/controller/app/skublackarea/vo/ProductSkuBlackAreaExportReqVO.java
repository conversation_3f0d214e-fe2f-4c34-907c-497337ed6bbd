package cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品sku禁售区域 Excel 导出 Request VO，参数和 ProductSkuBlackAreaPageReqVO 是一致的")
@Data
public class ProductSkuBlackAreaExportReqVO {

    @Schema(description = "spu编号")
    private Long spuId;

    @Schema(description = "商品 SPU 名字")
    private String spuName;

    @Schema(description = "供应商id")
    private Long supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] beginTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] endTime;

    @Schema(description = "所在省份")
    private Long provinceId;

    @Schema(description = "所在城市")
    private Long cityId;

    @Schema(description = "所在区县")
    private Long countyId;

    @Schema(description = "状态 1-启用 0-禁用")
    private Boolean status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
