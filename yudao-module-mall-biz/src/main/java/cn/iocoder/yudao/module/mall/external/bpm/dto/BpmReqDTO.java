package cn.iocoder.yudao.module.mall.external.bpm.dto;


import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.ApprovalUserInfoDTO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import lombok.Data;

import java.util.List;

/**
 * 审批发起参数
 */
@Data
public class BpmReqDTO {

    private PurchaseDO purchaseDO;
    private List<Long> orderIds;
    private List<ApprovalUserInfoDTO> approvalUserInfos;

}
