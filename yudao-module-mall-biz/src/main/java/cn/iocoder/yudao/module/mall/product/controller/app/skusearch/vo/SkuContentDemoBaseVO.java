package cn.iocoder.yudao.module.mall.product.controller.app.skusearch.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


@Data
public class SkuContentDemoBaseVO {

    @Schema(description = "商品SKU-ID")
    private String skuId;

    @Schema(description = "商品名称")
    private String spuName;

    @Schema(description = "分类ID")
    private Long categoryId1;

    @Schema(description = "分类名称")
    private String categoryName1;

    @Schema(description = "品牌ID")
    private Long brandId;

    @Schema(description = "商品状态")
    private Integer status;

    @Schema(description = "SKU规格值")
    private List<String> specValues;

}
