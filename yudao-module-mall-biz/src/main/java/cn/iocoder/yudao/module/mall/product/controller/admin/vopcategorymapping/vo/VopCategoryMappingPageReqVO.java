package cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 京东分类映射分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VopCategoryMappingPageReqVO extends PageParam {

    @Schema(description = "末级分类ID")
    private Long lastCategoryId;

    @Schema(description = "末级分类名称")
    private String lastCategoryName;

    @Schema(description = "完整分类id -分隔")
    private String fullCategoryId;

    @Schema(description = "完整分类名称")
    private String fullCategoryName;

    @Schema(description = "vop末级分类ID")
    private Long vopLastCategoryId;

    @Schema(description = "vop末级分类名称")
    private String vopLastCategoryName;

    @Schema(description = "vop完整分类id -分隔")
    private String vopFullCategoryId;

    @Schema(description = "vop完整分类名称")
    private String vopFullCategoryName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
