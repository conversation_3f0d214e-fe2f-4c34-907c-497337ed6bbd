package cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商品评价 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ProductCommentExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("订单id")
    private Long orderId;

    @ExcelProperty("订单编号")
    private String orderNo;

    @ExcelProperty("skuId")
    private Long skuId;

    @ExcelProperty("spuId")
    private Long spuId;

    @ExcelProperty("供应商id")
    private Long supplierId;

    @ExcelProperty("供应商名称")
    private String supplierName;

    @ExcelProperty("会员id")
    private Long memberId;

    @ExcelProperty("会员昵称")
    private String nickName;

    @ExcelProperty("头像")
    private String avatar;

    @ExcelProperty("评价星数 1->5")
    private Integer score;

    @ExcelProperty("评价内容")
    private String content;

    @ExcelProperty("上传图片地址，以逗号隔开")
    private String pics;

    @ExcelProperty("是否匿名 1-是 0 否")
    private Integer anonymousFlag;

    @ExcelProperty("审核状态 0-待审核 1-审核通过 2-审核驳货")
    private Integer auditStatus;

    @ExcelProperty("评价者ip")
    private String clientIp;

    @ExcelProperty("评价者地区")
    private String clientArea;

    @ExcelProperty("评价者点赞数")
    private Integer likeCount;

    @ExcelProperty("评价回复数")
    private Integer replyCount;

    @ExcelProperty("举报次数")
    private Integer reportCount;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
