package cn.iocoder.yudao.module.mall.product.service.tag;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSeoSkuExportRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuExportRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuPageRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagSimpleVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagSkuDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 标签SKU关联 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductTagSkuService extends IService<ProductTagSkuDO>  {

    /**
     * 批量sku打上批量的标签
     * @param tagIdList
     * @param skuIdList
     */
    void saveTagSku(List<Long> tagIdList, List<Long> skuIdList);

    /**
     * 批量移除sku上的标签
     * @param tagIdList
     * @param skuIdList
     */
    void deleteTagSku(List<Long> tagIdList, List<Long> skuIdList);

    /**
     * 批量给SKU打标签
     * @param skuTagIdsMap
     */
    void saveTagSkuBatch(Map<Long, List<Long>> skuTagIdsMap);

    /**
     * 批量移除SKU上的标签
     * @param tagIdList
     */
    void deleteTagSkuByTag(List<Long> tagIdList);

    /**
     * 批量移除SKU上的全部标签
     * @param skuIdList
     */
    void deleteTagSkuBySku(List<Long> skuIdList);

    void deleteTagSkuBySkuV2(List<Long> skuIdList);

    /**
     * 批量移除SKU上的标签
     * @param tagId
     */
    void deleteTagSkuByTag(Long tagId);

    /**
     * 查询SKU上的标签列表
     * @param skuId
     * @return
     */
    List<ProductTagDO> getTagListBySku(Long skuId);

    /**
     * 批量查询SKU上的标签列表
     * @param skuIdList
     * @return
     */
    List<ProductTagDO> getTagListBySku(List<Long> skuIdList);

    /**
     * 批量查询SKU上的标签MAP，key为SKU
     * @param skuIdList
     * @return
     */
    Map<Long, List<ProductTagDO>> getTagListMap(List<Long> skuIdList);

    /**
     * 批量查询SKU上的标签IDMAP，key为SKU
     * @param skuIdList
     * @return
     */
    Map<Long, List<Long>> getTagIdListMap(List<Long> skuIdList);

    /**
     * 填充标签信息
     */
    void fillTag(PageResult<ProductSkuPageRespVO> pageResult);

    /**
     * 填充标签信息
     */
    void fillTag2(List<ProductSeoSkuExportRespVO> seoSkuExportRespVOS);

    Map<Long, List<ProductTagSimpleVO>> getSkuTagMap(List<Long> skuIds);

}
