package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery;

import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo.*;
import cn.iocoder.yudao.module.mall.trade.service.delivery.ConfigDeliveryCompanyService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo.*;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryCompanyDO;
import cn.iocoder.yudao.module.mall.trade.convert.delivery.DeliveryCompanyConvert;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryCompanyService;

@Tag(name = "管理后台 - 物流公司")
@RestController
@RequestMapping("/trade/delivery-company")
@Validated
public class DeliveryCompanyController {

    @Resource
    private DeliveryCompanyService deliveryCompanyService;

    @PostMapping("/create")
    @Operation(summary = "创建物流公司")
    @PreAuthorize("@ss.hasPermission('trade:deliverycompany:create')")
    public CommonResult<Long> createDeliveryCompany(@Valid @RequestBody DeliveryCompanyCreateReqVO createReqVO) {
        return success(deliveryCompanyService.createDeliveryCompany(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新物流公司")
    @PreAuthorize("@ss.hasPermission('trade:deliverycompany:update')")
    public CommonResult<Boolean> updateDeliveryCompany(@Valid @RequestBody DeliveryCompanyUpdateReqVO updateReqVO) {
        deliveryCompanyService.updateDeliveryCompany(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除物流公司")
    @PreAuthorize("@ss.hasPermission('trade:deliverycompany:delete')")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteDeliveryCompany(@RequestParam("id") Long id) {
        deliveryCompanyService.deleteDeliveryCompany(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得物流公司")
    @PreAuthorize("@ss.hasPermission('trade:deliverycompany:query')")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<DeliveryCompanyRespVO> getDeliveryCompany(@RequestParam("id") Long id) {
        DeliveryCompanyDO deliveryCompany = deliveryCompanyService.getDeliveryCompany(id);
        return success(DeliveryCompanyConvert.INSTANCE.convert(deliveryCompany));
    }

    @GetMapping("/list")
    @Operation(summary = "获得物流公司列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasAnyPermissions('trade:deliverycompany:query', 'trade:delivery:query')")
    public CommonResult<List<DeliveryCompanyRespVO>> getDeliveryCompanyList(@RequestParam("ids") Collection<Long> ids) {
        List<DeliveryCompanyDO> list = deliveryCompanyService.getDeliveryCompanyList(ids);
        return success(DeliveryCompanyConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得物流公司分页")
    @PreAuthorize("@ss.hasAnyPermissions('trade:deliverycompany:query', 'trade:delivery:query')")
    public CommonResult<PageResult<DeliveryCompanyRespVO>> getDeliveryCompanyPage(DeliveryCompanyPageReqVO pageVO) {
        PageResult<DeliveryCompanyDO> pageResult = deliveryCompanyService.getDeliveryCompanyPage(pageVO);
        return success(DeliveryCompanyConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出物流公司 Excel")
    @PreAuthorize("@ss.hasPermission('trade:deliverycompany:export')")
    @OperateLog(type = EXPORT)
    public void exportDeliveryCompanyExcel(@Valid DeliveryCompanyExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<DeliveryCompanyDO> list = deliveryCompanyService.getDeliveryCompanyList(exportReqVO);
        // 导出 Excel
        List<DeliveryCompanyExcelVO> datas = DeliveryCompanyConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "物流公司.xls", "数据", DeliveryCompanyExcelVO.class, datas);
    }

    @GetMapping("/get-types")
    @Operation(summary = "物流公司类型")
    @PreAuthorize("@ss.hasPermission('trade:deliverycompany:query')")
    public CommonResult<List<String>> getTypes() {
        List<String> types = deliveryCompanyService.getTypes();
        return success(types);
    }

    @GetMapping("/batch-add")
    @Operation(summary = "批量添加物流公司")
    @PreAuthorize("@ss.hasPermission('trade:deliverycompany:update')")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<Boolean> batchAdd(@RequestParam("ids") Collection<Long> ids) {
        deliveryCompanyService.batchAdd(ids);
        return success(true);
    }
}
