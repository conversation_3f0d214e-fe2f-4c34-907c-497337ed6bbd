package cn.iocoder.yudao.module.mall.member.service.address;

import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.common.AppOpenAreaReqVO;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.AddressAreaRespVO;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.AppAddressQueryVO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AddressDO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AreaDO;

import java.util.List;

public interface AreaService {

    /**
     * 根据省市区查询地址
     * @param param
     * @return
     */
    List<AreaDO> getAreas(AppOpenAreaReqVO param);

    /**
     * 解析完整区域名称
     * @param fullAreaName
     * @return
     */
    AddressDO parseFullName(String fullAreaName);

    /**
     * 查询区域列表，主要针对前端地址分级展示
     * @param queryVO
     * @return
     */
    List<AddressAreaRespVO> getAreas(AppAddressQueryVO queryVO);

    /**
     *
     * 校验四级地址有效性
     * @param provinceId
     * @param cityId
     * @param countyId
     * @param townId
     * @return
     */
    String validateAreaFullLevel(Long provinceId, Long cityId, Long countyId, Long townId);

}
