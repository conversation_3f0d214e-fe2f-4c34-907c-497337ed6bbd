package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderSummaryVO {

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "审批状态")
    private Integer auditStatus;

    @Schema(description = "结算状态")
    private Integer settlementStatus;

    @Schema(description = "订单个数")
    private Long orderCount;

    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

}
