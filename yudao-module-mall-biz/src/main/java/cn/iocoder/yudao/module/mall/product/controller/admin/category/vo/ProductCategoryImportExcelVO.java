package cn.iocoder.yudao.module.mall.product.controller.admin.category.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 商品分类导入Excel模板字段
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免导入有问题
public class ProductCategoryImportExcelVO {

    @ExcelProperty("上级分类编码")
    @ColumnWidth(15)
    private Long parentCode;

    @ExcelProperty("*商品分类编码")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(25)
    private String code;

    @ExcelProperty("商品分类名称")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(25)
    private String name;

    @ExcelProperty("商品分类图标URL")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(25)
    private String iconUrl;

    @ExcelProperty("商品分类H5图标URL")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(25)
    private String h5IconUrl;

    @ExcelProperty("经济分类编码")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(25)
    private String economyClass;

    @ExcelProperty("固资经济分类编码")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(25)
    private String economyClass2;

    @ExcelProperty("排序")
    @ColumnWidth(15)
    private Integer sort;

}
