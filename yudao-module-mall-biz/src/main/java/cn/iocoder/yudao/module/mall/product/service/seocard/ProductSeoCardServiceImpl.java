package cn.iocoder.yudao.module.mall.product.service.seocard;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo.ProductSeoCardCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo.ProductSeoCardPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo.ProductSeoCardUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.convert.seocard.ProductSeoCardConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.seocard.ProductSeoCardDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.seocard.ProductSeoCardMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.SEO_CARD_NOT_EXISTS;

/**
 * 运营区域 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductSeoCardServiceImpl implements ProductSeoCardService {

    @Resource
    private ProductSeoCardMapper seoCardMapper;

    @Override
    public Long createSeoCard(ProductSeoCardCreateReqVO createReqVO) {
        // 插入
        ProductSeoCardDO seoCard = ProductSeoCardConvert.INSTANCE.convert(createReqVO);
        seoCardMapper.insert(seoCard);
        // 返回
        return seoCard.getId();
    }

    @Override
    public void updateSeoCard(ProductSeoCardUpdateReqVO updateReqVO) {
        // 校验存在
        validateSeoCardExists(updateReqVO.getId());
        // 更新
        ProductSeoCardDO updateObj = ProductSeoCardConvert.INSTANCE.convert(updateReqVO);
        seoCardMapper.updateById(updateObj);
    }


    @Override
    public void updateSeoCardStatus(Long id, Integer status) {
        // 校验存在
        validateSeoCardExists(id);

        seoCardMapper.updateById(new ProductSeoCardDO().setId(id).setStatus(status));
    }

    @Override
    public void deleteSeoCard(Long id) {
        // 校验存在
        validateSeoCardExists(id);
        // 删除
        seoCardMapper.deleteById(id);
    }

    private void validateSeoCardExists(Long id) {
        if (seoCardMapper.selectById(id) == null) {
            throw exception(SEO_CARD_NOT_EXISTS);
        }
    }

    @Override
    public ProductSeoCardDO getSeoCard(Long id) {
        return seoCardMapper.selectById(id);
    }

    @Override
    public List<ProductSeoCardDO> getSeoCardList(Collection<Long> ids) {
        return seoCardMapper.selectBatchIds(ids);
    }

    @Override
    public List<ProductSeoCardDO> getSeoCardList(Collection<Long> ids, Integer status) {
        return seoCardMapper.selectList(Wrappers.lambdaQuery(ProductSeoCardDO.class)
                .in(ProductSeoCardDO::getId, ids)
                .eq(status != null, ProductSeoCardDO::getStatus, status)
                .orderByDesc(ProductSeoCardDO::getSort, ProductSeoCardDO::getId));
    }

    @Override
    public PageResult<ProductSeoCardDO> getSeoCardPage(ProductSeoCardPageReqVO pageReqVO) {
        return seoCardMapper.selectPage(pageReqVO);
    }

}
