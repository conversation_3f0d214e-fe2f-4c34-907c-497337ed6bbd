package cn.iocoder.yudao.module.mall.product.dal.mysql.vopcategorymapping;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopGoodsCategoryDO;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryLevelEnum;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface VopGoodsCategoryMapper extends BaseMapperX<VopGoodsCategoryDO> {

    default List<VopGoodsCategoryDO> selectPrimaryJdList(){
        return selectList(new LambdaQueryWrapperX<VopGoodsCategoryDO>()
                .eq(VopGoodsCategoryDO::getCategoryLevel, ProductCategoryLevelEnum.LAVEL1.getLevel())
                .eq(VopGoodsCategoryDO::getStatus, ProductCategoryStatusEnum.ENABLE.getStatus())
                );
    }
}
