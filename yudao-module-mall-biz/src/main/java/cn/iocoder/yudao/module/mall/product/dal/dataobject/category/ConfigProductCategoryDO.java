package cn.iocoder.yudao.module.mall.product.dal.dataobject.category;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.List;

/**
 * 平台配置商品分类
 * 租户的商品分类需要从平台配置商品分类中订购
 *
 * @date 2023-10-17
 * <AUTHOR>
 */
@TableName("config_product_category")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigProductCategoryDO extends BaseDO {

    @TableId
    private Long id;
    /**
     * 分类名称
     */
    private String categoryName;
    /**
     * 分类图标
     */
    private String icon;
    /**
     * 移动端分类图标
     */
    private String iconH5;
    /**
     * 父级分类ID
     */
    private Long parentId;
    /**
     * 商品分类编号, 以-分隔
     */
    private String fullCategoryId;
    /**
     * 商品分类名称, 以/分隔
     */
    private String fullCategoryName;
    /**
     * 排序
     */
    private Integer orderSort;
    /**
     * 类型：1-京东，2-平台， {@link  cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryTypeEnum}
     */
    private Integer type;
    /**
     * 外部分类ID
     */
    private Long categoryId;
    /**
     * 分类层级
     */
    private Integer categoryLevel;
    /**
     * 状态，是否展示 {@link cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum}
     */
    private Integer status;
    /**
     * 子分类列表，非持久化字段
     */
    @TableField(exist = false)
    private List<ConfigProductCategoryDO> ChildCategoryList;

}
