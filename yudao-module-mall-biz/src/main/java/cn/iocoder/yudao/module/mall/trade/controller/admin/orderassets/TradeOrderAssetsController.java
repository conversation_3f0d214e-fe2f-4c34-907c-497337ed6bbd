package cn.iocoder.yudao.module.mall.trade.controller.admin.orderassets;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderassets.vo.*;
import cn.iocoder.yudao.module.mall.trade.convert.order.TradeOrderAssetsConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderAssetsDetailDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemAssetsDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderAssetsService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.trade.service.purchase.PurchaseService;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTask;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskUtils;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import com.alibaba.ttl.TtlRunnable;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 订单明细固资信息")
@RestController
@RequestMapping("/trade/order-assets")
@Validated
@Slf4j
public class TradeOrderAssetsController {

    @Resource
    private TradeOrderAssetsService orderItemAssetsService;
    @Resource
    private TradeOrderService tradeOrderService;
    @Resource
    private PurchaseService purchaseService;
    @Resource
    private AsyncFrontTaskUtils asyncFrontTaskUtils;

    @DeleteMapping("/delete")
    @Operation(summary = "删除订单明细固资信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:delete')")
    public CommonResult<Boolean> deleteOrderItemAssets(@RequestParam("id") Long id) {
        orderItemAssetsService.deleteOrderItemAssets(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得订单明细固资信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:query')")
    public CommonResult<TradeOrderItemAssetsRespVO> getOrderItemAssets(@RequestParam("id") Long id) {
        TradeOrderItemAssetsDO orderItemAssets = orderItemAssetsService.getOrderItemAssets(id);
        List<TradeOrderAssetsDetailDO> detailList = orderItemAssetsService.getOrderAssetsDetailList(orderItemAssets.getOrderItemId());

        PurchaseDO purchaseDO = purchaseService.getPurchaseByOrder(orderItemAssets.getOrderId());
        List<TradeOrderAssetsDetailVO> detailVOList = TradeOrderAssetsConvert.INSTANCE.convertList03(detailList);
        TradeOrderItemAssetsRespVO respVO = TradeOrderAssetsConvert.INSTANCE.convert(orderItemAssets, purchaseDO, detailVOList);

        return success(respVO);
    }

    @PutMapping("/update-category")
    @Operation(summary = "更新固资明细信息分类")
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:update')")
    public CommonResult<Boolean> updateOrderAssetsDetailCategory(@Valid @RequestBody List<TradeOrderItemAssetsDetailUpdateReqVO> reqVOList) {
        orderItemAssetsService.updateOrderAssetsDetailCategory(reqVOList);
        String orderNo = orderItemAssetsService.getOrderAssetsDetail(reqVOList.get(0).getId()).getOrderNo();
        orderItemAssetsService.pushOrderAsset2Ycrh(tradeOrderService.getOrderByNo(orderNo));
        return success(true);
    }

    @PostMapping("/update-complete")
    @Operation(summary = "更新固资状态为建档完成")
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:update')")
    public CommonResult<Boolean> update2Complete(@Valid @RequestBody TradeOrderAssetsCompleteReqVO reqVO) {
        orderItemAssetsService.updateOrderItemAssetComplete(reqVO);
        return success(true);
    }

    @PostMapping("/update-not-asset")
    @Operation(summary = "更新订单明细固资为无须建档")
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:update')")
    public CommonResult<Boolean> updateNotAsset(@RequestParam("orderItemId") Long orderItemId) {
        orderItemAssetsService.updateOrderItemNotAsset(orderItemId);
        return success(true);
    }

    @PostMapping("/push-asset-sys")
    @Operation(summary = "推送固资信息到外部系统")
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:push-sys')")
    public CommonResult<Boolean> pushAssetSys(@RequestParam("orderItemId") Long orderItemId) {
        orderItemAssetsService.pushOrderAssetsByItem(orderItemId);
        return success(true);
    }

    @PostMapping("/push-asset-ycrh")
    @Operation(summary = "推送订单资产到业财")
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:push-ycrh')")
    public CommonResult<Boolean> pushAssetYcrh(@RequestParam("orderId") Long orderId) {
        return success(orderItemAssetsService.pushOrderAsset2YcrhWithFile(orderId));
    }

    @GetMapping("/list")
    @Operation(summary = "获得订单明细固资信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:query')")
    public CommonResult<List<TradeOrderItemAssetsRespVO>> getOrderItemAssetsList(@RequestParam("ids") Collection<Long> ids) {
        List<TradeOrderItemAssetsDO> list = orderItemAssetsService.getOrderItemAssetsList(ids);
        return success(TradeOrderAssetsConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得订单明细固资信息分页")
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:query')")
    public CommonResult<PageResult<TradeOrderItemAssetsRespVO>> getOrderItemAssetsPage(@Valid TradeOrderItemAssetsPageReqVO pageVO) {
        PageResult<TradeOrderItemAssetsDO> pageResult = orderItemAssetsService.getOrderItemAssetsPage(pageVO);
        return success(TradeOrderAssetsConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出订单固资建档列表")
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:export')")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.ORDER_ASSETS_LIST)
    public CommonResult<String> exportOrderItemAssetsExcel(@Valid TradeOrderItemAssetsPageReqVO reqVO) {
        final String taskId = AsyncFrontTaskContext.getTaskId();
        asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), AsyncTaskTypeEnum.ORDER_ASSETS_LIST,
                TtlRunnable.get(() -> {
                    int pageSize = 1000, pageNo = 1;
                    reqVO.setPageNo(pageNo);
                    reqVO.setPageSize(pageSize);
                    List<TradeOrderItemAssetsDO> dataList = new ArrayList<>();
                    try {
                        asyncFrontTaskUtils.plusExportProgress(20);
                        PageResult<TradeOrderItemAssetsDO> pageResult = orderItemAssetsService.getOrderItemAssetsPage(reqVO);
                        log.info("任务{}导出订单固资建档信息，总数:{}", taskId, pageResult.getTotal());

                        while(CollUtil.isNotEmpty(pageResult.getList()) && pageResult.getPages() >= pageNo) {
                            dataList.addAll(pageResult.getList());
                            reqVO.setPageNo(++pageNo);
                            pageResult = orderItemAssetsService.getOrderItemAssetsPage(reqVO);
                            asyncFrontTaskUtils.plusExportProgress(dataList.size(), pageResult.getTotal(), 40);
                        }
                        if (CollUtil.isEmpty(dataList)) {
                            asyncFrontTaskUtils.exportDone(taskId, null);
                            return;
                        }

                        List<TradeOrderItemAssetsExcelVO> exportList = new ArrayList<>();
                        int batchSize = 500;
                        for (int i = 0; i < dataList.size(); i += batchSize) {
                            List<TradeOrderItemAssetsDO> subList = dataList.subList(i, Math.min(i + batchSize, dataList.size()));

                            List<Long> orderItemIds = subList.stream().map(TradeOrderItemAssetsDO::getOrderItemId).collect(Collectors.toList());
                            List<TradeOrderAssetsDetailDO> detailList = orderItemAssetsService.getOrderAssetsDetailList(orderItemIds);
                            List<TradeOrderAssetsDetailVO> detailVOList = TradeOrderAssetsConvert.INSTANCE.convertList03(detailList);
                            Map<Long, List<TradeOrderAssetsDetailVO>> detailVOMap = CollectionUtils.convertMultiMap(detailVOList, TradeOrderAssetsDetailVO::getOrderItemId);

                            List<TradeOrderItemAssetsExcelVO> subExportList = TradeOrderAssetsConvert.INSTANCE.convertList02(subList);
                            subExportList.forEach(item -> {
                                item.setDetailList(detailVOMap.get(item.getOrderItemId()));
                            });
                            exportList.addAll(subExportList);

                            asyncFrontTaskUtils.plusExportProgress(exportList.size(), pageResult.getTotal(), 30);
                        }

                        // 导出 Excel 文件
                        asyncFrontTaskUtils.exportDone(taskId, exportList);
                    } catch (Exception e) {
                        asyncFrontTaskUtils.exportFail();
                        log.error("任务{}导出订单固资建档信息失败：", taskId, e);
                    }
                })
        );

        return success(taskId);
    }

}
