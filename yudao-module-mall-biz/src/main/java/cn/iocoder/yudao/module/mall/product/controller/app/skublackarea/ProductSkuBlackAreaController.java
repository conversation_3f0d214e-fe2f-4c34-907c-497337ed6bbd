//package cn.iocoder.yudao.module.mall.product.controller.app.skublackarea;
//
//import cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo.*;
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import cn.iocoder.yudao.framework.common.pojo.PageResult;
//import cn.iocoder.yudao.framework.common.pojo.CommonResult;
//import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
//
//import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
//
//import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
//import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import cn.iocoder.yudao.module.mall.product.controller.admin.skublackarea.vo.*;
//import cn.iocoder.yudao.module.mall.product.dal.dataobject.skublackarea.ProductSkuBlackAreaDO;
//import cn.iocoder.yudao.module.mall.product.convert.skublackarea.ProductSkuBlackAreaConvert;
//import cn.iocoder.yudao.module.mall.product.service.skublackarea.ProductSkuBlackAreaService;
//
//@Tag(name = "管理后台 - 商品sku禁售区域")
//@RestController
//@RequestMapping("/product/sku-black-area")
//@Validated
//public class ProductSkuBlackAreaController {
//
//    @Resource
//    private ProductSkuBlackAreaService skuBlackAreaService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建商品sku禁售区域")
//    public CommonResult<Long> createSkuBlackArea(@Valid @RequestBody ProductSkuBlackAreaCreateReqVO createReqVO) {
//        return success(skuBlackAreaService.createSkuBlackArea(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新商品sku禁售区域")
//    public CommonResult<Boolean> updateSkuBlackArea(@Valid @RequestBody ProductSkuBlackAreaUpdateReqVO updateReqVO) {
//        skuBlackAreaService.updateSkuBlackArea(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除商品sku禁售区域")
//    @Parameter(name = "id", description = "编号", required = true)
//    public CommonResult<Boolean> deleteSkuBlackArea(@RequestParam("id") Long id) {
//        skuBlackAreaService.deleteSkuBlackArea(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得商品sku禁售区域")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    public CommonResult<ProductSkuBlackAreaRespVO> getSkuBlackArea(@RequestParam("id") Long id) {
//        ProductSkuBlackAreaDO skuBlackArea = skuBlackAreaService.getSkuBlackArea(id);
//        return success(ProductSkuBlackAreaConvert.INSTANCE.convert(skuBlackArea));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得商品sku禁售区域列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    public CommonResult<List<ProductSkuBlackAreaRespVO>> getSkuBlackAreaList(@RequestParam("ids") Collection<Long> ids) {
//        List<ProductSkuBlackAreaDO> list = skuBlackAreaService.getSkuBlackAreaList(ids);
//        return success(ProductSkuBlackAreaConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得商品sku禁售区域分页")
//    public CommonResult<PageResult<ProductSkuBlackAreaRespVO>> getSkuBlackAreaPage(@Valid ProductSkuBlackAreaPageReqVO pageVO) {
//        PageResult<ProductSkuBlackAreaDO> pageResult = skuBlackAreaService.getSkuBlackAreaPage(pageVO);
//        return success(ProductSkuBlackAreaConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出商品sku禁售区域 Excel")
//    @OperateLog(type = EXPORT)
//    public void exportSkuBlackAreaExcel(@Valid ProductSkuBlackAreaExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<ProductSkuBlackAreaDO> list = skuBlackAreaService.getSkuBlackAreaList(exportReqVO);
//        // 导出 Excel
//        List<ProductSkuBlackAreaExcelVO> datas = ProductSkuBlackAreaConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "商品sku禁售区域.xls", "数据", ProductSkuBlackAreaExcelVO.class, datas);
//    }
//
//}
