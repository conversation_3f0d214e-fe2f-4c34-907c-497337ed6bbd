package cn.iocoder.yudao.module.mall.product.service.tag;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.convert.tag.ProductTagConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.tag.ProductTagMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.TAG_NOT_EXISTS;

/**
 * 标签 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductTagServiceImpl extends ServiceImpl<ProductTagMapper, ProductTagDO>  implements ProductTagService {

    @Resource
    private ProductTagMapper tagMapper;
    @Resource
    @Lazy
    private ProductTagSkuService tagSkuService;

    @Override
    public Long createTag(ProductTagCreateReqVO createReqVO) {
        // 插入
        ProductTagDO tag = ProductTagConvert.INSTANCE.convert(createReqVO);
        tagMapper.insert(tag);
        // 返回
        return tag.getId();
    }

    @Override
    public void updateTag(ProductTagUpdateReqVO updateReqVO) {
        // 校验存在
        validateTagExists(updateReqVO.getId());
        // 更新
        ProductTagDO updateObj = ProductTagConvert.INSTANCE.convert(updateReqVO);
        tagMapper.updateById(updateObj);
    }

    @Override
    public void deleteTag(Long id) {
        // 校验存在
        validateTagExists(id);
        // 删除SKU上的标签
        tagSkuService.deleteTagSkuByTag(id);
        // 删除
        tagMapper.deleteById(id);
    }

    @Override
    public long countByGroup(Long groupId) {
        return tagMapper.selectCount(Wrappers.lambdaQuery(ProductTagDO.class)
                .eq(ProductTagDO::getGroupId, groupId));
    }

    private void validateTagExists(Long id) {
        if (tagMapper.selectById(id) == null) {
            throw exception(TAG_NOT_EXISTS);
        }
    }

    @Override
    public ProductTagDO getTag(Long id) {
        return tagMapper.selectById(id);
    }

    @Override
    public List<ProductTagDO> getTagList(Collection<Long> ids) {
        return tagMapper.selectBatchIds(ids);
    }

    @Override
    public List<ProductTagDO> listAll() {
        return tagMapper.selectList(Wrappers.lambdaQuery(ProductTagDO.class)
                .eq(ProductTagDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .orderByDesc(ProductTagDO::getUpdateTime));
    }

    @Override
    public List<ProductTagDO> getTagListByGroup(Collection<Long> groupIds, Integer status) {
        return tagMapper.selectList(Wrappers.lambdaQuery(ProductTagDO.class)
                .in(ProductTagDO::getGroupId, groupIds)
                .eq(status != null, ProductTagDO::getStatus, status));
    }

    @Override
    public PageResult<ProductTagDO> getTagPage(ProductTagPageReqVO pageReqVO) {
        return tagMapper.selectPage(pageReqVO);
    }

}
