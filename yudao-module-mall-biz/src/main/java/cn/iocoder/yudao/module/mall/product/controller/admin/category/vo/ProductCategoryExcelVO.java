package cn.iocoder.yudao.module.mall.product.controller.admin.category.vo;

import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 商品分类 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ProductCategoryExcelVO {

    @ExcelProperty("一级分类ID")
    private Long category1Id;

    @ExcelProperty("一级分类")
    private String category1Name;

    @ExcelProperty("二级分类ID")
    private Long category2Id;

    @ExcelProperty("二级分类")
    private String category2Name;

    @ExcelProperty("三级分类ID")
    private Long category3Id;

    @ExcelProperty("三级分类")
    private String category3Name;

}
