package cn.iocoder.yudao.module.mall.product.controller.admin.skusummary.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 供应商上架商品数统计 Excel 导出 Request VO，参数和 SkuSummaryPageReqVO 是一致的")
@Data
public class SkuSummaryExportReqVO {

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "供应商类型")
    private Integer supplierType;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "上架数量")
    private Long listingTotal;

    @Schema(description = "下架数量")
    private Long delistingTotal;

    @Schema(description = "截止时间")
    private Date deadline;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
