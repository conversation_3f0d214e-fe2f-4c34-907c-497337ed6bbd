package cn.iocoder.yudao.module.mall.product.service.spec;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecValueReqVO;
import cn.iocoder.yudao.module.mall.product.convert.spec.ProductSpecConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategorySpecDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSpuSpecDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.category.ConfigProductCategorySpecMapper;
import cn.iocoder.yudao.module.mall.product.dal.mysql.spec.ProductSpuSpecMapper;
import cn.iocoder.yudao.module.mall.product.enums.spec.SpecTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * spu规格值 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductSpuSpecServiceImpl extends ServiceImpl<ProductSpuSpecMapper, ProductSpuSpecDO> implements ProductSpuSpecService {

    @Resource
    private ConfigProductCategorySpecMapper configProductCategorySpecMapper;
    @Resource
    private ProductSpuSpecMapper productSpuSpecMapper;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public void handleSpuSpec(@Valid List<ProductSpecValueReqVO> specVaueList, Long productSpuId) {
        // 处理规格
        if(CollUtil.isNotEmpty(specVaueList)) {
            specVaueList.forEach(item -> {
                if(item.getSpecId() == null) {
                    item.setSpecId(0L);
                }
            });
        }
        // 先清空商品SPU的规格值
        productSpuSpecMapper.deleteBySpuId(productSpuId);
        // 保存规格值
        if(CollUtil.isNotEmpty(specVaueList)) {
            productSpuSpecMapper.insertBatch(ProductSpecConvert.INSTANCE.convertDOList13(specVaueList, productSpuId));
        }
    }

    @Override
    public void deleteSpuSpec(Long id) {
        // 校验存在
        validateSpuSpecExists(id);
        // 删除
        productSpuSpecMapper.deleteById2(id);
    }

    private void validateSpuSpecExists(Long id) {
        if (productSpuSpecMapper.selectById(id) == null) {
        }
    }

    @Override
    public ProductSpuSpecDO getSpuSpec(Long id) {
        return productSpuSpecMapper.selectById(id);
    }

    @Override
    public List<ProductSpuSpecDO> getSpuSpecList(Collection<Long> ids) {
        return productSpuSpecMapper.selectBatchIds(ids);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void saveBatchBySpu(List<ProductSpuSpecDO> productSpuSpecDOS, Long spuId) {
        if(spuId == null) {
            return;
        }
        String key = String.format("lock-product-spu-%d", spuId);
        RLock lock = redissonClient.getLock(key);
        try {
            lock.tryLock(10, 10, TimeUnit.SECONDS);
            productSpuSpecMapper.deleteBySpuId(spuId);
            if(CollUtil.isEmpty(productSpuSpecDOS)) {
                return;
            }
            productSpuSpecMapper.insertBatch(productSpuSpecDOS);
        } catch (Exception e) {
            log.error("saveBatchBySpu lock error: ", e);
        } finally {
            if(lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();;
            }
        }
    }

    @Override
    public List<ProductSpuSpecDO> getSpuSpecListBySpuId(Long spuId) {
        return getSpuSpecListBySpuIds(Arrays.asList(spuId));
    }

    @Override
    public List<ProductSpuSpecDO> getSpuSpecListBySpuIds(List<Long> spuIds) {
        if(CollectionUtils.isEmpty(spuIds)) {
            return null;
        }
        LambdaQueryWrapper<ProductSpuSpecDO> wrapper = Wrappers.lambdaQuery(ProductSpuSpecDO.class)
                .in(ProductSpuSpecDO::getSpuId, spuIds);
        return productSpuSpecMapper.selectList(wrapper);
    }

}
