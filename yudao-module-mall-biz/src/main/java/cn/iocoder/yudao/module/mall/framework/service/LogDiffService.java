package cn.iocoder.yudao.module.mall.framework.service;

import cn.iocoder.yudao.module.mall.annotation.DiffField;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import de.danielbechler.diff.ObjectDifferBuilder;
import de.danielbechler.diff.node.DiffNode;
import de.danielbechler.diff.selector.ElementSelector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/1/28
 */
@Slf4j
@Service
public class LogDiffService {

    /**
     * 字段名称的替换变量
     */
    private final String FIELD_PLACEHOLDER = "__fieldName";
    /**
     * 更新前的值的替换变量
     */
    private final String SOURCE_VALUE_PLACEHOLDER = "__sourceValue";
    /**
     * 更新后的值的替换变量
     */
    private final String TARGET_VALUE_PLACEHOLDER = "__targetValue";


    /**
     * 添加
     */
    private String addTemplate = "【" + FIELD_PLACEHOLDER + "】从【空】修改为【" + TARGET_VALUE_PLACEHOLDER + "】";

    /**
     * 修改
     */
    private String updateTemplate = "【" + FIELD_PLACEHOLDER + "】从【" + SOURCE_VALUE_PLACEHOLDER + "】修改为【" + TARGET_VALUE_PLACEHOLDER + "】";

    /**
     * 删除
     */
    private String deleteTemplate = "删除了【" + FIELD_PLACEHOLDER + "】:【" + SOURCE_VALUE_PLACEHOLDER + "】";
    /**
     * 多个字段的日志内容拼接一起的时候的分隔符
     */
    private static String fieldSeparator = ",";


    public  String diff(Object source, Object target) {
        if (source == null && target == null) {
            return "";
        }
        if (source == null || target == null) {
            try {
                Class<?> clazz = source == null ? target.getClass() : source.getClass();
                source = source == null ? clazz.getDeclaredConstructor().newInstance() : source;
                target = target == null ? clazz.getDeclaredConstructor().newInstance() : target;
            } catch (InstantiationException | IllegalAccessException | NoSuchMethodException |
                     InvocationTargetException e) {
                throw new RuntimeException(e);
            }
        }
        if (!Objects.equals(AopUtils.getTargetClass(source.getClass()), AopUtils.getTargetClass(target.getClass()))) {
            log.error("diff的两个对象类型不同");
            return "";
        }
        ObjectDifferBuilder objectDifferBuilder = ObjectDifferBuilder.startBuilding();

        DiffNode diffNode = objectDifferBuilder.build().compare(target, source);
        if (!diffNode.hasChanges()) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        Set<DiffNode> set = new HashSet<>();
        return toDiffContent(diffNode,source,target,stringBuilder,set);
    }


    private  String toDiffContent(DiffNode diffNode, final  Object source, final Object target, StringBuilder stringBuilder, Set<DiffNode> set) {
        diffNode.visit((node, visit) -> generateAllFieldLog(source, target, stringBuilder, node, set));
        set.clear();
        return stringBuilder.toString().replaceAll(fieldSeparator.concat("$"), "");
    }
    private  void generateAllFieldLog(final Object sourceObject, final Object targetObject, StringBuilder stringBuilder, DiffNode node, Set<DiffNode> set) {
        if (node.isRootNode() || node.getValueTypeInfo() != null || set.contains(node)) {
            return;
        }
        DiffField diffLogFieldAnnotation = node.getFieldAnnotation(DiffField.class);
        if (diffLogFieldAnnotation == null) {
            return;
        }
        String filedLogName = diffLogFieldAnnotation != null ? diffLogFieldAnnotation.name() : node.getPropertyName();
        if (StringUtils.isEmpty(filedLogName)) {
            return;
        }
        // 是否是容器类型的字段
        //boolean valueIsContainer = valueIsContainer(node, sourceObject, targetObject);
        String logContent =  getDiffLogContent(filedLogName, node, sourceObject, targetObject);
        if (!StringUtils.isEmpty(logContent)) {
            stringBuilder.append(logContent).append(fieldSeparator);
        }

        memorandum(node, set);
    }

    private void memorandum(DiffNode node, Set<DiffNode> set) {
        set.add(node);
        if (node.hasChildren()) {
            Field childrenField = ReflectionUtils.findField(DiffNode.class, "children");
            assert childrenField != null;
            ReflectionUtils.makeAccessible(childrenField);
            Map<ElementSelector, DiffNode> children = (Map<ElementSelector, DiffNode>) ReflectionUtils.getField(childrenField, node);
            assert children != null;
            for (DiffNode value : children.values()) {
                memorandum(value, set);
            }
        }
    }


    private boolean valueIsContainer(DiffNode node, Object sourceObject, Object targetObject) {
        if (sourceObject != null) {
            Object sourceValue = node.canonicalGet(sourceObject);
            if (sourceValue == null) {
                if (targetObject != null) {
                    return node.canonicalGet(targetObject) instanceof Collection || node.canonicalGet(targetObject).getClass().isArray();
                }
            } else {
                return sourceValue instanceof Collection || sourceValue.getClass().isArray();
            }
        }
        return false;
    }


    public String getDiffLogContent(String filedLogName, DiffNode node, Object sourceObject, Object targetObject) {
        switch (node.getState()) {
            case ADDED:
                return formatAdd(filedLogName, getFieldValue(node, targetObject));
            case CHANGED:
                return formatUpdate(filedLogName, getFieldValue(node, sourceObject),getFieldValue(node, targetObject));
            case REMOVED:
                return formatDeleted(filedLogName,getFieldValue(node, sourceObject));
            default:
                log.warn("diff log not support");
                return "";
        }
    }

    private Object getFieldValue(DiffNode node, Object object) {
        return node.canonicalGet(object);
    }


    private String formatAdd(String fieldName, Object targetValue) {
        return addTemplate.replace(FIELD_PLACEHOLDER, fieldName)
                .replace(TARGET_VALUE_PLACEHOLDER, String.valueOf(targetValue));
    }

    private String formatUpdate(String fieldName, Object sourceValue, Object targetValue) {
        return updateTemplate.replace(FIELD_PLACEHOLDER, fieldName)
                .replace(SOURCE_VALUE_PLACEHOLDER, String.valueOf(sourceValue))
                .replace(TARGET_VALUE_PLACEHOLDER, String.valueOf(targetValue));
    }

    private String formatDeleted(String fieldName, Object sourceValue) {
        return deleteTemplate.replace(FIELD_PLACEHOLDER, fieldName)
                .replace(SOURCE_VALUE_PLACEHOLDER, String.valueOf(sourceValue));
    }

}



