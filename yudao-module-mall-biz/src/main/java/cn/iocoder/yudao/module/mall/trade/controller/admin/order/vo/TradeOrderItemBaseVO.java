package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderItemAfterSaleStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 交易订单项 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class TradeOrderItemBaseVO {

    // ========== 订单项基本信息 ==========

    /**
     * id
     */
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    /**
     * 用户编号
     */
    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long userId;

    /**
     * 订单id
     */
    @Schema(description = "订单id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long orderId;

    // ========== 商品基本信息 ==========

    /**
     * 商品 SKU 编号
     */
    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long skuId;

    /**
     * 商品 三方SKU 编号
     */
    @Schema(description = "商品 三方SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String skuInnerId;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String skuName;

    @Schema(description = "规格，多个以逗号分隔", requiredMode = Schema.RequiredMode.REQUIRED, example = "512G")
    private String skuSpec;

    /**
     * 商品图片
     */
    @Schema(description = "商品图片", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/1.png")
    private String picUrl;

    /**
     * 购买数量
     */
    @Schema(description = "购买数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer count;

    // ========== 价格 + 支付基本信息 ==========

    /**
     * 商品单价
     *
     */
    @Schema(description = "商品单价", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private BigDecimal skuPrice;

    /**
     * 商品总价
     *
     */
    @Schema(description = "商品总价", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private BigDecimal skuTotalPrice;

    /**
     * {@link cn.iocoder.yudao.module.mall.trade.enums.order.AssetStatusEnum}
     */
    @Schema(description = "固资建档状态", example = "0")
    private Integer assetStatus;

    @Schema(description = "是否为固定资产", example = "0")
    private Boolean isAsset;

    /**
     *
     * 枚举 {@link TradeOrderItemAfterSaleStatusEnum}
     */
    @Schema(description = "售后状态", example = "0")
    private Integer afterSaleStatus;

    /**
     * 售后备注
     */
    private String afterSaleMemo;

    /**
     * 商品标签
     */
    private List<String> skuTags;

    /**
     * 验收状态
     */
    private Integer acceptStatus;

}
