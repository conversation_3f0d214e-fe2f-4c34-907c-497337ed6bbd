package cn.iocoder.yudao.module.mall.product.controller.admin.spu;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.annotation.ProductOperateLog;
import cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.spu.ProductSpuConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductOperateTypeEnum;
import cn.iocoder.yudao.module.mall.product.service.spu.ProductSpuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.SPU_NOT_EXISTS;

@Tag(name = "管理后台 - 商品 SPU")
@RestController
@RequestMapping("/product/spu")
@Validated
public class ProductSpuController {

    @Resource
    private ProductSpuService productSpuService;

    @PostMapping("/create")
    @Operation(summary = "创建商品 SPU")
    @PreAuthorize("@ss.hasPermission('product:spu:create')")
    public CommonResult<Long> createProductSpu(@Valid @RequestBody ProductSpuCreateReqVO createReqVO) {
        return success(productSpuService.createSpu(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品 SPU")
    @PreAuthorize("@ss.hasPermission('product:spu:update')")
    @ProductOperateLog(content = "更新商品",type = ProductOperateTypeEnum.UPDATE,spuId = "#updateReqVO.id")
    public CommonResult<Boolean> updateSpu(@Valid @RequestBody ProductSpuUpdateReqVO updateReqVO) {
        productSpuService.updateSpu(updateReqVO);
        return success(true);
    }

    @PostMapping("/update-status")
    @Operation(summary = "更新商品 SPU 状态")
    @PreAuthorize("@ss.hasPermission('product:spu:update')")
    @ProductOperateLog(content = "更新商品状态",type = ProductOperateTypeEnum.UPDATE,spuId = "#updateReqVO.id")
    public CommonResult<Boolean> updateSpu(@Valid @RequestBody ProductSpuStatusUpdateReqVO updateReqVO) {
        productSpuService.updateSpuStatus(updateReqVO);
        return success(true);
    }

    @PostMapping("/update-show-status")
    @Operation(summary = "更新商品显示状态")
    @PreAuthorize("@ss.hasPermission('product:spu:update-show-status')")
    public CommonResult<Boolean> updateSpuShowStatus(@Valid @RequestBody List<ProductSpuShowStatusUpdateReqVO> updateReqVOS) {
        productSpuService.updateSpuShowStatus(updateReqVOS);
        return success(true);
    }

    @PostMapping("/update-platform-status")
    @Operation(summary = "更新商品 SPU 状态")
    @PreAuthorize("@ss.hasPermission('product:spu:update-platform-status')")
    public CommonResult<Boolean> updateSpuPlatformStatus(@Valid @RequestBody List<ProductSpuStatusUpdateReqVO> updateReqVOS) {
        productSpuService.updateSpuPlatformStatus(updateReqVOS);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品 SPU")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:spu:delete')")
    @ProductOperateLog(content = "删除商品",type = ProductOperateTypeEnum.DELETE,spuId = "#id")
    public CommonResult<Boolean> deleteSpu(@RequestParam("id") Long id) {
        productSpuService.deleteSpu(id);
        return success(true);
    }

    @GetMapping("/get-detail")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:query', 'product:sku:seo')")
    public CommonResult<ProductSpuDetailRespVO> getSpuDetail(@RequestParam("id") Long id) {
        // 获得商品 SPU
        ProductSpuDO spu = productSpuService.getSpu(id);
        if (spu == null) {
            throw exception(SPU_NOT_EXISTS);
        }

        return success(productSpuService.getSpuDetail(id));
    }

    @GetMapping("/get-simple-list")
    @PreAuthorize("@ss.hasPermission('product:spu:query')")
    public CommonResult<List<ProductSpuSimpleRespVO>> getSpuSimpleList() {
        List<ProductSpuDO> list = productSpuService.getSpuList();
        return success(ProductSpuConvert.INSTANCE.convertList02(list));
    }

    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('product:spu:query')")
    public CommonResult<PageResult<ProductSpuRespVO>> getSpuPage(@Valid ProductSpuPageReqVO pageVO) {
        return success(ProductSpuConvert.INSTANCE.convertPage(productSpuService.getSpuPage(pageVO)));
    }

}
