package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.util.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * 业财融合接口请求参数DTO
 */
@Data
public class YcrhReqDTO implements Serializable {

    private String appid = "JYHCG";
    private String timestamp = DateUtil.format(new Date(), "yyyyMMddHHmmss");
    private String version = "1.0";
    private String method;
    private Map<String, Object> data = new TreeMap<>();

    private Map<String, Object> toTreeMap() {
        Map<String, Object> treeMap = new TreeMap<>();
        treeMap.put("appid", appid);
        treeMap.put("timestamp", timestamp);
        treeMap.put("version", version);
        treeMap.put("method", method);
        treeMap.put("data", data);

        return treeMap;
    }


    public JSONObject toJsonObj() {
        Map<String, Object> treeMap = this.toTreeMap();
        return new JSONObject(treeMap);
    }

    public JSONObject toJsonObj4Sign() {
        JSONObject originalJson = this.toJsonObj();
        Map<String, Object> treeMap = jsonToMap(originalJson);
        return new JSONObject(treeMap);
    }

    private static Map<String, Object> jsonToMap(JSONObject json) {
        Map<String, Object> treeMap = new TreeMap<String, Object>();
        Iterator<String> keys = json.keySet().iterator();
        while (keys.hasNext()) {
            String key = keys.next();
            Object value = json.get(key);
            // 判断传入kay-value中是否含有""或null
            if (json.get(key) == null || value == null || value.toString().length() == 0) {
                // 当JSON字符串存在null时,不将该kay-value放入Map中,即显示的结果不包括该kay-value
                continue;
            }
            // 判断是否为JSONArray(json数组)
            if (value instanceof JSONArray) {
                JSONArray jsonArray = (JSONArray) value;
                List<Object> arrayList = new ArrayList<>();
                for (Object object : jsonArray) {
                    // 判断是否为JSONObject，如果是 转化成TreeMap
                    if (object instanceof JSONObject) {
                        object = jsonToMap((JSONObject)object);
                    } else if (object instanceof Map) {
                        object = jsonToMap(new JSONObject((Map) value));
                    }
                    arrayList.add(object);
                }
                treeMap.put(key, arrayList);
            } else {
                // 判断该JSON中是否嵌套JSON
                if(value instanceof Map) {
                    value = jsonToMap(new JSONObject((Map) value));
                }
                // 其他基础类型直接放入treeMap
                // JSONObject可进行再次解析转换
                treeMap.put(key, value);
            }
        }
        return treeMap;
    }


}
