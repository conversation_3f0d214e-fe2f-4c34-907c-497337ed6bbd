package cn.iocoder.yudao.module.mall.external.assets.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/20
 */
@Getter
public enum AssetsMethodEnum {

    // 洛比相关接口
    LB_CREATE_ASSETS("/CreateAssets","资产建档申请", "get"),
    LB_COMPLETE_ASSETS("/FinanceAssets","资产财务对账", "get"),

    LBV20_CREATE_ASSETS("/financeassets.action","资产建档申请", "post"),
    LBV20_COMPLETE_ASSETS("/bzassets.action","资产财务对账", "get"),

    PLD_PUSH_ASSETS("/api/createAssetDetails.shtml","资产建档推送", "post");

    private String methodName;

    private String desc;

    private String httpMethod;

    AssetsMethodEnum(String methodName, String desc, String httpMethod) {
        this.methodName = methodName;
        this.desc = desc;
        this.httpMethod = httpMethod;
    }
}
