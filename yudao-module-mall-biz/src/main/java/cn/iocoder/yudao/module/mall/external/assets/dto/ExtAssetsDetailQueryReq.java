package cn.iocoder.yudao.module.mall.external.assets.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 资产建档申请DTO
 */
@Data
public class ExtAssetsDetailQueryReq extends ExtAssetsBaseReq {

    private static final Integer PAGE_NO = 1;
    private static final Integer PAGE_SIZE = 100;

    @Schema(description = "页码，从 1 开始", required = true, example = "1")
    @Min(value = 1, message = "页码最小值为 1")
    private Integer pageNo = PAGE_NO;

    @Schema(description = "每页条数，最大值为 100", required = true, example = "10")
    @Min(value = 1, message = "每页条数最小值为 1")
    @Max(value = 100, message = "每页条数最大值为 100")
    private Integer pageSize = PAGE_SIZE;

    @Schema(description = "员工工号")
    private String userNo;

    @Schema(description = "订单明细编号")
    private Long orderDetailNo;

}
