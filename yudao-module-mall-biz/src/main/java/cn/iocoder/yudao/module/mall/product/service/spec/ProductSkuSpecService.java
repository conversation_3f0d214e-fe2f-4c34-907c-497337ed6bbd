package cn.iocoder.yudao.module.mall.product.service.spec;

import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecValueReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSkuSpecDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * sku规格关联 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductSkuSpecService extends IService<ProductSkuSpecDO> {

    /**
     * 创建spu规格关联
     *
     * @param specVaueList 规格值列表
     * @param productSkuId 商品SKUID
     */
    void handleSkuSpec(@Valid List<ProductSpecValueReqVO> specVaueList, Long productSkuId);

    /**
     * 删除sku规格关联
     *
     * @param id 编号
     */
    void deleteProductSkuSpec(Long id);

    /**
     * 获得sku规格关联
     *
     * @param id 编号
     * @return sku规格关联
     */
    ProductSkuSpecDO getProductSkuSpec(Long id);

    /**
     * 获得sku规格关联列表
     *
     * @param ids 编号
     * @return sku规格关联列表
     */
    List<ProductSkuSpecDO> getProductSkuSpecList(Collection<Long> ids);

    /**
     * 批量保存商品规格
     * @param productSkuSpecDOS
     * @param skuId
     */
    void saveBatchBySku(List<ProductSkuSpecDO> productSkuSpecDOS, Long skuId);


    /**
     * 根据skuId获取商品属性
     * @param skuId
     * @return
     */
    List<ProductSkuSpecDO> getProductSkuSpecBySkuId(Long skuId);

    /**
     * 根据skuId获取商品属性
     * @param skuIds
     * @return
     */
    List<ProductSkuSpecDO> getProductSkuSpecBySkuIds(List<Long> skuIds);
}
