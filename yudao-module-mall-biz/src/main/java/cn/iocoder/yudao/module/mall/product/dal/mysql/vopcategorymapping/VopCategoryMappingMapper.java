package cn.iocoder.yudao.module.mall.product.dal.mysql.vopcategorymapping;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopCategoryMappingDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo.*;

/**
 * 京东分类映射 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface VopCategoryMappingMapper extends BaseMapperX<VopCategoryMappingDO> {

    default PageResult<VopCategoryMappingDO> selectPage(VopCategoryMappingPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VopCategoryMappingDO>()
                .eqIfPresent(VopCategoryMappingDO::getLastCategoryId, reqVO.getLastCategoryId())
                .likeIfPresent(VopCategoryMappingDO::getLastCategoryName, reqVO.getLastCategoryName())
                .eqIfPresent(VopCategoryMappingDO::getFullCategoryId, reqVO.getFullCategoryId())
                .likeIfPresent(VopCategoryMappingDO::getFullCategoryName, reqVO.getFullCategoryName())
                .eqIfPresent(VopCategoryMappingDO::getVopLastCategoryId, reqVO.getVopLastCategoryId())
                .likeIfPresent(VopCategoryMappingDO::getVopLastCategoryName, reqVO.getVopLastCategoryName())
                .eqIfPresent(VopCategoryMappingDO::getVopFullCategoryId, reqVO.getVopFullCategoryId())
                .likeIfPresent(VopCategoryMappingDO::getVopFullCategoryName, reqVO.getVopFullCategoryName())
                .betweenIfPresent(VopCategoryMappingDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VopCategoryMappingDO::getId));
    }

    default List<VopCategoryMappingDO> selectList(VopCategoryMappingExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VopCategoryMappingDO>()
                .eqIfPresent(VopCategoryMappingDO::getLastCategoryId, reqVO.getLastCategoryId())
                .likeIfPresent(VopCategoryMappingDO::getLastCategoryName, reqVO.getLastCategoryName())
                .eqIfPresent(VopCategoryMappingDO::getFullCategoryId, reqVO.getFullCategoryId())
                .likeIfPresent(VopCategoryMappingDO::getFullCategoryName, reqVO.getFullCategoryName())
                .eqIfPresent(VopCategoryMappingDO::getVopLastCategoryId, reqVO.getVopLastCategoryId())
                .likeIfPresent(VopCategoryMappingDO::getVopLastCategoryName, reqVO.getVopLastCategoryName())
                .eqIfPresent(VopCategoryMappingDO::getVopFullCategoryId, reqVO.getVopFullCategoryId())
                .likeIfPresent(VopCategoryMappingDO::getVopFullCategoryName, reqVO.getVopFullCategoryName())
                .betweenIfPresent(VopCategoryMappingDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VopCategoryMappingDO::getId));
    }

}
