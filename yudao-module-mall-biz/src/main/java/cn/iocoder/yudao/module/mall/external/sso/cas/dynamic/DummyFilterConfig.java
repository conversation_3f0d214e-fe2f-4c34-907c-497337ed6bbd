package cn.iocoder.yudao.module.mall.external.sso.cas.dynamic;

import javax.servlet.FilterConfig;
import javax.servlet.ServletContext;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Map;

/**
 * @author: HF
 * @description: TODO
 * @date: 2024/4/13 19:32
 */
public class DummyFilterConfig implements FilterConfig {

    private final Map<String, String> parameterMaps;
    private final String filterName;
    private final ServletContext context;

    public DummyFilterConfig(String filterName, ServletContext context, Map<String, String> parameterMaps) {
        this.filterName = filterName;
        this.context = context;
        this.parameterMaps = parameterMaps;
    }

    @Override
    public String getFilterName() {
        return this.filterName;
    }

    @Override
    public ServletContext getServletContext() {
        return context;
    }

    @Override
    public String getInitParameter(String s) {
        return parameterMaps.get(s);
    }

    @Override
    public Enumeration<String> getInitParameterNames() {
        return Collections.enumeration(parameterMaps.keySet());
    }
}
