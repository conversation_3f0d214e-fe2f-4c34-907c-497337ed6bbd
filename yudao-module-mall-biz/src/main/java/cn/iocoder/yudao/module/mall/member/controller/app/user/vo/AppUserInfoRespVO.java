package cn.iocoder.yudao.module.mall.member.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "用户 APP - 用户个人信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppUserInfoRespVO {

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String nickname;

    @Schema(description = "用户姓名", example = "芋艿")
    private String name;

    @Schema(description = "用户头像", example = "/infra/file/get/35a12e57-4297-4faa-bf7d-7ed2f211c952")
    private String avatar;

    @Schema(description = "用户手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15601691300")
    private String mobile;

    @Schema(description = "员工工号", requiredMode = Schema.RequiredMode.REQUIRED, example = "001245")
    private String userNo;

    @Schema(description = "部门编号", example = "545511233")
    private String deptCode;

    @Schema(description = "部门名称", example = "经管学院")
    private String deptName;

    @Schema(description = "职务名称", example = "副院长")
    private String jobTitle;

}
