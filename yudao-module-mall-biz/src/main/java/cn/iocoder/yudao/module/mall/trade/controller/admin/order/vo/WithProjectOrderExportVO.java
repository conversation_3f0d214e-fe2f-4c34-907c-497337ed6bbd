package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 订单主信息导出
 *
 * <AUTHOR>
 * @Date 2024/6/13
 */
@Schema(description = "管理后台 - 订单导出excel VO")
@Data
public class WithProjectOrderExportVO extends CommonOrderExportVO {

    @Schema(description = "经济分类")
    @Excel(name = "经济分类", orderNum = "7",width = 15)
    private String ecnomyClass;

    @Schema(description = "项目类型")
    @Excel(name = "项目类型", orderNum = "8",width = 20)
    private String projectType;

    @Schema(description = "项目编号")
    @Excel(name = "项目编号（经费卡卡号）", orderNum = "9",width = 30)
    private String projectNo;

    @Schema(description = "项目名称")
    @Excel(name = "项目名称（经费卡名称）", orderNum = "10",width = 35)
    private String projectName;

    @Schema(description = "项目所属部门")
    @Excel(name = "项目所属部门（经费卡所属部门）", orderNum = "11",width = 35)
    private String projectDepartment;

}
