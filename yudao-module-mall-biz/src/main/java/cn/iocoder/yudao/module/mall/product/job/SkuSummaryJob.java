package cn.iocoder.yudao.module.mall.product.job;

import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.mall.product.service.skusummary.SkuSummaryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 商品sku统计job
 */
@Component
@TenantJob
@Slf4j
public class SkuSummaryJob {

    @Resource
    private SkuSummaryService skuSummaryService;

    /**
     * 统计截止到今天0点的供应商sku上下架总数量
     * @return
     */
    @XxlJob("productSkuSummary")
    public ReturnT<String> productSkuSummary() {
        try {
            log.info("定时任务-productSkuSummary");
            skuSummaryService.saveSupplierSkuSummary();
        } catch (Exception e) {
            log.error("productSkuSummary fail", e);
        }
        return ReturnT.SUCCESS;
    }
}
