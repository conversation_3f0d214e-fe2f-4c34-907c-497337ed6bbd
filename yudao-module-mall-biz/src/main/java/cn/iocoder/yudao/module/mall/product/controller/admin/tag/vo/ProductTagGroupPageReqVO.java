package cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 标签分组分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductTagGroupPageReqVO extends PageParam {

    @Schema(description = "分组名称")
    private String name;

    @Schema(description = "状态, 0-启用，1-禁用")
    private Integer status;

}
