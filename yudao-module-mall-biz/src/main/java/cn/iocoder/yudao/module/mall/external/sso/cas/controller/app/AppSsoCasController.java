package cn.iocoder.yudao.module.mall.external.sso.cas.controller.app;

import cn.hutool.core.codec.Base64;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.SsoConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.integration.SsoConfigService;
import cn.iocoder.yudao.module.mall.external.sso.cas.CasContextHolder;
import cn.iocoder.yudao.module.mall.external.sso.cas.util.CasUtils;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jasig.cas.client.authentication.AttributePrincipal;
import org.jasig.cas.client.util.AbstractCasFilter;
import org.jasig.cas.client.validation.Assertion;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


/**
 * SSO-CAS统一身份认证
 * <AUTHOR>
 */
@Tag(name = "开放API - SSO CAS")
@Controller
@RequestMapping("/app-api/mall/open/sso")
@Slf4j
public class AppSsoCasController {

    @Resource
    private SsoConfigService ssoConfigService;

    @GetMapping("/cas/auth")
    @Operation(summary = "SSO-CAS回调")
    public String casLogin(HttpServletRequest request, String tcode, String redirect, String extParam) {
        //获取用户名并返回
        String uid = CasContextHolder.getUserId();
        Assertion assertion = (Assertion)request.getSession().getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
        log.info("sso-cas auth, sessionId:{}, uid:{}", request.getSession().getId(), uid);
        if(TenantContextHolder.getTenantId() == null) {
            log.info("sso-cas auth callback not tenant, ignore...");
            return "error";
        }
        if(assertion == null) {
            log.info("sso-cas auth assertion is null, userInfo:{}", CasContextHolder.getCasUserInfo());
            return "error";
        }

        if (assertion != null && assertion.getPrincipal() != null) {
            AttributePrincipal principal = assertion.getPrincipal();
            log.info("sso-cas auth: {}", JsonUtils.toJsonString(principal.getAttributes()));
        }
        SsoConfigDO configDO = ssoConfigService.getSsoConfig();
        String mainUrl = null;
        String redirect2 = null;
        if(StringUtils.isNotBlank(extParam)) {
            String extParamDecode = Base64.decodeStr(extParam);
            JSONObject extParamJson = JSONObject.parseObject(extParamDecode);
            if(extParamJson != null){
                redirect2 = extParamJson.getString("redirect");
                mainUrl = extParamJson.getString("mainUrl");
            }
        }

        String webUrl = configDO.getCasWebMainUrl();
        if(ServletUtils.isMobileRequest() && StringUtils.isNotBlank(configDO.getCasWebMainUrl2())) {
            webUrl = configDO.getCasWebMainUrl2();
        }
        if(StringUtils.isNotBlank(mainUrl)) {
            webUrl = mainUrl;
        }
        if(!webUrl.contains("?")) {
            webUrl += "?";
        } else {
            webUrl += "&";
        }
        webUrl += "tcode=" + tcode;
        webUrl += "&etype=2&etoken=cas";
        webUrl += "&uid=" + uid;
        if(StringUtils.isNotBlank(redirect)) {
            redirect2 = redirect;
        }
        if(StringUtils.isNotBlank(redirect2)){
            webUrl += "&redirect=" + CasUtils.encodeUrl(redirect2);
        }
        log.info("sso-cas auth redirect: {}", webUrl);
        return "redirect:" + webUrl;
    }

    @GetMapping("/cas/test-session")
    @ResponseBody
    public String testSession(HttpServletRequest request) {
        return request.getSession().getId();
    }

}

