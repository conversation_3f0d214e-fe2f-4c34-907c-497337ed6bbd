package cn.iocoder.yudao.module.mall.product.controller.app.sku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class ProductSkuUpdatePriceVO {
    /**
     * 商品skuId
     */
    @Schema(description = "商品skuId")
    @NotNull(message = "商品skuId不能为空")
    private Long skuId;

    /**
     * 商品价格
     */
    @Schema(description = "市场价", example = "100.5", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "市场价不能为空")
    private BigDecimal marketPrice;

    /**
     * 商品价格
     */
    @Schema(description = "销售价", example = "89.5", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "销售价不能为空")
    private BigDecimal salePrice;
}
