package cn.iocoder.yudao.module.mall.external.sso.cas;

import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.Map;

public class CasContextHolder {

    /**
     * 登录信息
     */
    private static final ThreadLocal<Map<String, Object>> CAS_USER_INFO = new TransmittableThreadLocal<>();
    private static final ThreadLocal<String> USER_ID = new TransmittableThreadLocal<>();

    public static Map<String, Object> getCasUserInfo() {
        return CAS_USER_INFO.get();
    }

    public static void setCasUserInfo(Map<String, Object> userInfo) {
        CAS_USER_INFO.set(userInfo);
    }

    public static String getUserId() {
        return USER_ID.get();
    }

    public static void setUserId(String userId) {
        USER_ID.set(userId);
    }

    public static void clear() {
        CAS_USER_INFO.remove();
        USER_ID.remove();
    }

}
