package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ProductSkuExportRespVO {
    @Schema(description = "序号")
    @Excel(needMerge = true, name = "序号", orderNum = "1", width = 20)
    private Integer rowNum;

    @Schema(description = "供应商编号", example = "1")
    @Excel(needMerge = true, name = "供应商编号", orderNum = "2", width = 20)
    private Long supplierId;

    @Schema(description = "供应商名称", example = "小米")
    @Excel(needMerge = true, name = "供应商名称", orderNum = "3", width = 20)
    private String supplierName;

    @Schema(description = "平台SKU", example = "11")
    @Excel(needMerge = true, name = "平台SKU", orderNum = "4", width = 20)
    private Long skuId;

    @Schema(description = "供应商SKU")
    @Excel(needMerge = true, name = "供应商SKU", orderNum = "5", width = 20)
    private String skuInnerId;

    @Schema(description = "商品SPU", example = "11")
    @Excel(needMerge = true, name = "商品SPU", orderNum = "6", width = 20)
    private Long spuId;

    @Schema(description = "品牌", example = "小米")
    @Excel(needMerge = true, name = "品牌", orderNum = "7", width = 20)
    private String brandName;

    @Schema(description = "商品名称", example = "iPhone")
    @Excel(needMerge = true, name = "商品名称", orderNum = "8", width = 20)
    private String skuName;

    @Schema(description = "商品创建时间", example = "2020-12-31 12:00:00")
    @Excel(needMerge = true, name = "商品创建时间", orderNum = "9", width = 20)
    private LocalDateTime createTime;

    @Schema(description = "上下架状态", example = "1")
    @Excel(needMerge = true, name = "上下架状态", orderNum = "10", width = 20)
    private String status;

    @Schema(description = "列出状态", example = "列出")
    @Excel(needMerge = true, name = "列出状态", orderNum = "11", width = 20)
    private String showStatus;

    @Schema(description = "一级分类")
    @Excel(needMerge = true, name = "一级分类", orderNum = "12", width = 20)
    private String category1Name;

    @Schema(description = "二级分类")
    @Excel(needMerge = true, name = "二级分类", orderNum = "13", width = 20)
    private String category2Name;

    @Schema(description = "三级分类")
    @Excel(needMerge = true, name = "三级分类", orderNum = "14", width = 20)
    private String category3Name;

    @Schema(description = "平台销售价", example = "1024")
    @Excel(needMerge = true, name = "平台销售价", orderNum = "15", width = 20)
    private BigDecimal salePrice;

    @Schema(description = "市场价", example = "1024")
    @Excel(needMerge = true, name = "市场价", orderNum = "16", width = 20)
    private BigDecimal marketPrice;

    @Schema(description = "节支率", example = "1")
    @Excel(needMerge = true, name = "节支率", orderNum = "17", width = 20)
    private BigDecimal costSavingRate;

    @Schema(description = "商品销量", example = "1")
    @Excel(needMerge = true, name = "商品销量", orderNum = "18", width = 20)
    private Integer salesCount;

    @Schema(description = "销售额", example = "1")
    @Excel(needMerge = true, name = "销售额", orderNum = "19", width = 20)
    private BigDecimal salesAmount;
}
