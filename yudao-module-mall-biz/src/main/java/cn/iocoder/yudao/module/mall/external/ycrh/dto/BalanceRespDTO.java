package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
@Data
public class BalanceRespDTO {

    /**
     * 部门编号
     */
    @JsonProperty("bmbh")
    private String departmentNo;

    /**
     * 项目编号
     */
    @JsonProperty("xmbh")
    private String projectNo;

    /**
     * 项目余额
     */
    @JsonProperty("xmye")
    private BigDecimal projectBalance;

}
