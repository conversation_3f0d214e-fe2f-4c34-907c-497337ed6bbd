package cn.iocoder.yudao.module.mall.product.service.spec;

import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecValueReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSpuSpecDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * spu规格值 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductSpuSpecService extends IService<ProductSpuSpecDO> {

    /**
     * 创建spu规格关联
     *
     * @param specVaueList 规格值列表
     * @param productSpuId 商品SPUID
     */
    void handleSpuSpec(@Valid List<ProductSpecValueReqVO> specVaueList, Long productSpuId);

    /**
     * 删除spu规格关联
     *
     * @param id 编号
     */
    void deleteSpuSpec(Long id);

    /**
     * 获得spu规格关联
     *
     * @param id 编号
     * @return spu规格关联
     */
    ProductSpuSpecDO getSpuSpec(Long id);

    /**
     * 获得spu规格关联列表
     *
     * @param ids 编号
     * @return spu规格关联列表
     */
    List<ProductSpuSpecDO> getSpuSpecList(Collection<Long> ids);

    /**
     * 批量保存商品规格
     * @param productSpuSpecDOS
     * @param spuId
     */
    void saveBatchBySpu(List<ProductSpuSpecDO> productSpuSpecDOS, Long spuId);


    /**
     * 根据spuId获取商品关联的spu规格属性
     * @param spuId
     * @return
     */
    List<ProductSpuSpecDO> getSpuSpecListBySpuId(Long spuId);

    /**
     * 根据spuId获取商品关联的spu规格属性
     * @param spuIds
     * @return
     */
    List<ProductSpuSpecDO> getSpuSpecListBySpuIds(List<Long> spuIds);

}
