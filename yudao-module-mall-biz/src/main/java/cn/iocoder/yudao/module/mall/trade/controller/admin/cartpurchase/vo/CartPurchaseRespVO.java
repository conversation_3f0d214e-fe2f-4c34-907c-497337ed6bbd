package cn.iocoder.yudao.module.mall.trade.controller.admin.cartpurchase.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

@Schema(description = "管理后台 - 采购订单购物车 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CartPurchaseRespVO extends CartPurchaseBaseVO {

    @Schema(description = "id", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private Date createTime;

}
