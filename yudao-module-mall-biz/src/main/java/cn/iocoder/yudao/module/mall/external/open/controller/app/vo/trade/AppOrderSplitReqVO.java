package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AppOrderSplitReqVO {

    @Schema(description = "父订单id")
    @NotNull(message = "父订单不能为空")
    private Long parentOrderId;

    @Schema(description = "子订单信息")
    @NotEmpty(message = "子订单信息不能为空")
    private List<AppOrderSplitOrderReqVO> orderReqVOList;

}
