package cn.iocoder.yudao.module.mall.external.hrms;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.HrmsConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.integration.HrmsConfigService;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtDeptDTO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtMemberDTO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtPageReqDTO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtPageRespDTO;
import cn.iocoder.yudao.module.mall.external.hrms.service.AbstractExtHrmsService;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 外部人事信息客户端
 * <AUTHOR>
 */
@Component
@Slf4j
public class HrmsClient {

    @Resource
    private HrmsConfigService hrmsConfigService;
    @Resource
    @Lazy
    private List<AbstractExtHrmsService> extHrmsServiceList;

    public boolean isExtHrmsServiceActive() {
        return getTargetService() != null;
    }

    private AbstractExtHrmsService getTargetService() {
        HrmsConfigDO hrmsConfig = hrmsConfigService.getHrmsConfig();
        if(hrmsConfig == null) {
            log.info("人事系统集成配置为null");
            return null;
        }
        if(ObjectUtil.notEqual(hrmsConfig.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            log.info("人事系统集成配置状态为禁用");
            return null;
        }

        if(CollUtil.isEmpty(extHrmsServiceList)) {
            log.info("人事系统客户端为空");
            return null;
        }

        AbstractExtHrmsService targetService = extHrmsServiceList.stream().filter(item -> item.getClientCode().equals(hrmsConfig.getSysCode()))
                .findFirst().orElse(null);
        if(targetService == null) {
            log.info("人事系统客户端不存在, {}", hrmsConfig.getSysCode());
            return null;
        }

        return targetService;
    }

    public String getRootDeptCode() {
        HrmsConfigDO hrmsConfig = hrmsConfigService.getHrmsConfig();
        if(hrmsConfig == null) {
            log.info("人事系统集成配置为null");
            return null;
        }

        return hrmsConfig.getRootDeptCode();
    }

    public void fillMemberInfo(String userNo, MemberUserDO userDO) {
        AbstractExtHrmsService targetService = getTargetService();
        if(targetService == null) {
            log.error("【外部人事系统】可用客户端为空");
            return;
        }
        try {
            targetService.fillMemberInfo(userNo, userDO);
        } catch (Exception e) {
            log.error("fillMemberInfo error:", e);
        }
    }

    public ExtMemberDTO queryMemberInfo(String userNo) {
        AbstractExtHrmsService targetService = getTargetService();
        if(targetService == null) {
            log.error("【外部人事系统】可用客户端为空");
            return null;
        }
        try {
            return targetService.queryMemberInfo(userNo);
        } catch (Exception e) {
            log.error("queryMemberInfo error:", e);
        }
        return null;
    }

    public ExtPageRespDTO<ExtMemberDTO> pageQueryMember(ExtPageReqDTO reqDTO) {
        AbstractExtHrmsService targetService = getTargetService();
        if(targetService == null) {
            log.error("【外部人事系统】可用客户端为空");
            return null;
        }
        try {
            return targetService.pageQueryMember(reqDTO);
        } catch (Exception e) {
            log.error("pageQueryMember error:", e);
        }
        return null;
    }

    public ExtPageRespDTO<ExtDeptDTO> pageQueryDept(ExtPageReqDTO reqDTO) {
        AbstractExtHrmsService targetService = getTargetService();
        if(targetService == null) {
            log.error("【外部人事系统】可用客户端为空");
            return null;
        }
        try {
            return targetService.pageQueryDept(reqDTO);
        } catch (Exception e) {
            log.error("pageQueryDept error:", e);
        }
        return null;
    }

}
