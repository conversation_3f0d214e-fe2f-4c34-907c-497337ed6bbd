package cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo;

import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.ApprovalResultRespVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理后台 - 采购 Response VO
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 采购 Response VO")
@Data
public class PurchaseRespVO {

    /**
     * 采购id
     */
    private Long purchaseId;

    /**
     * 审批流单据号
     */
    private String bpmNo;

    /**
     * 项目编号
     */
    private String projectNo;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目类型
     */
    private String projectType;
    /**
     * 项目负责人编号
     */
    private String projectChargeNo;
    /**
     * 项目负责人姓名
     */
    private String projectChargeName;
    /**
     * 项目所在部门名称
     */
    private String projectDepartmentName;
    /**
     * 项目所在部门编号
     */
    private String projectDepartmentNo;

    /**
     * 采购原因
     */
    private String purchaseReason;


    /**
     * 审批结果 1通过，2驳回，0撤销
     */
    private Integer auditStatus;

    /**
     * 审批意见
     */
    @JsonIgnore
    private String auditResult;

    /**
     * 审核意见
     */
    List<ApprovalResultRespVO> auditResults;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     *  验收人姓名
     */
    private String accepterName;

    /**
     * 验收人手机号
     */
    private String accepterMobile;

    /**
     * 验收人邮箱
     */
    private String accepterEmail;

    /**
     * 附件，附件URL并且逗号分隔
     */
    @Schema(description = "附件")
    private String attachments;


}
