package cn.iocoder.yudao.module.mall.external.bpm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.yudao.module.bpm.api.task.BpmTaskApi;
import cn.iocoder.yudao.module.bpm.api.task.dto.instance.BpmProcessInstanceCancelReqDTO;
import cn.iocoder.yudao.module.bpm.api.task.dto.instance.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.bpm.api.task.dto.instance.BpmProcessInstanceRespDTO;
import cn.iocoder.yudao.module.bpm.api.task.dto.task.BpmTaskRespDTO;
import cn.iocoder.yudao.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.BpmConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.integration.HrmsConfigService;
import cn.iocoder.yudao.module.mall.enums.external.enums.BpmAuditStatusEnum;
import cn.iocoder.yudao.module.mall.external.bpm.BpmClientConfig;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmAuditResultDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmCallbackResultDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmReqDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.JctBpmAuditResultRespDTO;
import cn.iocoder.yudao.module.mall.external.bpm.enums.BpmClientTypeEnum;
import cn.iocoder.yudao.module.mall.external.hrms.HrmsClient;
import cn.iocoder.yudao.module.mall.external.ycrh.YcrhClient;
import cn.iocoder.yudao.module.mall.external.ycrh.dto.YgInfoRespDTO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseValidateReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseAttachmentDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserReqDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.BPM_BEFORE_VALIDATE_ERROR;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.BPM_USER_NOT_EXITS;

/**
 * 金采通审批流实现
 * <AUTHOR>
 * @date 2024/08/22
 */
@Service
@Slf4j
public class JctBpmServiceImpl implements BpmService {

    @Resource
    private BpmClientConfig bpmClientConfig;
    @Resource
    private TradeOrderService tradeOrderService;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private BpmProcessInstanceApi bpmProcessInstanceApi;
    @Resource
    private BpmTaskApi bpmTaskApi;
    @Resource
    private MemberUserService memberUserService;
    @Resource
    private HrmsConfigService hrmsConfigService;
    @Resource
    private HrmsClient hrmsClient;
    @Resource
    private YcrhClient ycrhClient;

    @Override
    public String getClientType() {
        return BpmClientTypeEnum.JCT.getType();
    }

    @Override
    public void validateBeforePush(PurchaseValidateReqVO reqVO) {
        try {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            if(loginUser != null) {
                if(!syncSystemUser(loginUser.getUserNo())) {
                    throw ServiceExceptionUtil.exception(BPM_USER_NOT_EXITS, loginUser.getUserNo());
                }
            }
            if(reqVO.getProjectInfo() != null) {
                if(!syncSystemUser(reqVO.getProjectInfo().getChargeNo())) {
                    throw ServiceExceptionUtil.exception(BPM_USER_NOT_EXITS, reqVO.getProjectInfo().getChargeNo());
                }
            }
        } catch (Exception e) {
            log.error("审批流前置校验异常:", e);
            throw ServiceExceptionUtil.exception(BPM_BEFORE_VALIDATE_ERROR, e.getMessage());
        }
    }

    @Override
    public String pushBpmInfo(BpmReqDTO bpmReqDTO) {
        PurchaseDO purchaseDO = bpmReqDTO.getPurchaseDO();
        BpmConfigDO bpmConfigDO = bpmClientConfig.getBpmConfigDO();
        Assert.notNull(bpmConfigDO.getParamExt1(), "扩展参数1不能为空");

        AdminUserRespDTO adminStartUser = adminUserApi.getUserByNo(purchaseDO.getYgUserNo()).getCheckedData();
        Assert.notNull(adminStartUser, "用户审批信息未同步");
        CommonResult<String> res =  bpmProcessInstanceApi.createProcessInstance(adminStartUser.getId(), new BpmProcessInstanceCreateReqDTO()
                .setProcessDefinitionKey(bpmConfigDO.getParamExt1())
                .setBusinessKey(purchaseDO.getBpmNo())
                .setStartUserSelectAssignees(null)
                .setVariables(createPurchaseOrder(bpmReqDTO)));
        Assert.isTrue(res.isSuccess(), "提交bpm审批失败:{}", res.getMsg());
        log.info("提交bpm审批：{}", res);
        return res.getData();
    }

    private boolean syncSystemUser(String userNo) {
        if(StrUtil.isBlank(userNo)) {
            return false;
        }
        // 先检查用户是否存在，存在则忽略
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUserByNo(userNo).getCheckedData();
        if(adminUserRespDTO != null) {
            return true;
        }

        MemberUserDO memberUserDO = memberUserService.getUserByUserNo(userNo);
        if(memberUserDO != null) {
            AdminUserReqDTO mem = new AdminUserReqDTO();
            mem.setUserNo(userNo);
            mem.setName(memberUserDO.getName());
            mem.setNickname(memberUserDO.getNickname());
            mem.setUsername(userNo);
            mem.setMobile(memberUserDO.getMobile());
            mem.setLoginSwitch(false);
            mem.setDeptId(memberUserDO.getDeptId());

            adminUserApi.save(mem);
            return true;
        }

        if(hrmsClient.isExtHrmsServiceActive()) {
            hrmsConfigService.syncSystemUser(userNo);
            return true;
        }

        if(ycrhClient.isYcrhConfigValid()) {
            YgInfoRespDTO ygInfo = ycrhClient.getYgInfo(userNo);
            if(ygInfo != null) {
                AdminUserReqDTO mem = new AdminUserReqDTO();
                mem.setUserNo(userNo);
                mem.setName(ygInfo.getYgName());
                mem.setNickname(ygInfo.getYgName());
                mem.setUsername(userNo);
                mem.setMobile(ygInfo.getMobile());
                mem.setLoginSwitch(false);

                adminUserApi.save(mem);
                return true;
            }
        }

        return false;
    }

    @Override
    public BpmCallbackResultDTO handleBpmCallback(String body, Map<String, String> params) {
        log.info("处理bpm回调：{}", params);
        BpmCallbackResultDTO bpmCallbackResultDTO = new BpmCallbackResultDTO();
        bpmCallbackResultDTO.setBpmNo(params.get("bpmNo"));
        bpmCallbackResultDTO.setResult(true);
        return bpmCallbackResultDTO;
    }

    @Override
    public BpmAuditResultDTO pullBpmStatus(String bpmNo) {
        log.info("拉取bpm状态：{}", bpmNo);
        CommonResult<BpmProcessInstanceRespDTO> processInstanceRespDTOCommonResult = bpmProcessInstanceApi.getProcessInstanceByBusinessKey(bpmNo);
        if(processInstanceRespDTOCommonResult.getData() == null) {
            processInstanceRespDTOCommonResult = bpmProcessInstanceApi.getProcessInstance(bpmNo);
        }
        if(!processInstanceRespDTOCommonResult.isSuccess()) {
            log.info("bpm实例{}查询失败", bpmNo);
            return null;
        }
        BpmProcessInstanceRespDTO processInstanceRespDTO = processInstanceRespDTOCommonResult.getData();
        if(processInstanceRespDTO == null) {
            log.info("bpm实例{}为null", bpmNo);
            return null;
        }

        // 查询bpm任务
        CommonResult<List<BpmTaskRespDTO>> bpmTaskRespDTOCommonResult = bpmTaskApi.getTaskListByProcessInstanceId(processInstanceRespDTO.getId());
        if(!bpmTaskRespDTOCommonResult.isSuccess() || CollUtil.isEmpty(bpmTaskRespDTOCommonResult.getData())) {
            log.info("bpm实例{}审批任务查询失败", bpmNo);
            return null;
        }

        List<BpmTaskRespDTO> bpmTaskRespDTOS = bpmTaskRespDTOCommonResult.getData();
//        // 过滤系统取消的审批任务
//        bpmTaskRespDTOS.removeIf(item -> item.getStatus().equals(BpmTaskStatusEnum.CANCEL.getStatus()));

        BpmAuditResultDTO bpmAuditResultDTO = new BpmAuditResultDTO();
        if(processInstanceRespDTO.getStatus().equals(BpmProcessInstanceStatusEnum.APPROVE.getStatus())){
            bpmAuditResultDTO.setAuditStatus(BpmAuditStatusEnum.PASS);
        } else if(processInstanceRespDTO.getStatus().equals(BpmProcessInstanceStatusEnum.REJECT.getStatus())){
            bpmAuditResultDTO.setAuditStatus(BpmAuditStatusEnum.REJECT);
        }else if(processInstanceRespDTO.getStatus().equals(BpmProcessInstanceStatusEnum.CANCEL.getStatus())){
            bpmAuditResultDTO.setAuditStatus(BpmAuditStatusEnum.CANCEL);
        } else if(processInstanceRespDTO.getStatus().equals(BpmProcessInstanceStatusEnum.RUNNING.getStatus())){
            bpmAuditResultDTO.setAuditStatus(BpmAuditStatusEnum.PENDING);
        }
        bpmAuditResultDTO.setAuditResult(processAuditResult(bpmTaskRespDTOS));
        return bpmAuditResultDTO;
    }

    /**
     * 审批流结果字段转换
     * @param respDTOS
     * @return
     */
    private String processAuditResult(List<BpmTaskRespDTO> respDTOS) {
        if(CollUtil.isEmpty(respDTOS)) {
            return null;
        }

        List<JctBpmAuditResultRespDTO> jctDTOS = new ArrayList<>();
        Integer level = 1;
        // 发起人信息
        if(respDTOS.size() > 0){
            JctBpmAuditResultRespDTO jctDTO = new JctBpmAuditResultRespDTO();

            BpmTaskRespDTO respDTO = respDTOS.get(0);
            // 审批状态
            jctDTO.setAuditStatus("0");
            if(respDTO.getStatus().equals(BpmProcessInstanceStatusEnum.APPROVE.getStatus())){
                jctDTO.setAuditStatus("1");
            } else if(respDTO.getStatus().equals(BpmProcessInstanceStatusEnum.REJECT.getStatus())){
                jctDTO.setAuditStatus("2");
            }
            // 审批人
            BpmProcessInstanceRespDTO.User startUser = respDTO.getProcessInstance().getStartUser();
            jctDTO.setApprovalUserName(startUser.getNickname());
            CommonResult<AdminUserRespDTO> userRes = adminUserApi.getUser(startUser.getId());
            Assert.isTrue(userRes.isSuccess(), "审批人信息未同步");
            AdminUserRespDTO adminUserRespDTO = userRes.getData();
            if(adminUserRespDTO != null && adminUserRespDTO.getUserNo() != null){
                jctDTO.setApprovalUserNo(adminUserRespDTO.getUserNo());
            }
            jctDTO.setApprovalRoleName(startUser.getRoleName());

            // 审批时间
            jctDTO.setApprovalDate(DateUtil.format(respDTO.getCreateTime(), "yyyyMMdd"));
            jctDTO.setApprovalTime(DateUtil.format(respDTO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));

            // 审批级别
            jctDTO.setApprovalLevel(String.valueOf(level));

            jctDTOS.add(jctDTO);
        }

        level = 2;
        for (int i = respDTOS.size() - 1; i >= 0; i--) {
            BpmTaskRespDTO respDTO = respDTOS.get(i);
            JctBpmAuditResultRespDTO jctDTO = new JctBpmAuditResultRespDTO();
            // 审批状态
            jctDTO.setAuditStatus("0");
            if(respDTO.getStatus().equals(BpmProcessInstanceStatusEnum.APPROVE.getStatus())){
                jctDTO.setAuditStatus("1");
            } else if(respDTO.getStatus().equals(BpmProcessInstanceStatusEnum.REJECT.getStatus())){
                jctDTO.setAuditStatus("2");
            }
            jctDTO.setAuditResult(respDTO.getReason());
            // 审批人
            jctDTO.setApprovalUserName(respDTO.getAssigneeUser().getNickname());
            CommonResult<AdminUserRespDTO> userRes = adminUserApi.getUser(respDTO.getAssigneeUser().getId());
            Assert.isTrue(userRes.isSuccess(), "审批人信息未同步");
            AdminUserRespDTO adminUserRespDTO = userRes.getData();
            if(adminUserRespDTO != null && adminUserRespDTO.getUserNo() != null){
                jctDTO.setApprovalUserNo(adminUserRespDTO.getUserNo());
            }
            jctDTO.setApprovalRoleName(respDTO.getAssigneeUser().getRoleName());

            // 审批时间
            jctDTO.setApprovalDate(DateUtil.format(respDTO.getEndTime(), "yyyyMMdd"));
            jctDTO.setApprovalTime(DateUtil.format(respDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss"));

            jctDTO.setApprovalLevel(String.valueOf(level));

            // 审批级别
            // 或签审批中状态可能存在多个审批人，如果出现审批中那么审批中一定是最新的状态
            if(!jctDTO.getAuditStatus().equals(BpmProcessInstanceStatusEnum.RUNNING.getStatus())){
                level++;
            }

            jctDTOS.add(jctDTO);
        }

        return JSON.toJSONString(jctDTOS);
    }

    @Override
    public void cancelBpmInfo(String bpmNo, String reason, Long userId) {
        log.info("取消bpm：{}", bpmNo);
        CommonResult<BpmProcessInstanceRespDTO> processInstanceRespDTOCommonResult = bpmProcessInstanceApi.getProcessInstanceByBusinessKey(bpmNo);
        if(processInstanceRespDTOCommonResult.getData() == null) {
            processInstanceRespDTOCommonResult = bpmProcessInstanceApi.getProcessInstance(bpmNo);
        }
        Assert.isTrue(processInstanceRespDTOCommonResult.isSuccess(), "bpm实例{}查询失败：{}", bpmNo, processInstanceRespDTOCommonResult.getMsg());

        MemberUserDO memberUserDO = memberUserService.getUser(userId);
        Assert.notNull(memberUserDO.getUserNo(), "下单用户缺少工号");

        CommonResult<AdminUserRespDTO> userRes = adminUserApi.getUserByNo(memberUserDO.getUserNo());
        AdminUserRespDTO adminUserRespDTO = null;
        if(userRes.isSuccess()){
            adminUserRespDTO = userRes.getData();
        }
        Assert.notNull(adminUserRespDTO, "用户审批信息未同步");

        BpmProcessInstanceRespDTO processInstanceRespDTO = processInstanceRespDTOCommonResult.getData();
        Assert.notNull(processInstanceRespDTO, "bpm实例{}为null", bpmNo);

        BpmProcessInstanceCancelReqDTO bpmProcessInstanceCancelReqDTO = new BpmProcessInstanceCancelReqDTO();
        bpmProcessInstanceCancelReqDTO.setId(processInstanceRespDTO.getId());
        bpmProcessInstanceCancelReqDTO.setReason(reason);
        bpmProcessInstanceApi.cancelProcessInstanceByStartUser(adminUserRespDTO.getId(), bpmProcessInstanceCancelReqDTO);
    }

    private Map<String, Object> createPurchaseOrder(BpmReqDTO bpmReqDTO) {
        PurchaseDO purchaseDO = bpmReqDTO.getPurchaseDO();
        List<Long> orderIds = bpmReqDTO.getOrderIds();
        // 根据采购单获取订单列表
        List<TradeOrderDO> orderDOS = tradeOrderService.getOrders(orderIds);
        Map<String, Object> purchaseOrderMap = new HashMap<>();
        List<Map<String, Object>> orderDataList = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        for(TradeOrderDO tradeOrderDO : orderDOS) {
            Map<String, Object> orderData = new HashMap<>();
            List<TradeOrderItemDO> orderItems = tradeOrderService.getOrderItemListByOrderId(tradeOrderDO.getId());
            List<Map<String, Object>> orderDataItems = new ArrayList<>();
            for(TradeOrderItemDO tradeOrderItemDO : orderItems){
                Map<String, Object> orderItem = new HashMap<>();
                orderItem.put("skuImg", tradeOrderItemDO.getPicUrl());
                orderItem.put("skuName", tradeOrderItemDO.getSkuName());
                orderItem.put("count", tradeOrderItemDO.getCount());
                orderItem.put("skuPrice", tradeOrderItemDO.getSkuPrice().toPlainString());
                orderDataItems.add(orderItem);
            }

            orderData.put("orderNo", tradeOrderDO.getNo());
            orderData.put("orderPrice", tradeOrderDO.getOrderPrice().toPlainString());
            orderData.put("orderItems", orderDataItems.toArray());
            orderData.put("orderTime", tradeOrderDO.getSubmitTime());
            orderData.put("userName", tradeOrderDO.getUserName());
            orderData.put("userNo", purchaseDO.getYgUserNo());
            orderData.put("receiverName", tradeOrderDO.getReceiverName());
            orderData.put("receiverMobile", tradeOrderDO.getReceiverMobile());
            orderData.put("receiverAddress", String.format("%s%s%s", tradeOrderDO.getReceiverCityName(), tradeOrderDO.getReceiverCountyName(), tradeOrderDO.getReceiverDetailAddress()));
            orderDataList.add(orderData);
            totalAmount = totalAmount.add(tradeOrderDO.getOrderPrice());
        }

        BpmConfigDO bpmConfigDO = bpmClientConfig.getBpmConfigDO();
        Integer signType = 0;
        String watermark = null;
        if(bpmConfigDO.getParamExt2() != null) {
            // 不显示
            if(bpmConfigDO.getParamExt2().equals("0")) {
                signType = 0;
            } else if(bpmConfigDO.getParamExt2().equals("1")) {
                // 签名
                signType = 1;
            } else if(bpmConfigDO.getParamExt2().equals("2")) {
                // 盖章
                signType = 2;
            }
        }
        if(StringUtils.isNotBlank(bpmConfigDO.getParamExt3())){
            watermark = bpmConfigDO.getParamExt3();
        }
        purchaseOrderMap.put("signType", signType);
        purchaseOrderMap.put("watermark", watermark);

        if(StrUtil.isNotBlank(purchaseDO.getProjectNo())) {
            purchaseOrderMap.put("projectName", purchaseDO.getProjectName());
            purchaseOrderMap.put("projectNo", purchaseDO.getProjectNo());
            purchaseOrderMap.put("projectType", purchaseDO.getProjectType());
            purchaseOrderMap.put("projectDepartmentNo", purchaseDO.getProjectDepartmentNo());
            purchaseOrderMap.put("projectDepartmentName", purchaseDO.getProjectDepartmentName());
            purchaseOrderMap.put("projectChargeNo", purchaseDO.getProjectChargeNo());
            purchaseOrderMap.put("projectChargeName", purchaseDO.getProjectChargeName());
            purchaseOrderMap.put("economyClass", purchaseDO.getEconomicCode());
            purchaseOrderMap.put("economyClassName", purchaseDO.getEconomicName());
            purchaseOrderMap.put("projectDepartmentNo", purchaseDO.getProjectDepartmentNo());
            purchaseOrderMap.put("projectDepartmentName", purchaseDO.getProjectDepartmentName());
            purchaseOrderMap.put("projectChargeNo", purchaseDO.getProjectChargeNo());
            purchaseOrderMap.put("projectChargeName", purchaseDO.getProjectChargeName());
        }

        if(StrUtil.isNotBlank(purchaseDO.getAccepterName())) {
            purchaseOrderMap.put("accepterName", purchaseDO.getAccepterName());
            purchaseOrderMap.put("accepterMobile", purchaseDO.getAccepterMobile());
            purchaseOrderMap.put("accepterEmail", purchaseDO.getAccepterEmail());
        }

        purchaseOrderMap.put("ordersData", orderDataList);
        purchaseOrderMap.put("purchaseTotalPrice", totalAmount.toPlainString());
        purchaseOrderMap.put("reason", purchaseDO.getPurchaseReason());

        List<PurchaseAttachmentDO> attachments2 = purchaseDO.getAttachmentList2();
        if(CollUtil.isNotEmpty(attachments2)) {
            List<HashMap<String, String>> bpmAttachments = new ArrayList<>();
            for(PurchaseAttachmentDO attachment : attachments2) {
                HashMap<String, String> bpmAttachment = new HashMap<>();
                if(StringUtils.isNotBlank(attachment.getFileUrl())){
                    bpmAttachment.put("fileUrl", attachment.getFileUrl());
                }
                if(StringUtils.isNotBlank(attachment.getFileName())){
                    bpmAttachment.put("fileName", attachment.getFileName());
                }
                bpmAttachments.add(bpmAttachment);
            }
            purchaseOrderMap.put("files", bpmAttachments);
        }

        purchaseOrderMap.put("callbackUrl", bpmClientConfig.buildVoucherNotifyUrl());
        return purchaseOrderMap;
    }

}
