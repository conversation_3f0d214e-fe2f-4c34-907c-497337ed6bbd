package cn.iocoder.yudao.module.mall.product.dal.dataobject.spu;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.mall.annotation.DiffField;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSkuShowStatusEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品 SPU DO
 *
 * <AUTHOR>
 */
@TableName(value = "product_spu", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSpuDO extends TenantBaseDO {

    /**
     * 商品 SPU 编号，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 供应商内部商品spuId
     */
    @DiffField(name = "供应商内部商品spuId")
    private String spuInnerId;

    /**
     * 供应商ID
     */
    @DiffField(name = "供应商ID")
    private Long supplierId;

    /**
     * 供应商类型
     */
    @DiffField(name = "供应商类型")
    private Integer supplierType;

    /**
     * 供应商名称
     */
    @DiffField(name = "供应商名称")
    private String supplierName;
    /**
     * 商品品牌编号
     */
    @DiffField(name = "商品品牌编号")
    private Long brandId;
    /**
     * 商品品牌名称
     */
    @DiffField(name = "商品品牌名称")
    private String brandName;
    /**
     * 完整分类id -分隔
     */
    @DiffField(name = "完整分类id")
    private String fullCategoryId;
    /**
     * 规格类型：0 单规格 1 多规格
     */
    @DiffField(name = "规格类型")
    private Integer specType;
    /**
     * 商品名称
     */
    @DiffField(name = "商品名称")
    private String spuName;
    /**
     * 卖点
     */
    @DiffField(name = "卖点")
    private String sellPoint;
    /**
     * spu单位
     */
    @DiffField(name = "spu单位")
    private String unit;
    /**
     * 描述
     */
    @DiffField(name = "描述")
    private String description;
    /**
     * H5端描述
     */
    private String descriptionH5;
    /**
     * 商品封面图片
     */
    @DiffField(name = "商品封面图片")
    private String picUrl;
    /**
     * 商品轮播图
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> sliderPicUrls;

    /**
     * 商品实际销量
     */
    private Integer salesCount;
    /**
     * 商品初始销量，控制ES排序用，和实际销量无关
     */
    private Integer initSalesCount;
    /**
     * 排序字段
     */
    @DiffField(name = "排序字段")
    private Integer sort;
    /**
     * 一级分类id
     */
    @DiffField(name = "一级分类id")
    private Long category1Id;
    /**
     * 二级分类id
     */
    @DiffField(name = "二级分类id")
    private Long category2Id;
    /**
     * 三级分类id
     */
    @DiffField(name = "三级分类id")
    private Long category3Id;
    /**
     * 一级分类名称
     */
    @DiffField(name = "一级分类名称")
    private String category1Name;
    /**
     * 二级分类名称
     */
    @DiffField(name = "二级分类名称")
    private String category2Name;
    /**
     * 三级分类名称
     */
    @DiffField(name = "三级分类名称")
    private String category3Name;
    /**
     * 销售价
     */
    @DiffField(name = "销售价")
    private BigDecimal salePrice;
    /**
     * 完整分类名称，以/分隔
     */
    private String fullCategoryName;
    /**
     * 状态：  {@link cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum}
     */
    @DiffField(name = "状态")
    private Integer status;

    /**
     * 商品显示状态  {@link ProductSkuShowStatusEnum}
     */
    private Integer showStatus;

    /**
     * 平台上下架状态：  {@link cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum}
     */
    @DiffField(name = "状态")
    private Integer platformStatus;

    /**
     * 获取最末级的分类名称
     * @return
     */
    @JsonIgnore
    public String getEndCategoryName() {
        if(StringUtils.isNotBlank(fullCategoryName)) {
            String str =  StringUtils.substringAfterLast(fullCategoryName, "/");
            if(StringUtils.isBlank(str)) {
                return fullCategoryName;
            }

            return str;
        }
        return null;
    }

    /**
     * 获取最末级的分类ID
     * @return
     */
    @JsonIgnore
    public String getEndCategoryId() {
        if(StringUtils.isNotBlank(fullCategoryId)) {
            String str =  StringUtils.substringAfterLast(fullCategoryId, "-");
            if(StringUtils.isBlank(str)) {
                return fullCategoryId;
            }

            return str;
        }
        return null;
    }

}
