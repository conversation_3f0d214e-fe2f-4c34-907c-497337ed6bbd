package cn.iocoder.yudao.module.mall.product.service.wish;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.service.common.NotifyTaskService;
import cn.iocoder.yudao.module.mall.enums.basis.NotifyTaskEnum;
import cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo.ProductWishExportReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo.ProductWishPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.wish.vo.AppProductWishCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.wish.vo.AppProductWishUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.convert.wish.ProductWishConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.wish.ProductWishDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.wish.ProductWishMapper;
import cn.iocoder.yudao.module.mall.product.enums.wish.ProductWishStatusEnum;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.WISH_NOT_EXISTS;

/**
 * 心愿单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductWishServiceImpl implements ProductWishService {

    @Resource
    private ProductWishMapper wishMapper;
    @Resource
    private NotifyTaskService notifyTaskService;

    @Override
    public Long createWish(AppProductWishCreateReqVO createReqVO) {
        // 插入
        ProductWishDO wish = ProductWishConvert.INSTANCE.convert(createReqVO);
        wish.setStatus(ProductWishStatusEnum.INIT.getValue());
        wishMapper.insert(wish);
        // 返回
        return wish.getId();
    }

    @Override
    public void updateWish(AppProductWishUpdateReqVO updateReqVO) {
        // 校验存在
        validateWishExists(updateReqVO.getId());
        // 更新
        ProductWishDO updateObj = ProductWishConvert.INSTANCE.convert(updateReqVO);
        wishMapper.updateById(updateObj);
    }

    @Override
    public void deleteWish(Long id) {
        // 校验存在
        validateWishExists(id);
        // 删除
        wishMapper.deleteById(id);
    }

    private void validateWishExists(Long id) {
        if (wishMapper.selectById(id) == null) {
            throw exception(WISH_NOT_EXISTS);
        }
    }

    @Override
    public void updateWithStatus(ProductWishDO bean) {
        validateWishExists(bean.getId());

        ProductWishDO wishDO = wishMapper.selectById(bean.getId());
        wishDO.setStatus(bean.getStatus());
        wishDO.setReplyContent(bean.getReplyContent());
        wishDO.setExtParams(bean.getExtParams());
        wishMapper.updateById(wishDO);

        // 发送通知
        sendNotifyAfterReply(wishDO);
    }

    @Override
    public ProductWishDO getWish(Long id) {
        return wishMapper.selectById(id);
    }

    @Override
    public List<ProductWishDO> getWishList(Collection<Long> ids) {
        return wishMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ProductWishDO> getWishPage(ProductWishPageReqVO pageReqVO) {
        return wishMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProductWishDO> getWishList(ProductWishExportReqVO exportReqVO) {
        return wishMapper.selectList(exportReqVO);
    }

    /**
     * 针对心愿单回复后发送相应通知
     * @param wishDO
     */
    private void sendNotifyAfterReply(ProductWishDO wishDO) {
        if(ObjectUtil.notEqual(wishDO.getStatus(), ProductWishStatusEnum.REPLIED.getValue())) {
            return;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("createTime", LocalDateTimeUtil.formatNormal(wishDO.getCreateTime()));
        params.put("content", wishDO.getReplyContent());
        params.put("extParam", wishDO.getExtParams());
        params.put("date", LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy年MM月dd日"));
        if(StrUtil.isNotBlank(wishDO.getExtParams())) {
            notifyTaskService.sendNotifyAsync(NotifyTaskEnum.SKU_WISH_SKU_REPLY.getCode(), params, wishDO.getUserId());
        } else {
            notifyTaskService.sendNotifyAsync(NotifyTaskEnum.SKU_WISH_REPLY.getCode(), params, wishDO.getUserId());
        }
    }

}
