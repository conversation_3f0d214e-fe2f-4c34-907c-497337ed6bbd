package cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.experimental.Accessors;

/**
 * 京东分类映射 Excel VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class VopCategoryMappingExcelVO {

    @ExcelProperty("末级分类ID")
    private Long lastCategoryId;

    @ExcelProperty("末级分类名称")
    private String lastCategoryName;

    @ExcelProperty("完整分类ID")
    private String fullCategoryId;

    @ExcelProperty("完整分类名称")
    private String fullCategoryName;

    @ExcelProperty("vop末级分类ID")
    private Long vopLastCategoryId;

    @ExcelProperty("vop末级分类名称")
    private String vopLastCategoryName;

    @ExcelProperty("vop完整分类ID")
    private String vopFullCategoryId;

    @ExcelProperty("vop完整分类名称")
    private String vopFullCategoryName;

}
