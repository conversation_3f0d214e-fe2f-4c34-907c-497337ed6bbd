package cn.iocoder.yudao.module.mall.external.open.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.common.OpenAppDO;
import cn.iocoder.yudao.module.mall.basis.service.common.openapp.OpenAppService;
import cn.iocoder.yudao.module.mall.external.open.aop.OpenAuthAspectV2;
import cn.iocoder.yudao.module.mall.external.open.context.OpenContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
public class OpenRequestFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("OpenRequestFilter init...");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            chain.doFilter(handleDecryption(request), response);
        } catch (ServiceException e) {
            HttpServletResponse httpResponse = (HttpServletResponse) response;
            CommonResult<?> result = CommonResult.error(e.getCode(), e.getMessage());
            ServletUtils.writeJSON(httpResponse, result);
        } finally {
            OpenContextHolder.clear();
        }
    }

    /**
     * 请求体数据解密
     * @param request
     * @return
     */
    private ServletRequest handleDecryption(ServletRequest request) {
        HttpServletRequest httpRequest = (HttpServletRequest)request;
        String token = StrUtil.blankToDefault(httpRequest.getHeader(OpenAuthAspectV2.HEADER_TOKEN), httpRequest.getHeader(OpenAuthAspectV2.HEADER_TOKEN_V1));
        if(StrUtil.isBlank(token)) {
            return request;
        }

        OpenAppService openAppService = SpringUtil.getBean(OpenAppService.class);
        OpenAppDO openApp = openAppService.getOpenAppFromCache(token);
        if(openApp == null) {
            return request;
        }

        if(openApp.getEncodingStatus() && StrUtil.isNotBlank(openApp.getEncodingKey())) {
            if(request.getContentType().contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
                return new OpenAesRequestWrapper(httpRequest);
            }
            OpenAesRequestWrapper wrapperRequest = new OpenAesRequestWrapper(httpRequest);
            wrapperRequest.decryptRequestBody(openApp.getEncodingKey());
            return wrapperRequest;
        }

        return request;
    }

}
