package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.framework.common.validation.Mobile;
import cn.iocoder.yudao.module.mall.trade.enums.order.InvoiceStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderBillStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 交易订单的分页 Request VO")
@Data
public class TradeOrderPageReqVO extends PageParam {

    @Schema(description = "订单号", example = "88888888")
    private String no;

    @Schema(description = "父订单号", example = "88888888")
    private String parentNo;

    @Schema(description = "三方订单号", example = "88888888")
    private String thirdOrderId;

    @Schema(description = "审批单号")
    private String bpmNo;

    @Schema(description = "订单号、父订单号、三方订单号、审批单号", example = "88888888")
    private String orderCode;

    @Schema(description = "用户ID", example = "1024")
    private Long userId;

    @Schema(description = "用户ID列表", example = "1024")
    private Set<Long> userIds;

    @Schema(description = "属性账单ID", example = "1024")
    private Long billId;

    @Schema(description = "不属性账单ID", example = "1024")
    private Long notBillId;

    @Schema(description = "用户手机号", example = "小王")
    @Mobile
    private String userMobile;

    @Schema(description = "收件人名称", example = "小红")
    private String receiverName;

    @Schema(description = "收件人手机", example = "1560")
    @Mobile
    private String receiverMobile;

    @Schema(description = "订单状态", example = "1")
    @InEnum(value = TradeOrderStatusEnum.class, message = "订单状态必须是 {value}")
    private Integer status;

    @Schema(description = "发票状态", example = "1")
    @InEnum(value = InvoiceStatusEnum.class, message = "发票状态必须是 {value}")
    private Integer invoiceStatus;

    @Schema(description = "账单状态", example = "1")
    @InEnum(value = TradeOrderBillStatusEnum.class, message = "账单状态必须是 {value}")
    private Integer billStatus;

    @Schema(description = "下单终端", example = "1")
    private Integer platform;

    @Schema(description = "父子订单类型", example = "1")
    private Integer parentType;

    @Schema(description = "订单状态", example = "1")
    private List<Integer> statusList;

    @Schema(description = "审批状态", example = "1")
    private List<Integer> auditStatusList;

    @Schema(description = "支付渠道", example = "wx_lite")
    private String payChannelCode;

    @Schema(description = "支付方式", example = "1")
    private Integer paymentMethod;

    @Schema(description = "支付方式", example = "1")
    private List<Integer> paymentMethodList;

    @Schema(description = "支付方式", example = "1")
    private Integer notPaymentMethod;

    @Schema(description = "支付方式", example = "1")
    private List<Integer> notPaymentMethodList;

    @Schema(description = "是否需要提交采购", example = "false")
    private Boolean needPurchase;

    @Schema(description = "是否线下结算", example = "false")
    private Boolean offlineSettlement;

    @Schema(description = "是否支付", example = "false")
    private Boolean payed;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "下单时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] submitTime;

    @Schema(description = "确认时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] confirmTime;

    @Schema(description = "取消时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] cancelTime;

    @Schema(description = "收货时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] receiveTime;

    @Schema(description = "更新时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updateTime;

    @Schema(description = "完成时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] finishTime;

    @Schema(description = "用户名", example = "hello")
    private String userName;

    @Schema(description = "用户昵称", example = "hello")
    private String userNickname;

    @Schema(description = "供应商ID", example = "2")
    private Long supplierId;

    @Schema(description = "是否需要推送到业财融合", example = "true")
    private Boolean ycrhNeed;

    @Schema(description = "业财融合状态", example = "1")
    private String ycrhStatus;

    @Schema(description = "业财融合预约单号", example = "1")
    private String voucherNo;

    @Schema(description = "平台SKU", example = "1")
    private Long skuId;

    @Schema(description = "三方SKU", example = "1")
    private String thirdSkuId;

    @Schema(description = "平台、三方SKU", example = "1")
    private String skuCode;

    @Schema(description = "商品名称", example = "1")
    private String skuName;

    @Schema(description = "固资状态", example = "1")
    private Integer assetStatus;

    @Schema(description = "固资状态", example = "[1]")
    private List<Integer> assetStatusList;

    @Schema(description = "非供应商类型", example = "true")
    private Integer notSupplierType;

    private Integer settleStatus;

    @Schema(description = "物流状态", example = "0")
    private Integer deliveryStatus;

    @Schema(description = "审批完成时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditCompleteTime;

    /**
     * 默认为submitTime逆序
     * 1-submitTime desc 11-submitTime asc
     * 2-finishTime desc 22-finishTime asc
     * 3-cancelTime desc 33-cancelTime asc
     * 4-confirmTime desc 44-confirmTime asc
     */
    @Schema(description = "排序类型", example = "1")
    private Integer sortType;

    @Schema(description = "商品标签", example = "[1]")
    private List<Long> skuTags;

    @Schema(description = "订单实际金额是否大于0", example = "true")
    private Boolean notZero;

    private Long teamUserId;
    private String teamOrderNo;

}
