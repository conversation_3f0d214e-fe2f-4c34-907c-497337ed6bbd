package cn.iocoder.yudao.module.mall.product.service.category;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ConfigProductCategorySpecCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ConfigProductCategorySpecUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategorySpecDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 商品规格 Service 接口
 *
 * <AUTHOR>
 */
public interface ConfigProductCategorySpecService {

    /**
     * 创建商品规格
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSpec(@Valid ConfigProductCategorySpecCreateReqVO createReqVO);

    /**
     * 更新商品规格
     *
     * @param updateReqVO 更新信息
     */
    void updateSpec(@Valid ConfigProductCategorySpecUpdateReqVO updateReqVO);
    /**
     * 删除商品规格
     *
     * @param id 编号
     */
    void deleteSpec(Long id);

    /**
     * 获得商品规格
     *
     * @param id 编号
     * @return 商品规格
     */
    ConfigProductCategorySpecDO getSpec(Long id);

    /**
     * 获得商品规格列表
     *
     * @param ids 编号
     * @return 商品规格列表
     */
    List<ConfigProductCategorySpecDO> getSpecList(Collection<Long> ids);

    /**
     * 获得商品规格分页
     *
     * @param pageReqVO 分页查询
     * @return 商品规格分页
     */
    PageResult<ConfigProductCategorySpecDO> getSpecPage(ProductSpecPageReqVO pageReqVO);

    /**
     * 商品在新增或编辑时获得商品规格列表
     *
     * @param productCategoryId 商品分类编号
     * @return 商品规格列表
     */
    List<ConfigProductCategorySpecDO> getSpecList4product(Long productCategoryId);

}
