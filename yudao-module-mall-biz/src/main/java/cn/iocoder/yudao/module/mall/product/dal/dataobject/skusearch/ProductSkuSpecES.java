package cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch;

import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@Data
public class ProductSkuSpecES {

    @Field(type = FieldType.Keyword)
    private String specName;    // 属性名（如"颜色"）
    @Field(type = FieldType.Long)
    private Long specValueId; // 属性值ID（如"red"）
    @Field(type = FieldType.Keyword)
    private String specValue;   // 属性值（如"红色"）

}
