package cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 商品规格 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ConfigProductCategorySpecBaseVO {

    @Schema(description = "商品分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品分类ID不能为空")
    private Long categoryId;

    @Schema(description = "规格名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "规格名不能为空")
    private String name;

    @Schema(description = "规格类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "规格类型不能为空")
    private Integer specType;

}
