package cn.iocoder.yudao.module.mall.product.dal.mysql.tag;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagGroupDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 标签分组 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductTagGroupMapper extends BaseMapperX<ProductTagGroupDO> {

    default PageResult<ProductTagGroupDO> selectPage(ProductTagGroupPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductTagGroupDO>()
                .likeIfPresent(ProductTagGroupDO::getName, reqVO.getName())
                .eqIfPresent(ProductTagGroupDO::getStatus, reqVO.getStatus())
                .orderByDesc(ProductTagGroupDO::getId));
    }

}
