package cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo;

import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsCategoryItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class VopGoodsCategoryBaseVO {
    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String categoryName;

    /**
     * 父级分类ID
     */
    @Schema(description = "父级分类ID")
    private long parentId;
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer orderSort;
    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    private long categoryId;
    /**
     * 分类级别
     */
    @Schema(description = "分类级别")
    private int categoryLevel;
    /**
     * 分类是否显示
     */
    @Schema(description = "分类是否显示")
    private int status;


}
