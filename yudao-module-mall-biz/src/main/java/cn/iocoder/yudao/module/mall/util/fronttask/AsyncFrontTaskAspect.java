package cn.iocoder.yudao.module.mall.util.fronttask;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import com.alibaba.ttl.TtlRunnable;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @date 2024/8/16
 */
@Component
@Aspect
@Order(5002)
@Slf4j
public class AsyncFrontTaskAspect {

    @Resource
    private AsyncFrontTaskUtils asyncFrontTaskUtils;
    @Resource
    private HttpServletResponse response;

    @Around("@annotation(asyncFrontTask)")
    public Object around(ProceedingJoinPoint joinPoint, AsyncFrontTask asyncFrontTask) throws Throwable {
        if (SecurityFrameworkUtils.getLoginUser() == null) {
            throw exception(UNAUTHORIZED);
        }

        String taskId = AsyncFrontTaskUtils.generateTaskId();
        try {
            AsyncTaskTypeEnum taskType = asyncFrontTask.taskType();
            if(taskType == null) {
                throw new RuntimeException("taskType不能为空");
            }
            AsyncFrontTaskContext.setTaskId(taskId);
            // 非内嵌执行时，会自动封装成子线程任务
            if(!asyncFrontTask.isNested()) {
                TtlRunnable runnable = TtlRunnable.get(() -> {
                    try {
                        joinPoint.proceed();
                    } catch (Throwable e) {
                        log.error("ayncTask error: ", e);
                    } finally {
                        clean();
                    }
                });
                // 前端异步任务，开启任务后，直接返回任务ID
                asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), taskId, taskType, runnable);
                ServletUtils.writeJSON(response, CommonResult.success(taskId));
                return null;
            } else {
                return joinPoint.proceed();
            }
        } catch (Exception e) {
            clean();
            throw e;
        }
    }

    private void clean() {
        try {
            AsyncFrontTaskContext.clear();
        } catch(Exception e) {
            log.error("async task context clean error: ", e);
        }
    }

}
