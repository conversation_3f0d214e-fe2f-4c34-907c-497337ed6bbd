package cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.util.List;


/**
 * SKU搜索演示DO
 */
@Data
@EqualsAndHashCode()
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuSpecValueDO implements Serializable {

    @Field(type = FieldType.Long)
    private Long specId;

    @Field(type = FieldType.Keyword)
    private String specValue;

}
