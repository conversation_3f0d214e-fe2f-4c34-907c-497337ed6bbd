package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Data
public class SupplierSalesSettleSummaryReqVO {
    @Schema(description = "供应商ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    public LocalDateTime getStartTime() {
        return startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public LocalDateTime getEndTime() {
        return endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
}
