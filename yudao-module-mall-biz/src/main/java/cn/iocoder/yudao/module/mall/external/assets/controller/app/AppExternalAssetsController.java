package cn.iocoder.yudao.module.mall.external.assets.controller.app;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.tenant.TenantIdUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.AssetsConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.integration.assetsconfig.AssetsConfigService;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsBaseReq;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsDetailQueryReq;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsDetailRespDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsStatusUpdateReq;
import cn.iocoder.yudao.module.mall.external.assets.enums.AssetClientTypeEnum;
import cn.iocoder.yudao.module.mall.external.assets.util.LBJwtUtil;
import cn.iocoder.yudao.module.mall.trade.enums.order.AssetStatusEnum;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderAssetsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.*;


@Tag(name = "外部API - 资产")
@RestController
@RequestMapping("/mall/external/assets")
@Validated
@Slf4j
public class AppExternalAssetsController {

    @Resource
    private AssetsConfigService assetsConfigService;
    @Lazy
    @Resource
    private TradeOrderAssetsService tradeOrderAssetsService;

    /**
     * 构建上下文
     * @param tcode
     */
    private void buildContext(String tcode) {
        try {
            Long tenantId = TenantIdUtils.decryptTenantId(tcode);
            TenantContextHolder.setTenantId(tenantId);
            TenantContextHolder.setIgnore(false);
        } catch (Exception e) {
            log.error("构建上下文时解析租户ID错误:", e);
            throw ServiceExceptionUtil.exception(EXTERNAL_TENANT_ID_PARSE_FAIL);
        }
    }

    /**
     * 查询资产系统集成配置
     * @return
     */
    private AssetsConfigDO getAndCheckAssetsConfig(String sysCode) {
        AssetsConfigDO config = assetsConfigService.getAssetsConfigBySys(sysCode);
        if(config == null) {
            throw ServiceExceptionUtil.exception(ASSETS_CONFIG_NULL);
        }
        return config;
    }

    /**
     * 洛比：查询订单明细
     * @param lbReq
     * @return
     */
    @PostMapping("/luobi/queryOrderDetail")
    @Operation(summary = "查询订单明细")
    public CommonResult<PageResult<ExtAssetsDetailRespDTO>> queryOrderDetail4LB(@Valid @RequestBody ExtAssetsBaseReq lbReq) {
        buildContext(lbReq.getTcode());
        AssetsConfigDO config = getAndCheckAssetsConfig(AssetClientTypeEnum.LUOBI.getType());
        log.info("LB token: {}", lbReq.getToken());
        try {
            String json = LBJwtUtil.dencrty(lbReq.getToken(), config.getKey1(), config.getKey2(), Long.valueOf(config.getKey3()));
            ExtAssetsDetailQueryReq queryReq = JsonUtils.parseObject(json, ExtAssetsDetailQueryReq.class);

            PageResult<ExtAssetsDetailRespDTO> pageResult = tradeOrderAssetsService.getOrderItemAssetsPage(queryReq, AssetClientTypeEnum.LUOBI.getType());
            return success(pageResult);
        } catch (Exception e) {
            log.error("queryOrderDetail4LB error: {}", lbReq.getToken(), e);
            throw ServiceExceptionUtil.exception(ASSETS_DETAIL_QUERY_FAIL);
        }
    }

    /**
     * 洛比：更新审批状态
     * @param lbReq
     * @return
     */
    @PostMapping("/luobi/updateStatus")
    @Operation(summary = "更新审批状态")
    public CommonResult<Boolean> updateStatus4LB(@Valid @RequestBody ExtAssetsBaseReq lbReq) {
        buildContext(lbReq.getTcode());
        AssetsConfigDO config = getAndCheckAssetsConfig(AssetClientTypeEnum.LUOBI.getType());
        try {
            String json = decrypt(lbReq.getToken(),  config);
            log.info("洛比调用接口更新固资建档状态:{}", json);
            ExtAssetsStatusUpdateReq updateReq = JsonUtils.parseObject(json, ExtAssetsStatusUpdateReq.class);
            validateUpdateParams(updateReq);

            tradeOrderAssetsService.updateAssetsStatus(updateReq);
            return success(true);
        } catch (Exception e) {
            log.error("updateAssetStatus4LB error: {}", lbReq.getToken(), e);
            throw ServiceExceptionUtil.exception(ASSETS_STATUS_UPDATE_FAIL, e.getMessage());
        }
    }

    /**
     * 通用：查询订单明细
     * @param lbReq
     * @return
     */
    @PostMapping("/common/{sysCode}/queryOrderDetail")
    @Operation(summary = "查询订单明细")
    public CommonResult<PageResult<ExtAssetsDetailRespDTO>> queryOrderDetail(@Valid @RequestBody ExtAssetsBaseReq lbReq, @PathVariable String sysCode) {
        buildContext(lbReq.getTcode());
        AssetsConfigDO config = getAndCheckAssetsConfig(sysCode);
        try {
            String json = LBJwtUtil.dencrty(lbReq.getToken(), config.getKey1(), config.getKey2(), Long.valueOf(config.getKey3()));
            log.info("调用通用接口查询订单明细:{}", json);
            ExtAssetsDetailQueryReq queryReq = JsonUtils.parseObject(json, ExtAssetsDetailQueryReq.class);

            PageResult<ExtAssetsDetailRespDTO> pageResult = tradeOrderAssetsService.getOrderItemAssetsPage(queryReq, sysCode);
            return success(pageResult);
        } catch (Exception e) {
            log.error("queryOrderDetail4LB error: {}", lbReq.getToken(), e);
            throw ServiceExceptionUtil.exception(ASSETS_DETAIL_QUERY_FAIL);
        }
    }

    /**
     * 通用接口：更新审批状态
     * @param lbReq
     * @return
     */
    @PostMapping("/common/{sysCode}/updateStatus")
    @Operation(summary = "更新审批状态")
    public CommonResult<Boolean> updateStatus(@Valid @RequestBody ExtAssetsBaseReq lbReq, @PathVariable String sysCode) {
        buildContext(lbReq.getTcode());
        AssetsConfigDO config = getAndCheckAssetsConfig(sysCode);
        try {
            String json = LBJwtUtil.dencrty(lbReq.getToken(), config.getKey1(), config.getKey2(), Long.valueOf(config.getKey3()));
            log.info("调用通用接口更新固资建档状态:{}", json);
            ExtAssetsStatusUpdateReq updateReq = JsonUtils.parseObject(json, ExtAssetsStatusUpdateReq.class);
            validateUpdateParams(updateReq);

            tradeOrderAssetsService.updateAssetsStatus(updateReq);
            return success(true);
        } catch (Exception e) {
            log.error("updateAssetStatus error: {}", lbReq.getToken(), e);
            throw ServiceExceptionUtil.exception(ASSETS_STATUS_UPDATE_FAIL, e.getMessage());
        }
    }

    private void validateUpdateParams(ExtAssetsStatusUpdateReq updateReq) {
        if(StringUtils.isBlank(updateReq.getOrderDetailNo())) {
            throw ServiceExceptionUtil.exception(ASSETS_STATUS_UPDATE_PARAM_INVALID);
        }
        if(updateReq.getApproveStatus() == null) {
            throw ServiceExceptionUtil.exception(ASSETS_STATUS_UPDATE_PARAM_INVALID);
        }
        if(ObjectUtil.equal(AssetStatusEnum.ASSET_SUCCESS.getStatus(), updateReq.getApproveStatus())) {
            if(StringUtils.isBlank(updateReq.getCategory6()) || StringUtils.isBlank(updateReq.getCategory16())) {
                log.warn("资产6大类代码或16大类代码为空");
//                throw ServiceExceptionUtil.exception(ASSETS_STATUS_UPDATE_PARAM_INVALID);
            }
        }
    }

    private String decrypt(String token, AssetsConfigDO config) throws UnsupportedEncodingException {
        return LBJwtUtil.dencrty(token, config.getKey1(), config.getKey2(), Long.valueOf(config.getKey3()));
    }

}
