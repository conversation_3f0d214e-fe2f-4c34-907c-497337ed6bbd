package cn.iocoder.yudao.module.mall.product.service.tag;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 标签 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductTagService extends IService<ProductTagDO> {

    /**
     * 创建标签
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTag(@Valid ProductTagCreateReqVO createReqVO);

    /**
     * 更新标签
     *
     * @param updateReqVO 更新信息
     */
    void updateTag(@Valid ProductTagUpdateReqVO updateReqVO);

    /**
     * 删除标签
     *
     * @param id 编号
     */
    void deleteTag(Long id);

    /**
     * 根据分组查询标签数据
     * @param groupId
     * @return
     */
    long countByGroup(Long groupId);

    /**
     * 获得标签
     *
     * @param id 编号
     * @return 标签
     */
    ProductTagDO getTag(Long id);

    /**
     * 获得标签列表
     *
     * @param ids 编号
     * @return 标签列表
     */
    List<ProductTagDO> getTagList(Collection<Long> ids);

    /**
     * 查询所有标签
     * @return
     */
    List<ProductTagDO> listAll();

    /**
     * 根据分组查询标签
     * @param groupIds
     * @param status
     * @return
     */
    List<ProductTagDO> getTagListByGroup(Collection<Long> groupIds, Integer status);

    /**
     * 获得标签分页
     *
     * @param pageReqVO 分页查询
     * @return 标签分页
     */
    PageResult<ProductTagDO> getTagPage(ProductTagPageReqVO pageReqVO);

}
