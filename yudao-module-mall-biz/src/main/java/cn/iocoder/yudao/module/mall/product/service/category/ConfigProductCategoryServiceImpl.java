package cn.iocoder.yudao.module.mall.product.service.category;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryListReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsCategoryItem;
import cn.iocoder.yudao.module.mall.product.convert.category.ProductCategoryConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.category.ConfigProductCategoryMapper;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryLevelEnum;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryTypeEnum;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsCategoryService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.*;

/**
 * 平台配置商品分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ConfigProductCategoryServiceImpl implements ConfigProductCategoryService {

    @Resource
    private ConfigProductCategoryMapper configProductCategoryMapper;
    @Resource
    private VopGoodsCategoryService vopGoodsCategoryService;

    private volatile List<ConfigProductCategoryDO> cacheList = null;
    private final Object lock = new Object();

    private List<ConfigProductCategoryDO> getCacheList() {
        if (cacheList == null) {
            synchronized (lock) {
                if (cacheList == null) {
                    log.info("初始化商品分类缓存...");
                    cacheList = configProductCategoryMapper.selectList(
                            Wrappers.<ConfigProductCategoryDO>lambdaQuery());
                    log.info("商品分类缓存初始化完成，共{}条记录", cacheList.size());
                }
            }
        }
        return cacheList;
    }

    // 清除缓存的方法
    public void clearCache() {
        synchronized (lock) {
            cacheList = null;
        }
    }

    public void reloadCache() {
        clearCache();
        getCacheList();
    }

    @Override
    @TenantIgnore
    public Long createCategory(ProductCategoryCreateReqVO createReqVO) {
        // 校验父分类存在
        validateParentProductCategory(createReqVO.getParentId());

        // 插入
        ConfigProductCategoryDO category = ProductCategoryConvert.INSTANCE.convert2(createReqVO);
        configProductCategoryMapper.insert(category);
        reloadCache();
        // 返回
        return category.getId();
    }

    @Override
    @TenantIgnore
    public void updateCategory(ProductCategoryUpdateReqVO updateReqVO) {
        // 校验分类是否存在
        validateProductCategoryExists(updateReqVO.getId());
        // 校验父分类存在
        validateParentProductCategory(updateReqVO.getParentId());

        // 更新
        ConfigProductCategoryDO updateObj = ProductCategoryConvert.INSTANCE.convert2(updateReqVO);
        configProductCategoryMapper.updateById(updateObj);

        ConfigProductCategoryDO configProductCategoryDO =  configProductCategoryMapper.selectById(updateObj.getId());
        List<ConfigProductCategoryDO> parentsCategoryDOS = new ArrayList<>();
        getParentCategoryList(configProductCategoryDO.getCategoryId(), parentsCategoryDOS);
        CollectionUtil.reverse(parentsCategoryDOS);
        parentsCategoryDOS.add(configProductCategoryDO);
        String fullCategoryName = parentsCategoryDOS.stream().map(ConfigProductCategoryDO::getCategoryName).collect(Collectors.joining("/"));
        String fullCategoryId = parentsCategoryDOS.stream().map(ConfigProductCategoryDO::getCategoryId).map(String::valueOf).collect(Collectors.joining("-"));
        LambdaUpdateWrapper<ConfigProductCategoryDO> updateWrapper = new LambdaUpdateWrapper<ConfigProductCategoryDO>()
                .eq(ConfigProductCategoryDO::getId, updateObj.getId())
                .set(ConfigProductCategoryDO::getFullCategoryId, fullCategoryId)
                .set(ConfigProductCategoryDO::getFullCategoryName, fullCategoryName);
        configProductCategoryMapper.update(null, updateWrapper);

        reloadCache();
    }

    @Override
    @TenantIgnore
    public void deleteCategory(Long id) {
        // 校验分类是否存在
        validateProductCategoryExists(id);
        // TODO 芋艿 补充只有不存在商品才可以删除
        // 删除
        configProductCategoryMapper.deleteById(id);
        reloadCache();
    }

    @TenantIgnore
    private void validateParentProductCategory(Long id) {
        // 如果是根分类，无需验证
        if (Objects.equals(id, ProductCategoryDO.PARENT_ID_NULL)) {
            return;
        }
        // 父分类不存在
        ConfigProductCategoryDO category = configProductCategoryMapper.selectByCategoryId(id);
        if (category == null) {
            throw exception(CATEGORY_PARENT_NOT_EXISTS);
        }
    }

    @TenantIgnore
    private void validateProductCategoryExists(Long id) {
        ConfigProductCategoryDO category = configProductCategoryMapper.selectById(id);
        if (category == null) {
            throw exception(CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    @TenantIgnore
    public ConfigProductCategoryDO getCategory(Long id) {
        return configProductCategoryMapper.selectById(id);
    }

    @Override
    @TenantIgnore
    public ConfigProductCategoryDO getCategoryByCategoryId(Long categoryId) {
        return configProductCategoryMapper.selectOne(Wrappers.<ConfigProductCategoryDO>lambdaQuery()
                .eq(ConfigProductCategoryDO::getStatus, ProductCategoryStatusEnum.ENABLE.getStatus())
                .eq(ConfigProductCategoryDO::getCategoryId, categoryId));
    }

    @Override
    @TenantIgnore
    public String getNamePath(String idPath, String seperator) {
        if(StringUtils.isBlank(idPath)) {
            return null;
        }
        String[] ids = StringUtils.split(idPath, ",");
        List<String> idList =Arrays.asList(ids);

        return getNamePath(idList.stream().map(Long::valueOf).collect(Collectors.toList()), seperator);
    }

    @Override
    @TenantIgnore
    public String getNamePath(List<Long> ids, String seperator) {
        if(CollUtil.isEmpty(ids)) {
            return null;
        }
        if(StringUtils.isBlank(seperator)) {
            seperator = "/";
        }

        List<String> nameList = getCacheList().stream().filter(item -> CollectionUtil.contains(ids, item.getCategoryId())).map(item -> item.getCategoryName()).collect(Collectors.toList());
        return CollectionUtil.join(nameList, seperator);
    }

    @Override
    @TenantIgnore
    public void validateCategory(Long id) {
        ConfigProductCategoryDO category = configProductCategoryMapper.selectById(id);
        if (category == null) {
            throw exception(CATEGORY_NOT_EXISTS);
        }
        if (Objects.equals(category.getStatus(), ProductCategoryStatusEnum.DISABLE.getStatus())) {
            throw exception(CATEGORY_DISABLED, category.getCategoryName());
        }
    }

    // 缓存可能需要更新，不从缓存获取数据
    @TenantIgnore
    public void getParentCategoryList(Long categoryId, List<ConfigProductCategoryDO> parentCategoryList) {
        ConfigProductCategoryDO categoryDO = getCategoryByCategoryId(categoryId);
        if(categoryDO.getCategoryLevel() == 0){
            return;
        }
        ConfigProductCategoryDO parentCategory = getCategoryByCategoryId(categoryDO.getParentId());
        parentCategoryList.add(parentCategory);
        getParentCategoryList(parentCategory.getCategoryId(), parentCategoryList);
    }

    @Override
    @TenantIgnore
    public List<ConfigProductCategoryDO> getAllCategoryList(ProductCategoryListReqVO listReqVO) {
        List<ConfigProductCategoryDO> cacheList = getCacheList();
        List<ConfigProductCategoryDO> list = cacheList.stream().filter(item -> item.getStatus().equals(ProductCategoryStatusEnum.ENABLE.getStatus())).collect(Collectors.toList());
        if(StringUtils.isNotBlank(listReqVO.getName())) {
            list = cacheList.stream().filter(item -> StringUtils.isNotBlank(listReqVO.getName()) && item.getCategoryName().contains(listReqVO.getName()))
                    .collect(Collectors.toList());
        }
        if(listReqVO.getStatus() != null) {
            list = cacheList.stream().filter(item -> item.getStatus().equals(listReqVO.getStatus())).collect(Collectors.toList());
        }
        CollectionUtil.sort(list, (obj1 ,obj2) -> obj2.getOrderSort() - obj1.getOrderSort());
        return list;
    }

    /**
     * 查询一级大类
     *
     * @return
     */
    @Override
    @TenantIgnore
    public List<ConfigProductCategoryDO> getRootCategoryList(Integer type, ProductCategoryStatusEnum status) {
        List<ConfigProductCategoryDO> list = getCacheList().stream().filter(item -> {
            return Objects.equals(item.getCategoryLevel(), ProductCategoryLevelEnum.LAVEL1.getLevel()) && (type == null || item.getType().equals(type));
        }).collect(Collectors.toList());

        if(status != null) {
            list = list.stream().filter(item -> item.getStatus().equals(status.getStatus())).collect(Collectors.toList());
        }
        CollectionUtil.sort(list, (obj1 ,obj2) -> obj2.getOrderSort() - obj1.getOrderSort());
        return list;
    }

    @Override
    @TenantIgnore
    public void updateCategoryStatus(Long id, ProductCategoryStatusEnum status) {
        // 校验分类是否存在
        validateProductCategoryExists(id);

        ConfigProductCategoryDO updateObj = new ConfigProductCategoryDO();
        updateObj.setId(id);
        updateObj.setStatus(status.getStatus());
        configProductCategoryMapper.updateById(updateObj);
        this.reloadCache();
    }

    /**
     * 查询一个分类下全部子分类
     * @param parentCategoryId
     * @return
     */
    @Override
    @TenantIgnore
    public List<ConfigProductCategoryDO> getChildCategoryList(Long parentCategoryId, Integer status) {
        if (null == parentCategoryId) {
            return Collections.emptyList();
        }
        List<ConfigProductCategoryDO> list = getCacheList().stream().filter(item -> Objects.equals(item.getParentId(), parentCategoryId)).collect(Collectors.toList());
        if(status != null) {
            list = list.stream().filter(item -> item.getStatus().equals(status)).collect(Collectors.toList());
        }
        CollectionUtil.sort(list, (obj1 ,obj2) -> obj2.getOrderSort() - obj1.getOrderSort());
        return list;
    }

    @Override
    @TenantIgnore
    public Long getCategoryTotalCount(Integer type) {
        if(type == null) {
            return getCacheList().size() * 1L;
        }
        return getCacheList().stream().filter(item -> item.getType().equals(type)).count();
    }

    /**
     * 根据父级分类ID进行递归构建分类树
     * @param parentCategoryId
     * @return
     */
    @Override
    @TenantIgnore
    public List<ConfigProductCategoryDO> getChildCategoryTreeList(Long parentCategoryId, ProductCategoryStatusEnum status) {
        List<ConfigProductCategoryDO> list = getCacheList().stream().filter(item -> Objects.equals(item.getParentId(), parentCategoryId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if(status != null) {
            list = list.stream().filter(item -> item.getStatus().equals(status.getStatus())).collect(Collectors.toList());
        }
        for (ConfigProductCategoryDO categoryDO : list) {
            categoryDO.setChildCategoryList(this.getChildCategoryTreeList(categoryDO.getCategoryId(), status));
        }

        CollectionUtil.sort(list, (obj1 ,obj2) -> obj2.getOrderSort() - obj1.getOrderSort());
        return list;
    }

    @TenantIgnore
    @Override
    public List<ConfigProductCategoryDO> getCategoryListByCategoryId(Collection<Long> categoryIdList) {
        List<ConfigProductCategoryDO> list = getCacheList().stream().filter(item -> CollectionUtil.contains(categoryIdList, item.getCategoryId())).collect(Collectors.toList());
        CollectionUtil.sort(list, (obj1 ,obj2) -> obj2.getOrderSort() - obj1.getOrderSort());
        return list;
    }

    public void syncProductCatogory(List<VopGoodsCategoryItem> vopGoodsCategoryItems){
        vopGoodsCategoryItems.forEach(vopGoodsCategoryItem -> {
            ConfigProductCategoryDO configProductCategoryDO = configProductCategoryMapper.selectByCategoryId(vopGoodsCategoryItem.getCategoryId());
            if(configProductCategoryDO == null){
                ConfigProductCategoryDO newConfigProductCategoryDO = new ConfigProductCategoryDO()
                        .setCategoryId(vopGoodsCategoryItem.getCategoryId())
                        .setCategoryLevel(vopGoodsCategoryItem.getCategoryLevel())
                        .setCategoryName(vopGoodsCategoryItem.getCategoryName())
                        .setParentId(vopGoodsCategoryItem.getParentId())
                        .setOrderSort(vopGoodsCategoryItem.getOrderSort())
                        .setStatus(ProductCategoryStatusEnum.ENABLE.getStatus())
                        .setType(ProductCategoryTypeEnum.JD.getType());
                configProductCategoryMapper.insert(newConfigProductCategoryDO);
            }
            else if(configProductCategoryDO.getCategoryLevel() != vopGoodsCategoryItem.getCategoryLevel()
                    || !configProductCategoryDO.getCategoryName().equals(vopGoodsCategoryItem.getCategoryName())
                    || configProductCategoryDO.getParentId() != vopGoodsCategoryItem.getParentId()
                    || !configProductCategoryDO.getOrderSort().equals(vopGoodsCategoryItem.getOrderSort())){
                configProductCategoryDO.setCategoryId(vopGoodsCategoryItem.getCategoryId())
                        .setCategoryLevel(vopGoodsCategoryItem.getCategoryLevel())
                        .setCategoryName(vopGoodsCategoryItem.getCategoryName())
                        .setParentId(vopGoodsCategoryItem.getParentId())
                        .setOrderSort(vopGoodsCategoryItem.getOrderSort());
                configProductCategoryMapper.updateById(configProductCategoryDO);
            }

            syncProductCatogory(vopGoodsCategoryItem.getChildCategoryList());
        });
    }

    @TenantIgnore
    @Override
    public boolean sync() {
        log.info("Vop商品分类同步开始");
        boolean needRefreshCache = false;
        try {
            List<ConfigProductCategoryDO> configProductCategoryDOS = this.getRootCategoryList(ProductCategoryTypeEnum.JD.getType(), null);
            for (int i = 0; i < configProductCategoryDOS.size(); i++) {
                ConfigProductCategoryDO configProductCategoryDO = configProductCategoryDOS.get(i);
                List<VopGoodsCategoryItem> vopGoodsCategoryItems = vopGoodsCategoryService.getChildCategoryList(configProductCategoryDO.getCategoryId());
                needRefreshCache = true;
                syncProductCatogory(vopGoodsCategoryItems);
            }
        } finally {
            if(needRefreshCache){
                reloadCache();
            }
            log.info("Vop商品分类同步结束");
        }

        return true;
    }
}
