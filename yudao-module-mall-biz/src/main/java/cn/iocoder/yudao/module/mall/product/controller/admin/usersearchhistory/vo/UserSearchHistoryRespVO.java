package cn.iocoder.yudao.module.mall.product.controller.admin.usersearchhistory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户搜索历史 Response VO")
@Data
@ToString(callSuper = true)
public class UserSearchHistoryRespVO{
    /**
     * 搜索历史id
     */
    private Long id;
    /**
     * 会员姓名
     */
    private String nickname;
    /**
     * 关键字
     */
    private String keyword;
    /**
     * 搜索次数
     */
    private Integer count;
    /**
     * 最近搜索时间
     */
    private LocalDateTime updateTime;

}
