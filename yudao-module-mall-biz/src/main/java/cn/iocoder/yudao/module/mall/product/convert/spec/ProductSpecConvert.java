package cn.iocoder.yudao.module.mall.product.convert.spec;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenProductSpecRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.*;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.BrotherSku;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.BrotherSkuSpecValue;
import cn.iocoder.yudao.module.mall.product.controller.app.spec.vo.AppProductSpecValueVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategorySpecDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSkuSpecDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSpuSpecDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品规格 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSpecConvert {

    ProductSpecConvert INSTANCE = Mappers.getMapper(ProductSpecConvert.class);

    ConfigProductCategorySpecDO convert(ConfigProductCategorySpecCreateReqVO bean);

    ConfigProductCategorySpecDO convert(ConfigProductCategorySpecUpdateReqVO bean);

    ConfigProductCategorySpecRespVO convert(ConfigProductCategorySpecDO bean);

    List<ConfigProductCategorySpecRespVO> convertList(List<ConfigProductCategorySpecDO> list);

    List<AppOpenProductSpecRespVO> convertList04(List<ConfigProductCategorySpecDO> list);

    PageResult<ConfigProductCategorySpecRespVO> convertPage(PageResult<ConfigProductCategorySpecDO> page);

    List<ProductSpecValueReqVO> convertVOList01(List<AppProductSpecValueVO> list);

    ProductSkuSpecDO convertDO2(AppProductSpecValueVO bean);

    ProductSpuSpecDO convertDO3(AppProductSpecValueVO bean);

    default List<BrotherSku> convertList02(Long selectSkuId, List<ProductSkuDO> productSkuDOS, List<ProductSkuSpecDO> specDOS) {
        Map<String, List<ProductSkuSpecDO>> groupedBySpecName = specDOS.stream()
                .collect(Collectors.groupingBy(ProductSkuSpecDO::getSpecName));

        List<BrotherSku> brotherSkus = new ArrayList<>();

        boolean flag = true;
        for (Map.Entry<String, List<ProductSkuSpecDO>> entry : groupedBySpecName.entrySet()) {
            String specName = entry.getKey();
            List<ProductSkuSpecDO> specList = entry.getValue();

            BrotherSku brotherSku = new BrotherSku();
            brotherSku.setSpecName(specName);

            Map<String, List<ProductSkuSpecDO>> groupedBySpecValue = specList.stream()
                    .collect(Collectors.groupingBy(ProductSkuSpecDO::getSpecValue));

            List<BrotherSkuSpecValue> specValues = new ArrayList<>();

            for (Map.Entry<String, List<ProductSkuSpecDO>> valueEntry : groupedBySpecValue.entrySet()) {
                String specValue = valueEntry.getKey();
                List<ProductSkuSpecDO> skuList = valueEntry.getValue();
                BrotherSkuSpecValue brotherSkuSpecValue = new BrotherSkuSpecValue();
                brotherSkuSpecValue.setSpecValue(specValue);
                List<Long> skuIds = skuList.stream()
                        .map(ProductSkuSpecDO::getSkuId)
                        .collect(Collectors.toList());
                brotherSkuSpecValue.setSkuIdList(skuIds);
                brotherSkuSpecValue.setSelected(false);
                if(flag){
                    skuIds.forEach(skuId -> {
                        productSkuDOS.forEach(sku -> {
                            if (sku.getId().equals(skuId)) {
                                brotherSkuSpecValue.setImagePath(sku.getPicUrl());
                            }
                        });
                    });
                }
                else {
                    brotherSkuSpecValue.setImagePath(null);
                }

                brotherSkuSpecValue.setSelected(brotherSkuSpecValue.getSkuIdList().contains(selectSkuId));
                specValues.add(brotherSkuSpecValue);
            }

            brotherSku.setSpecValues(specValues);

            brotherSkus.add(brotherSku);
            flag = false;
        }

        return brotherSkus;
    }

    default List<ProductSkuSpecDO> convertDOList02(List<AppProductSpecValueVO> list, Long skuId) {
        if (list == null || list.size() == 0) {
            return new ArrayList<>();
        }
        List<ProductSkuSpecDO> doList = new ArrayList<>();
        for (AppProductSpecValueVO specValueVO : list) {
            ProductSkuSpecDO skuSpecDO  = convertDO2(specValueVO);
            skuSpecDO.setSpecId(0L);
            skuSpecDO.setSkuId(skuId);
            doList.add(skuSpecDO);
        }
        return doList;
    }

    default List<ProductSpuSpecDO> convertDOList03(List<AppProductSpecValueVO> list, Long spuId) {
        if (list == null || list.size() == 0) {
            return new ArrayList<>();
        }

        List<ProductSpuSpecDO> doList = new ArrayList<>();
        for (AppProductSpecValueVO specValueVO : list) {
            ProductSpuSpecDO spuSpecDO  = convertDO3(specValueVO);
            spuSpecDO.setSpuId(spuId);
            spuSpecDO.setSpecId(0L);
            doList.add(spuSpecDO);
        }
        return doList;
    }

    ProductSkuSpecDO convertD12(ProductSpecValueReqVO bean);

    ProductSpuSpecDO convertD13(ProductSpecValueReqVO bean);

    default List<ProductSkuSpecDO> convertDOList12(List<ProductSpecValueReqVO> list, Long skuId) {
        if (list == null || list.size() == 0) {
            return new ArrayList<>();
        }
        List<ProductSkuSpecDO> doList = new ArrayList<>();
        for (ProductSpecValueReqVO specValueVO : list) {
            ProductSkuSpecDO skuSpecDO  = convertD12(specValueVO);
            if (skuSpecDO.getSpecId() == null) {
                skuSpecDO.setSpecId(0L);
            }
            skuSpecDO.setSkuId(skuId);
            doList.add(skuSpecDO);
        }
        return doList;
    }

    default List<ProductSpuSpecDO> convertDOList13(List<ProductSpecValueReqVO> list, Long spuId) {
        if (list == null || list.size() == 0) {
            return new ArrayList<>();
        }
        List<ProductSpuSpecDO> doList = new ArrayList<>();
        for (ProductSpecValueReqVO specValueVO : list) {
            ProductSpuSpecDO spuSpecDO  = convertD13(specValueVO);
            spuSpecDO.setSpuId(spuId);
            if (specValueVO.getSpecId() == null) {
                specValueVO.setSpecId(0L);
            }
            doList.add(spuSpecDO);
        }
        return doList;
    }

    List<AppProductSpecValueVO> convertVOList03(List<ProductSpuSpecDO> list);

    List<AppProductSpecValueVO> convertVOList04(List<ProductSkuSpecDO> list);

    List<ProductSpecValueRespVO> convertVOList05(List<ProductSkuSpecDO> list);

    List<ProductSpecValueRespVO> convertVOList06(List<ProductSpuSpecDO> list);


}
