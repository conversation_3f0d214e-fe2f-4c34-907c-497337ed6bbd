package cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 商品sku禁售区域 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ProductSkuBlackAreaExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("spu编号")
    private Long spuId;

    @ExcelProperty("商品 SPU 名字")
    private String spuName;

    @ExcelProperty("供应商id")
    private Long supplierId;

    @ExcelProperty("供应商名称")
    private String supplierName;

    @ExcelProperty("开始时间")
    private Date beginTime;

    @ExcelProperty("结束时间")
    private Date endTime;

    @ExcelProperty("所在省份")
    private Long provinceId;

    @ExcelProperty("所在城市")
    private Long cityId;

    @ExcelProperty("所在区县")
    private Long countyId;

    @ExcelProperty("状态 1-启用 0-禁用")
    private Boolean status;

    @ExcelProperty("创建时间")
    private Date createTime;

}
