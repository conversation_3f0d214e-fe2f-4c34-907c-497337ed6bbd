package cn.iocoder.yudao.module.mall.trade.controller.admin.orderoperatelog.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 订单操作日志记录创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderOperateLogCreateReqVO extends OrderOperateLogBaseVO {

}
