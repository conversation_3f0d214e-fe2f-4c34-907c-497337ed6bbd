package cn.iocoder.yudao.module.mall.product.convert.es;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.Stock;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuDetailRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppAppProductSkuOpenRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.SkuGoodsPageItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/8
 */
@Mapper
public interface SkuGoodsPageItemConvert {

    SkuGoodsPageItemConvert INSTANCE = Mappers.getMapper(SkuGoodsPageItemConvert.class);


    /**
     * 商品转换
     *
     * @param skuOpenRespVO
     * @return
     */
    default SkuGoodsPageItem convert(AppAppProductSkuOpenRespVO skuOpenRespVO) {
        return new SkuGoodsPageItem()
                .setSkuId(skuOpenRespVO.getSkuId())
                .setSpuId(skuOpenRespVO.getSpu().getSpuId())
                .setCategoryName3(skuOpenRespVO.getSpu().getCategory3Name())
                .setCategoryName2(skuOpenRespVO.getSpu().getCategory2Name())
                .setCategoryName1(skuOpenRespVO.getSpu().getCategory1Name())
                .setImageUrl(skuOpenRespVO.getSpu().getPicUrl())
                .setSkuName(skuOpenRespVO.getSkuName())
                .setCategoryId1(skuOpenRespVO.getSpu().getCategory1Id())
                .setCategoryId2(skuOpenRespVO.getSpu().getCategory2Id())
                .setCategoryId3(skuOpenRespVO.getSpu().getCategory3Id())
                .setBrandName(skuOpenRespVO.getSpu().getBrandName())
                .setBrandId(skuOpenRespVO.getSpu().getBrandId())
                .setSkuState(1)
                .setSalePrice(skuOpenRespVO.getSalePrice())
                .setSaleStatus(1)
                .setStockStateType(33)
                .setStockStateDesc("有货")
                .setSupplierId(skuOpenRespVO.getSupplierId())
                .setSupplierName(skuOpenRespVO.getSupplierName());
    }


    /**
     * 商品转换
     *
     * @param skuDetail
     * @return
     */
    @Mappings(value = {
            @Mapping(source = "skuDetail.skuId", target = "skuId"),
            @Mapping(source = "skuDetail.category1Id", target = "categoryId1"),
            @Mapping(source = "skuDetail.category2Id", target = "categoryId2"),
            @Mapping(source = "skuDetail.category3Id", target = "categoryId3"),
            @Mapping(source = "skuDetail.category1Name", target = "categoryName1"),
            @Mapping(source = "skuDetail.category2Name", target = "categoryName2"),
            @Mapping(source = "skuDetail.category3Name", target = "categoryName3"),
            @Mapping(source = "skuDetail.skuPicUrl", target = "imageUrl"),
            @Mapping(source = "skuDetail.lowestBuy", target = "lowestBuy"),
            @Mapping(source = "productSkuStock.stockStateType", target = "stockStateType"),
            @Mapping(source = "productSkuStock.stockStateDesc", target = "stockStateDesc")
    })
    SkuGoodsPageItem convert0(AppProductSkuDetailRespVO skuDetail, Stock productSkuStock);


    /**
     * 商品集合转换
     *
     * @param skuDetails
     * @return
     */
    default List<SkuGoodsPageItem> convertList(List<AppProductSkuDetailRespVO> skuDetails, List<Stock> productSkuStocks) {
        if (skuDetails == null) {
            return null;
        }
        List<SkuGoodsPageItem> list = new ArrayList<SkuGoodsPageItem>(skuDetails.size());
        for (AppProductSkuDetailRespVO productSkuDetailRespVO : skuDetails) {
            Map<Long, Stock> stockMap = CollectionUtils.convertMap(productSkuStocks, Stock::getSkuId);
            list.add(convert0(productSkuDetailRespVO, stockMap.get(productSkuDetailRespVO.getSkuId())));
        }

        return list;
    }


}
