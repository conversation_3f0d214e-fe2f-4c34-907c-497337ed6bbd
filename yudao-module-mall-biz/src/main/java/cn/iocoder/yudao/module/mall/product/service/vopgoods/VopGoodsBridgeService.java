package cn.iocoder.yudao.module.mall.product.service.vopgoods;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.productrule.ProductRuleDO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.basisconfig.BasisConfigService;
import cn.iocoder.yudao.module.mall.basis.service.productrule.ProductRuleService;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.external.open.context.OpenContextHolder;
import cn.iocoder.yudao.module.mall.mq.producer.product.ProductSkuProducer;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuOpenVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spec.vo.AppProductSpecValueVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuOpenVO;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.params.GoodsStockInfoReq;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.params.VopStockByIdGoodsReq;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.AreaStockInfoResp;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsDetailResp;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsSearchPageResultResp;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopSkuGoodsPageItem;
import cn.iocoder.yudao.module.mall.product.convert.vopgoods.VopGoodsConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skustock.ProductSkuStockDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopCategoryMappingDO;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryLevelEnum;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuSpecTypeEnum;
import cn.iocoder.yudao.module.mall.product.service.category.ConfigProductCategoryService;
import cn.iocoder.yudao.module.mall.product.service.category.ProductCategoryService;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.skustock.ProductSkuStockService;
import cn.iocoder.yudao.module.mall.product.service.spu.ProductSpuService;
import cn.iocoder.yudao.module.mall.product.service.vopcategorymapping.VopCategoryMappingService;
import cn.iocoder.yudao.module.mall.trade.service.cart.TradeCartService;
import cn.iocoder.yudao.module.mall.trade.service.cart.TradeCollectService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsStockService;
import cn.iocoder.yudao.module.mall.vop.controller.admin.vopskucategory.vo.VopSkuCategoryCreateReqVO;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopAccessTokenDO;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopConfigDO;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopSkuCategoryDO;
import cn.iocoder.yudao.module.mall.vop.dto.SkuPriceRespDTO;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsGetSkuDetailReq;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsQueryAreaStockStatesReq;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsSearchReq;
import cn.iocoder.yudao.module.mall.vop.service.VopAccessTokenService;
import cn.iocoder.yudao.module.mall.vop.service.VopConfigService;
import cn.iocoder.yudao.module.mall.vop.service.VopSkuCategoryService;
import cn.iocoder.yudao.module.mall.vop.service.VopSkuPoolService;
import com.alibaba.ttl.TtlRunnable;
import com.alibaba.ttl.TtlWrappers;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.request.getNewStockById.AreaBaseInfoGoodsReq;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.request.getNewStockById.GetStockByIdGoodsReq;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.request.getNewStockById.SkuNumBaseGoodsReq;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.response.getNewStockById.GetStockByIdGoodsResp;
import com.jd.open.api.sdk.domain.vopkc.SkuStockGoodsProvider.response.queryAreaStockStates.QueryAreaStockConditionResp;
import com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getCategoryInfoList.GetCategoryInfoGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SearchGoodsProvider.response.searchSku.CategorySearchAggGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SearchGoodsProvider.response.searchSku.OpenPagingResult;
import com.jd.open.api.sdk.domain.vopsp.SearchGoodsProvider.response.searchSku.SearchSkuGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SearchGoodsProvider.response.searchSku.SkuHitSearchGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.checkSkuSaleList.CheckSkuSaleGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSellPrice.GetSellPriceGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSimilarSkuList.GetSimilarSkuGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.GetSkuPoolInfoGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.ParamAttributeResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.ParamGroupAttributeGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuImageList.SkuImageItemGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuStateList.GetSkuStateGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuPoolGoodsProvider.response.getSkuPoolInfo.GetSkuPoolInfoItemGoodsResp;
import com.jd.open.api.sdk.response.vopkc.VopGoodsGetNewStockByIdResponse;
import com.jd.open.api.sdk.response.vopkc.VopGoodsQueryAreaStockStatesResponse;
import com.jd.open.api.sdk.response.vopsp.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.FutureTask;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.yudao.module.mall.basis.dal.redis.RedisKeyConstants.MALL_GOODS_POOL_SYNC;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/6 20:52
 */
@Service
@Slf4j
public class VopGoodsBridgeService {

    public static final Long DEFAULT_PROVINCE_ID = 17L;
    public static final Long DEFAULT_CITY_ID = 1381L;
    public static final Long DEFAULT_COUNTY_ID = 50718L;
    public static final Long DEFAULT_TOWN_ID = 53768L;

    @Resource
    VopGoodsService vopGoodsService;
    @Resource
    VopGoodsStockService vopGoodsStockService;
    @Resource
    ProductCategoryService productCategoryService;
    @Resource
    ConfigProductCategoryService configProductCategoryService;
    @Resource
    ThreadPoolExecutor threadPoolExecutor;
    @Resource
    VopGoodsCategoryService vopGoodsCategoryService;
    @Resource
    @Lazy
    ProductSkuService productSkuService;
    @Resource
    @Lazy
    ProductSpuService productSpuService;
    @Resource
    private VopAccessTokenService vopAccessTokenService;
    @Resource
    private RedissonClient redissonclient;
    @Resource
    @Lazy
    private ProductSkuProducer productSkuProducer;
    @Resource
    private VopSkuPoolService vopSkuPoolService;
    @Resource
    @Lazy
    private BasisConfigService basisConfigService;
    @Resource
    private VopConfigService vopConfigService;
    @Resource
    private ProductRuleService productRuleService;
    @Lazy
    @Resource
    private VopSkuCategoryService vopSkuCategoryService;
    @Lazy
    @Resource
    private TradeCartService tradeCartService;
    @Lazy
    @Resource
    private TradeOrderService tradeOrderService;
    @Lazy
    @Resource
    private TradeCollectService tradeCollectService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private ProductSkuStockService productSkuStockService;
    @Resource
    private VopCategoryMappingService vopCategoryMappingService;

    @Value("${jd.image-path:https://img13.360buyimg.com/n12/}")
    private String jdImagePath;

    /** 
    * @Description: 拼接京东图片链接
    * @Param: [imgUrl]
    * @return: java.lang.String
    * @Author: lujun
    * @Date: 2023/11/3 11:15
    */
    private String convertJDImage(String imgUrl){
        return String.format("%s%s", jdImagePath, imgUrl);
    }

    /**
    * @Description: 查询商品池内所有商品编号
    * @Param: [poolId]
    * @return: java.util.List<java.lang.Long>
    * @Author: lujun
    * @Date: 2023/12/7 17:19
    */
    public List<Long> getSkuIdListByPoolId(String poolId) {
        List<Long> skuIdList = new ArrayList<>();
        Long offset = 0L;
        int pageSize = 1000;
        boolean hasNextPage = true;
        try {
            while (hasNextPage) {
                VopGoodsQuerySkuByPageResponse vopGoodsQuerySkuByPageResponse = vopGoodsService.querySku(poolId, offset, pageSize);
                if (vopGoodsQuerySkuByPageResponse.getOpenRpcResult().getResultCode().compareTo("0000") != 0) {
                    log.error("查询池{}, {}, {}内商品编号失败：{}", poolId, offset, pageSize, vopGoodsQuerySkuByPageResponse.getMsg());
                    break;
                }
                if (vopGoodsQuerySkuByPageResponse.getOpenRpcResult().getResult() == null
                        || vopGoodsQuerySkuByPageResponse.getOpenRpcResult().getResult().getRemainPage() == null) {
                    log.info("查询池{}, {}, {}内商品查询完成：{}", poolId, offset, pageSize, vopGoodsQuerySkuByPageResponse.getMsg());
                    break;
                }
                skuIdList.addAll(vopGoodsQuerySkuByPageResponse.getOpenRpcResult().getResult().getSkus());
                offset = vopGoodsQuerySkuByPageResponse.getOpenRpcResult().getResult().getOffset();
                hasNextPage = vopGoodsQuerySkuByPageResponse.getOpenRpcResult().getResult().getRemainPage() <= 0;
            }
        } catch(Exception e) {
            log.error("getSkuIdListByPoolId error: ", e);
        }

        return skuIdList;
    }

    /**
     * 针对查询京东商品返回不在商品池的场景，同时检查并将本地商品下架或删除处理
     * @param jdSkuId
     * @param errorMsg
     * @param supplierId
     */
    private void handleInvalidProduct(Long jdSkuId, String errorMsg, Long supplierId) {
        if(StringUtils.contains(errorMsg, "不在用户商品池") && jdSkuId != null) {
            threadPoolExecutor.execute(TtlRunnable.get(() -> {
                productSkuService.updateOffJdSku(jdSkuId.toString(), supplierId);
            }));
        }
    }

    /**
     * 查询商品SPU
     * @param spuInnerId
     * @param supplierId
     */
    private ProductSpuDO getProductSpu(String spuInnerId, Long supplierId) {
        return productSpuService.getSpuCategoryByInnerIdAndSupplierId(spuInnerId, supplierId);
    }

    /**
     * 查询spuInnerId并填充分类ID
     * @param productSpuOpenVO
     * @param spuDO
     */
    private boolean handleProductCategoryBySpu(ProductSpuOpenVO productSpuOpenVO, ProductSpuDO spuDO) {
        if(spuDO != null) {
            productSpuOpenVO.setCategory3Id(spuDO.getCategory3Id());
            productSpuOpenVO.setCategory3Name(spuDO.getCategory3Name());
            productSpuOpenVO.setCategory2Id(spuDO.getCategory2Id());
            productSpuOpenVO.setCategory2Name(spuDO.getCategory2Name());
            productSpuOpenVO.setCategory1Id(spuDO.getCategory1Id());
            productSpuOpenVO.setCategory1Name(spuDO.getCategory1Name());

            return true;
        }
        return false;
    }

    /**
    * @Description: 同步商品信息
    * @Param: [jdSkuId]
    * @Param: [supplierId]
    * @Param: [poolName]
    * @return: ProductSpuOpenVO
    * @Author: lujun
    * @Date: 2023/12/7 17:19
    */
    public ProductSpuOpenVO goodsDetailSync(Long jdSkuId, Long supplierId, String poolName, Long categoryId) {
        VopConfigDO vopConfig = vopConfigService.getVopConfigRequired();
        // 校验商品分类，单供应商模式，可以不用强制匹配本地商品分类；多供应商模式时需要强制匹配；
        ProductCategoryDO productCategoryDO = null;
        if(!vopConfig.getFullPoolSwitch()){
            if (vopConfig.getPoolNameValidate() != null && vopConfig.getPoolNameValidate()) {
                if (StringUtils.isNotBlank(poolName)) {
                    categoryId = productCategoryService.getCategoryIdByPoolName(poolName);
                    if(categoryId == null){
                        log.error("供应商{}，商品{}分类解析失败", supplierId, jdSkuId);
                    }
                }
            }
        }

        productCategoryDO = productCategoryService.getByCategoryId(categoryId);
        if(productCategoryDO == null){
            log.info("供应商{}，商品{}分类查不到，商品分类：{}", supplierId, jdSkuId, categoryId);
            return null;
        }

        VopGoodsGetSkuStateListResponse vopGoodsGetSkuStateListResponse = vopGoodsService.getSkuStateList(String.valueOf(jdSkuId));
        if(!vopGoodsGetSkuStateListResponse.getOpenRpcResult().getSuccess() || CollUtil.isEmpty(vopGoodsGetSkuStateListResponse.getOpenRpcResult().getResult())) {
            log.error("获取商品{}状态失败：{}", jdSkuId, vopGoodsGetSkuStateListResponse.getMsg());
            return null;
        }
        GetSkuStateGoodsResp getSkuStateGoodsResp = vopGoodsGetSkuStateListResponse.getOpenRpcResult().getResult().get(0);
        if(getSkuStateGoodsResp.getSkuState() == 0){
            log.info("商品{}已下架", jdSkuId);
            handleInvalidProduct(jdSkuId, "不在用户商品池", supplierId);
            return null;
        }

        //获取商品详情
        VopGoodsGetSkuDetailInfoResponse vopGoodsGetSkuDetailInfoResponse = vopGoodsService.getSkuDetail(jdSkuId, "1,2,3,4,5");
        if(vopGoodsGetSkuDetailInfoResponse.getOpenRpcResult().getResultCode().compareTo("0000") != 0){
            log.error("获取商品{}详情失败：{}", jdSkuId, vopGoodsGetSkuDetailInfoResponse.getMsg());
            handleInvalidProduct(jdSkuId, vopGoodsGetSkuDetailInfoResponse.getMsg(), supplierId);
            return null;
        }
        GetSkuPoolInfoGoodsResp skuDetail = vopGoodsGetSkuDetailInfoResponse.getOpenRpcResult().getResult();
        if(skuDetail == null || skuDetail.getSpuId() == null) {
            log.error("获取商品{}详情失败：{}", jdSkuId, vopGoodsGetSkuDetailInfoResponse.getMsg());
            handleInvalidProduct(jdSkuId, vopGoodsGetSkuDetailInfoResponse.getMsg(), supplierId);
            return null;
        }

        //获取商品价格，因为批量获取价格接口有问题，只能一个一个获取
        VopGoodsGetSellPriceResponse vopGoodsGetSellPriceResponse = vopGoodsService.getSellPrice(jdSkuId.toString(), null);
        if(vopGoodsGetSellPriceResponse.getOpenRpcResult().getResultCode().compareTo("0000") != 0){
            //打印错误，单个商品可能出现不在商品池的错误，这是京东接口导致的
            log.info("获取商品{}价格失败：{}", jdSkuId, vopGoodsGetSellPriceResponse.getMsg());
            handleInvalidProduct(jdSkuId, vopGoodsGetSellPriceResponse.getMsg(), supplierId);
            return null;
        }
        if(vopGoodsGetSellPriceResponse.getOpenRpcResult() == null
                || vopGoodsGetSellPriceResponse.getOpenRpcResult().getResult() == null
                || vopGoodsGetSellPriceResponse.getOpenRpcResult().getResult().isEmpty()
                || vopGoodsGetSellPriceResponse.getOpenRpcResult().getResult().get(0) == null
                || vopGoodsGetSellPriceResponse.getOpenRpcResult().getResult().get(0).getSalePrice() == null
                || (vopGoodsGetSellPriceResponse.getOpenRpcResult().getResult().get(0).getJdPrice() == null
                && vopGoodsGetSellPriceResponse.getOpenRpcResult().getResult().get(0).getMarketPrice() == null)){
            log.error("获取商品{}价格结果异常：{}", jdSkuId, vopGoodsGetSellPriceResponse.getMsg());
            handleInvalidProduct(jdSkuId, vopGoodsGetSellPriceResponse.getMsg(), supplierId);
            return null;
        }
        GetSellPriceGoodsResp getSellPriceGoodsResp = vopGoodsGetSellPriceResponse.getOpenRpcResult().getResult().get(0);

        List<String> sliderPicUrls = new ArrayList<>();
        String primaryPicUrl = null;

        boolean syncImageFlag = false;
        //查询商品图片
        if(syncImageFlag){
            VopGoodsGetSkuImageListResponse vopGoodsGetSkuImageListResponse = vopGoodsService.getSkuImageList(jdSkuId.toString());
            if(vopGoodsGetSkuImageListResponse.getOpenRpcResult().getResultCode().compareTo("0000") != 0){
                log.error("获取商品{}图片失败：{}", jdSkuId, vopGoodsGetSkuImageListResponse.getMsg());
                return null;
            }
            if(vopGoodsGetSkuImageListResponse.getOpenRpcResult() == null
                    || vopGoodsGetSkuImageListResponse.getOpenRpcResult().getResult() == null
                    || vopGoodsGetSkuImageListResponse.getOpenRpcResult().getResult().isEmpty()
                    || vopGoodsGetSkuImageListResponse.getOpenRpcResult().getResult().get(0) == null
                    || vopGoodsGetSkuImageListResponse.getOpenRpcResult().getResult().get(0).getSkuImageList() == null
                    || vopGoodsGetSkuImageListResponse.getOpenRpcResult().getResult().get(0).getSkuImageList().isEmpty()){
                log.error("获取商品{}图片结果异常：{}", jdSkuId, vopGoodsGetSkuImageListResponse.getMsg());
                //京东商品图片为空也可以下单
            }

            if(vopGoodsGetSkuImageListResponse.getOpenRpcResult() != null
                    && vopGoodsGetSkuImageListResponse.getOpenRpcResult().getResult() != null
                    && !vopGoodsGetSkuImageListResponse.getOpenRpcResult().getResult().isEmpty()
                    && vopGoodsGetSkuImageListResponse.getOpenRpcResult().getResult().get(0).getSkuImageList() != null
                    && !vopGoodsGetSkuImageListResponse.getOpenRpcResult().getResult().get(0).getSkuImageList().isEmpty()){
                List<SkuImageItemGoodsResp> skuImageList = vopGoodsGetSkuImageListResponse.getOpenRpcResult().getResult().get(0).getSkuImageList();
                for (SkuImageItemGoodsResp skuImageItemGoodsResp : skuImageList) {
                    if(skuImageItemGoodsResp.getIsPrimary() == 1){
                        primaryPicUrl = skuImageItemGoodsResp.getShortPath();
                    }
                    sliderPicUrls.add(convertJDImage(skuImageItemGoodsResp.getShortPath()));
                }
            }
        }

        //商品分类
        String categoryIdsStr = skuDetail.getCategory().replaceAll(";", ",");
        VopGoodsGetCategoryInfoListResponse vopGoodsGetCategoryInfoListResponse = vopGoodsService.getCategoryInfoList(categoryIdsStr);
        if(vopGoodsGetCategoryInfoListResponse.getOpenRpcResult().getResultCode().compareTo("0000") != 0){
            log.error("获取商品{}分类失败：{}", jdSkuId, vopGoodsGetCategoryInfoListResponse.getMsg());
            return null;
        }
        List<GetCategoryInfoGoodsResp> categoryInfoGoodsRespList = vopGoodsGetCategoryInfoListResponse.getOpenRpcResult().getResult();

        //商品规格
        List<AppProductSpecValueVO> appProductSpecValueVOList = new ArrayList<>();
        if(skuDetail.getParamGroupAttrList() != null) {
            for (ParamGroupAttributeGoodsResp paramGroupAttributeGoodsResp : skuDetail.getParamGroupAttrList()) {
                for (ParamAttributeResp paramAttributeResp : paramGroupAttributeGoodsResp.getParamAttributeList()) {
                    AppProductSpecValueVO appProductSpecValueVO = new AppProductSpecValueVO();
                    appProductSpecValueVO.setSpecId(0L);
                    appProductSpecValueVO.setSpecName(paramAttributeResp.getParamAttrName());
                    StringBuilder specValue = new StringBuilder();
                    for (String[] strings : paramAttributeResp.getParamAttrValList()) {
                        for (String string : strings) {
                            if(specValue.length() > 0){
                                specValue.append(";");
                            }
                            specValue.append(string);
                        }
                    }
                    appProductSpecValueVO.setSpecValue(specValue.toString());
                    appProductSpecValueVOList.add(appProductSpecValueVO);
                }
            }
        }

        List<AppProductSpecValueVO> skuSpecValueList = new ArrayList<>();
        VopGoodsGetSimilarSkuListResponse vopGoodsGetSimilarSkuListResponse = vopGoodsService.getSimilarSkuList(jdSkuId);
        if(!vopGoodsGetSimilarSkuListResponse.getOpenRpcResult().getSuccess()){
            log.error("获取相似商品失败：{}", jdSkuId, vopGoodsGetSimilarSkuListResponse.getMsg());
            return null;
        }
        List<GetSimilarSkuGoodsResp> similarSkuList = vopGoodsGetSimilarSkuListResponse.getOpenRpcResult().getResult();
        if(CollUtil.isNotEmpty(similarSkuList)){
            for (GetSimilarSkuGoodsResp item : similarSkuList) {
                boolean flag = false;
                for (String labelName : item.getSaleLabelMap().keySet()) {
                    Set<Long> skuIdSet = item.getSaleLabelMap().get(labelName).getSkuIdSet();
                    if(skuIdSet.contains(jdSkuId)){
                        AppProductSpecValueVO appProductSpecValueVO = new AppProductSpecValueVO();
                        appProductSpecValueVO.setSpecName(item.getSaleName());
                        appProductSpecValueVO.setSpecValue(labelName);
                        skuSpecValueList.add(appProductSpecValueVO);
                        break;
                    }
                }
            }
        }

        //组装sku list
        List<AppProductSkuOpenVO> appProductSkuOpenVOList = new ArrayList<>();
        AppProductSkuOpenVO appProductSkuOpenVO = new AppProductSkuOpenVO();
        appProductSkuOpenVO.setSkuInnerId(String.valueOf(skuDetail.getSkuId()));
        if(getSellPriceGoodsResp != null){
            if(getSellPriceGoodsResp.getJdPrice() != null){
                appProductSkuOpenVO.setMarketPrice(getSellPriceGoodsResp.getJdPrice());
            }
            else if(getSellPriceGoodsResp.getMarketPrice() != null){
                appProductSkuOpenVO.setMarketPrice(getSellPriceGoodsResp.getMarketPrice());
            }
            if(getSellPriceGoodsResp.getSalePrice() != null){
                appProductSkuOpenVO.setSalePrice(getSellPriceGoodsResp.getSalePrice());
            }
        }
        appProductSkuOpenVO.setPicUrl(convertJDImage(skuDetail.getImagePath()));
        appProductSkuOpenVO.setSkuName(skuDetail.getSkuName());
        Integer lowestBuy = 1;
        if(skuDetail.getLowestBuy() != null){
            lowestBuy = skuDetail.getLowestBuy();
        }
        appProductSkuOpenVO.setLowestBuy(lowestBuy);
        if(CollUtil.isNotEmpty(skuSpecValueList)){
            appProductSkuOpenVO.setSkuSpecValueList(skuSpecValueList);
        }

        // 上下架状态
        appProductSkuOpenVO.setStatus(getSkuStateGoodsResp.getSkuState());

        // 京东是单规格商品，规格只在spu里存储
        appProductSkuOpenVOList.add(appProductSkuOpenVO);

        //组装spu
        ProductSpuOpenVO productSpuOpenVO = new ProductSpuOpenVO();
        productSpuOpenVO.setSpuInnerId(String.valueOf(skuDetail.getSpuId()));
        productSpuOpenVO.setBrandName(skuDetail.getBrandName());
        productSpuOpenVO.setSpuName(skuDetail.getSpuName());
        productSpuOpenVO.setUnit(skuDetail.getSaleUnit());
        productSpuOpenVO.setSpecType(ProductSpuSpecTypeEnum.Single.getType());
        productSpuOpenVO.setSpuSpecValueList(appProductSpecValueVOList);

        //自建商品分类
        if(productCategoryDO != null) {
            // 判断当前分类层级为3级时
            if(ObjectUtil.equal(productCategoryDO.getCategoryLevel(), ProductCategoryLevelEnum.LAVEL3.getLevel())) {
                productSpuOpenVO.setCategory3Id(productCategoryDO.getCategoryId());
                productSpuOpenVO.setCategory3Name(productCategoryDO.getCategoryName());

                // 查找二级类目
                ProductCategoryDO productCategory2DO = productCategoryService.getByCategoryId(productCategoryDO.getParentId());
                productSpuOpenVO.setCategory2Id(productCategory2DO.getCategoryId());
                productSpuOpenVO.setCategory2Name(productCategory2DO.getCategoryName());

                // 查找一级类目
                ProductCategoryDO productCategory1DO = productCategoryService.getByCategoryId(productCategory2DO.getParentId());
                productSpuOpenVO.setCategory1Id(productCategory1DO.getCategoryId());
                productSpuOpenVO.setCategory1Name(productCategory1DO.getCategoryName());
            } else if(ObjectUtil.equal(productCategoryDO.getCategoryLevel(), ProductCategoryLevelEnum.LAVEL2.getLevel())) {
                // 判断当前分类层级为2级时
                productSpuOpenVO.setCategory2Id(productCategoryDO.getCategoryId());
                productSpuOpenVO.setCategory2Name(productCategoryDO.getCategoryName());

                ProductCategoryDO productCategory1DO = productCategoryService.getByCategoryId(productCategoryDO.getParentId());
                productSpuOpenVO.setCategory1Id(productCategory1DO.getCategoryId());
                productSpuOpenVO.setCategory1Name(productCategory1DO.getCategoryName());
            } else if(ObjectUtil.equal(productCategoryDO.getCategoryLevel(), ProductCategoryLevelEnum.LAVEL1.getLevel())) {
                // 判断当前分类层级为1级时
                productSpuOpenVO.setCategory1Id(productCategoryDO.getCategoryId());
                productSpuOpenVO.setCategory1Name(productCategoryDO.getCategoryName());
            }
        } else {
            //京东商品分类
            for (GetCategoryInfoGoodsResp categoryInfoGoodsResp : categoryInfoGoodsRespList) {
                if(categoryInfoGoodsResp.getCategoryLevel() == 0) {
                    productSpuOpenVO.setCategory1Id(categoryInfoGoodsResp.getCategoryId());
                    productSpuOpenVO.setCategory1Name(categoryInfoGoodsResp.getCategoryName());
                }
                else if(categoryInfoGoodsResp.getCategoryLevel() == 1) {
                    productSpuOpenVO.setCategory2Id(categoryInfoGoodsResp.getCategoryId());
                    productSpuOpenVO.setCategory2Name(categoryInfoGoodsResp.getCategoryName());
                }
                else if(categoryInfoGoodsResp.getCategoryLevel() == 2) {
                    productSpuOpenVO.setCategory3Id(categoryInfoGoodsResp.getCategoryId());
                    productSpuOpenVO.setCategory3Name(categoryInfoGoodsResp.getCategoryName());
                }
            }
        }

        List<Long> categoryIds = new ArrayList<>();
        if (productSpuOpenVO.getCategory1Id() != null) {
            categoryIds.add(productSpuOpenVO.getCategory1Id());
        }
        if (productSpuOpenVO.getCategory2Id() != null) {
            categoryIds.add(productSpuOpenVO.getCategory2Id());
        }
        if (productSpuOpenVO.getCategory3Id() != null) {
            categoryIds.add(productSpuOpenVO.getCategory3Id());
        }
        productSpuOpenVO.setFullCategoryId(StringUtils.join(categoryIds, "-"));
        String fullNames = productCategoryService.getNamePath(categoryIds, "/");
        productSpuOpenVO.setFullCategoryName(fullNames);

        if(primaryPicUrl != null){
            productSpuOpenVO.setPicUrl(convertJDImage(primaryPicUrl));
        } else if(skuDetail.getImagePath() != null){
            productSpuOpenVO.setPicUrl(convertJDImage(skuDetail.getImagePath()));
        }
        if(!sliderPicUrls.isEmpty()){
            productSpuOpenVO.setSliderPicUrls(sliderPicUrls);
        }
        productSpuOpenVO.setSkus(appProductSkuOpenVOList);
        return productSpuOpenVO;
    }

    /**
     * 统计商品池数量
     * @param bizPoolIds 商品池id字符串，多个id由逗号拼接，如果为空，则统计所有商品池
     * @return
     */
    public Long goodsPoolSyncCount(String bizPoolIds){
        List<String> bizPoolIdList = new ArrayList<>();
        if(bizPoolIds != null && !bizPoolIds.equals("")){
            bizPoolIdList.addAll(Arrays.stream(bizPoolIds.split(",")).collect(Collectors.toList()));
        }
        if(!vopAccessTokenService.isTenantVopAccessTokenOk()) {
            log.info("当前租户VOP未打开或配置未生效：{}", TenantContextHolder.getTenantId());
            return 0L;
        }
        Long tenantId = TenantContextHolder.getTenantId();
        AtomicLong count = new AtomicLong(0L);
        //查询商品池编号
        VopGoodsGetSkuPoolInfoResponse vopGoodsGetSkuPoolInfoResponse = vopGoodsService.getSkuPool();
        if(vopGoodsGetSkuPoolInfoResponse.getOpenRpcResult().getResultCode().compareTo("0000") != 0) {
            log.info("查询商品池数量失败，{}", vopGoodsGetSkuPoolInfoResponse);
            return 0L;
        }
        List<GetSkuPoolInfoItemGoodsResp> skuPoolInfoRespList = vopGoodsGetSkuPoolInfoResponse.getOpenRpcResult().getResult().getSkuPoolList();

        //查询池内商品列表
        try {
            int threads = 6;
            for(int i = 0 ; i < skuPoolInfoRespList.size() ;) {
                List<CompletableFuture<Integer>> futures = new ArrayList<>();
                for(int k = 0 ; k < threads && i < skuPoolInfoRespList.size() ; k++) {
                    GetSkuPoolInfoItemGoodsResp skuPool = skuPoolInfoRespList.get(i++);
                    log.info("商品池id：{}", skuPool.getBizPoolId());
                    if(!bizPoolIdList.isEmpty() && !bizPoolIdList.contains(skuPool.getBizPoolId())){
                        continue;
                    }
                    // TtlWrappers 包装后会自动传递线程变量
                    futures.add(CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
                        TenantContextHolder.setTenantId(tenantId);
                        log.info("商品池id：{} 获取商品sku列表开始", skuPool.getBizPoolId());
                        List<Long> skuIdList = this.getSkuIdListByPoolId(skuPool.getBizPoolId());
                        log.info("商品池id：{} 获取商品sku列表结束， 商品数量：{}", skuPool.getBizPoolId(), skuIdList.size());
                        count.addAndGet(skuIdList.size());
                        return 1;
                    })));
                }
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{})).get();
            }
        } catch(Exception e) {
            log.error("查询商品池数量异常: ", e);
        }

        log.info("pool sku count：{}", count.get());
        return count.get();
    }

    private void buildContext4Sync() {
        SupplierDO supplierDO = supplierService.getSupplierJD();
        OpenContextHolder.setSupplierId(supplierDO.getId());
        OpenContextHolder.setSupplierName(supplierDO.getName());
    }

    private List<ProductSpuOpenVO> distinct(List<ProductSpuOpenVO> spuList) {
        if(CollUtil.isNotEmpty(spuList)) {
            return spuList.stream().collect(
                    Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ProductSpuOpenVO::getSpuInnerId))),
                            ArrayList::new));
        }
        return null;
    }

    /**
     * 京东定制商品池同步，同步池内所有商品，支持增量同步，此接口比较耗时，因此改为异步调用
     * @param tenantId 租户ID
     * @param isAsync 是否异步
     */
    public void customGoodsPoolSync(Long tenantId, boolean isAsync) {
        TenantContextHolder.setTenantId(tenantId);
        if(isAsync) {
            threadPoolExecutor.execute(TtlRunnable.get(() -> {
                customGoodsPoolSync(tenantId);
            }));
        } else {
            customGoodsPoolSync(tenantId);
        }
    }

    /**
     * @Description: 京东定制商品池同步，同步池内所有商品，支持增量同步，此接口比较耗时，因此改为异步调用
     * @Author: lujun
     * @Date: 2023/10/31 19:14
     */
    public void customGoodsPoolSync(Long tenantId) {
        log.info("goodsPoolSync start");
        TenantContextHolder.setTenantId(tenantId);
        long start = System.currentTimeMillis();
        if(!vopAccessTokenService.isTenantVopAccessTokenOk()) {
            log.info("当前租户VOP未打开或配置未生效：{}", TenantContextHolder.getTenantId());
            return;
        }
        SupplierDO supplierDO = supplierService.getSupplierJD();
        OpenContextHolder.setSupplierId(supplierDO.getId());
        String lockKey = MALL_GOODS_POOL_SYNC + ":synclock:" + tenantId;
        RLock lock = redissonclient.getLock(lockKey);
        try {
            if(lock.tryLock()) {
                long startTime = System.currentTimeMillis();
                List<String> existSkuIdStringList = productSkuService.getInnerIdsBySupplierId(supplierDO.getId());
                List<Long> existSkuIdList  = existSkuIdStringList.stream().map(Long::valueOf).collect(Collectors.toList());

                //查询商品池编号
                VopGoodsGetSkuPoolInfoResponse vopGoodsGetSkuPoolInfoResponse = vopGoodsService.getSkuPool();
                if(vopGoodsGetSkuPoolInfoResponse.getOpenRpcResult().getResultCode().compareTo("0000") != 0) {
                    log.error("查询VOP商品池编号失败：{}", vopGoodsGetSkuPoolInfoResponse.getMsg());
                    return;
                }
                List<GetSkuPoolInfoItemGoodsResp> skuPoolInfoRespList = vopGoodsGetSkuPoolInfoResponse.getOpenRpcResult().getResult().getSkuPoolList();

                // 构建上下文
                buildContext4Sync();
                //查询池内商品列表
                long goodsTotal = 0;
                for (int i = 0; i < skuPoolInfoRespList.size(); i++) {
                    GetSkuPoolInfoItemGoodsResp skuPool = skuPoolInfoRespList.get(i);
                    String bizPoolId = skuPool.getBizPoolId();
                    String bizPoolName = skuPool.getName();
                    List<Long> skuIdList = this.getSkuIdListByPoolId(bizPoolId);
                    log.info("goodsPoolSync pool name：{} pool id：{} sku size：{} start sync", skuPool.getName(), bizPoolId, skuIdList.size());
                    skuIdList.removeAll(existSkuIdList);
                    List<String> skuPools = new ArrayList<>();
                    int skuSaveBatchCount = 20;
                    List<ProductSpuOpenVO> productSpuOpenVOArrayList = new ArrayList<>();
                    goodsTotal += skuIdList.size();
                    int threads = 3;
                    for (int j = 0; j < skuIdList.size();) {
                        List<CompletableFuture<?>> futures = new ArrayList<>();
                        for(int k = 0 ; k < threads && j < skuIdList.size() ; k++) {
                            Long jdSkuId = skuIdList.get(j++);
                            log.info("goodsPoolSync sku {} start sync, {}/{}", jdSkuId, j + k, skuIdList.size());
                            // TtlWrappers 包装后会自动传递线程变量
                            futures.add(CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
                                try {
                                    Long categoryId = null;
                                    if(vopConfigService.getVopConfig().getFullPoolSwitch()){
                                        // 全量商品池
                                        VopSkuCategoryDO vopSkuCategoryDO = vopSkuCategoryService.getVopSkuCategoryByJdSkuId(String.valueOf(jdSkuId));
                                        if(vopSkuCategoryDO == null){
                                            return false;
                                        }
                                        categoryId = vopSkuCategoryDO.getCategoryId();
                                    }
                                    ProductSpuOpenVO productSpuOpenVO = this.goodsDetailSync(jdSkuId, OpenContextHolder.getSupplierId(), skuPool.getName(), categoryId);
                                    if(productSpuOpenVO != null) {
                                        for(AppProductSkuOpenVO sku : productSpuOpenVO.getSkus()){
                                            productSkuService.validProductRule(sku.getSkuInnerId(), productSpuOpenVO.getFullCategoryId(), sku.getSalePrice(),
                                                    Arrays.asList(productSpuOpenVO.getSpuName(), sku.getSkuName()), sku.getStatus());
                                        }
                                        productSpuOpenVOArrayList.add(productSpuOpenVO);
                                        skuPools.add(String.valueOf(jdSkuId));
                                        log.info("goodsDetailSync is not null:{}", jdSkuId);
                                    }
                                    else {
                                        log.info("goodsDetailSync is null:{}", jdSkuId);
                                    }
                                    return true;
                                } catch (Exception e) {
                                   log.error("同步VOP商品异常, sku:{}, tenantId:{}", jdSkuId, tenantId, e);
                                }
                                return false;
                            })));
                        }

                        CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{})).get();
                        if(j % 100 == 0) {
                            log.info("vopGoodsSync pool:{}, sku progress: {}, time total: {}", bizPoolId, j, System.currentTimeMillis() - startTime);
                        }
                        // 批量保存
                        if (productSpuOpenVOArrayList.size() > skuSaveBatchCount) {
                            try {
                                productSkuService.addProductSku(preHandleProductSpu(productSpuOpenVOArrayList), OpenContextHolder.getSupplierId());
                                if(!vopConfigService.getVopConfig().getFullPoolSwitch()){
                                    vopSkuPoolService.batchSaveSkuPool(skuPools, bizPoolId, bizPoolName);
                                }
                            } catch(Exception e) {
                                log.error("商品批量保存异常跳过, j: {}, size: {}, poolId: {}", j, productSpuOpenVOArrayList.size(), bizPoolId, e);
                            }
                            productSpuOpenVOArrayList.clear();
                            skuPools.clear();
                        }
                    }

                    if(!productSpuOpenVOArrayList.isEmpty()) {
                        try {
                            productSkuService.addProductSku(preHandleProductSpu(productSpuOpenVOArrayList), OpenContextHolder.getSupplierId());
                            if(!vopConfigService.getVopConfig().getFullPoolSwitch()){
                                vopSkuPoolService.batchSaveSkuPool(skuPools, bizPoolId, bizPoolName);
                            }
                        } catch(Exception e) {
                            log.error("商品批量保存异常跳过, size: {}, poolId: {}", productSpuOpenVOArrayList.size(), bizPoolId, e);
                        }
                        productSpuOpenVOArrayList.clear();
                        skuPools.clear();
                    }

                    log.info("goodsPoolSync total: {}", goodsTotal);
                }
            } else {
                log.error("租户{}的VOP全量同步还未结束，请稍后再试", tenantId);
            }
        } catch(Exception e) {
            log.error("VOP商品同步异常", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            OpenContextHolder.clear();
        }

        long end = System.currentTimeMillis();

        log.info("goodsPoolSync end, 耗时: {}秒", (end - start)/1000);
    }

    /**
     * 京东全量商品池同步，只同步mall_vop_sku_category表中的商品
     * @param tenantId 租户ID
     * @param isAsync 是否异步
     */
    public void fullGoodsPoolSync(Long tenantId, boolean isAsync) {
        TenantContextHolder.setTenantId(tenantId);
        if(isAsync) {
            threadPoolExecutor.execute(TtlRunnable.get(() -> {
                fullGoodsPoolSync(tenantId);
            }));
        } else {
            fullGoodsPoolSync(tenantId);
        }
    }

    /**
     * 京东全量商品池同步，只同步mall_vop_sku_category表中的商品
     * @param tenantId
     */
    public void fullGoodsPoolSync(Long tenantId) {
        log.info("fullGoodsPoolSync start");
        TenantContextHolder.setTenantId(tenantId);
        long start = System.currentTimeMillis();
        if(!vopAccessTokenService.isTenantVopAccessTokenOk()) {
            log.info("fullGoodsPoolSync 当前租户VOP未打开或配置未生效：{}", TenantContextHolder.getTenantId());
            return;
        }
        SupplierDO supplierDO = supplierService.getSupplierJD();
        OpenContextHolder.setSupplierId(supplierDO.getId());
        String lockKey = MALL_GOODS_POOL_SYNC + ":synclock:" + tenantId;
        RLock lock = redissonclient.getLock(lockKey);
        try {
            if(lock.tryLock()) {
                long startTime = System.currentTimeMillis();
                List<String> existSkuIdStringList = productSkuService.getInnerIdsBySupplierId(supplierDO.getId());
                Set<String> existSkuIdSet = new HashSet<>(existSkuIdStringList);

                //查询关系表中所有商品
                List<VopSkuCategoryDO> vopSkuCategoryDOS = vopSkuCategoryService.list(new LambdaQueryWrapperX<VopSkuCategoryDO>().select(VopSkuCategoryDO::getSkuInnerId));
                List<String> jdSkuIds = vopSkuCategoryDOS.stream().map(VopSkuCategoryDO::getSkuInnerId).map(String::valueOf).collect(Collectors.toList());
                Set<String> jdSkuIdSet = new HashSet<>(jdSkuIds);
                log.info("总同步商品数量：{}", jdSkuIdSet.size());
                jdSkuIdSet.removeAll(existSkuIdSet);
                log.info("待同步商品总数量：{}", jdSkuIdSet.size());
                List<String> filteredList = new ArrayList<>(jdSkuIdSet);

                // 构建上下文
                buildContext4Sync();
                //查询池内商品列表
                long goodsTotal = 0;
                int skuSaveBatchCount = 20;
                List<ProductSpuOpenVO> productSpuOpenVOArrayList = new ArrayList<>();
                goodsTotal += productSpuOpenVOArrayList.size();
                int threads = 3;
                for (int j = 0; j < filteredList.size();) {
                    List<CompletableFuture<?>> futures = new ArrayList<>();
                    for(int k = 0 ; k < threads && j < filteredList.size(); k++) {
                        String jdSkuId = filteredList.get(j++);
                        log.info("fullGoodsPoolSync sku {} start sync, {}/{}", jdSkuId, j + k, filteredList.size());
                        // TtlWrappers 包装后会自动传递线程变量
                        futures.add(CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
                            try {
                                Long categoryId = null;
                                if(vopConfigService.getVopConfig().getFullPoolSwitch()){
                                    // 全量商品池
                                    VopSkuCategoryDO vopSkuCategoryDO = vopSkuCategoryService.getVopSkuCategoryByJdSkuId(jdSkuId);
                                    if(vopSkuCategoryDO == null){
                                        return false;
                                    }
                                    categoryId = vopSkuCategoryDO.getCategoryId();
                                }
                                ProductSpuOpenVO productSpuOpenVO = this.goodsDetailSync(Long.valueOf(jdSkuId), OpenContextHolder.getSupplierId(), null, categoryId);
                                if(productSpuOpenVO != null) {
                                    for(AppProductSkuOpenVO sku : productSpuOpenVO.getSkus()){
                                        productSkuService.validProductRule(sku.getSkuInnerId(), productSpuOpenVO.getFullCategoryId(), sku.getSalePrice(),
                                                Arrays.asList(productSpuOpenVO.getSpuName(), sku.getSkuName()), sku.getStatus());
                                    }
                                    productSpuOpenVOArrayList.add(productSpuOpenVO);
                                    log.info("fullGoodsPoolSync is not null:{}", jdSkuId);
                                }
                                else {
                                    log.info("fullGoodsPoolSync is null:{}", jdSkuId);
                                }
                                return true;
                            } catch (Exception e) {
                                log.error("同步VOP商品异常, sku:{}, tenantId:{}", jdSkuId, tenantId, e);
                            }
                            return false;
                        })));
                    }

                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{})).get();
                    if(j % 100 == 0) {
                        log.info("fullGoodsPoolSync, sku progress: {}, time total: {}", j, System.currentTimeMillis() - startTime);
                    }
                    // 批量保存
                    if (productSpuOpenVOArrayList.size() > skuSaveBatchCount) {
                        try {
                            productSkuService.addProductSku(preHandleProductSpu(productSpuOpenVOArrayList), OpenContextHolder.getSupplierId());
                        } catch(Exception e) {
                            log.error("商品批量保存异常跳过, j: {}, size: {}", j, productSpuOpenVOArrayList.size(), e);
                        }
                        productSpuOpenVOArrayList.clear();
                    }
                }

                if(!productSpuOpenVOArrayList.isEmpty()) {
                    try {
                        productSkuService.addProductSku(preHandleProductSpu(productSpuOpenVOArrayList), OpenContextHolder.getSupplierId());
                    } catch(Exception e) {
                        log.error("商品批量保存异常跳过, size: {}", productSpuOpenVOArrayList.size(), e);
                    }
                    productSpuOpenVOArrayList.clear();
                }

                log.info("fullGoodsPoolSync total: {}", goodsTotal);
            } else {
                log.error("fullGoodsPoolSync 租户{}的VOP全量同步还未结束，请稍后再试", tenantId);
            }
        } catch(Exception e) {
            log.error("VOP商品同步异常", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            OpenContextHolder.clear();
        }

        long end = System.currentTimeMillis();
        log.info("fullGoodsPoolSync end, 耗时: {}秒", (end - start)/1000);
    }

    /**
     * 京东商品中会存在一个spu对应多个sku的情况，需要合并spu
     * @param productSpuOpenVOArrayList
     * @return
     */
    private List<ProductSpuOpenVO> preHandleProductSpu(List<ProductSpuOpenVO> productSpuOpenVOArrayList) {
        Map<String, ProductSpuOpenVO> innerIdDic = new HashMap<>();
        productSpuOpenVOArrayList.forEach(spu -> {
            log.info("vop商品同步处理，数量：{}", spu.getSkus().size());
            String key = spu.getSpuInnerId();
            if(!innerIdDic.containsKey(key)) {
                innerIdDic.put(key, spu);
            } else {
                innerIdDic.get(key).getSkus().addAll(spu.getSkus());
            }
            if(innerIdDic.get(key).getSkus().size() > 1) {
                innerIdDic.get(key).setSpecType(ProductSpuSpecTypeEnum.Multiple.getType());
            }
        });

        return new ArrayList<>(innerIdDic.values());
    }

    /**
     * @Description: 同步商品池，支持增量同步，此接口比较耗时，因此改为MQ异步
     * @Author: lujun
     * @Date: 2023/10/31 19:14
     */
    public void goodsPoolSyncV2() {
        log.info("goodsPoolSyncV2 start");
        long start = System.currentTimeMillis();
        if(!vopAccessTokenService.isTenantVopAccessTokenOk()) {
            log.info("goodsPoolSyncV2 当前租户VOP未打开或配置未生效：{}", TenantContextHolder.getTenantId());
            return;
        }
        Long tenantId = TenantContextHolder.getTenantId();
        String lockKey = MALL_GOODS_POOL_SYNC + ":synclock:" + tenantId;
        RLock lock = redissonclient.getLock(lockKey);
        SupplierDO supplierDO = supplierService.getSupplierJD();
        try {
            if(lock.tryLock()) {
                //查询商品池编号
                VopGoodsGetSkuPoolInfoResponse vopGoodsGetSkuPoolInfoResponse = vopGoodsService.getSkuPool();
                if(vopGoodsGetSkuPoolInfoResponse.getOpenRpcResult().getResultCode().compareTo("0000") != 0) {
                    log.error("goodsPoolSyncV2 查询VOP商品池编号失败：{}", vopGoodsGetSkuPoolInfoResponse.getMsg());
                    return;
                }
                List<GetSkuPoolInfoItemGoodsResp> skuPoolInfoRespList = vopGoodsGetSkuPoolInfoResponse.getOpenRpcResult().getResult().getSkuPoolList();

                List<String> existSkuIdStringList = productSkuService.getInnerIdsBySupplierId(supplierDO.getId());
                List<Long> existSkuIdList  = existSkuIdStringList.stream().map(Long::valueOf).collect(Collectors.toList());

                //查询池内商品列表
                long goodsTotal = 0;
                for (int i = 0; i < skuPoolInfoRespList.size(); i++) {
                    GetSkuPoolInfoItemGoodsResp skuPool = skuPoolInfoRespList.get(i);
                    List<Long> skuIdList = this.getSkuIdListByPoolId(skuPool.getBizPoolId());
                    log.info("goodsPoolSyncV2 pool name：{} pool id：{} sku size：{} start sync", skuPool.getName(), skuPool.getBizPoolId(), skuIdList.size());
                    skuIdList.removeAll(existSkuIdList);

                    goodsTotal += skuIdList.size();
                    productSkuProducer.sendVopProductFetch(skuPool.getName(), skuIdList.toArray(new Long[0]));
                }
                log.info("goodsPoolSyncV2 total: {}", goodsTotal);
            } else {
                log.error("goodsPoolSyncV2 租户{}的VOP全量同步还未结束，请稍后再试", tenantId);
            }
        } catch(Exception e) {
            log.error("VOP商品同步异常", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        long end = System.currentTimeMillis();

        log.info("goodsPoolSyncV2 end, 耗时: {}秒", (end - start)/1000);
    }

    /**
     * 按分类同步商品，如果分类ID为空，则同步所有分类
     * @param categoryId
     */
    public void syncVopProductByCategory(Long categoryId, Long maxPageSize) {
        log.info("syncVopProductByCategory start {} {}", categoryId, maxPageSize);
        long start = System.currentTimeMillis();
        if(!vopAccessTokenService.isTenantVopAccessTokenOk()) {
            log.info("syncVopProductByCategory 当前租户VOP未打开或配置未生效：{}", TenantContextHolder.getTenantId());
            return;
        }

        // 查询最大价格
        BigDecimal maxPrice = null;
        BigDecimal minPrice = null;
        ProductRuleDO productRuleDO = productRuleService.getPriceRule();
        if(productRuleDO != null){
            String [] priceRange = productRuleDO.getRuleContent().split(" - ");
            minPrice = new BigDecimal(priceRange[0]);
            maxPrice = new BigDecimal(priceRange[1]);
        }

        List<VopCategoryMappingDO> vopCategoryMappingDOS = vopCategoryMappingService.list();
        List<ProductCategoryDO> productCategoryDOS = productCategoryService.list(
                new LambdaQueryWrapperX<ProductCategoryDO>().eq(ProductCategoryDO::getStatus, ProductCategoryStatusEnum.ENABLE.getStatus()));
        List<Long> categoryIdList = productCategoryDOS.stream().map(ProductCategoryDO::getCategoryId).collect(Collectors.toList());
        List<VopCategoryMappingDO> enabledVopCategoryMappingDOS = vopCategoryMappingDOS.stream().filter(
                item -> categoryIdList.contains(item.getLastCategoryId())).collect(Collectors.toList());
        Map<Long, VopCategoryMappingDO> vopCategoryMappingDOMap = convertMap(enabledVopCategoryMappingDOS, VopCategoryMappingDO::getVopLastCategoryId);

        AtomicLong syncCount = new AtomicLong();
        for (int i = 0; i < enabledVopCategoryMappingDOS.size(); i++) {
            VopCategoryMappingDO vopCategoryMappingDO = enabledVopCategoryMappingDOS.get(i);
            if(categoryId != null && !vopCategoryMappingDO.getLastCategoryId().equals(categoryId)){
                continue;
            }

            log.info("syncVopProductByCategory start, category: {}", vopCategoryMappingDO.getVopFullCategoryId());

            long pageCount = 1;
            for (int j = 1; j <= pageCount; j++) {
                VopGoodsSearchReq searchReq = new VopGoodsSearchReq();
                searchReq.setCategoryId3(vopCategoryMappingDO.getVopLastCategoryId());
                searchReq.setPageSize(100);
                searchReq.setPageIndex(j);
                if(maxPrice != null) {
                    searchReq.setMaxPrice(maxPrice);
                }
                if(minPrice != null && minPrice.compareTo(BigDecimal.ZERO) > 0){
                    searchReq.setMinPrice(minPrice);
                }
                searchReq.setSortType(8);
                VopGoodsSearchSkuResponse response = vopGoodsService.goodsSearchPageList(searchReq);
                if(!response.getOpenRpcResult().getSuccess()){
                    log.error("syncVopProductByCategory, category: {}, page: {}, error: {}", vopCategoryMappingDO.getVopFullCategoryId(), j, response.getOpenRpcResult().getResultMessage());
                    continue;
                }
                SearchSkuGoodsResp goodsResp = response.getOpenRpcResult().getResult();
                OpenPagingResult openPagingResult = goodsResp.getSkuHitResultPaging();
                pageCount = Math.min(openPagingResult.getPageTotal(), maxPageSize);
                List<SkuHitSearchGoodsResp> items = openPagingResult.getItems();
                if(CollUtil.isEmpty(items)){
                    break;
                }
                log.info("syncVopProductByCategory, category: {}, page: {}, size: {}", vopCategoryMappingDO.getVopFullCategoryId(), j, items.size());

                List<String> jdSkuIds = items.stream().map(SkuHitSearchGoodsResp::getSkuId).map(String::valueOf).collect(Collectors.toList());
                List<String> existJdSkuIds = productSkuService.existJdSkuIds(jdSkuIds);
                List<SkuHitSearchGoodsResp> newItems = items.stream().filter(item -> !existJdSkuIds.contains(String.valueOf(item.getSkuId()))).collect(Collectors.toList());

                Map<String, Long> syncMap = new HashMap<>();
                for (int k = 0; k < newItems.size(); k++) {
                    SkuHitSearchGoodsResp skuHitSearchGoodsResp = items.get(k);
                    if(skuHitSearchGoodsResp.getYn() != 1 || skuHitSearchGoodsResp.getSkuState() != 1){
                        continue;
                    }

                    VopCategoryMappingDO vopCategoryMappingDO1 = vopCategoryMappingDOMap.get(skuHitSearchGoodsResp.getCategoryId3());
                    if(vopCategoryMappingDO1 == null) {
                        continue;
                    }

                    syncMap.put(String.valueOf(skuHitSearchGoodsResp.getSkuId()), vopCategoryMappingDO1.getLastCategoryId());
                }

                List<String> syncSkuIds = new ArrayList<>(syncMap.keySet());
                int threads = 4;
                for(int l = 0 ; l < syncSkuIds.size() ; l++) {
                    try {
                        List<CompletableFuture<?>> futures = new ArrayList<>();
                        for (int n = 0; n < threads && l < syncSkuIds.size(); n++) {
                            String jdSkuId = syncSkuIds.get(l++);
                            Long lastCategoryId = syncMap.get(jdSkuId);
                            futures.add(CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
                                try {
                                    VopSkuCategoryCreateReqVO createReqVO = new VopSkuCategoryCreateReqVO();
                                    createReqVO.setSkuInnerId(jdSkuId);
                                    createReqVO.setCategoryId(lastCategoryId);
                                    Long vopSkuCategoryId = vopSkuCategoryService.createVopSkuCategory(createReqVO, null, null);
                                    syncCount.getAndIncrement();
                                    return vopSkuCategoryId != null;
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                }
                                return false;
                            })));
                        }

                        CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{})).get();
                    }
                    catch (Exception ex) {
                        log.error(ex.getMessage());
                    }
                }
            }
        }

        long end = System.currentTimeMillis();
        log.info("syncVopProductByCategory end, 耗时: {}秒", (end - start)/1000);
        log.info("syncVopProductByCategory total: {}", syncCount);
    }

    /**
     * 商品信息更新
     * @param poolName
     * @param jdSkuIds
     * @return
     */
    public List<Long> goodsUpdate(Boolean syncSubTenant, String poolName, Long... jdSkuIds) {
        ArrayList successSkuIdList = new ArrayList<>();
        log.info("商品同步开始，商品池名称：{}，商品sku：{}", poolName, jdSkuIds);
        Long tenantId = TenantContextHolder.getTenantId();
        if(!vopAccessTokenService.isTenantVopAccessTokenOk()) {
            log.info("当前租户VOP未打开或配置未生效：{}", tenantId);
            return successSkuIdList;
        }
        if(jdSkuIds.length == 0) {
            return successSkuIdList;
        }

        try {
            buildContext4Sync();
            // 并行4个子线程同步
            int threads = 4;
            for(int i = 0 ; i < jdSkuIds.length ; i++) {
                List<CompletableFuture<?>> futures = new ArrayList<>();
                for(int k = 0 ; k < threads && i < jdSkuIds.length ; k++) {
                    Long skuId = jdSkuIds[i++];
                    futures.add(CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
                        try {
                            Long categoryId = null;
                            if(vopConfigService.getVopConfig().getFullPoolSwitch()){
                                // 全量商品池
                                VopSkuCategoryDO vopSkuCategoryDO = vopSkuCategoryService.getVopSkuCategoryByJdSkuId(String.valueOf(skuId));
                                if(vopSkuCategoryDO == null){
                                    return false;
                                }
                                categoryId = vopSkuCategoryDO.getCategoryId();
                            }
                            log.info("租户：{}，goodsUpdate：{}", TenantContextHolder.getTenantId(), skuId);
                            ProductSpuOpenVO productSpuOpenVO = this.goodsDetailSync(skuId, OpenContextHolder.getSupplierId(), poolName, categoryId);
                            if(productSpuOpenVO == null) {
                                return false;
                            }
                            for(AppProductSkuOpenVO sku : productSpuOpenVO.getSkus()){
                                productSkuService.validProductRule(sku.getSkuInnerId(), productSpuOpenVO.getFullCategoryId(), sku.getSalePrice(),
                                        Arrays.asList(productSpuOpenVO.getSpuName(), sku.getSkuName()), sku.getStatus());
                            }
                            productSkuService.saveOrUpdateProductSku(productSpuOpenVO, OpenContextHolder.getSupplierId(), null);
                            if(syncSubTenant){
                                List<VopConfigDO> vopConfigDOS = vopConfigService.getAll();
                                if(vopConfigService.getVopConfig().getFullPoolSwitch()){
                                    // 子租户商品更新，过滤未开启全量商品池的子租户
                                    List<VopAccessTokenDO> vopAccessTokenDOList = vopAccessTokenService.getAll();
                                    for (VopAccessTokenDO vopAccessTokenDO : vopAccessTokenDOList) {
                                        if(!vopAccessTokenDO.getTenantId().equals(TenantContextHolder.getTenantId())
                                                && StringUtils.isNotBlank(vopAccessTokenDO.getAccessToken())){
                                            for (VopConfigDO vopConfigDO : vopConfigDOS) {
                                                if(vopConfigDO.getTenantId().equals(vopAccessTokenDO.getTenantId()) && vopConfigDO.getFullPoolSwitch()){
                                                    subTenantGoodsUpdate(productSpuOpenVO, vopAccessTokenDO.getSupplierId(), vopAccessTokenDO.getTenantId());
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            successSkuIdList.add(skuId);
                            return true;
                        } catch (ServiceException e) {
                            log.info("同步VOP商品详情失败, sku:{}, tenantId:{}, {}", skuId, tenantId, e.getMessage());
                        } catch (Exception e) {
                            log.error("同步VOP商品详情异常, sku:{}, tenantId:{}", skuId, tenantId, e);
                        }
                        return false;
                    })));
                }
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{})).get();
            }
            return successSkuIdList;
        } catch (Exception e) {
            log.error("同步VOP商品详情错误, {}, {}", tenantId, jdSkuIds, e);
        } finally {
            OpenContextHolder.clear();
            log.info("商品同步结束，商品池id：{}，商品sku：{}", poolName, jdSkuIds);
        }
        return successSkuIdList;
    }

    public void subTenantGoodsUpdate(ProductSpuOpenVO reqVO, Long supplierId, Long tenantId) {
        String skuIdStr = reqVO.getSkus().stream()
                .map(AppProductSkuOpenVO::getSkuInnerId)
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        List<CompletableFuture<Boolean>> futures = new ArrayList<>();
        futures.add(CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
            TenantContextHolder.setTenantId(tenantId);
            log.info("同步主租户{}商品开始：{}", tenantId, skuIdStr);
            for (int i = 0; i < reqVO.getSkus().size(); i++) {
                AppProductSkuOpenVO appProductSkuOpenVO = reqVO.getSkus().get(i);
                Boolean existJdSkuId = productSkuService.isExistJdSkuId(appProductSkuOpenVO.getSkuInnerId());
                VopGoodsGetSkuStateListResponse vopGoodsGetSkuStateListResponse = vopGoodsService.getSkuStateList(appProductSkuOpenVO.getSkuInnerId());
                if(vopGoodsGetSkuStateListResponse.getOpenRpcResult().getSuccess()
                        && CollUtil.isNotEmpty(vopGoodsGetSkuStateListResponse.getOpenRpcResult().getResult())
                        && vopGoodsGetSkuStateListResponse.getOpenRpcResult().getResult().get(0).getSkuState() == 1){
                    // 商品上架，校验商品规则后更新商品
                    log.info("商品规则校验：{}", appProductSkuOpenVO.getSkuInnerId());
                    productSkuService.validProductRule(appProductSkuOpenVO.getSkuInnerId(),
                            reqVO.getFullCategoryId(),
                            appProductSkuOpenVO.getSalePrice(),
                            Arrays.asList(reqVO.getSpuName(), appProductSkuOpenVO.getSkuName()),
                            vopGoodsGetSkuStateListResponse.getOpenRpcResult().getResult().get(0).getSkuState());
                    appProductSkuOpenVO.setStatus(1);
                    productSkuService.saveOrUpdateProductSku(reqVO, supplierId, tenantId);
                    log.info("同步主租户{}商品结束：{}，类型：1", tenantId, skuIdStr);
                    return true;
                }
                else if(vopGoodsGetSkuStateListResponse.getOpenRpcResult().getSuccess()
                        && CollUtil.isNotEmpty(vopGoodsGetSkuStateListResponse.getOpenRpcResult().getResult())
                        && vopGoodsGetSkuStateListResponse.getOpenRpcResult().getResult().get(0).getSkuState() == 0
                        && existJdSkuId) {
                    // 本地商品存在，商品下架，不校验商品规则更新商品
                    appProductSkuOpenVO.setStatus(0);
                    productSkuService.saveOrUpdateProductSku(reqVO, supplierId, tenantId);
                    log.info("同步主租户{}商品结束：{}，类型：2", tenantId, skuIdStr);
                    return true;
                }
                else if(vopGoodsGetSkuStateListResponse.getOpenRpcResult().getSuccess()
                        && CollUtil.isNotEmpty(vopGoodsGetSkuStateListResponse.getOpenRpcResult().getResult())
                        && vopGoodsGetSkuStateListResponse.getOpenRpcResult().getResult().get(0).getSkuState() == 0
                        && !existJdSkuId) {
                    // 本地商品不存在，商品下架，不更新商品
                    log.info("同步主租户{}商品结束：{}，类型：3", tenantId, skuIdStr);
                    return true;
                }
                else {
                    // vop请求出错或商品不在商品池，不更新商品
                    log.info("同步主租户{}商品结束：{}，类型：4", tenantId, skuIdStr);
                    return false;
                }
            }
            return false;
        })));
    }

    /**
     * 搜索商品-核心接口
     *
     * @param searchReq
     * @return
     */
    public List<VopSkuGoodsPageItem> goodsSearchSimpleList(VopGoodsSearchReq searchReq) {
        VopGoodsSearchSkuResponse response = vopGoodsService.goodsSearchPageList(searchReq);
        SearchSkuGoodsResp goodsResp = response.getOpenRpcResult().getResult();
        OpenPagingResult openPagingResult = goodsResp.getSkuHitResultPaging();

        List<VopSkuGoodsPageItem> list = turnPageItems(searchReq, openPagingResult.getItems());
        if (CollUtil.isNotEmpty(list)) {
            SupplierDO supplier = supplierService.getSupplierJD();
            Long supplierId = supplier.getId();
            list.forEach(item -> {
                item.setImageUrl(convertJDImage(item.getImageUrl()));
                item.setSupplierId(supplierId);
                item.setSupplierName(supplier.getName());
                item.setLogoUrl(supplier.getLogoUrl());
            });
        }
        return list;
    }

    /**
     * 搜索商品-核心接口
     *
     * @param searchReq
     * @return
     */
    public VopGoodsSearchPageResultResp goodsSearchPageList(VopGoodsSearchReq searchReq) {
        VopGoodsSearchSkuResponse response = vopGoodsService.goodsSearchPageList(searchReq);
        SearchSkuGoodsResp goodsResp = response.getOpenRpcResult().getResult();
        OpenPagingResult openPagingResult = goodsResp.getSkuHitResultPaging();

        SupplierDO supplier = supplierService.getSupplierJD();
        List<VopSkuGoodsPageItem> pageItems = turnPageItems(searchReq, openPagingResult.getItems());
        if (supplier != null && CollUtil.isNotEmpty(pageItems)) {
            pageItems.forEach(pi -> {
                pi.setSupplierId(supplier.getId());
                pi.setSupplierName(supplier.getName());
            });
        }

        // 封装结果
        VopGoodsSearchPageResultResp pageResultResp = new VopGoodsSearchPageResultResp();
        List<CategorySearchAggGoodsResp> cateJdList = goodsResp.getCategoryAggList();
        if (!CollectionUtils.isEmpty(cateJdList)) {
            cateJdList.sort(Comparator.comparingInt(CategorySearchAggGoodsResp::getCateLevel));
            pageResultResp.setCategoryAggList(cateJdList);
        } else {
           // 搜索分类为空，添加分类
            List<Long> cateList = new ArrayList<>();
            if (searchReq.getCategoryId1() != null) {
                cateList.add(searchReq.getCategoryId1());
            }
            if (searchReq.getCategoryId2() != null) {
                cateList.add(searchReq.getCategoryId2());
            }
            if (searchReq.getCategoryId3() != null) {
                cateList.add(searchReq.getCategoryId3());
            }
            if (!CollectionUtils.isEmpty(cateList)) {
                List<ProductCategoryDO> categoryDOList = productCategoryService.getByCategoryIds(cateList);

                List<ProductCategoryDO> allList = new ArrayList<>();
                for (ProductCategoryDO category : categoryDOList) {
                    allList.add(category);
                    if (!Objects.equals(category.getCategoryLevel(), 0) &&
                            categoryDOList.stream().noneMatch(item -> Objects.equals(item.getCategoryId(), category.getParentId()))) {
                        allList.addAll(vopGoodsCategoryService.queryParentList(category));
                    }
                }
                List<CategorySearchAggGoodsResp> aggCategoryResps = new ArrayList<>();
                if (!CollectionUtils.isEmpty(allList)) {
                    allList.forEach(item -> {
                        CategorySearchAggGoodsResp goodsResp1 = new CategorySearchAggGoodsResp();
                        goodsResp1.setCategoryId(item.getCategoryId().intValue());
                        goodsResp1.setParentCategoryId(item.getParentId().intValue());

                        // 分类级别问题。。
                        goodsResp1.setCateLevel(item.getCategoryLevel() + 1);
                        goodsResp1.setCateName(item.getCategoryName());
                        aggCategoryResps.add(goodsResp1);
                    });
                }
                aggCategoryResps.sort(Comparator.comparingInt(CategorySearchAggGoodsResp::getCateLevel));
                pageResultResp.setCategoryAggList(aggCategoryResps);
            }
        }

        PageResult<VopSkuGoodsPageItem> pageResult = new PageResult<>(searchReq.getPageIndex(),
                searchReq.getPageSize(),
                (int) (openPagingResult.getPageItemTotal() % searchReq.getPageSize() == 0 ? openPagingResult.getPageItemTotal() / searchReq.getPageSize() :
                        openPagingResult.getPageItemTotal() / searchReq.getPageSize() + 1),
                pageItems,
                openPagingResult.getPageItemTotal());
        pageResultResp.setBrandAggList(goodsResp.getBrandAggList());
        pageResultResp.setPriceAggList(goodsResp.getPriceAggList());
        pageResultResp.setExtAttrAggList(goodsResp.getExtAttrAggList());
        pageResultResp.setPageResult(pageResult);
        pageResult.getList().forEach(item -> {
            item.setImageUrl(convertJDImage(item.getImageUrl()));
        });
        return pageResultResp;
    }

    /**
     * 将VOP商品转换成前段展示商品
     * @param searchReq
     * @param searchGoodsRespList
     * @return
     */
    @NotNull
    private List<VopSkuGoodsPageItem> turnPageItems(VopGoodsSearchReq searchReq, List<SkuHitSearchGoodsResp> searchGoodsRespList) {
        List<VopSkuGoodsPageItem> pageItems = new ArrayList<>();

        try {
            if (!CollectionUtils.isEmpty(searchGoodsRespList)) {
                List<Long> skuIdList = searchGoodsRespList.stream().map(SkuHitSearchGoodsResp::getSkuId).collect(Collectors.toList());
                String allSkuIds = StringUtils.join(skuIdList, ",");

                VopGoodsCheckSkuSaleListResponse saleListResponse = vopGoodsService.checkSkuSaleList(allSkuIds);
                List<CheckSkuSaleGoodsResp> goodsRespList = saleListResponse.getOpenRpcResult().getResult();
                String skuIds;
                List<Long> validIdList;
                if (!CollectionUtils.isEmpty(goodsRespList)) {
                    validIdList = goodsRespList.stream().filter(item -> Objects.equals(item.getSaleState(), 1))
                            .map(CheckSkuSaleGoodsResp::getSkuId).collect(Collectors.toList());
                    skuIds = StringUtils.join(validIdList, ",");
                } else {
                    skuIds = allSkuIds;
                    validIdList = skuIdList;
                }

                // 批量查询售卖价
                FutureTask<VopGoodsGetSellPriceResponse> future1 = new FutureTask<>(() -> vopGoodsService.getSellPrice(skuIds, null));
                threadPoolExecutor.submit(TtlRunnable.get(future1));

                // 批量查询上下架状态
                FutureTask<VopGoodsGetSkuStateListResponse> future2 = new FutureTask<>(() -> vopGoodsService.getSkuStateList(skuIds));
                threadPoolExecutor.submit(TtlRunnable.get(future2));

                // 批量查询库存状态
                GetStockByIdGoodsReq getStockByIdGoodsReq = new GetStockByIdGoodsReq();
                AreaBaseInfoGoodsReq areaBaseInfoGoodsReq = new AreaBaseInfoGoodsReq();
                String areaGroup = searchReq.getAreaIds();
                if (StringUtils.isNotEmpty(areaGroup)) {
                    String[] areaArr = areaGroup.split(",");
                    areaBaseInfoGoodsReq.setProvinceId(Long.parseLong(areaArr[0].trim()));
                    areaBaseInfoGoodsReq.setCityId(Long.parseLong(areaArr[1].trim()));
                    areaBaseInfoGoodsReq.setCountyId(Long.parseLong(areaArr[2].trim()));
                } else {
                    areaBaseInfoGoodsReq.setProvinceId(DEFAULT_PROVINCE_ID);
                    areaBaseInfoGoodsReq.setCityId(DEFAULT_CITY_ID);
                    areaBaseInfoGoodsReq.setCountyId(DEFAULT_COUNTY_ID);
                    areaBaseInfoGoodsReq.setTownId(DEFAULT_TOWN_ID);
                }
                getStockByIdGoodsReq.setAreaInfo(areaBaseInfoGoodsReq);
                List<SkuNumBaseGoodsReq> goodsReqs = new ArrayList<>();

                searchGoodsRespList.forEach(item -> {
                    if (validIdList.contains(item.getSkuId())) {
                        SkuNumBaseGoodsReq numBaseGoodsReq = new SkuNumBaseGoodsReq();
                        numBaseGoodsReq.setSkuId(item.getSkuId());
                        numBaseGoodsReq.setSkuNumber(1);
                        goodsReqs.add(numBaseGoodsReq);
                    }
                });
                getStockByIdGoodsReq.setSkuNumInfoList(goodsReqs);

                FutureTask<VopGoodsGetNewStockByIdResponse> future3 = new FutureTask<>(() -> vopGoodsStockService.getNewStockById(getStockByIdGoodsReq));
                threadPoolExecutor.submit(TtlRunnable.get(future3));

                VopGoodsGetSellPriceResponse priceResponse = future1.get();
                List<GetSellPriceGoodsResp> goodsResps = priceResponse.getOpenRpcResult().getResult();
                Map<Long, List<GetSellPriceGoodsResp>> priceMap = CollectionUtils.isEmpty(goodsResps) ? Collections.emptyMap():
                        goodsResps.stream().collect(Collectors.groupingBy(GetSellPriceGoodsResp::getSkuId));

                VopGoodsGetSkuStateListResponse stateListResponse = future2.get();
                List<GetSkuStateGoodsResp> stateGoodsRespList = stateListResponse.getOpenRpcResult().getResult();
                Map<Long, List<GetSkuStateGoodsResp>> saleStateMap = CollectionUtils.isEmpty(stateGoodsRespList) ? Collections.emptyMap():
                        stateGoodsRespList.stream().collect(Collectors.groupingBy(GetSkuStateGoodsResp::getSkuId));

                VopGoodsGetNewStockByIdResponse stockByIdResponse = future3.get();
                List<GetStockByIdGoodsResp> getStockByIdGoodsResps = stockByIdResponse.getOpenRpcResult().getResult();
                Map<Long, List<GetStockByIdGoodsResp>> stockMap =  CollectionUtils.isEmpty(getStockByIdGoodsResps) ? Collections.emptyMap():
                        getStockByIdGoodsResps.stream().collect(Collectors.groupingBy(GetStockByIdGoodsResp::getSkuId));

                List<Integer> lowestBuys = new ArrayList<>();

                searchGoodsRespList.forEach(item -> {
                    VopSkuGoodsPageItem pageItem = VopGoodsConvert.INSTANCE.convertVopGoodsItem(item);
                    List<GetSellPriceGoodsResp> priceGoodsRespList = priceMap.get(item.getSkuId());
                    if (!CollectionUtils.isEmpty(priceGoodsRespList)) {
                        pageItem.setSalePrice(priceGoodsRespList.get(0).getSalePrice());
                        if(priceGoodsRespList.get(0).getJdPrice() != null){
                            pageItem.setMarketPrice(priceGoodsRespList.get(0).getJdPrice());
                        }
                        else {
                            pageItem.setMarketPrice(priceGoodsRespList.get(0).getMarketPrice());
                        }
                    }
                    List<GetSkuStateGoodsResp> saleStateList = saleStateMap.get(item.getSkuId());
                    if (!CollectionUtils.isEmpty(saleStateList)) {
                        pageItem.setSaleStatus(saleStateList.get(0).getSkuState());
                    } else {
                        pageItem.setSaleStatus(0);
                    }

                    List<GetStockByIdGoodsResp> stockList = stockMap.get(item.getSkuId());
                    if (!CollectionUtils.isEmpty(stockList)) {
                        pageItem.setArrivalDays(stockList.get(0).getArrivalDays());
                        pageItem.setStockStateType(stockList.get(0).getStockStateType());
                        pageItem.setStockStateDesc(stockList.get(0).getStockStateDesc());
                        pageItem.setRemainNum(stockList.get(0).getRemainNum());
                    }
                    pageItem.setLowestBuy(1);
                    pageItems.add(pageItem);
                });
            }
        } catch (Exception e) {
            log.info("turnPageItems, 出错，error：", e);
        }
        return pageItems;
    }

    /**
     * 查询商品上架、详情图片、可售组合接口
     *
     * @param skuId
     * @return
     */
    public VopGoodsDetailResp getSkuStockPriceAndImages(Long skuId) {
        VopGoodsDetailResp detailResp = new VopGoodsDetailResp();
        try {

            FutureTask<VopGoodsGetSkuImageListResponse> future1 = new FutureTask<>(() -> vopGoodsService.getSkuImageList(String.valueOf(skuId)));
            threadPoolExecutor.submit(TtlRunnable.get(future1));

            FutureTask<VopGoodsCheckSkuSaleListResponse> future2 = new FutureTask<>(() -> vopGoodsService.checkSkuSaleList(String.valueOf(skuId)));
            threadPoolExecutor.submit(TtlRunnable.get(future2));

            FutureTask<VopGoodsGetSkuStateListResponse> future3 = new FutureTask<>(() -> vopGoodsService.getSkuStateList(String.valueOf(skuId)));
            threadPoolExecutor.submit(TtlRunnable.get(future3));

            String cid;
            Long userId = SecurityFrameworkUtils.getLoginUserId();
            if(userId != null){
                cid = "c" + String.valueOf(userId);
            } else {
                cid = null;
            }

            FutureTask<VopGoodsGetSellPriceResponse> future4 = new FutureTask<>(() -> vopGoodsService.getSellPrice(String.valueOf(skuId), cid));
            threadPoolExecutor.submit(TtlRunnable.get(future4));

            VopGoodsGetSkuDetailReq detailReq = new VopGoodsGetSkuDetailReq();
            detailReq.setSkuId(skuId);

            VopGoodsGetSkuImageListResponse imageListResponse = future1.get();
            if (!CollectionUtils.isEmpty(imageListResponse.getOpenRpcResult().getResult())) {
                detailResp.setSkuImageList(imageListResponse.getOpenRpcResult().getResult().get(0).getSkuImageList());
            }
            detailResp.setSkuSaleList(future2.get().getOpenRpcResult().getResult());
            detailResp.setSkuStateList(future3.get().getOpenRpcResult().getResult());
            detailResp.setPriceGoodsList(future4.get().getOpenRpcResult().getResult());
        } catch (Exception e) {
            log.info("getSkuStockPriceAndImages, 出错，error：", e);
        }
        return detailResp;
    }


    /**
     * 查询商品上架、详情图片、可售、权益等组合接口
     *
     * @param skuId
     * @return
     */
    public VopGoodsDetailResp getAssociationSkuDetailInfo(Long skuId) {
        VopGoodsDetailResp detailResp = new VopGoodsDetailResp();
        try {

            FutureTask<VopGoodsGetSkuImageListResponse> future1 = new FutureTask<>(() -> vopGoodsService.getSkuImageList(String.valueOf(skuId)));
            threadPoolExecutor.submit(TtlRunnable.get(future1));

            FutureTask<VopGoodsCheckSkuSaleListResponse> future2 = new FutureTask<>(() -> vopGoodsService.checkSkuSaleList(String.valueOf(skuId)));
            threadPoolExecutor.submit(TtlRunnable.get(future2));

            FutureTask<VopGoodsGetSkuStateListResponse> future3 = new FutureTask<>(() -> vopGoodsService.getSkuStateList(String.valueOf(skuId)));
            threadPoolExecutor.submit(TtlRunnable.get(future3));

            FutureTask<VopGoodsGetSellPriceResponse> future4 = new FutureTask<>(() -> vopGoodsService.getSellPrice(String.valueOf(skuId), null));
            threadPoolExecutor.submit(TtlRunnable.get(future4));

            FutureTask<VopGoodsGetSimilarSkuListResponse> future5 = new FutureTask<>(() -> vopGoodsService.getSimilarSkuList(skuId));
            threadPoolExecutor.submit(TtlRunnable.get(future5));

            VopGoodsGetSkuDetailReq detailReq = new VopGoodsGetSkuDetailReq();
            detailReq.setSkuId(skuId);
            FutureTask<VopGoodsGetSkuDetailInfoResponse> future6 = new FutureTask<>(() -> vopGoodsService.getSkuDetailInfo(detailReq));
            threadPoolExecutor.submit(TtlRunnable.get(future6));

            VopGoodsGetSkuImageListResponse imageListResponse = future1.get();
            if (!CollectionUtils.isEmpty(imageListResponse.getOpenRpcResult().getResult())) {
                detailResp.setSkuImageList(imageListResponse.getOpenRpcResult().getResult().get(0).getSkuImageList());
            }
            detailResp.setSkuSaleList(future2.get().getOpenRpcResult().getResult());
            detailResp.setSkuStateList(future3.get().getOpenRpcResult().getResult());
            detailResp.setPriceGoodsList(future4.get().getOpenRpcResult().getResult());
            detailResp.setSimilarSkuGoodsList(future5.get().getOpenRpcResult().getResult());

            VopGoodsGetSkuDetailInfoResponse detailInfoResponse = future6.get();
            if (detailInfoResponse.getOpenRpcResult().getResult() != null) {
                String cate = detailInfoResponse.getOpenRpcResult().getResult().getCategory();
                String[] cateArr = cate.split(";");
                if (cateArr.length > 0) {
                    ConfigProductCategoryDO categoryDO = configProductCategoryService.getCategoryByCategoryId(Long.parseLong((cateArr[cateArr.length - 1]).trim()));
                    if (null != categoryDO) {
                        VopGoodsSearchReq searchReq = new VopGoodsSearchReq();
                        searchReq.setPageIndex(1);
                        searchReq.setPageSize(7);
                        searchReq.setKeyword(categoryDO.getCategoryName());
                        VopGoodsSearchSkuResponse response = vopGoodsService.goodsSearchPageList(searchReq);
                        List<VopSkuGoodsPageItem> pageItemList = this.turnPageItems(searchReq, response.getOpenRpcResult().getResult().getSkuHitResultPaging().getItems());
                        if (!CollectionUtils.isEmpty(pageItemList)) {
                            detailResp.setSuggestGoodsList(pageItemList.stream().filter(item -> !Objects.equals(item.getSkuId(), skuId)).collect(Collectors.toList()));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("getAssociationSkuDetailInfo, 出错，error：", e);
        }
        return detailResp;
    }

    /**
     * 库存状态 :
     * 33: 有货 现货-下单立即发货;
     * 39: 有货 在途-正在内部配货，预计2-6天到达本仓库;
     * 40: 有货 可配货-下单后从有货仓库配货;
     * 36: 预订;
     * 34: 无货;
     * 99: 无货开预定，该状态(99)的查询需要依赖合同是否开通'无货开预定'，并且到货周期略长，请谨慎使用。
     *
     * @param stockInfoReq
     * @return
     */
    public AreaStockInfoResp queryGoodsStockInfo(GoodsStockInfoReq stockInfoReq) {
        ProductSkuDO productSkuDO = productSkuService.getSimpleSkuById(stockInfoReq.getSkuId());
        Assert.notNull(productSkuDO, String.format("商品sku：%d不存在", stockInfoReq.getSkuId()));
        AreaStockInfoResp resp = new AreaStockInfoResp();
        resp.setSkuId(stockInfoReq.getSkuId());
        if(productSkuDO.isJd()) {
            String areaStr = "";
            if (stockInfoReq.getProvinceId() != null
                    && stockInfoReq.getCityId() != null
                    && stockInfoReq.getCountyId() != null
                    && stockInfoReq.getTownId() != null) {
                areaStr = stockInfoReq.getProvinceId() + ","
                        + stockInfoReq.getCityId() + ","
                        + stockInfoReq.getCountyId() + ","
                        + stockInfoReq.getTownId();
            } else {
                areaStr = DEFAULT_PROVINCE_ID + "," + DEFAULT_CITY_ID + ","
                        + DEFAULT_COUNTY_ID + "," + DEFAULT_TOWN_ID;
            }

            VopGoodsQueryAreaStockStatesReq stockStatesReq = new VopGoodsQueryAreaStockStatesReq();
            String skuInnerId = productSkuDO.getSkuInnerId();
            stockStatesReq.setSkuId(Long.valueOf(skuInnerId));
            stockStatesReq.setAreaLevel(areaStr);
            stockStatesReq.setStockState("33,39,40");
            VopGoodsQueryAreaStockStatesResponse stockStatesResponse = vopGoodsStockService.queryAreaStock(stockStatesReq);

            // 批量查询库存状态
            GetStockByIdGoodsReq getStockByIdGoodsReq = new GetStockByIdGoodsReq();
            AreaBaseInfoGoodsReq areaBaseInfoGoodsReq = new AreaBaseInfoGoodsReq();
            if (stockInfoReq.getProvinceId() != null
                    && stockInfoReq.getCityId() != null
                    && stockInfoReq.getCountyId() != null
                    ) {
                areaBaseInfoGoodsReq.setProvinceId(stockInfoReq.getProvinceId());
                areaBaseInfoGoodsReq.setCityId(stockInfoReq.getCityId());
                areaBaseInfoGoodsReq.setCountyId(stockInfoReq.getCountyId());
                if (stockInfoReq.getTownId() != null){
                    areaBaseInfoGoodsReq.setTownId(stockInfoReq.getTownId());
                }
            } else {
                areaBaseInfoGoodsReq.setProvinceId(DEFAULT_PROVINCE_ID);
                areaBaseInfoGoodsReq.setCityId(DEFAULT_CITY_ID);
                areaBaseInfoGoodsReq.setCountyId(DEFAULT_COUNTY_ID);
                areaBaseInfoGoodsReq.setTownId(DEFAULT_TOWN_ID);
            }
            getStockByIdGoodsReq.setAreaInfo(areaBaseInfoGoodsReq);
            List<SkuNumBaseGoodsReq> goodsReqs = new ArrayList<>();
            SkuNumBaseGoodsReq numBaseGoodsReq = new SkuNumBaseGoodsReq();
            numBaseGoodsReq.setSkuId(Long.valueOf(skuInnerId));
            numBaseGoodsReq.setSkuNumber(1);
            goodsReqs.add(numBaseGoodsReq);
            getStockByIdGoodsReq.setSkuNumInfoList(goodsReqs);

            VopGoodsGetNewStockByIdResponse stockByIdResponse = vopGoodsStockService.getNewStockById(getStockByIdGoodsReq);
            List<GetStockByIdGoodsResp> getStockByIdGoodsResps = stockByIdResponse.getOpenRpcResult().getResult();

            // 返回结果
            QueryAreaStockConditionResp conditionResp = stockStatesResponse.getOpenRpcResult().getResult();
            if (null != conditionResp) {
                resp.setStockStateList(conditionResp.getAreaStockStateRespList());
                resp.setCreated(conditionResp.getCreated());
                resp.setSkuId(conditionResp.getSkuId());
            }

            if (!CollectionUtils.isEmpty(getStockByIdGoodsResps)) {
                resp.setArrivalDays(getStockByIdGoodsResps.get(0).getArrivalDays());
                resp.setRemainNum(getStockByIdGoodsResps.get(0).getRemainNum());
                resp.setStockStateDesc(getStockByIdGoodsResps.get(0).getStockStateDesc());
                resp.setStockStateType(getStockByIdGoodsResps.get(0).getStockStateType());
            }
            return resp;
        } else {
            ProductSkuStockDO skuStock = productSkuStockService.getSkuStock(stockInfoReq.getSkuId());
            if (skuStock == null || skuStock.getStock() < 1) {
                resp.setStockStateDesc("无货");
                resp.setStockStateType(34);
            } else {
                resp.setStockStateDesc("有货");
                resp.setStockStateType(33);
            }
        }
        return resp;
    }

    /**
     * 批量获取商品库存
     * @param stockInfoReq
     */
    public List<AreaStockInfoResp> getNewStockById(@Valid VopStockByIdGoodsReq stockInfoReq) {
        List<ProductSkuDO> productSkuDOS = productSkuService.list(new LambdaQueryWrapperX<ProductSkuDO>()
                .in(ProductSkuDO::getId, stockInfoReq.getSkuIds())
                .select(ProductSkuDO::getId, ProductSkuDO::getSkuInnerId));
        Map<String, ProductSkuDO> skuInnerIdMap = convertMap(productSkuDOS, ProductSkuDO::getSkuInnerId);
        Long supplierId = stockInfoReq.getSupplierId();
        SupplierDO supplier = supplierService.getSupplier(supplierId);
        List<AreaStockInfoResp> respList = new ArrayList<>();
        if(supplier != null && supplier.isJd()) {
            String areaStr = "";
            if (stockInfoReq.getProvinceId() != null
                    && stockInfoReq.getCityId() != null
                    && stockInfoReq.getCountyId() != null
                    && stockInfoReq.getTownId() != null) {
                areaStr = stockInfoReq.getProvinceId() + ","
                        + stockInfoReq.getCityId() + ","
                        + stockInfoReq.getCountyId() + ","
                        + stockInfoReq.getTownId();
            } else {
                areaStr = DEFAULT_PROVINCE_ID + "," + DEFAULT_CITY_ID + ","
                        + DEFAULT_COUNTY_ID + "," + DEFAULT_TOWN_ID;
            }

            // 批量查询库存状态
            GetStockByIdGoodsReq getStockByIdGoodsReq = new GetStockByIdGoodsReq();
            AreaBaseInfoGoodsReq areaBaseInfoGoodsReq = new AreaBaseInfoGoodsReq();
            if (stockInfoReq.getProvinceId() != null
                    && stockInfoReq.getCityId() != null
                    && stockInfoReq.getCountyId() != null
                    ) {
                areaBaseInfoGoodsReq.setProvinceId(stockInfoReq.getProvinceId());
                areaBaseInfoGoodsReq.setCityId(stockInfoReq.getCityId());
                areaBaseInfoGoodsReq.setCountyId(stockInfoReq.getCountyId());
                if (stockInfoReq.getTownId() != null){
                    areaBaseInfoGoodsReq.setTownId(stockInfoReq.getTownId());
                }
            } else {
                areaBaseInfoGoodsReq.setProvinceId(DEFAULT_PROVINCE_ID);
                areaBaseInfoGoodsReq.setCityId(DEFAULT_CITY_ID);
                areaBaseInfoGoodsReq.setCountyId(DEFAULT_COUNTY_ID);
                areaBaseInfoGoodsReq.setTownId(DEFAULT_TOWN_ID);
            }
            getStockByIdGoodsReq.setAreaInfo(areaBaseInfoGoodsReq);
            List<SkuNumBaseGoodsReq> goodsReqs = new ArrayList<>();
            List<Long> skuInnerIds = productSkuDOS.stream().map(item -> Long.valueOf(item.getSkuInnerId())).collect(Collectors.toList());
            for (Long skuInnerId : skuInnerIds) {
                SkuNumBaseGoodsReq numBaseGoodsReq = new SkuNumBaseGoodsReq();
                numBaseGoodsReq.setSkuId(skuInnerId);
                numBaseGoodsReq.setSkuNumber(1);
                goodsReqs.add(numBaseGoodsReq);
            }
            getStockByIdGoodsReq.setSkuNumInfoList(goodsReqs);
            VopGoodsGetNewStockByIdResponse stockByIdResponse = vopGoodsStockService.getNewStockById(getStockByIdGoodsReq);
            List<GetStockByIdGoodsResp> getStockByIdGoodsResps = stockByIdResponse.getOpenRpcResult().getResult();
            getStockByIdGoodsResps.forEach(getStockByIdGoodsResp -> {
                AreaStockInfoResp resp = new AreaStockInfoResp();
                resp.setArrivalDays(getStockByIdGoodsResp.getArrivalDays());
                resp.setRemainNum(getStockByIdGoodsResp.getRemainNum());
                resp.setStockStateDesc(getStockByIdGoodsResp.getStockStateDesc());
                resp.setStockStateType(getStockByIdGoodsResp.getStockStateType());
                resp.setSkuId(skuInnerIdMap.get(String.valueOf(getStockByIdGoodsResp.getSkuId())).getId());
                respList.add(resp);
            });

            List<SkuPriceRespDTO> skuPriceRespDTOS = vopGoodsService.getVopSellPrice(stockInfoReq.getSkuIds());
            Map<Long, SkuPriceRespDTO> skuPriceMap = convertMap(skuPriceRespDTOS, SkuPriceRespDTO::getSkuId);
            for (AreaStockInfoResp areaStockInfoResp : respList) {
                Long skuId = areaStockInfoResp.getSkuId();
                SkuPriceRespDTO skuPriceRespDTO = skuPriceMap.get(skuId);
                if(skuPriceRespDTO != null
                        && skuPriceRespDTO.getHasPromotion() != null
                        && skuPriceRespDTO.getHasPromotion()) {
                    areaStockInfoResp.setRemainNum(skuPriceRespDTO.getRemainNum());
                    int stockStateType = 33;
                    if(skuPriceRespDTO.getRemainNum() == 0){
                        stockStateType = 34;
                        areaStockInfoResp.setStockStateDesc("限购商品，已售罄");
                    }
                    areaStockInfoResp.setStockStateType(stockStateType);
                }
            }

            return respList;
        } else {
            List<ProductSkuStockDO> skuStockDOS = productSkuStockService.list(new LambdaQueryWrapperX<ProductSkuStockDO>().in(ProductSkuStockDO::getSkuId, stockInfoReq.getSkuIds()));
            skuStockDOS.forEach(skuStock -> {
                AreaStockInfoResp resp = new AreaStockInfoResp();
                resp.setSkuId(skuStock.getSkuId());
                if (skuStock == null || skuStock.getStock() < 1) {
                    resp.setStockStateDesc("无货");
                    resp.setStockStateType(34);
                } else {
                    resp.setStockStateDesc("有货");
                    resp.setStockStateType(33);
                }
                respList.add(resp);
            });
        }
        return respList;
    }

    public void deleteGoods(Long jdSkuId, Long supplierId) {
        ProductSkuDO productSkuDO = productSkuService.getSimpleSkuByInnerIdAndSupplierId(String.valueOf(jdSkuId), supplierId);
        if(productSkuDO != null){
            List<ProductSkuDO> productSkuDOS = productSkuService.getSkuListBySpuId(productSkuDO.getSpuId());
            if(productSkuDOS.size() <= 1){
                if(tradeCartService.hasProductSku(productSkuDO.getId())
                        || tradeOrderService.hasProductSku(productSkuDO.getId())
                        || tradeCollectService.hasProductSku(productSkuDO.getId())
                        || tradeCartService.hasProductSku(productSkuDO.getId())){
                    productSpuService.disableSpu(productSkuDO.getSpuId());
                }
                else {
                    productSpuService.deleteSpu(productSkuDO.getSpuId());
                }
            }
            else {
                if(tradeCartService.hasProductSku(productSkuDO.getId())
                        || tradeOrderService.hasProductSku(productSkuDO.getId())
                        || tradeCollectService.hasProductSku(productSkuDO.getId())
                        || tradeCartService.hasProductSku(productSkuDO.getId())){
                    productSkuDO.setStatus(0);
                    productSkuService.saveOrUpdate(productSkuDO);
                }
                else {
                    productSkuService.deleteSku(productSkuDO.getId());
                }
            }
        }
    }

}
