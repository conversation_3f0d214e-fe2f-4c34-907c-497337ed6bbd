package cn.iocoder.yudao.module.mall.product.convert.spu;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.product.api.spu.dto.ProductSpuRespDTO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo.*;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商品 SPU Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSpuConvert {

    ProductSpuConvert INSTANCE = Mappers.getMapper(ProductSpuConvert.class);

    ProductSpuDO convert(ProductSpuCreateReqVO bean);

    ProductSpuDO convert(ProductSpuUpdateReqVO bean);

    ProductSpuDO convert(ProductSpuStatusUpdateReqVO bean);

    ProductSpuDO convert(ProductSpuShowStatusUpdateReqVO bean);

    List<ProductSpuDO> convertSpus(List<ProductSpuShowStatusUpdateReqVO> list);

    List<ProductSpuDO> convertList(List<ProductSpuDO> list);

    PageResult<ProductSpuRespVO> convertPage(PageResult<ProductSpuDO> page);

    ProductSpuPageReqVO convert(AppProductSpuPageReqVO bean);

    List<ProductSpuRespDTO> convertList2(List<ProductSpuDO> list);

    List<ProductSpuSimpleRespVO> convertList02(List<ProductSpuDO> list);

    default AppProductSpuDetailRespVO convert(ProductSpuDO spu, List<ProductSkuDO> skus) {
        AppProductSpuDetailRespVO spuVO = convert02(spu).setSalesCount(spu.getSalesCount());
        spuVO.setSkus(convertList03(skus));
        return spuVO;
    }

    AppProductSpuDetailRespVO convert02(ProductSpuDO spu);

    List<AppProductSpuDetailRespVO.Sku> convertList03(List<ProductSkuDO> skus);

    PageResult<AppProductSpuPageItemRespVO> convertPage02(PageResult<ProductSpuDO> page);

    ProductSpuDetailRespVO convert03(ProductSpuDO spu);

    default ProductSpuDO convertDO(ProductSpuOpenVO productSpuOpenVO, SupplierDO supplierDO) {
        if(productSpuOpenVO.getSpuId() != null && productSpuOpenVO.getSpuId() <= 0) {
            productSpuOpenVO.setSpuId(null);
        }
        ProductSpuDO productSpuDO = convertDO(productSpuOpenVO);
        if (productSpuDO != null) {
            productSpuDO.setSupplierId(supplierDO.getId());
            productSpuDO.setSupplierName(supplierDO.getName());
            productSpuDO.setSupplierType(supplierDO.getType());
        }
        return productSpuDO;
    }

    @Mappings({
            @Mapping(source = "spuId", target = "id")
    })
    ProductSpuDO convertDO(ProductSpuOpenVO productSpuOpenVO);


    @Mappings({
            @Mapping(source = "id", target = "spuId")
    })
    ProductSpuOpenRespVO convertVO(ProductSpuDO productSpuDO);

}
