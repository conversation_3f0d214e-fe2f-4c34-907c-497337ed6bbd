package cn.iocoder.yudao.module.mall.product.controller.admin.spec;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ConfigProductCategorySpecCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ConfigProductCategorySpecRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ConfigProductCategorySpecUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.convert.spec.ProductSpecConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategorySpecDO;
import cn.iocoder.yudao.module.mall.product.service.category.ConfigProductCategorySpecService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 商品规格")
@RestController
@RequestMapping("/product/config/category-spec")
@Validated
public class ConfigProductCategorySpecController {

    @Resource
    private ConfigProductCategorySpecService specService;

    @PostMapping("/create")
    @Operation(summary = "创建商品规格")
    @PreAuthorize("@ss.hasPermission('config:product-category:spec:create')")
    public CommonResult<Long> createSpec(@Valid @RequestBody ConfigProductCategorySpecCreateReqVO createReqVO) {
        return success(specService.createSpec(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品规格")
    @PreAuthorize("@ss.hasPermission('config:product-category:spec:update')")
    public CommonResult<Boolean> updateSpec(@Valid @RequestBody ConfigProductCategorySpecUpdateReqVO updateReqVO) {
        specService.updateSpec(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品规格")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('config:product-category:spec:delete')")
    public CommonResult<Boolean> deleteSpec(@RequestParam("id") Long id) {
        specService.deleteSpec(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品规格")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('config:product-category:spec:query')")
    public CommonResult<ConfigProductCategorySpecRespVO> getSpec(@RequestParam("id") Long id) {
        ConfigProductCategorySpecDO spec = specService.getSpec(id);
        return success(ProductSpecConvert.INSTANCE.convert(spec));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商品规格列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('config:product-category:spec:query')")
    public CommonResult<List<ConfigProductCategorySpecRespVO>> getSpecList(@RequestParam("ids") Collection<Long> ids) {
        List<ConfigProductCategorySpecDO> list = specService.getSpecList(ids);
        return success(ProductSpecConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品规格分页")
    @PreAuthorize("@ss.hasPermission('config:product-category:spec:query')")
    public CommonResult<PageResult<ConfigProductCategorySpecRespVO>> getSpecPage(@Valid ProductSpecPageReqVO pageVO) {
        PageResult<ConfigProductCategorySpecDO> pageResult = specService.getSpecPage(pageVO);
        return success(ProductSpecConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list-by-category")
    @Operation(summary = "获得商品规格")
    @PreAuthorize("@ss.hasAnyPermissions('config:product-category:spec:query', 'product:category:query')")
    public CommonResult<List<ConfigProductCategorySpecRespVO>> getSpecListByCategory(Long productCategoryId) {
        List<ConfigProductCategorySpecDO> list = specService.getSpecList4product(productCategoryId);
        return success(ProductSpecConvert.INSTANCE.convertList(list));
    }


}
