package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 单据
 * <AUTHOR>
 * @date 2023/7/24
 */
@Data
public class VoucherDTO {

    /**
     * 模块代码（由审批系统分配）
     */
    @JsonProperty("mkdm")
    private String moduleName;

    /**
     * 业务编号 自定义2-4位大写英文，提交登记
     */
    @JsonProperty("ywbh")
    private String businessCode;

    /**
     * 业务流水号 自定义不重复的流水号
     */
    @JsonProperty("ywlsh")
    private String businessNo;

    /**
     * 申请人名称
     */
    @JsonProperty("sqr")
    private String applicantName;

    /**
     * 申请人编号
     */
    @JsonProperty("sqrbh")
    private String applicantNo;

    /**
     * 申请日期 最大长度8位 如“20210625”
     */
    @JsonProperty("sqrq")
    private String applicantDate;

    /**
     * 申请时间 如“2021-06-25”
     */
    @JsonProperty("sqsj")
    private String applicantTime;

    /**
     * 申请内容摘要 	可选一般字符串或Json格式
     */
    @JsonProperty("sqnr")
    private JSONObject contentOverview;

    /**
     * 当前审批级别 初始默认为1
     */
    @JsonProperty("spjb")
    private String approvalLevel;

    /**
     * 	审批状态 初始默认为0，0未处理  1审批通过  2驳回
     */
    @JsonProperty("spzt")
    private String approvalStatus;

    /**
     * 审批标题 	在列表页面中审批单据要显示的信息，Json格式
     */
    @JsonProperty("spbt")
    private JSONObject approvalTitle;

    /**
     * 回写URL 	用于审批结果回写（详见异步通知说明）
     */
    @JsonProperty("cf5")
    private String notifyUrl;
}
