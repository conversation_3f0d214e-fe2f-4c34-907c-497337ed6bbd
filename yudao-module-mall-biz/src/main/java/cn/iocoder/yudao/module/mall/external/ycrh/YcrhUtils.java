package cn.iocoder.yudao.module.mall.external.ycrh;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.module.mall.external.ycrh.dto.PushBillReqDTO;
import cn.iocoder.yudao.module.mall.external.ycrh.dto.VoucherInfoDTO;
import cn.iocoder.yudao.module.mall.external.ycrh.enums.YcrhFileTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业财融合接口工具类
 */
@Slf4j
public class YcrhUtils {

    private static final String VOUCHER_NAME_PATTERN = "JYH_00_%d_ZC_%s_%s.%s";

    /**
     * 生成签名
     * @param jsonObj 接口请求数据
     * @param appSecret 应用密钥
     * @return
     */
    public static String generateSign(JSONObject jsonObj, String appSecret) {
        String source = jsonObj.toString();
        log.info("业财融合接口签名源字符串: {}", source);
        String signSource = appSecret + source;
        return DigestUtil.md5Hex(signSource).toUpperCase();
    }

    /**
     * Bean转成Map，null属性会转成空符串
     * @param bean
     * @return
     */
    public static Map<String, Object> bean2Map(Object bean) {
        CopyOptions copyOpts = CopyOptions.create().setFieldValueEditor((name, value) -> value == null ? "" : value);
        return BeanUtil.beanToMap(bean, new HashMap<>(), copyOpts);
    }

    /**
     * 生成凭证文件名
     * @param bizNo 流水号
     * @param index 序号
     * @param extension 扩展名 如 pdf
     * @return
     */
    public static String generateVoucherName(String bizNo, int index, String extension) {
        int year = LocalDate.now().getYear();
        String seqStr = StringUtils.leftPad(index + "", 5, "0");
        return String.format(VOUCHER_NAME_PATTERN, year, bizNo, seqStr, extension);
    }

    /**
     * 构建结算凭证数组
     * @param voucherMap
     * @return
     */
    public static List<VoucherInfoDTO> buildVoucherArray(Map<String, YcrhFileTypeEnum> voucherMap) {
        List<VoucherInfoDTO> list = new ArrayList<>();
        if(CollUtil.isNotEmpty(voucherMap)) {
            voucherMap.forEach((key, value) -> {
                list.add(buildVoucherObj(key, value));
            });
        }

        return list;
    }

    /**
     * 构建结算凭证对象
     * @param voucherName
     * @param type
     * @return
     */
    public static VoucherInfoDTO buildVoucherObj(String voucherName, YcrhFileTypeEnum type) {
        if(StrUtil.isBlank(voucherName)) {
            return null;
        }

        VoucherInfoDTO obj = new VoucherInfoDTO();
        obj.setVoucherName(voucherName);
        obj.setVoucherType(type.getType());
        return obj;
    }

    public static void main(String[] args) {
        PushBillReqDTO bean = new PushBillReqDTO();
        Map<String, Object> map = BeanUtil.beanToMap(bean);
        System.out.println("map1=" + map);
        map = MapUtil.map(map, (k, v) -> v == null ? "" : v);
        System.out.println("map2=" + map);
        String filePath = "http://oss.suning.com/esps/invoice_pdf/25327000000280386654788570b9-edc9-3dfd-85cb-29b257f34c0f.pdf";
        String extension = FilenameUtils.getExtension(filePath);
        System.out.println("====" + generateVoucherName("123L", 2, extension));
    }

}
