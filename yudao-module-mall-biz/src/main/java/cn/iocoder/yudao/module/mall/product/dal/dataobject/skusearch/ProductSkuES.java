package cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * sku ES商品
 */
@Data
@EqualsAndHashCode()
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "es_product_sku")
public class ProductSkuES implements Serializable {

    @Id
    @Field(type = FieldType.Keyword)
    private String skuId;
    /**
     * 随机数，方便排序，防止搜索结果全是某个供应商的情况
     */
    @Field(type = FieldType.Long)
    private Integer randSeq;

    @Field(type = FieldType.Keyword)
    private String skuInnerId;

    @Field(type = FieldType.Long)
    private Long tenantId;

    @Field(type = FieldType.Long)
    private Long supplierId;

    @Field(type = FieldType.Integer)
    private Integer supplierWeight;

    @Field(type = FieldType.Text,analyzer = "ik_max_word")
    private String supplierName;

    @Field(type = FieldType.Long)
    private Long spuId;

    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String spuName;

    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String skuName;

    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String sellPoint;

    @Field(type = FieldType.Long)
    private Long categoryId1;

    @Field(type = FieldType.Keyword)
    private String categoryName1;

    @Field(type = FieldType.Long)
    private Long categoryId2;

    @Field(type = FieldType.Keyword)
    private String categoryName2;

    @Field(type = FieldType.Long)
    private Long categoryId3;

    @Field(type = FieldType.Keyword)
    private String categoryName3;

    /**
     * 商品分类ID路径, 以中逗号分隔, 如 101,1006,10007
     */
    @Field(type = FieldType.Keyword)
    private String categoryIdPath;

    @Field(type = FieldType.Long)
    private Long brandId;

    @Field(type = FieldType.Keyword)
    private String brandName;

    @Field(type = FieldType.Integer)
    private Integer status;

    @Field(type = FieldType.Long)
    private List<Long> tagIds;

    @Field(type = FieldType.Nested)
    private List<ProductSkuSpecES> spuSpecs; // 嵌套对象，存储SPU规格

    /**
     * 规格值
     */
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private List<String> specValues;

    @Field(type = FieldType.Scaled_Float, scalingFactor = 100)
    private BigDecimal salePrice;

    @Field(type = FieldType.Integer)
    private Integer salesCount;


}
