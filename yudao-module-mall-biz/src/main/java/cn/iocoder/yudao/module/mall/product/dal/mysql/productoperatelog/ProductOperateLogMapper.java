package cn.iocoder.yudao.module.mall.product.dal.mysql.productoperatelog;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productoperatelog.ProductOperateLogDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo.*;

/**
 * 商品操作日志记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductOperateLogMapper extends BaseMapperX<ProductOperateLogDO> {

    default PageResult<ProductOperateLogDO> selectPage(ProductOperateLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductOperateLogDO>()
                .eqIfPresent(ProductOperateLogDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(ProductOperateLogDO::getSpuId, reqVO.getSpuId())
                .eqIfPresent(ProductOperateLogDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(ProductOperateLogDO::getUserId, reqVO.getUserId())
                .eqIfPresent(ProductOperateLogDO::getOperateType, reqVO.getOperateType())
                .betweenIfPresent(ProductOperateLogDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductOperateLogDO::getId));
    }

    default List<ProductOperateLogDO> selectList(ProductOperateLogExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ProductOperateLogDO>()
                .eqIfPresent(ProductOperateLogDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(ProductOperateLogDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(ProductOperateLogDO::getUserId, reqVO.getUserId())
                .eqIfPresent(ProductOperateLogDO::getOperateType, reqVO.getOperateType())
                .eqIfPresent(ProductOperateLogDO::getContent, reqVO.getContent())
                .betweenIfPresent(ProductOperateLogDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductOperateLogDO::getId));
    }

}
