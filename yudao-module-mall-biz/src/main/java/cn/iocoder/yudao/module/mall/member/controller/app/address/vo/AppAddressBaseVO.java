package cn.iocoder.yudao.module.mall.member.controller.app.address.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 用户收件地址 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class AppAddressBaseVO {

    /**
     * 收件人名称
     */
    @Schema(description = "收件人名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "收件人名称不能为空")
    private String name;

    /**
     * 手机号
     */
    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "手机号不能为空")
    private String mobile;

    /**
     * 省编号
     */
    @Schema(description = "省编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "省编号不能为空")
    private Long provinceId;

    /**
     * 省名称
     */
    @Schema(description = "省名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String provinceName;

    /**
     * 市编号
     */
    @Schema(description = "市编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "市编号不能为空")
    private Long cityId;

    /**
     * 市名称
     */
    @Schema(description = "市名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cityName;

    /**
     * 区县编号
     */
    @Schema(description = "区县编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "区县编号不能为空")
    private Long countyId;

    /**
     * 区县名称
     */
    @Schema(description = "区县名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "区县名称不能为空")
    private String countyName;

    /**
     * 乡镇编号
     */
    @Schema(description = "乡镇编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long townId;

    /**
     * 乡镇名称
     */
    @Schema(description = "乡镇名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String townName;

    /**
     * 邮编
     */
    @Schema(description = "邮编", requiredMode = Schema.RequiredMode.REQUIRED)
    private String consigneeZip;

    /**
     * 收件详细地址
     */
    @Schema(description = "收件详细地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "收件详细地址不能为空")
    private String consigneeAddress;

    /**
     * 收件详细地址
     */
    private String detailAddress;

    /**
     * 是否默认地址
     */
    @Schema(description = "是否默认地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否默认地址不能为空")
    private Boolean defaulted;

    /**
     * 别名
     */
    @Schema(description = "别名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String alias;

    /**
     * 固定电话
     */
    @Schema(description = "固定电话", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String telephone;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String email;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳")
    private Long timestamp;

}
