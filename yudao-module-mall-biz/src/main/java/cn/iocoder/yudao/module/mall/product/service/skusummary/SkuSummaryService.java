package cn.iocoder.yudao.module.mall.product.service.skusummary;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.mall.product.controller.admin.skusummary.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusummary.SkuSummaryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 供应商上架商品数统计 Service 接口
 *
 * <AUTHOR>
 */
public interface SkuSummaryService extends IService<SkuSummaryDO> {

    /**
     * 创建供应商上架商品数统计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSkuSummary(@Valid SkuSummaryCreateReqVO createReqVO);

    /**
     * 更新供应商上架商品数统计
     *
     * @param updateReqVO 更新信息
     */
    void updateSkuSummary(@Valid SkuSummaryUpdateReqVO updateReqVO);

    /**
     * 删除供应商上架商品数统计
     *
     * @param id 编号
     */
    void deleteSkuSummary(Long id);

    /**
     * 获得供应商上架商品数统计
     *
     * @param id 编号
     * @return 供应商上架商品数统计
     */
    SkuSummaryDO getSkuSummary(Long id);

    /**
     * 获得供应商上架商品数统计列表
     *
     * @param ids 编号
     * @return 供应商上架商品数统计列表
     */
    List<SkuSummaryDO> getSkuSummaryList(Collection<Long> ids);

    /**
     * 获得供应商上架商品数统计分页
     *
     * @param pageReqVO 分页查询
     * @return 供应商上架商品数统计分页
     */
    PageResult<SkuSummaryDO> getSkuSummaryPage(SkuSummaryPageReqVO pageReqVO);

    /**
     * 获得供应商上架商品数统计列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 供应商上架商品数统计列表
     */
    List<SkuSummaryDO> getSkuSummaryList(SkuSummaryExportReqVO exportReqVO);

    /**
     * 统计截止到今天0点的供应商sku上下架总数量
     */
    List<SkuSummaryDO> saveSupplierSkuSummary();

    List<SkuSummaryDaysRespVO> supplierSkuSummary(SkuSummaryDaysReqVO skuSummaryDaysReqVO);
}
