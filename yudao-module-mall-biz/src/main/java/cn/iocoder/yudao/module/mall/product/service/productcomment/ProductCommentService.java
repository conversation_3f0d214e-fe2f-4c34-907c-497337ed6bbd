package cn.iocoder.yudao.module.mall.product.service.productcomment;

import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.ProductCommentAuditReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.ProductCommentPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 商品评价 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductCommentService {

    /**
     * 创建商品评价
     *
     * @param createReqVO 创建信息
     * @param memberId 会员id
     * @return 编号
     */
    Long createComment(@Valid AppProductCommentCreateReqVO createReqVO, Long memberId);

    /**
     * 更新商品评价
     *
     * @param updateReqVO 更新信息
     */
    void updateComment(@Valid AppProductCommentUpdateReqVO updateReqVO);

    /**
     * 删除商品评价
     *
     * @param id 编号
     */
    void deleteComment(Long id);

    /**
     * 获得商品评价
     *
     * @param id 编号
     * @return 商品评价
     */
    ProductCommentDO getComment(Long id);

    /**
     * 获得商品评价列表
     *
     * @param ids 编号
     * @return 商品评价列表
     */
    List<ProductCommentDO> getCommentList(Collection<Long> ids);

    /**
     * 获得商品评价分页
     *
     * @param pageReqVO 分页查询
     * @return 商品评价分页
     */
    PageResult<ProductCommentDO> getCommentPage(AppProductCommentPageReqVO pageReqVO);

    PageResult<ProductCommentDO> getProductCommentPage(AppProductCommentOrderPageReqVO pageReqVO);


    /**
     * 获得商品评价分页
     *
     * @param pageReqVO 分页查询
     * @return 商品评价分页
     */
    PageResult<ProductCommentDO> getCommentPage(ProductCommentPageReqVO pageReqVO);

    /**
     * 获得商品评价列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 商品评价列表
     */
    List<ProductCommentDO> getCommentList(AppProductCommentExportReqVO exportReqVO);


    /**
     * 审批
     * @param reqVO
     * @return
     */
    void updateAuditStatus(ProductCommentAuditReqVO reqVO);

    /**
     * 点赞
     * @param id
     */
    void like(Long id);

}
