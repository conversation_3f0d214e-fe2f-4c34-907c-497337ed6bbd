package cn.iocoder.yudao.module.mall.product.job;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.ProductSkuES;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopConfigDO;
import cn.iocoder.yudao.module.mall.vop.service.VopAccessTokenService;
import cn.iocoder.yudao.module.mall.vop.service.VopConfigService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 商品同步es
 * <AUTHOR>
 * @date 2023/12/8
 */
@Service
@Slf4j
@TenantJob
public class ProductSkuJob {

    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private VopGoodsBridgeService vopGoodsBridgeService;
    @Resource
    private VopAccessTokenService vopAccessTokenService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private VopConfigService vopConfigService;

    /**
     * 获取job参数
     * @param defaultVal 缺省参数值
     * @return
     */
    private String getJobParam(String defaultVal) {
        return StrUtil.blankToDefault(XxlJobHelper.getJobParam(), defaultVal);
    }

    @XxlJob("syncAllProductSku")
    public ReturnT<String> syncAllProductSku() {
        log.info("定时任务-syncAllProductSku-开始");
        RLock lock = redissonClient.getLock("syncAllProductSku-lock");
        try {
            boolean isLocked = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if(isLocked) {
                boolean existsIndex = elasticsearchTemplate.indexOps(ProductSkuES.class).exists();
                if(!existsIndex) {
                    //创建索引
                    elasticsearchTemplate.indexOps(ProductSkuES.class).create();
                    //创建mapping
                    elasticsearchTemplate.indexOps(ProductSkuES.class).putMapping();
                }
            }
            productSkuService.syncAllProductSkuIndex2ES();
            log.info("定时任务-syncAllProductSku-结束");
        } catch (Exception e) {
            log.error("syncAllProductSku fail", e);
        } finally {
            if(lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("syncUpdateProductSku")
    public ReturnT<String> syncUpdateProductSku() {
        String jobParam = getJobParam("30");
        log.info("start syncUpdateProductSku job: {}", jobParam);
        try {
            log.info("定时任务-syncUpdateProductSku");
            int days = Integer.parseInt(jobParam);
            productSkuService.syncProductSkuIndex2ESRecently(days);
        } catch (Exception e) {
            log.error("syncUpdateProductSku fail", e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("syncVopProductByCategory")
    public ReturnT<String> syncVopProductByCategory() {
        try {
            log.debug("定时任务-syncVopProduct开始：{}", TenantContextHolder.getTenantId());

            String jobParam = getJobParam("0,100");
            String[] params = jobParam.split(",");
            List<Long> categoryIds = null;
            if(!"0".equals(params[0])){
                categoryIds = Arrays.stream(params)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                categoryIds.remove(categoryIds.size() - 1);
            }
            Long maxPageSize = Long.valueOf(params[params.length - 1]);
            vopGoodsBridgeService.syncVopProductByCategory(categoryIds, maxPageSize);
        } catch (Exception e) {
            log.error("定时任务-syncVopProduct失败：{}，error：", TenantContextHolder.getTenantId(), e);
        }
        log.debug("定时任务-syncVopProduct结束：{}", TenantContextHolder.getTenantId());
        return ReturnT.SUCCESS;
    }

    /**
     * 判断京东供应商及VOP状态是否正常
     * @return
     */
    private boolean isSupplierAndVopOk() {
        SupplierDO supplierDO = supplierService.getSupplierJD();
        if(supplierDO == null) {
            return false;
        }
        if(!supplierDO.isEnabled()) {
            return false;
        }

        return isVopOk();
    }

    private boolean isVopOk() {
        return vopAccessTokenService.isTenantVopAccessTokenOk();
    }
}
