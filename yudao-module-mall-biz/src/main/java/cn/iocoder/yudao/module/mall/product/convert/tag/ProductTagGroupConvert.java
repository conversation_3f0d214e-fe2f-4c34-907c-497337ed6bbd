package cn.iocoder.yudao.module.mall.product.convert.tag;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagGroupDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMultiMap;

/**
 * 标签分组 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductTagGroupConvert {

    ProductTagGroupConvert INSTANCE = Mappers.getMapper(ProductTagGroupConvert.class);

    ProductTagGroupDO convert(ProductTagGroupCreateReqVO bean);

    ProductTagGroupDO convert(ProductTagGroupUpdateReqVO bean);

    ProductTagGroupRespVO convert(ProductTagGroupDO bean);

    List<ProductTagGroupRespVO> convertList(List<ProductTagGroupDO> list);

    PageResult<ProductTagGroupRespVO> convertPage(PageResult<ProductTagGroupDO> page);

    ProductTagGroupSimpleVO convert01(ProductTagGroupDO bean);

    ProductTagSimpleVO convert02(ProductTagDO bean);

    List<ProductTagGroupSimpleVO> convertList01(List<ProductTagGroupDO> groupList);

    List<ProductTagSimpleVO> convertList02(List<ProductTagDO> tagList);

    default List<ProductTagGroupSimpleVO> convertList(List<ProductTagGroupDO> groupList, List<ProductTagDO> tagList) {
        List<ProductTagGroupSimpleVO> list = new ArrayList<>();
        if(CollUtil.isEmpty(groupList)) {
            return list;
        }
        Map<Long, List<ProductTagDO>> tagMap = convertMultiMap(tagList, ProductTagDO::getGroupId);
        groupList.forEach(group -> {
            ProductTagGroupSimpleVO vo = convert01(group);
            vo.setTagList(convertList02(tagMap.get(group.getId())));
            list.add(vo);
        });
        return list;
    }

}
