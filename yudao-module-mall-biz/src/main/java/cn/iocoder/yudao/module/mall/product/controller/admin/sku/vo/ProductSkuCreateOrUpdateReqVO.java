package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecValueReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 商品 SKU 创建/更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSkuCreateOrUpdateReqVO extends ProductSkuBaseVO {

    @Schema(description = "主键", example = "1024")
    private Long id;

    @Schema(description = "库存", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "库存不能为空")
    private Integer stock;

    @Schema(description = "预警库存", example = "1")
    private Integer warnStock;

    @Schema(description = "供应商skuId", example = "内部生成")
    private String skuInnerId;

    /**
     * 规格数组
     */
    private List<ProductSpecValueReqVO> specValueList;

}
