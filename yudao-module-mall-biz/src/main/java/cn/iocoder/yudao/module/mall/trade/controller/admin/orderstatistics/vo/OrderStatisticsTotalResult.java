package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单统计总数
 */
@Data
public class OrderStatisticsTotalResult {

    /**
     * 订单总数
     */
    private Integer orderNum;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单状态 1-未确认 2-已确认 3-已发货 4-已送达 5-已签收 6-售后中 7-售后完成 8-已完成 9-已取消  21-已结算 20-未结算
     */
    private Integer  status;



}
