package cn.iocoder.yudao.module.mall.member.convert.user;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.member.api.user.dto.MemberUserRespDTO;
import cn.iocoder.yudao.module.mall.member.controller.admin.user.vo.*;
import cn.iocoder.yudao.module.mall.member.controller.app.user.vo.AppUserInfoRespVO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppTradeOrderPageReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserConvert {

    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    AppUserInfoRespVO convert(MemberUserDO bean);

    MemberUserRespDTO convert2(MemberUserDO bean);

    List<MemberUserRespDTO> convertList2(List<MemberUserDO> list);

    PageResult<UserInfoRespVO> convertPage(PageResult<MemberUserDO> userPage);

    UserInfoDetailRespVO convert02(MemberUserDO bean);

    MemberUserDO convert(MemberUserCreateReqVO bean);

    MemberUserDO convert(MemberUserUpdateReqVO bean);

    AppTradeOrderPageReqVO convertVO(UserOrderPageReqVo bean);

}
