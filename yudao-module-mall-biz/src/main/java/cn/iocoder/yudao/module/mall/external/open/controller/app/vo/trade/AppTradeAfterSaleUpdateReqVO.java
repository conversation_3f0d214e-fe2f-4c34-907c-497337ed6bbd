package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 供应商售后单信息状态更新
 */
@Data
public class AppTradeAfterSaleUpdateReqVO {

    /**
     * 售后ID
     */
    @NotNull(message = "afterSaleId不能为空")
    private Long afterSaleId;
    /**
     * 更新状态：
     * 20: 同意退款申请
     * 40: 确认收货
     * 62: 拒绝退款申请
     * 63: 拒绝收货
     */
    @NotNull(message = "status不能为空")
    private Integer status;
    /**
     * 备注，拒绝申请或拒绝收货时为必填
     */
    @Size(max = 200, message = "memo长度不能超过200")
    private String memo;

}
