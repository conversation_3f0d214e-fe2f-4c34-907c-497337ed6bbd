package cn.iocoder.yudao.module.mall.external.open.controller.app;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.ObjectUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import cn.iocoder.yudao.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import cn.iocoder.yudao.module.mall.annotation.OrderOperateLog;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.basisconfig.InvoiceConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.basisconfig.InvoiceConfigService;
import cn.iocoder.yudao.module.mall.external.open.annotation.OpenAuth;
import cn.iocoder.yudao.module.mall.external.open.context.OpenContextHolder;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.common.AppOpenIdReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade.AppOrderSplitReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade.AppTradeAfterSaleUpdateReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade.AppTradeOrderIdReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade.AppTradeOrderSendInvoiceReqVO;
import cn.iocoder.yudao.module.mall.member.convert.address.AddressConvert;
import cn.iocoder.yudao.module.mall.trade.controller.admin.aftersale.vo.TradeAfterSaleDisagreeReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.aftersale.vo.TradeAfterSaleRefuseReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.message.vo.AppTradeMessageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.message.vo.AppTradeMessageRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppOpenAddressRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppTradeOrderDetailOpenRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppTradeOrderPageReqVO;
import cn.iocoder.yudao.module.mall.trade.convert.message.TradeMessageConvert;
import cn.iocoder.yudao.module.mall.trade.convert.order.TradeOrderConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.aftersale.TradeAfterSaleDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.message.TradeMessageDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.trade.enums.aftersale.TradeAfterSaleStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.OrderOperateTypeEnum;
import cn.iocoder.yudao.module.mall.trade.service.aftersale.TradeAfterSaleService;
import cn.iocoder.yudao.module.mall.trade.service.message.TradeMessageService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.trade.service.settle.OrderSettleService;
import com.alibaba.excel.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.module.mall.trade.enums.ErrorCodeConstants.*;

/**
 * 开放API - 订单"
 *
 * <AUTHOR>
 * @date 2023/9/12
 */
@Tag(name = "开放API - 订单")
@RestController
@RequestMapping("/mall/open/order")
@Validated
@Slf4j
@OpenAuth
public class AppOpenOrderController {

    @Resource
    private TradeOrderService tradeOrderService;
    @Resource
    private TradeMessageService tradeMessageService;
    @Resource
    private TradeAfterSaleService tradeAfterSaleService;
    @Resource
    private InvoiceConfigService invoiceConfigService;
    @Resource
    private OrderSettleService orderSettleService;

    /**
     * 订单发货
     * @param reqVO 订单id
     * @return
     */
    @PostMapping("/delivery")
    @Operation(summary = "订单发货")
    @OrderOperateLog(content = "订单发货",type = OrderOperateTypeEnum.DO_DELIVERING, userName = "开放API", orderId = "#reqVO.orderId")
    public CommonResult<Boolean> orderDelivery(@Valid @RequestBody AppTradeOrderIdReqVO reqVO) {
        return success(tradeOrderService.updateOrderDelivery(OpenContextHolder.getSupplierId(),reqVO.getOrderId()));
    }

    /**
     * 订单寽投
     * @param reqVO 订单id
     * @return
     */
    @PostMapping("/deliveryDone")
    @Operation(summary = "订单寽投")
    @OrderOperateLog(content = "订单寽投",type = OrderOperateTypeEnum.DELIVER_DONE, userName = "开放API", orderId = "#reqVO.orderId")
    public CommonResult<Boolean> orderCheckAndReceive(@Valid @RequestBody AppTradeOrderIdReqVO reqVO) {
        return success(tradeOrderService.updateOrderDeliveryDone(OpenContextHolder.getSupplierId(),reqVO.getOrderId()));
    }

    /**
     * 查询订单详情
     *
     * @param reqVO
     * @return
     */
    @RateLimiter(count = 60, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class, openSupplier = true)
    @PostMapping("/detail")
    @Operation(summary = "查询订单详情")
    public CommonResult<AppTradeOrderDetailOpenRespVO> getOrderDetail(@Valid @RequestBody AppOpenIdReqVO reqVO) {
        Long supplierId = OpenContextHolder.getSupplierId();
        // 查询订单
        TradeOrderDO order = tradeOrderService.getOrder4Supplier(supplierId, reqVO.getId());
        AppOpenAddressRespVO address = AddressConvert.INSTANCE.convertAddress02(order);
        // 查询订单项
        List<TradeOrderItemDO> tradeOrderItemDOS = tradeOrderService.getOrderItemListByOrderId(order.getId());
        AppTradeOrderDetailOpenRespVO appTradeOrderDetailRespVO = TradeOrderConvert.INSTANCE.convert4(order, tradeOrderItemDOS);
        appTradeOrderDetailRespVO.setAddress(address);
        return success(appTradeOrderDetailRespVO);
    }


    /**
     * 查询订单详情分页
     *
     * @param reqVO
     * @return
     */
    @RateLimiter(count = 30, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class, openSupplier = true)
    @PostMapping("/detailPage")
    @Operation(summary = "查询订单详情分页")
    public CommonResult<PageResult<AppTradeOrderDetailOpenRespVO>> getOrderPage(@Valid @RequestBody AppTradeOrderPageReqVO reqVO) {
        Long supplierId = OpenContextHolder.getSupplierId();
        PageResult<TradeOrderDO> pageResult = tradeOrderService.getSupplierOrderPage(supplierId, reqVO);
        if (pageResult.getTotal() == 0) {
            return success(null);
        }
        // 查询订单项
        List<TradeOrderItemDO> orderItems = tradeOrderService.getOrderItemListByOrderId(
                convertSet(pageResult.getList(), TradeOrderDO::getId));
        PageResult<AppTradeOrderDetailOpenRespVO> result = TradeOrderConvert.INSTANCE.convertDetailOpenPage(pageResult, orderItems);
        return success(result);
    }

    /**
     * 售后状态更新
     *
     * @param reqVO
     * @return
     */
    @PostMapping("/after-sale/update")
    @Operation(summary = "售后状态更新")
    public CommonResult<?> commitOrderMessage(@Valid @RequestBody AppTradeAfterSaleUpdateReqVO reqVO) {
        if(StringUtils.isBlank(reqVO.getMemo()) && ObjectUtils.equalsAny(reqVO.getStatus(),
                TradeAfterSaleStatusEnum.SELLER_DISAGREE.getStatus(), TradeAfterSaleStatusEnum.SELLER_REFUSE.getStatus())) {
            throw exception(AFTER_SALE_MEMO_NULL);
        }

        Long supplierId = OpenContextHolder.getSupplierId();
        TradeAfterSaleDO afterSaleDO = tradeAfterSaleService.getAfterSale4Supplier(supplierId, reqVO.getAfterSaleId());
        if (afterSaleDO == null) {
            throw exception(AFTER_SALE_NOT_FOUND);
        }
        Long userId = supplierId * -1;
        if(ObjectUtil.equal(reqVO.getStatus(), TradeAfterSaleStatusEnum.SELLER_AGREE.getStatus())) {
            tradeAfterSaleService.agreeAfterSale(userId, reqVO.getAfterSaleId());
        } else if(ObjectUtil.equal(reqVO.getStatus(), TradeAfterSaleStatusEnum.SELLER_DISAGREE.getStatus())) {
            tradeAfterSaleService.disagreeAfterSale(userId, new TradeAfterSaleDisagreeReqVO()
                    .setId(reqVO.getAfterSaleId())
                    .setAuditReason(reqVO.getMemo()));
        } else if(ObjectUtil.equal(reqVO.getStatus(), TradeAfterSaleStatusEnum.SELLER_REFUSE.getStatus())) {
            tradeAfterSaleService.refuseAfterSale(userId, new TradeAfterSaleRefuseReqVO()
                    .setId(reqVO.getAfterSaleId())
                    .setRefuseMemo(reqVO.getMemo()));
        } else if(ObjectUtil.equal(reqVO.getStatus(), TradeAfterSaleStatusEnum.WAIT_REFUND.getStatus())) {
            tradeAfterSaleService.receiveAfterSale(userId, reqVO.getAfterSaleId());
        } else {
            throw exception(AFTER_SALE_UNKNOWN_STATUS);
        }

        return success(null);
    }

    /**
     * 查询订单消息
     *
     * @param reqVO
     * @return
     */
    @RateLimiter(count = 2, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class, openSupplier = true)
    @PostMapping("/messageList")
    @Operation(summary = "查询订单消息")
    public CommonResult<List<AppTradeMessageRespVO>> getOrderMessage(@Valid @RequestBody AppTradeMessageReqVO reqVO) {
        Long supplierId = OpenContextHolder.getSupplierId();
        List<TradeMessageDO> tradeMessageDOS = tradeMessageService.getUnReadMessageList(supplierId, reqVO, 100);
        return success(TradeMessageConvert.INSTANCE.convertList(tradeMessageDOS));
    }

    /**
     * 确认订单消息
     *
     * @param ids
     * @return
     */
    @PostMapping("/commitMessage")
    @Operation(summary = "确认订单消息")
    public CommonResult<?> commitOrderMessage(@RequestBody List<Long> ids) {
        Long supplierId = OpenContextHolder.getSupplierId();
        tradeMessageService.commitMessages(supplierId, ids);
        return success(null);
    }

    /**
     * 订单发票
     * @param reqVO
     * @return
     */
    @Idempotent(timeout = 60)
    @PostMapping("/sendInvoice")
    @Operation(summary = "订单发票")
    public CommonResult<?> sendInvoice(@RequestBody AppTradeOrderSendInvoiceReqVO reqVO) {
        Long supplierId = OpenContextHolder.getSupplierId();
        TradeOrderDO tradeOrderDO = tradeOrderService.getOrder4Supplier(supplierId, reqVO.getOrderId());
        Assert.assertNotNull("orderId错误", tradeOrderDO);
        InvoiceConfigDO invoiceConfig = invoiceConfigService.getInvoiceConfig();
        if(invoiceConfig == null) {
            log.error("发票配置不能为空...");
            throw new ServiceException(ErrorCodeConstants.INVOICE_APPLY_NOT_CONFIG);
        }

        orderSettleService.receiveInvoice4ThirdSupplier(tradeOrderDO, invoiceConfig, reqVO);
        return success(null);
    }

    /**
     * 拆单
     * @param reqVO
     * @return
     */
    @PostMapping("/split")
    @Operation(summary = "拆单")
    public CommonResult<List<Long>> split(@Valid @RequestBody AppOrderSplitReqVO reqVO) {
        List<Long> orderIds = tradeOrderService.splitOrder(reqVO);
        return success(orderIds);
    }

}
