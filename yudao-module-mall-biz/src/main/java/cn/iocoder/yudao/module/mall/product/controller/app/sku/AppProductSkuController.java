package cn.iocoder.yudao.module.mall.product.controller.app.sku;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import cn.iocoder.yudao.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.AssetsConfigDO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.*;
import cn.iocoder.yudao.module.mall.product.service.searchhistory.ProductSearchHistoryService;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.skusearch.ProductSkuElasticsearchService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderAssetsService;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsBrotherSkuReq;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsGetSkuDetailReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.mall.basis.dal.redis.RedisKeyConstants.MALL_PRODUCT_BROTHER_SKUS;
import static cn.iocoder.yudao.module.mall.basis.dal.redis.RedisKeyConstants.MALL_PRODUCT_DETAIL;

@Tag(name = "用户 APP - 商品 SKU")
@RestController
@RequestMapping("/product/sku")
@Validated
public class AppProductSkuController {

    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private ProductSkuElasticsearchService productSkuElasticsearchService;
    @Resource
    private ProductSearchHistoryService productSearchHistoryService;
    @Resource
    private TradeOrderAssetsService tradeOrderAssetsService;

    /**
     * 查询单个商品的详细信息，包含商品售卖单位、重量、产地、包装清单、主图、规格参数等
     *
     * @param skuDetailReq 商品详情请求参数
     * @return 包含商品详细信息的通用结果
     */
    @GetMapping("getSkuDetailInfo")
    @Operation(summary = "查询单个商品的详细信息，包含商品售卖单位、重量、产地、包装清单、主图、规格参数等")
    @RateLimiter(count = 300, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    @Cacheable(value = MALL_PRODUCT_DETAIL + "#1d", key = "#skuDetailReq.cacheKey")
    public CommonResult<AppSkuDetailInfo> getSkuDetailInfo(VopGoodsGetSkuDetailReq skuDetailReq) {
        // 获取商品详细信息
        AppSkuDetailInfo productSkuDetail = productSkuService.getSkuDetailInfo(skuDetailReq);
        // 如果用户未登录，设置价格为 -1
        productSkuService.setSalePriceIfNotLoggedIn(productSkuDetail);
        return CommonResult.success(productSkuDetail);
    }

    @GetMapping("getBrotherSkus")
    @Operation(summary = "获取兄弟商品")
    @RateLimiter(count = 300, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    @Cacheable(value = MALL_PRODUCT_BROTHER_SKUS + "#1d", key = "#brotherSkuReq.cacheKey")
    public CommonResult<List<BrotherSku>> getBrotherSkus(VopGoodsBrotherSkuReq brotherSkuReq) {
        List<BrotherSku> brotherSkuList = productSkuService.getBrotherSkus(brotherSkuReq);
        return CommonResult.success(brotherSkuList);
    }

    /**
     * 查询商品的库存状态，价格及轮播图片及兄弟商品
     *
     * @param productSkuStockReqVO 商品sku库存VO
     * @return 包含商品库存状态、价格及轮播图片的通用结果
     */
    @PostMapping("getSkuStockInfo")
    @Operation(summary = "查询商品的库存状态，价格及轮播图片等")
    public CommonResult<SkuStockInfo> getSkuStockInfo(@Valid @RequestBody AppProductSkuStockReqVO productSkuStockReqVO) {
        // 获取商品库存状态、价格及轮播图片
        SkuStockInfo skuStockInfo = productSkuService.getSkuStockInfo(productSkuStockReqVO);
        // 如果用户未登录，设置价格为 -1
        productSkuService.setSalePriceIfNotLoggedIn(skuStockInfo);
        return CommonResult.success(skuStockInfo);
    }

    /**
     * 查询单个商品相关的品目销量排行榜列表
     *
     * @param skuDetailReq 商品详情请求参数
     * @return 包含品目销量排行榜列表的通用结果
     */
    @GetMapping("getSkuSuggestList")
    @Operation(summary = "查询单个商品的品目销售排行榜")
    public CommonResult<List<?>> getSkuSuggestList(VopGoodsGetSkuDetailReq skuDetailReq) {
        // 调用 ProductSkuService 获取品目销量排行榜列表
        List<SkuGoodsPageItem> pageItems = productSkuService.getSkuSuggestList(skuDetailReq);
        // 如果用户未登录，设置价格为 -1
        productSkuService.setSalePriceIfNotLoggedIn(pageItems);
        return CommonResult.success(pageItems);
    }

    /**
     * 搜索商品，返回分页信息和列表
     *
     * @param searchReq 商品搜索请求参数
     * @return 包含分页信息和商品列表的通用结果
     */
    @RateLimiter(count = 30, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    @GetMapping("goodsSearchPageList")
    @Operation(summary = "搜索商品")
    public CommonResult<?> goodsSearchPageList(GoodsSearchReq searchReq) {
        return doGoodsSearchPageList(searchReq);
    }

    /**
     * 搜索商品，返回分页信息和列表
     *
     * @param searchReq 商品搜索请求参数
     * @return 包含分页信息和商品列表的通用结果
     */
    @RateLimiter(count = 30, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    @PostMapping("/v2/goodsSearchPageList")
    @Operation(summary = "搜索商品")
    public CommonResult<?> goodsSearchPageListV2(@RequestBody GoodsSearchReq searchReq) {
        return doGoodsSearchPageList(searchReq);
    }

    private CommonResult<?> doGoodsSearchPageList(GoodsSearchReq searchReq) {
        // 调用 ProductSkuElasticsearchService 进行商品搜索
        GoodsSearchPageResultResp resp = productSkuElasticsearchService.goodsSearchPageList(searchReq);
        // 如果用户未登录，设置价格为 -1
        productSkuService.setSalePriceIfNotLoggedIn(resp.getPageResult());
        // 添加搜索关键字到搜索历史
        productSearchHistoryService.addUserSearchHistory(getLoginUserId(), searchReq.getKeyword());
        return CommonResult.success(resp);
    }

    /**
     * 搜索商品，只返回商品列表
     *
     * @param searchReq 商品搜索请求参数
     * @return 包含商品列表的通用结果
     */
    @RateLimiter(count = 60, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    @GetMapping("goodsSearchSimpleList")
    @Operation(summary = "搜索商品")
    public CommonResult<List<?>> goodsSearchSimpleList(GoodsSearchReq searchReq) {
        // 调用 ProductSkuElasticsearchService 进行商品搜索
        GoodsSearchPageResultResp resp = productSkuElasticsearchService.goodsSearchPageList(searchReq);
        List<SkuGoodsPageItem> skuGoodsPageItems = resp.getPageResult().getList();
        // 如果需要登录才能查看价格，且用户未登录，设置价格为 -1
        productSkuService.setSalePriceIfNotLoggedIn(skuGoodsPageItems);
        return CommonResult.success(skuGoodsPageItems);
    }

    /**
     * 搜索商品，只返回商品列表
     *
     * @param searchReq 商品搜索请求参数
     * @return 包含商品列表的通用结果
     */
    @RateLimiter(count = 60, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    @GetMapping("/v2/goodsSearchSimpleList")
    @Operation(summary = "搜索商品")
    public CommonResult<?> goodsSearchSimpleListV2(GoodsSearchReq searchReq) {
        // 调用 ProductSkuElasticsearchService 进行商品搜索
        GoodsSearchPageResultResp resp = productSkuElasticsearchService.goodsSearchPageList(searchReq);
        // 如果用户未登录，设置价格为 -1
        productSkuService.setSalePriceIfNotLoggedIn(resp.getPageResult());
        return CommonResult.success(resp);
    }


    /**
     * 商品比价
     *
     * @param skuIds 商品 SKU 编号数组
     * @return 包含比价结果的通用结果
     */
    @RateLimiter(count = 10, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    @PostMapping("skuPriceCompare")
    @Operation(summary = "商品比价")
    @PreAuthenticated
    public CommonResult<List<SkuPriceCompareRespVO>> skuPriceCompare(@Valid @RequestBody List<Long> skuIds) {
        // 调用 ProductSkuService 进行商品比价
        return CommonResult.success(productSkuService.skuPriceCompare(skuIds));
    }

    /**
     * 商品固资检测
     *
     * @param reqVO 商品信息
     * @return 固资检测结果
     */
    @RateLimiter(count = 10, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    @PostMapping("checkAssets")
    @Operation(summary = "商品固资检测")
    @PreAuthenticated
    public CommonResult<Integer> checkAssets(@Valid @RequestBody AppProductAssetsCheckReqVO reqVO) {
        AssetsConfigDO configDO = tradeOrderAssetsService.assetsCheck(reqVO);
        return CommonResult.success(configDO == null ? 0 : 1);
    }

}

