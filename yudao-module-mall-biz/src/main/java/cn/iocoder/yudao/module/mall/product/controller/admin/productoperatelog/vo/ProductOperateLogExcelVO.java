package cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 商品操作日志记录 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ProductOperateLogExcelVO {

    @ExcelProperty("日志主键")
    private Long id;

    @ExcelProperty("订单id")
    private Long skuId;

    @ExcelProperty("供应商id")
    private Long supplierId;

    @ExcelProperty("用户编号")
    private Long userId;

    @ExcelProperty("操作分类")
    private Integer operateType;

    @ExcelProperty("操作内容")
    private String content;

    @ExcelProperty("创建时间")
    private Date createTime;

}
