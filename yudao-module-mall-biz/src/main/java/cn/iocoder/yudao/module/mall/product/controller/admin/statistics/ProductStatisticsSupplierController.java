package cn.iocoder.yudao.module.mall.product.controller.admin.statistics;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SupplierSkuSummaryRespVO;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 商品供应商统计")
@RestController
@RequestMapping("/product/statistics/supplier")
@Validated
public class ProductStatisticsSupplierController {
    @Resource
    private ProductSkuService productSkuService;

    /**
     * 统计供应商当前sku总数
     * @return
     */
    @GetMapping("/getSupplierSkuTotal")
    @Operation(summary = "统计供应商当前sku总数")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:supplier')")
    public CommonResult<List<SupplierSkuSummaryRespVO>> getSupplierSkuTotal() {
        List<SupplierSkuSummaryRespVO> supplierSkuSummaryRespVOS = productSkuService.getSupplierSkuTotal();
        return success(supplierSkuSummaryRespVOS);
    }

}
