package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.brand;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "开放API - 商品品牌分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppOpenProductBrandPageReqVO extends PageParam {

    @Schema(description = "品牌名称", example = "芋道")
    private String name;

}
