package cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
 * 运营区域 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ProductSeoCardBaseVO {

    @Schema(description = "区域名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "区域名称不能为空")
    private String name;

    @Schema(description = "显示标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "显示标题不能为空")
    private String title;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "背景图")
    private String imageUrl;

    @Schema(description = "区域类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "区域类型不能为空")
    private Integer type;

    @Schema(description = "区域布局", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "区域布局不能为空")
    private Integer layout;

    @Schema(description = "区域备注")
    private String memo;

    @Schema(description = "区域排序")
    private Integer sort;

    @Schema(description = "状态, 0-启用，1-禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态, 0-启用，1-禁用不能为空")
    private Integer status;

}
