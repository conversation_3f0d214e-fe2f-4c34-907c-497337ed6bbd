package cn.iocoder.yudao.module.mall.product.dal.dataobject.tag;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 标签SKU关联 DO
 *
 * <AUTHOR>
 */
@TableName("product_tag_sku")
@KeySequence("product_tag_sku_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductTagSkuDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 标签id
     */
    private Long tagId;
    /**
     * 平台商品SKU
     */
    private Long skuId;

}
