package cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.external.bigscreen.service.BigScreenService;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenProductCategoryProportionRespVO;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenSupplierProductProportionRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SkuCountCategorySummaryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.ProductStatisticsReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.ProductStatisticsRespVO;
import cn.iocoder.yudao.module.mall.product.service.productstatistics.ProductStatisticsService;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SkuSummaryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SupplierSkuSummaryRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 商品sku统计
 */
@Tag(name = "管理后台 - 商品sku统计")
@RestController
@RequestMapping("/product/statistics")
@Validated
@Slf4j
public class ProductStatisticsController {

    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private ProductStatisticsService productStatisticsService;
    @Resource
    private BigScreenService bigScreenService;

    /**
     * 商品数量概览统计
     * @param reqVO
     * @return
     */
    @GetMapping("/overview")
    @Operation(summary = "商品数量概览统计")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:query')")
    public CommonResult<ProductStatisticsRespVO> overview(@Valid ProductStatisticsReqVO reqVO) {
        return success(productStatisticsService.overview(reqVO));
    }

    /**
     * 商品分类占比
     * @return
     */
    @GetMapping("getProductCategoryProportion")
    @Operation(summary = "商品分类占比")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:query')")
    public CommonResult<List<BigScreenProductCategoryProportionRespVO>> getProductCategoryProportion() {
        List<BigScreenProductCategoryProportionRespVO> bigScreenProductCategoryProportionRespVOS = bigScreenService.getProductCategoryProportion();
        return CommonResult.success(bigScreenProductCategoryProportionRespVOS);
    }

    /**
     * 供应商商品占比
     * @return
     */
    @GetMapping("getSupplierProductProportion")
    @Operation(summary = "供应商商品占比")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:query')")
    public CommonResult<List<BigScreenSupplierProductProportionRespVO>> getSupplierProductProportion() {
        List<BigScreenSupplierProductProportionRespVO> bigScreenSupplierProductProportionRespVOS = bigScreenService.getSupplierProductProportion();
        return CommonResult.success(bigScreenSupplierProductProportionRespVOS);
    }

    /**
     * 统计当前sku总数
     * @return
     */
    @GetMapping("/getSkuTotal")
    @Operation(summary = "统计当前sku总数")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:query','product:sku-statistics:query')")
    public CommonResult<SkuSummaryRespVO> getSkuTotal() {
        SkuSummaryRespVO skuSummaryRespVO = productSkuService.getSkuTotal();
        return success(skuSummaryRespVO);
    }

    /**
     * 统计供应商当前sku总数
     * @return
     */
    @GetMapping("/getSupplierSkuTotal")
    @Operation(summary = "统计供应商当前sku总数")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:query','product:sku-statistics:query')")
    public CommonResult<List<SupplierSkuSummaryRespVO>> getSupplierSkuTotal() {
        List<SupplierSkuSummaryRespVO> supplierSkuSummaryRespVOS = productSkuService.getSupplierSkuTotal();
        return success(supplierSkuSummaryRespVOS);
    }

    /**
     * 统计商品数量分类排行
     * @return
     */
    @GetMapping("/getProductCountByCategory")
    @Operation(summary = "统计供应商当前sku总数")
    @PreAuthorize("@ss.hasAnyPermissions('product:spu:query','product:sku-statistics:query')")
    public CommonResult<List<SkuCountCategorySummaryRespVO>> getProductCountByCategory() {
        List<SkuCountCategorySummaryRespVO> skuCountCategorySummaryRespVOS = productSkuService.getProductCountByCategory();
        return success(skuCountCategorySummaryRespVOS);
    }
}
