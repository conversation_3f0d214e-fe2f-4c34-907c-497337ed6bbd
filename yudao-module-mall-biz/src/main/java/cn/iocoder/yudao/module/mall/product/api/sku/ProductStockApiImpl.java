package cn.iocoder.yudao.module.mall.product.api.sku;

import cn.iocoder.yudao.module.mall.product.api.sku.dto.AreaDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.SkuBaseReqDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.Stock;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsStockService;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.request.getNewStockById.AreaBaseInfoGoodsReq;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.request.getNewStockById.GetStockByIdGoodsReq;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.request.getNewStockById.SkuNumBaseGoodsReq;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.response.getNewStockById.GetStockByIdGoodsResp;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.response.getNewStockById.OpenRpcResult;
import com.jd.open.api.sdk.response.vopkc.VopGoodsGetNewStockByIdResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/18
 */
@Service
public class ProductStockApiImpl implements ProductStockApi {

    @Autowired
    private VopGoodsStockService vopGoodsStockService;


    @Override
    public List<Stock> getJdProductStocks(List<SkuBaseReqDTO> reqs, AreaDTO area) {
        if(CollectionUtils.isEmpty(reqs)) {
            return null;
        }
        GetStockByIdGoodsReq getStockReq = new GetStockByIdGoodsReq();
        AreaBaseInfoGoodsReq areaBaseInfoGoodsReq = new AreaBaseInfoGoodsReq();
        areaBaseInfoGoodsReq.setProvinceId(area.getProvinceId());
        areaBaseInfoGoodsReq.setCityId(area.getCityId());
        areaBaseInfoGoodsReq.setCountyId(area.getCountyId());
        areaBaseInfoGoodsReq.setTownId(area.getTownId());
        getStockReq.setAreaInfo(areaBaseInfoGoodsReq);

        List<SkuNumBaseGoodsReq> skuNumBaseGoodsReqs = reqs.stream().map(req -> {
            SkuNumBaseGoodsReq skuNumBaseGoodsReq = new SkuNumBaseGoodsReq();
            skuNumBaseGoodsReq.setSkuId(req.getSkuId());
            skuNumBaseGoodsReq.setSkuNumber(req.getSkuNumber());
            return skuNumBaseGoodsReq;
        }).collect(Collectors.toList());
        getStockReq.setSkuNumInfoList(skuNumBaseGoodsReqs);
        VopGoodsGetNewStockByIdResponse response = vopGoodsStockService.getNewStockById(getStockReq);
        if (response == null || response.getOpenRpcResult() == null || CollectionUtils.isEmpty(response.getOpenRpcResult().getResult())) {
            return null;
        }
        OpenRpcResult openRpcResult = response.getOpenRpcResult();
        List<GetStockByIdGoodsResp> stocks = openRpcResult.getResult();
        return stocks.stream().map(stockResp -> new Stock()
                        .setSkuId(stockResp.getSkuId())
                        .setStockStateType(stockResp.getStockStateType())
                        .setStockStateDesc(stockResp.getStockStateDesc())
                        .setRemainNumInt(stockResp.getRemainNum()))
                .collect(Collectors.toList());
    }
}
