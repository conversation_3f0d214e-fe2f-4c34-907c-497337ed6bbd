package cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.open.api.sdk.domain.vopkc.SkuStockGoodsProvider.response.queryAreaStockStates.AreaStockStateResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Data
public class AreaStockInfoResp {

    /**
     * 区域对应库存状态
     */
    @Schema(description = "区域对应库存状态")
    List<AreaStockStateResp> stockStateList;

    /**
     * 时间
     */
    @Schema(description = "时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private Date created;

    /**
     * skuId
     */
    @Schema(description = "skuId")
    private Long skuId;

    /**
     * 剩余数量。
     */
    @Schema(description = "剩余数量")
    private int remainNum;

    /**
     * 到货天数，可能为空
     */
    @Schema(description = "到货天数，可能为空")
    private Integer arrivalDays;

    /**
     * 库存状态类型，参考枚举值： 33,39,40,36,34,99
     */
    @Schema(description = "库存状态类型，参考枚举值： 33,39,40,36,34,99")
    private int stockStateType;

    /**
     * 库存状态描述。以下为stockStateId不同时，此字段不同的返回值：
     * 33 有货 现货-下单立即发货
     * 39 有货 在途-正在内部配货，预计2-6天到达本仓库
     * 40 有货 可配货-下单后从有货仓库配货
     * 36 预订
     * 34 无货
     * 99 无货开预定
     */
    @Schema(description = "库存状态描述。以下为stockStateId不同时，此字段不同的返回值： 33 有货 现货-下单立即发货 39 有货 在途-正在内部配货，预计2-6天到达本仓库 40 有货 可配货-下单后从有货仓库配货 36 预订 34 无货 99 无货开预定")
    private String stockStateDesc;
}
