package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/8/9
 */
@Data
public class ApprovalTitleDTO {

    private String title;

    /**
     * 申请单号
     */
    @JSONField(name = "申请单号")
    private String approvalNo;

    /**
     * 项目名称
     */
    @JSONField(name = "项目名称")
    private String projectName;

    /**
     * 报销金额
     */
    @JSONField(name = "报销金额")
    private BigDecimal amount;

    /**
     * 备注
     */
    @JSONField(name = "备注")
    private String remark;

}
