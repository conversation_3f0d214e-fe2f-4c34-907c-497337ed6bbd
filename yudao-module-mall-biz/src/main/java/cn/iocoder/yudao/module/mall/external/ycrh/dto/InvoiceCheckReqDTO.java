package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 发票验真
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class InvoiceCheckReqDTO {

    /**
     * 查询类型("0":以识别结果为准 "1":以传入参数为准)
     */
    private String type;

    /**
     * 发票URL(与参数file二选一)
     */
    private String invoiceUrl;

    /**
     * 文件内容的Base64字符串( 与参数invoiceUrl 二选一)
     */
    private String file;

    /**
     * 试点票文件内容的Base64字符串(发票类型为31时必填)
     */
    private String zipFile;

    /**
     * 是否上传凭证附件 "0":否(默认) "1":是
     */
    private String isVoucherUpload;

    /**
     * 文件名称(命名规则参考 附录11.1)( IsVoucherUpload为"01"时必填)
     */
    private String fileName;

    /**
     * 试点票文件名称 (命名规则参考 附录11.1) (发票类型为31时必填)
     */
    private String zipFileName;

    /**
     * 附件类型编号( IsVoucherUpload为"01"时必填):
     * "03":电子发票
     * "04":纸质发票
     */
    @JsonProperty("fjlx")
    private String uploadFileType;

    /**
     * pdf签名校验(1:是 0:否)
     */
    private String isPdf;

    /**
     * 发票代码
     */
    @JsonProperty("fpdm")
    private String invoiceCode;

    /**
     * 发票号码
     */
    @JsonProperty("fphm")
    private String invoiceNo;

    /**
     * 开票日期
     */
    @JsonProperty("kprq")
    private String invoiceDate;

    /**
     * 发票金额
     */
    @JsonProperty("fpje")
    private String invoiceAmount;

    /**
     * 发票校验码
     */
    @JsonProperty("fpjym")
    private String invoiceCheckCode;

    /**
     * 发票类型 (参考账务发票类型字典的类型代码)
     */
    @JsonProperty("fplx")
    private String invoiceType;

}
