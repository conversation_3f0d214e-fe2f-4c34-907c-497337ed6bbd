package cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 商品评价 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class AppProductCommentExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("订单id")
    private Long orderId;

    @ExcelProperty("skuId")
    private Long skuId;

    @ExcelProperty("spuId")
    private Long spuId;

    @ExcelProperty("供应商id")
    private Long supplierId;

    @ExcelProperty("会员id")
    private Long memberId;

    @ExcelProperty("会员昵称")
    private String nickName;

    @ExcelProperty("评价星数 1->5")
    private Integer score;

    @ExcelProperty("评价内容")
    private String content;

    @ExcelProperty("上传图片地址，以逗号隔开")
    private String pics;

    @ExcelProperty("是否匿名 1-是 0 否")
    private Integer anonymousFlag;

    @ExcelProperty("审核状态")
    private Integer auditStatus;

    @ExcelProperty("创建时间")
    private Date createTime;

}
