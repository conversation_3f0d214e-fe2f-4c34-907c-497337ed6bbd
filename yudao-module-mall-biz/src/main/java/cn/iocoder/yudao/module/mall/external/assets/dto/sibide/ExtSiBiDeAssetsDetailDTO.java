package cn.iocoder.yudao.module.mall.external.assets.dto.sibide;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExtSiBiDeAssetsDetailDTO {

    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单明细编号
     */
    private String orderDetailNo;
    /**
     * 验收单编号
     */
    private String acceptNo;
    /**
     * 资产名称
     */
    private String assetName;
    /**
     * 资产编号
     */
    private String assetCode;
    /**
     * 资产类别
     */
    private String assetType;
    /**
     * 6大类代码，国标分类
     */
    private String category6;
    /**
     * 16大类代码，教育分类
     */
    private String category16;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 数量
     */
    private Integer count;
    /**
     * 存放地点
     */
    private String placeName;
    /**
     * 资产状态 预入库、已入库、已退库、一波
     */
    private String assetStatus;

}
