package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class ShopOrderDTO {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单总金额
     */
    private BigDecimal totalPrice;

    /**
     * 支付类型 1-资金冻结预占(默认)  2-资金不预占
     */
    private String payType;

    /**
     * 运费
     */
    private BigDecimal freight;

    /**
     * 使用单一项目 1-是(默认)  2-否
     */
    private String singleProject;

    /**
     * 部门编号 singleProject为1时必填
     */
    @JsonProperty("bmbh")
    private String departmentNo;

    /**
     * 项目编号 singleProject为1时必填
     */
    @JsonProperty("xmbh")
    private String projectNo;

    /**
     * 项目负责人编号(singleProject为1时必填)
     */
    @JsonProperty("xmfzrbh")
    private String chargeNo;

    /**
     * 项目信息列表 singleProject为2时必填
     */
    private List<XmlInfoDTO> xmInfoList;


    /**
     * 下单时间(yyyy-MM-dd HH:mm:ss)
      */
    private String orderTime;

    /**
     * 下单人工号
     */
    @JsonProperty("ygbh")
    private String ygNo;

    /**
     * 保管人工号(商品分类包含固定资产时必填)
     */
    private String assetsKeeper;

    /**
     * 保管地点(商品分类包含固定资产时必填)
     */
    private String assetsPalce;

    /**
     * 备注
     */
    @JsonProperty("bz")
    private String remark;
    /**
     * 采购原因
     */
    @JsonProperty("reason")
    private String reason;

    /**
     * 商品列表
     */
    private List<SkuInfoDTO> wbSkuInfo;
}
