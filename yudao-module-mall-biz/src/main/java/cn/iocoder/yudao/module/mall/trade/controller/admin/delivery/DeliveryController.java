package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.module.mall.annotation.OrderOperateLog;
import cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo.*;
import cn.iocoder.yudao.module.mall.trade.convert.delivery.DeliveryConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryDO;
import cn.iocoder.yudao.module.mall.trade.enums.order.OrderOperateTypeEnum;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryService;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTask;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import groovy.util.logging.Slf4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 物流信息")
@RestController
@RequestMapping("/trade/delivery")
@Validated
@Slf4j
public class DeliveryController {

    @Resource
    private DeliveryService deliveryService;

    @PutMapping("/update")
    @Operation(summary = "更新物流信息")
    @PreAuthorize("@ss.hasPermission('trade:delivery:update')")
    public CommonResult<Boolean> updateDelivery(@Valid @RequestBody DeliveryUpdateReqVO updateReqVO) {
        deliveryService.updateDelivery(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除物流信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('trade:delivery:delete')")
    public CommonResult<Boolean> deleteDelivery(@RequestParam("id") Long id) {
        deliveryService.deleteDelivery(id);
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "获得物流信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('trade:delivery:query')")
    public CommonResult<List<DeliveryRespSimpleVO>> getDeliveryList(@RequestParam("ids") Collection<Long> ids) {
        List<DeliveryDO> list = deliveryService.getDeliveryList(ids);
        return success(DeliveryConvert.INSTANCE.convertList03(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得物流信息分页")
    @PreAuthorize("@ss.hasPermission('trade:delivery:query')")
    public CommonResult<PageResult<DeliveryRespSimpleVO>> getDeliveryPage(@Valid DeliveryPageReqVO pageVO) {
        PageResult<DeliveryDO> pageResult = deliveryService.getDeliveryPage(pageVO);
        return success(DeliveryConvert.INSTANCE.convertPage02(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出物流信息 Excel")
    @OperateLog(type = EXPORT)
    @PreAuthorize("@ss.hasPermission('trade:delivery:query')")
    public void exportDeliveryExcel(@Valid DeliveryExportReqVO exportReqVO, HttpServletResponse response) throws IOException {
        List<DeliveryDO> list = deliveryService.getDeliveryList(exportReqVO);
        // 导出 Excel
        List<DeliveryExcelVO> datas = DeliveryConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "物流信息.xls", "数据", DeliveryExcelVO.class, datas);
    }

    /**
     * 物流发货，支持绑定多个订单
     * @param deliveryBindReqVO
     * @return
     */
    @PostMapping("/product-send")
    @Operation(summary = "物流发货")
    @PreAuthorize("@ss.hasPermission('trade:delivery:create')")
    @OrderOperateLog(content = "已发货", type = OrderOperateTypeEnum.DO_DELIVERING, orderNo = "#deliveryBindReqVO.orderNo")
    public CommonResult<Boolean> productSend(@Valid @RequestBody DeliveryBindReqVO deliveryBindReqVO) {
        Boolean result = deliveryService.productSend(deliveryBindReqVO, null, null);
        return success(result);
    }

    /**
     * 虚拟商品、服务发货
     * @param deliveryServiceReqVO
     * @return
     */
    @PostMapping("/service-send")
    @Operation(summary = "虚拟商品、服务发货")
    @PreAuthorize("@ss.hasPermission('trade:delivery:create')")
    @OrderOperateLog(content = "虚拟商品、服务已发货", type = OrderOperateTypeEnum.DO_DELIVERING, orderNo = "#deliveryServiceReqVO.orderNo")
    public CommonResult<Boolean> serviceSend(@Valid @RequestBody DeliveryServiceReqVO deliveryServiceReqVO) {
        Boolean result = deliveryService.serviceSend(deliveryServiceReqVO, null, null);
        return success(result);
    }

    @PostMapping("/self-send")
    @Operation(summary = "自配送发货")
    @PreAuthorize("@ss.hasPermission('trade:delivery:create')")
    @OrderOperateLog(content = "自配送发货", type = OrderOperateTypeEnum.DO_DELIVERING, orderNo = "#deliverySelfReqVO.orderNo")
    public CommonResult<Boolean> selfSend(@Valid @RequestBody DeliverySelfReqVO deliverySelfReqVO) {
        Boolean result = deliveryService.selfSend(deliverySelfReqVO, null, null);
        return success(result);
    }

    @GetMapping("/delete-by-order")
    @Operation(summary = "删除发货")
    @PreAuthorize("@ss.hasAnyPermissions('trade:delivery:delete', 'trade:delivery:create')")
    public CommonResult<Boolean> deleteByOrder(@RequestParam("orderNo") String orderNo) {
        deliveryService.deleteByOrder(orderNo);
        return success(true);
    }

    /**
     * 根据物流ID查询物流信息
     * @param id
     * @return
     */
    @GetMapping("/get")
    @Operation(summary = "获得物流信息")
    @PreAuthorize("@ss.hasPermission('trade:delivery:query')")
    public CommonResult<DeliveryRespVO> getById(@RequestParam Long id) {
        DeliveryDO deliveryDO = deliveryService.getById(id);
        return success(DeliveryConvert.INSTANCE.convert(deliveryDO));
    }

    /**
     * 根据物流单号查询物流信息
     * @param deliveryNum
     * @return
     */
    @GetMapping("/get-by-num")
    @Operation(summary = "获得物流信息")
    @Parameter(name = "deliveryNum", description = "物流单号", required = true, example = "JT3061108279737")
    @PreAuthorize("@ss.hasPermission('trade:delivery:query')")
    public CommonResult<DeliveryRespVO> getDelivery(@RequestParam("deliveryNum") String deliveryNum) {
        return success(deliveryService.getByDeliveryNum(deliveryNum));
    }

    /**
     * 根据订单号查询物流信息
     * @param orderNo 订单编号
     * @return
     */
    @GetMapping("/get-by-order")
    @Operation(summary = "根据订单号查询物流信息")
    @Parameter(name = "orderNo", description = "订单流水号", required = true, example = "112312301452410152")
    @PreAuthorize("@ss.hasPermission('trade:delivery:query')")
    public CommonResult<List<DeliveryRespVO>> getDetailByOrderId(@RequestParam("orderNo") String orderNo) {
        return success(deliveryService.getByOrderNo(orderNo));
    }

    /**
     * 根据物流单号和订单号查询物流信息
     * @param deliveryNum
     * @return
     */
    @GetMapping("/get-by-num-and-order")
    @Operation(summary = "获得物流信息")
    @Parameter(name = "deliveryNum", description = "物流单号", required = true, example = "JT3061108279737")
    @PreAuthorize("@ss.hasPermission('trade:delivery:query')")
    public CommonResult<DeliveryRespVO> getDelivery(@RequestParam("deliveryNum") String deliveryNum,@RequestParam("orderNo") String orderNo) {
        return success(deliveryService.getByDeliveryNumAndOrderNo(deliveryNum, orderNo));
    }





    /**
     * 获得订单物流导入模板
     * @param response
     * @throws IOException
     */
    @GetMapping("/get-import-template")
    @PreAuthorize("@ss.hasPermission('mall:delivery:import')")
    @Operation(summary = "获得订单物流导入模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<DeliveryImportExcelVO> list = Arrays.asList(
                DeliveryImportExcelVO.builder()
                        .orderNo("100007490071")
                        .deliveryType("物流发货")
                        .deliveryNum("JDVC31011324411")
                        .deliveryName("京东物流")
                        .build(),
                DeliveryImportExcelVO.builder()
                        .orderNo("100007490072")
                        .deliveryType("供应商自配送")
                        .deliveryContent("您的包裹已出库，正在配送中......联系人：卢先生，联系方式：18600000000")
                        .build(),
                DeliveryImportExcelVO.builder()
                        .orderNo("100007490073")
                        .deliveryType("虚拟商品/服务类")
                        .build()
        );
        // 输出
        ExcelUtils.write(response, "订单物流导入模板.xls", "Sheet1", DeliveryImportExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "导入订单物流 Excel")
    @PreAuthorize("@ss.hasPermission('mall:delivery:import')")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.ORDER_DELIVERY_IMPORT)
    public CommonResult<String> importExcel(@RequestParam("file") MultipartFile file) throws Exception {
        String taskId = AsyncFrontTaskContext.getTaskId();
        List<DeliveryImportExcelVO> list = ExcelUtils.read(file, DeliveryImportExcelVO.class);
        deliveryService.importExcel(list);
        return success(taskId);
    }

}
