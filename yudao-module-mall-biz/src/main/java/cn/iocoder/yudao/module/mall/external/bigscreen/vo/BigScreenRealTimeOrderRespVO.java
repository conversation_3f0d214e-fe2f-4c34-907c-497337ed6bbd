package cn.iocoder.yudao.module.mall.external.bigscreen.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.bytebuddy.asm.Advice;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class BigScreenRealTimeOrderRespVO {
    @Schema(description = "订单时间")
    private String orderDate;

    @Schema(description = "订单金额")
    private BigDecimal orderPrice;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户部门")
    private String deptName;
}
