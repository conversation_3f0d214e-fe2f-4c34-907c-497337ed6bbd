package cn.iocoder.yudao.module.mall.external.sso.cas.util;

import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.URLUtil;
import cn.iocoder.yudao.framework.common.util.tenant.TenantIdUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * Cas统一身份认证工具类
 * <AUTHOR>
 */
public class CasUtils {

    private static final String END_STR = "/";
    /**
     * 生成CAS认证URL
     * @param casServerUrl cas服务器地址
     * @param clientHostUrl 客户端地址
     * @param clientAuthUrl 客户端认证路径
     * @param tenantId 租户ID
     * @param redirectPath 前端返回路径
     * @return
     */
    public static String buildCasAuthUrl(String casServerUrl, String clientHostUrl,String clientAuthUrl, Long tenantId, String redirectPath) {
        String tcode = TenantIdUtils.encryptTenantId(tenantId);
        if(clientHostUrl.endsWith(END_STR) && clientAuthUrl.startsWith(END_STR)) {
            clientHostUrl = clientHostUrl.substring(0, clientHostUrl.lastIndexOf(END_STR));
        }
        String backUrl = clientHostUrl + clientAuthUrl + "?tcode=" + tcode;
        if(StringUtils.isNotBlank(redirectPath)) {
            backUrl += "&redirect=" + redirectPath;
        }
        backUrl = URLUtil.encodeAll((backUrl));
        StringBuilder sb = new StringBuilder();
        sb.append(casServerUrl);
        sb.append("?service=");
        sb.append(backUrl);

        return sb.toString();
    }

    /**
     * 生成CAS退出URL
     * @param casServerLogoutUrl cas服务端退出地址
     * @param clientHostUrl 客户端地址
     * @return
     */
    public static String buildCasLogoutUrl(String casServerLogoutUrl, String clientHostUrl) {
        String backUrl = clientHostUrl;
        backUrl = URLUtil.encodeAll((backUrl));
        StringBuilder sb = new StringBuilder();
        sb.append(casServerLogoutUrl);
        sb.append("?service=");
        sb.append(backUrl);

        return sb.toString();
    }

    /**
     * 生成CAS登录处理异常跳转URL
     * @param hostUrl
     * @return
     */
    public static String buildCasErrorUrl(String hostUrl) {
        StringBuilder sb = new StringBuilder();
        sb.append(hostUrl);
        if(sb.indexOf("?") < 0) {
            sb.append("?error=100");
        } else {
            sb.append("&error=100");
        }

        return sb.toString();
    }

    /**
     * Url编码
     * @param url
     * @return
     */
    public static String encodeUrl(String url) {
        return URLEncodeUtil.encodeAll(url);
    }

    /**
     * 生成用户唯一标识
     * @return
     */
    public static String generateUuid() {
        return IdUtil.fastSimpleUUID();
    }

    public static void main(String[] args) {
        String url = "http://zc2.whu.edu.cn/app-api/mall/open/sso/cas/auth?tcode=122bca2038aa158bfa946a7b4a0b302a&redirect=/abc";
        System.out.println("url===" + URLEncodeUtil.encodeAll(url));
    }
}
