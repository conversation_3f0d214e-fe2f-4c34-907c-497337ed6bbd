package cn.iocoder.yudao.module.mall.external.bpm;

import cn.iocoder.yudao.framework.common.util.tenant.TenantIdUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.basisconfig.BasisConfigDO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.BpmConfigDO;
import cn.iocoder.yudao.module.mall.basis.framework.config.MallProperties;
import cn.iocoder.yudao.module.mall.basis.service.basisconfig.BasisConfigService;
import cn.iocoder.yudao.module.mall.basis.service.integration.BpmConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 审批流的整体配置
 * <AUTHOR>
 * @date 2024/08/22
 */
@Component
@Slf4j
public class BpmClientConfig {

    @Resource
    private MallProperties mallProperties;
    @Resource
    protected BasisConfigService basisConfigService;
    @Resource
    protected BpmConfigService bpmConfigService;

    public static final String NOTIFY_CALLBACK_URL = "%s/app-api/mall/external/bpm/notify/%s";

    public boolean isBpmOpen() {
        BasisConfigDO basisConfigDO = basisConfigService.getBasisConfig();
        return basisConfigDO != null && basisConfigDO.needApprove();
    }

    public BpmConfigDO getBpmConfigDO() {
        return bpmConfigService.getBpmConfig();
    }

    public String getBpmSysCode() {
        BpmConfigDO bpmConfigDO = getBpmConfigDO();
        if(bpmConfigDO == null) {
            return null;
        }
        return bpmConfigDO.getSysCode();
    }

    public String buildVoucherNotifyUrl() {
        String tcode = TenantIdUtils.encryptTenantId(TenantContextHolder.getTenantId());
        return String.format(NOTIFY_CALLBACK_URL, mallProperties.getHostDomain(), tcode);
    }


}
