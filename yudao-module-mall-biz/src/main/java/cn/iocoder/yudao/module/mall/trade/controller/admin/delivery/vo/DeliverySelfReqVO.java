package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import com.kuaidi100.sdk.response.SubscribePushResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "自配送物流信息 - Request VO")
@Data
public class DeliverySelfReqVO {
    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @NotNull(message = "订单编号不能为空")
    private String orderNo;

    /**
     * skuIds
     */
    @Schema(description = "订单包含的商品skuIds字符串，多个sku由逗号隔开")
    @NotNull(message = "skuIds不能为空")
    private String skuIds;

    /**
     * detail
     */
    @Schema(description = "物流详情")
    @NotNull(message = "物流详情不能为空")
    private String detail;
}
