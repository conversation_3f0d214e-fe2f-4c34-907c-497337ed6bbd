package cn.iocoder.yudao.module.mall.product.service.skustock;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenSkuStockUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.AreaDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.Stock;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuCreateOrUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.ProductSkuStockCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.ProductSkuStockExportReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.ProductSkuStockPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.ProductSkuStockUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skustock.ProductSkuStockDO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppTradeOrderCreateReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * sku库存 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductSkuStockService extends IService<ProductSkuStockDO> {

    /**
     * 创建sku库存
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSkuStock(@Valid ProductSkuStockCreateReqVO createReqVO);

    /**
     * 更新sku库存
     *
     * @param updateReqVO 更新信息
     */
    void updateSkuStock(@Valid ProductSkuStockUpdateReqVO updateReqVO);

    /**
     * 批量更新SKU 库存
     * @param updateReqVOs
     */
    void batchUpdateSkuStock2(@Valid List<AppOpenSkuStockUpdateReqVO> updateReqVOs, Long supplierId);

    /**
     * 批量更新SKU 库存
     * @param skuDOs
     */
    void batchUpdateSkuStock(List<ProductSkuStockDO> skuDOs);

    /**
     * 删除sku库存
     *
     * @param id 编号
     */
    void deleteSkuStock(Long id);

    /**
     * 获得sku库存
     *
     * @param skuId 编号
     * @return sku库存
     */
    ProductSkuStockDO getSkuStock(Long skuId);

    /**
     * 创建或更新SKU库存
     * @param sku
     * @param skuVO
     * @return
     */
    ProductSkuStockDO createOrUpdateStock(ProductSkuDO sku, ProductSkuCreateOrUpdateReqVO skuVO);

    /**
     * 获得sku库存列表
     *
     * @param ids 编号
     * @return sku库存列表
     */
    List<ProductSkuStockDO> getSkuStockList(Collection<Long> ids);

    /**
     * 获得sku库存分页
     *
     * @param pageReqVO 分页查询
     * @return sku库存分页
     */
    PageResult<ProductSkuStockDO> getSkuStockPage(ProductSkuStockPageReqVO pageReqVO);

    /**
     * 获得sku库存列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return sku库存列表
     */
    List<ProductSkuStockDO> getSkuStockList(ProductSkuStockExportReqVO exportReqVO);


    /**
     * 批量获取商品库存
     * @param skuIds
     * @return
     */
    List<ProductSkuStockDO> getSkuStocks(List<Long> skuIds);

    /**
     * 扣减商品库存
     * @param items
     */
    void deductInventory(List<AppTradeOrderCreateReqVO.Item> items);

    /**
     * 商品库存回滚，如订单取消场景
     * @param items
     */
    void rollbackInventory(List<TradeOrderItemDO> items);

    /**
     * 查询商品库存
     * @param skuIds
     * @param areaDTO
     * @return
     */
    List<Stock> getProductSkuStocks(List<Long> skuIds, AreaDTO areaDTO);

    void saveBatchProductSkuStocks(List<ProductSkuStockDO> productSkuStockDOS);

}
