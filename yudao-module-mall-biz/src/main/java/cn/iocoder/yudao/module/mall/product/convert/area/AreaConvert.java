package cn.iocoder.yudao.module.mall.product.convert.area;

import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.common.AppOpenAreaRespVO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AreaDO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.AreaDTO;
import com.alibaba.excel.util.StringUtils;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.request.getSkusAllSaleState.AreaBaseInfoGoodsReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 区域 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AreaConvert {

    AreaConvert INSTANCE = Mappers.getMapper(AreaConvert.class);

    List<AppOpenAreaRespVO> convertList(List<AreaDO> list);

    AreaBaseInfoGoodsReq convertSaleStateArea(AreaDTO bean);

    com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.request.getNewStockById.AreaBaseInfoGoodsReq convertStockArea(AreaDTO bean);


    default AreaDTO convertArea(String areaIds) {
        if(StringUtils.isBlank(areaIds)) {
            return new AreaDTO();
        }
        String[] areaArr = areaIds.split(",");
        AreaDTO areaDTO = new AreaDTO();
        if (areaArr.length > 0 && NumberUtil.isNumber(areaArr[0])) {
            areaDTO.setProvinceId(NumberUtil.parseLong(areaArr[0]));
        }
        if (areaArr.length > 1 && NumberUtil.isNumber(areaArr[1])) {
            areaDTO.setCityId(NumberUtil.parseLong(areaArr[1]));
        }
        if (areaArr.length > 2 && NumberUtil.isNumber(areaArr[2])) {
            areaDTO.setCountyId(NumberUtil.parseLong(areaArr[2]));
        }
        if (areaArr.length > 3 && NumberUtil.isNumber(areaArr[3])) {
            areaDTO.setTownId(NumberUtil.parseLong(areaArr[3]));
        }
        return areaDTO;
    }
}
