package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商品 SKU 更新销量 VO")
@Data
@ToString(callSuper = true)
public class ProductSkuSeoUpdateReqVO {

    @Schema(description = "主键", example = "1024")
    @NotNull
    private Long id;

    @Schema(description = "初始销量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "初始销量不能为空")
    @Min(-9999999)
    @Max(9999999)
    private Integer initSalesCount;

}
