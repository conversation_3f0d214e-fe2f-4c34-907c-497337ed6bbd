package cn.iocoder.yudao.module.mall.external.assets.dto.sibide;

import lombok.Data;

import java.util.Date;

@Data
public class ExtSiBiDeAssetsStatusDTO {

    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单明细编号
     */
    private String orderDetailNo;
    /**
     * 验收单编号
     */
    private String acceptNo;
    /**
     * 更新状态值，建账中、已建账、已入库、已退库、已驳回
     */
    private String approveStatus;
    /**
     * 状态名称
     * 0 审批中 已提交建账申请
     * 1 已完成 完成建账入库，生成了资产编号
     * -1 被驳回 审批人驳回建账业务
     * -9 被撤回 申请人撤回建账申请
     * -2 草稿 未提交建账申请
     */
    private String approveStatusName;
    /**
     * 审批备注
     */
    private String approveMemo;
    /**
     * 审批人
     */
    private String approveBy;
    /**
     * 审批日期，格式为时间戳，毫秒数
     */
    private Date approveTime;
    /**
     * 资产名称
     */
    private String assetName;
    /**
     * 资产编号
     */
    private String assetCode;
    /**
     * 6大类代码，国标分类
     */
    private String category6;
    /**
     * 16大类代码，教育分类
     */
    private String category16;

}
