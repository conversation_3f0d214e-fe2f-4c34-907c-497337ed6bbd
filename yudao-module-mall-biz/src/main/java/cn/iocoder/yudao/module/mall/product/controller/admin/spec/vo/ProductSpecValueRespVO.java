package cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商品规格更新 Request VO")
@Data
@EqualsAndHashCode
@ToString(callSuper = true)
public class ProductSpecValueRespVO {

    @Schema(description = "规格Id，0代表自定义规格", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long specId;

    @Schema(description = "规格名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String specName;

    @Schema(description = "规格值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String specValue;

}
