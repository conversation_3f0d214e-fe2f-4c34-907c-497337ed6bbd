package cn.iocoder.yudao.module.mall.product.controller.app.searchhistory;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.product.controller.app.searchhistory.vo.ProductSearchHistoryRespVO;
import cn.iocoder.yudao.module.mall.product.convert.searchhistory.ProductSearchHistoryConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.searchhistory.ProductSearchHistoryDO;
import cn.iocoder.yudao.module.mall.product.service.searchhistory.ProductSearchHistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 用户搜索历史")
@RestController
@RequestMapping("/product/search-history")
@Validated
@Slf4j
public class AppProductSearchHistoryController {

    @Resource
    private ProductSearchHistoryService searchHistoryService;

    @GetMapping("/get-list")
    @Operation(summary = "查询搜索历史")
    public CommonResult<List<ProductSearchHistoryRespVO>> getList() {
        if(getLoginUserId() != null) {
            List<ProductSearchHistoryDO> list = searchHistoryService.getList(getLoginUserId(), 10);
            log.info(list.toString());
            return success(ProductSearchHistoryConvert.INSTANCE.convertList(list));
        }
        return success(null);
    }

    @PostMapping("/clean")
    @Operation(summary = "清除搜索历史")
    public CommonResult<List<ProductSearchHistoryRespVO>> clean() {
        if(getLoginUserId() != null) {
            searchHistoryService.deleteByUser(getLoginUserId());
        }
        return success(null);
    }

    @PostMapping("/deleteByWord")
    @Operation(summary = "清除某个搜索历史")
    public CommonResult<List<ProductSearchHistoryRespVO>> deleteByWord(@RequestParam String keyword) {
        if(getLoginUserId() != null) {
            searchHistoryService.deleteByKeyWord(getLoginUserId(),keyword);
        }
        return success(null);
    }

    @GetMapping("/get-hot-list")
    @Operation(summary = "查询热门搜索")
    public CommonResult<List<ProductSearchHistoryRespVO>> getHotList(@RequestParam Integer count){
        List<ProductSearchHistoryDO> list = searchHistoryService.getHotList(count!=null?count:10);
        return success(ProductSearchHistoryConvert.INSTANCE.convertList(list));
    }

}

