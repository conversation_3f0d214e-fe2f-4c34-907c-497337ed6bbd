package cn.iocoder.yudao.module.mall.external.hrms.impl.whu;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.HrmsConfigDO;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.external.hrms.impl.whu.dto.*;
import cn.iocoder.yudao.module.mall.external.hrms.impl.whu.enums.WhuMethodEnum;
import cn.iocoder.yudao.module.mall.external.hrms.impl.whu.vo.WhuAccessTokenVO;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Component
@Slf4j
public class WhuClient {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private ObjectMapper objectMapper;

    private WhuAccessTokenVO getAccessToken(HrmsConfigDO configDO) {
        Long tenantId = TenantContextHolder.getTenantId();
        if(tenantId == null) {
            log.error("租户ID不能为空");
            return null;
        }

        String lockKey = "lock:external:whu:open:" + tenantId;
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        try {
            String valueKey = "external:whu:open:token:" + tenantId;
            ValueOperations<String, WhuAccessTokenVO> valueOpt = redisTemplate.opsForValue();
            WhuAccessTokenVO accessTokenVO = valueOpt.get(valueKey);
            if(accessTokenVO == null || !accessTokenVO.checkValidTime()) {
                WhuAccessTokenRespDTO respDTO = queryWhuAccessToken(configDO);
                accessTokenVO = new WhuAccessTokenVO()
                        .setAccessToken(respDTO.getAccessToken()).setExpiresIn(respDTO.getExpiresIn()).setCreateTime(LocalDateTime.now());
                valueOpt.set(valueKey, accessTokenVO, 1, TimeUnit.DAYS);
            }

            return accessTokenVO;
        } catch (Exception e) {
            log.error("获取WhuAccessToken异常", e);
        } finally {
            if(lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }

        return null;
    }

    private WhuAccessTokenRespDTO queryWhuAccessToken(HrmsConfigDO configDO) {
        TypeReference<WhuResp<WhuAccessTokenRespDTO>> typeReference = new TypeReference<WhuResp<WhuAccessTokenRespDTO>>() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        Map<String, Object> param = new HashMap<>();
        param.put("key", configDO.getKey1());
        param.put("secret", configDO.getKey2());
        WhuResp<WhuAccessTokenRespDTO> resp = sendRequest(null, configDO.getServerUrl(), WhuMethodEnum.GET_ACCESS_TOKEN, param, javaType, true);
        return resp.getResult();
    }

    public WhuPageRespDTO<WhuStaffDTO> getStaffPage(HrmsConfigDO configDO, WhuStaffPageReqDTO reqDTO) {
        WhuAccessTokenVO accessTokenVO = getAccessToken(configDO);
        if(accessTokenVO == null) {
            return null;
        }
        Map<String, Object> params = BeanUtil.beanToMap(reqDTO, new HashMap<>(), false, true);
        TypeReference<WhuResp<WhuPageRespDTO<WhuStaffDTO>> > typeReference = new TypeReference<WhuResp<WhuPageRespDTO<WhuStaffDTO>> >() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        WhuResp<WhuPageRespDTO<WhuStaffDTO>> resp = sendRequest(accessTokenVO.getAccessToken(), configDO.getServerUrl(), WhuMethodEnum.GET_STAFF_LIST, params, javaType, false);
        return resp.getResult();
    }

    public WhuStaffDTO getStaffInfo(HrmsConfigDO configDO, String userNo) {
        WhuStaffPageReqDTO reqDTO = new WhuStaffPageReqDTO().setZGH(userNo);
        WhuPageRespDTO<WhuStaffDTO> resp = getStaffPage(configDO, reqDTO);
        if(resp != null && CollUtil.isNotEmpty(resp.getList())) {
            return resp.getList().get(0);
        }

        return null;
    }

    public WhuPageRespDTO<WhuDeptDTO> getDeptPage(HrmsConfigDO configDO, WhuDeptPageReqDTO reqDTO) {
        WhuAccessTokenVO accessTokenVO = getAccessToken(configDO);
        if(accessTokenVO == null) {
            return null;
        }
        Map<String, Object> params = BeanUtil.beanToMap(reqDTO, new HashMap<>(), false, true);
        TypeReference<WhuResp<WhuPageRespDTO<WhuDeptDTO>> > typeReference = new TypeReference<WhuResp<WhuPageRespDTO<WhuDeptDTO>> >() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        WhuResp<WhuPageRespDTO<WhuDeptDTO>> resp = sendRequest(accessTokenVO.getAccessToken(), configDO.getServerUrl(), WhuMethodEnum.GET_DEPT_LIST, params, javaType, true);
        return resp.getResult();
    }

    public WhuGhInfoPageRespDTO getGhInfoPage(HrmsConfigDO configDO, WhuGhInfoPageReqDTO reqDTO) {
        WhuAccessTokenVO accessTokenVO = getAccessToken(configDO);
        if(accessTokenVO == null) {
            return null;
        }
        Map<String, Object> params = BeanUtil.beanToMap(reqDTO, new HashMap<>(), false, true);
        TypeReference<WhuResp<WhuGhInfoPageRespDTO> > typeReference = new TypeReference<WhuResp<WhuGhInfoPageRespDTO> >() {};
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        WhuResp<WhuGhInfoPageRespDTO> resp = sendRequest(accessTokenVO.getAccessToken(), configDO.getServerUrl(), WhuMethodEnum.GET_GH_INFO_LIST, params, javaType, true);
        return resp.getResult();
    }

    private <T> WhuResp<T> sendRequest(String accessToken, String serverUrl, WhuMethodEnum methodEnum, Map<String, Object> param, JavaType javaType, boolean checkFail) {
        WhuResp<T> resp = sendRequest(accessToken, serverUrl, methodEnum, param, javaType);
        if (checkFail && !resp.isSuccess()) {
            throw exception(ErrorCodeConstants.HRMS_WHU_FAIL, resp.getMessage());
        }

        return resp;
    }

    private <T> WhuResp<T> sendRequest(String accessToken, String serverUrl, WhuMethodEnum methodEnum, Map<String, Object> param, JavaType javaType) {
        try {
            StringBuilder comUrl = new StringBuilder(serverUrl);
            comUrl.append(methodEnum.getMethodName());
            if(accessToken != null) {
                comUrl.append("?access_token=");
                comUrl.append(accessToken);
            }

            HttpRequest req = HttpRequest.get(comUrl.toString());
            req.form(param);

            log.info("{} param: {}", methodEnum.getMethodName(), param);
            HttpResponse response = req.timeout(5000).execute();
            log.info("response: {}", response);
            if (response.isOk()) {
                String body = response.body();
                log.info("{} resp: {}", methodEnum.getMethodName(), body);
                JSONObject respObj = JSONObject.parseObject(body);
                if (respObj.containsKey("code")) {
                    return objectMapper.readValue(body, javaType);
                } else {
                    log.error("{} resp no code...", methodEnum.getMethodName());
                    throw exception(ErrorCodeConstants.HRMS_WHU_FAIL,"返回格式不正确");
                }
            } else {
                log.error("{} error: {}", methodEnum.getMethodName(), response.getStatus());
                throw exception(ErrorCodeConstants.HRMS_WHU_FAIL,"结果异常");
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("{} error: ", methodEnum.getMethodName(), e);
            throw exception(ErrorCodeConstants.HRMS_WHU_FAIL,"系统异常");
        }
    }

}
