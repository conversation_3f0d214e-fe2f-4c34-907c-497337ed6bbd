package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
public class SplitOrderReqDTO {

    /**
     * 原订单号
     */
    private String parentOrderNo;

    /**
     * 拆分订单列表
     */
    private List<SplitShopOrderDTO> splitShopOrderList;


    /**
     * 是否覆盖，0:否(默认) 1:是
     * 支持京东vop拆单没有中间父订单的场景
     */
    private String isOverwrite;
}
