package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class PushBillReqDTO {

    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 业务类型: "01":采购 "02":采购(免建账)  "03":采购(自动生成预约单) "04":采购(校内转账)
     */
    private String businessType;

    /**
     * 账单开始日(推送日期  yyyy-MM-dd)
     */
    private String billStartDate;

    /**
     * 账单结束日(产生逾期费的前一天,没有可不填  yyyy-MM-dd)
     */
    private String billEndDate;

    /**
     * 账单金额
     */
    private BigDecimal billAmount;

    /**
     * 1:是 0:否(默认)  是否为分期支付账单（值为1情况下，订单可以分期结算）
     */
    private String isInstallmentBill;

    /**
     * 订单信息
     */
    private List<OrderInfoDTO> orderInfo;

    /**
     * 对公支付单位信息 (业务类型为"01"、"02"、"03"时必填) (单个account和多个accountList传其中一个,两个都传则优先account)
     */
    private AccountDTO account;

    /**
     * 对公支付单位信息集合 (业务类型为"01"、"02"、"03"时必填) (单个account和多个accountList传其中一个,两个都传则优先account)
     */
    private List<AccountDTO> accountList;

    /**
     * 校内对私支付数据
     */
    private List<PrivatePaymentDTO> privatePaymentList;

    /**
     * 校内转账信息  (业务类型为"04"时必填)
     */
    private List<AccountSchoolDTO> accountSchool;

    /**
     * 需要审批，调用接口账单审批并通过后才能制单 1：是  0：否（默认）
     */
    private String needApproval;
}
