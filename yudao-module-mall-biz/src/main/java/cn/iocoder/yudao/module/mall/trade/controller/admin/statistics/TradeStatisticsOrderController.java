package cn.iocoder.yudao.module.mall.trade.controller.admin.statistics;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenProductCategoryProportionRespVO;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenSupplierProductProportionRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.*;
import cn.iocoder.yudao.module.mall.trade.service.orderstatistics.OrderStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 订单统计")
@RestController
@RequestMapping("/trade/statistics/order")
@Validated
public class TradeStatisticsOrderController {
    @Resource
    private OrderStatisticsService orderStatisticsService;

    @GetMapping("/getSellCount")
    @Operation(summary = "总销量")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<Long> getSellCount() {
        Long sellCount = orderStatisticsService.getSellCount();
        return success(sellCount);
    }

    @GetMapping("/getSellAmount")
    @Operation(summary = "总销售额")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<BigDecimal> getSellAmount() {
        BigDecimal sellAmount = orderStatisticsService.getSellAmount();
        return success(sellAmount);
    }

    @GetMapping("/getNotSettleAmount")
    @Operation(summary = "待结算金额")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<BigDecimal> getNotSettleAmount() {
        BigDecimal notSettleAmount = orderStatisticsService.getNotSettleAmount();
        return success(notSettleAmount);
    }

    @GetMapping("/getOrderSummary")
    @Operation(summary = "订单统计：待确认订单、待发货订单、售后订单、已完成订单、待审批订单、已审批订单、待结算订单、已结算订单、已取消订单")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<OrderSummaryRespVO> getOrderSummary() {
        OrderSummaryRespVO orderSummary = orderStatisticsService.getOrderSummary();
        return success(orderSummary);
    }

    @GetMapping("/getOrderSummaryByDept")
    @Operation(summary = "部门销售额排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderDeptSummaryRespVO>> getOrderSummaryByDept(Integer limit) {
        List<OrderDeptSummaryRespVO> orderDeptSummaryRespVOS = orderStatisticsService.getOrderSummaryByDept(limit);
        return success(orderDeptSummaryRespVOS);
    }

    @GetMapping("/getOrderSummaryByProject")
    @Operation(summary = "项目销售额排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderProjectSummaryRespVO>> getOrderSummaryByProject() {
        List<OrderProjectSummaryRespVO> orderProjectSummaryRespVOS = orderStatisticsService.getOrderSummaryByProject();
        return success(orderProjectSummaryRespVOS);
    }

    @GetMapping("/getOrderSummaryBySupplier")
    @Operation(summary = "供应商销售额排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderSupplierSummaryRespVO>> getOrderSummaryBySupplier() {
        List<OrderSupplierSummaryRespVO> orderSupplierSummaryRespVOS = orderStatisticsService.getOrderSummaryBySupplier();
        return success(orderSupplierSummaryRespVOS);
    }

    @GetMapping("/getAfterSaleOrderSummary")
    @Operation(summary = "供应商售后订单量排行")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderAfterSaleSummaryRespVO>> getAfterSaleOrderSummary() {
        List<OrderAfterSaleSummaryRespVO> orderAfterSaleSummaryRespVOS = orderStatisticsService.getAfterSaleOrderSummary();
        return success(orderAfterSaleSummaryRespVOS);
    }
}
