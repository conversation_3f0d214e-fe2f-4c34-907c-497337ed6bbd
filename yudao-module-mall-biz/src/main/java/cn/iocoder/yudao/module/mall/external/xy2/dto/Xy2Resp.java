package cn.iocoder.yudao.module.mall.external.xy2.dto;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 湘雅二接口响应结果
 * <AUTHOR>
 * @param <T>
 */
@Data
public class Xy2Resp<T> {

    private String code;

    private String msg;

    private T data;

    @JsonIgnore
    public boolean isSuccess() {
        return ObjectUtil.equal("0", this.code);
    }

    /**
     * 如果失败 抛出异常
     *
     * @param checkFail
     * @return
     */
    public T getResult(boolean checkFail) {
        if (!isSuccess()) {
            throw exception(ErrorCodeConstants.XY2_FAIL, this.msg);
        }
        return data;
    }
}
