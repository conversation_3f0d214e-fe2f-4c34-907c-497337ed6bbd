package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * 供应商售后单信息
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppSupplierAfterSaleVO {

    private Long id;
    private Integer way;

    private String wayName;
    private String applyReasonName;

    private String contact;
    private String phone;
    private String applyReason;
    private String applyDescription;
    private List<String> applyPicUrls;
    private Integer count;
    private BigDecimal refundPrice;

    private Long orderId;
    private String orderNo;
    private Long orderItemId;
    private String spuName;
    private Long skuId;
    private String picUrl;

    private String logisticsName;
    private String logisticsNo;
    private LocalDateTime deliveryTime;

    private String memo;



}
