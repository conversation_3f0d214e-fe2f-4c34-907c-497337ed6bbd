package cn.iocoder.yudao.module.mall.product.dal.dataobject.myfootprint;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;



@TableName("mall_my_footprint")
@KeySequence("mall_my_footprint_seq")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyFootprintDO extends TenantBaseDO {


    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商品skuID,逗号分隔
     */
    private Long skuId;
    /**
     * 会员用户id
     */
    private Long userId;
    /**
     * 供应商id
     */
    private String skuInnerId;
}
