package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class PrivatePaymentDTO {

    /**
     * 银行账号
     */
    @JsonProperty("dfzh")
    private String bankAccount;

    /**
     * 员工编号
     */
    @JsonProperty("ygbh")
    private String ygNo;

    /**
     * 姓名
     */
    @JsonProperty("xm")
    private String name;

    /**
     * 电话
     */
    private String tel;

    /**
     * 附言
     */
    @JsonProperty("bz")
    private String remark;

    /**
     * 部门编号
     */
    @JsonProperty("bmbh")
    private String departmentNo;

    /**
     * 项目编号
     */
    @JsonProperty("xmbh")
    private String projectNo;

    /**
     * 金额
     */
    @JsonProperty("je")
    private BigDecimal amount;

    /**
     * 开户银行名称
     */
    @JsonProperty("khyh")
    private String bankName;

    /**
     * 银行简称
     */
    @JsonProperty("kssh")
    private String bankAbbreviationName;

    /**
     * 联行号
     */
    @JsonProperty("lhh")
    private String lhh;
}
