package cn.iocoder.yudao.module.mall.external.ycrh;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.util.object.ObjectUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.YcrhConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.integration.YcrhConfigService;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.external.ycrh.dto.YcrhResp;
import cn.iocoder.yudao.module.mall.external.ycrh.enums.YcrhMethodEnum;
import cn.iocoder.yudao.module.mall.util.QwRobotUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TtlRunnable;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 业财融合接口客户端封装
 * <AUTHOR>
 */
@Service
@Slf4j
public class HttpCommonClient {

    /**
     * 请求超时时间30秒
     */
    private static final int REQ_TIMEOUT = 30 * 1000;
    @Resource
    private ObjectMapper objectMapper;

    /**
     * 发送业财融合接口请求，通用逻辑封装
     *
     * @param ycrhMethodEnum 业务方法
     * @param param          业务数据
     * @param clazz          返回结果Class
     * @param <T>            返回结果类型
     * @return
     */
    public <T> YcrhResp<T> sendRequest(String url, YcrhMethodEnum ycrhMethodEnum, Object param, Class<T> clazz) {
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(YcrhResp.class, clazz);
        return sendRequest(url, ycrhMethodEnum, param, javaType);
    }


    /**
     * 发送业财融合接口请求，通用逻辑封装
     *
     * @param ycrhMethodEnum 业务方法
     * @param param          业务数据
     * @param typeReference  返回结果Class
     * @return
     */
    public <T> YcrhResp<T> sendRequest(String url, YcrhMethodEnum ycrhMethodEnum, Object param, TypeReference<YcrhResp<T>> typeReference) {
        JavaType javaType = objectMapper.getTypeFactory().constructType(typeReference);
        return sendRequest(url, ycrhMethodEnum, param, javaType);
    }

    /**
     * 发送业财融合接口请求，通用逻辑封装
     *
     * @param ycrhMethodEnum
     * @param param
     * @param clazz
     * @param checkFail      为true时如果返回结果不成功则抛异常
     * @param <T>
     * @return
     */
    public <T> YcrhResp<T> sendRequest(String url, YcrhMethodEnum ycrhMethodEnum, Object param, Class<T> clazz, boolean checkFail) {
        YcrhResp<T> resp = sendRequest(url, ycrhMethodEnum, param, clazz);
        if (checkFail && !resp.isSuccess()) {
            sendError(ycrhMethodEnum, resp.getResultMsg());
            throw exception(ErrorCodeConstants.YCRH_FAIL, resp.getResultMsg());
        }
        return resp;
    }

    /**
     * 发送业财融合接口请求，通用逻辑封装
     *
     * @param ycrhMethodEnum
     * @param param
     * @param typeReference
     * @param checkFail      为true时如果返回结果不成功则抛异常
     * @param <T>
     * @return
     */
    public <T> YcrhResp<T> sendRequest(String url, YcrhMethodEnum ycrhMethodEnum, Object param, TypeReference<YcrhResp<T>> typeReference, boolean checkFail) {
        YcrhResp<T> resp = sendRequest(url, ycrhMethodEnum, param, typeReference);
        if (checkFail && !resp.isSuccess()) {
            sendError(ycrhMethodEnum, resp.getResultMsg());
            throw exception(ErrorCodeConstants.YCRH_FAIL, resp.getResultMsg());
        }
        return resp;
    }

    /**
     * 发送业财融合接口请求，通用逻辑封装
     *
     * @param ycrhMethodEnum 业务方法
     * @param param          业务数据
     * @param javaType
     * @return
     */
    public <T> YcrhResp<T> sendRequest(String url, YcrhMethodEnum ycrhMethodEnum, Object param, JavaType javaType) {
//        int maxStrLength = 5000;
        String paramStr = null, logParams = null;
        try {
            HttpRequest req = HttpUtil.createPost(url);
            paramStr = JSON.toJSONString(param);
            logParams = compressParams(ycrhMethodEnum, paramStr);
            HttpResponse response = req.body(paramStr).timeout(REQ_TIMEOUT).execute();
            log.info("ycapi {} params: {} resp：{}", ycrhMethodEnum.getMethodName(), logParams, response);
            if (response.isOk()) {
                String body = response.body();
                JSONObject respObj = JSONObject.parseObject(body);
                if (respObj.containsKey("code")) {
                    String respCode = respObj.getString("code");
                    String errMsg = respObj.getString("message");
                    if (StringUtils.isNotBlank(respCode) && respCode.equals("00")) {
                        JSONObject resultObj = respObj.getJSONObject("result");
                        return objectMapper.readValue(resultObj.toJSONString(), javaType);
                    } else {
                        throw exception(ErrorCodeConstants.YCRH_FAIL, errMsg);
                    }
                } else {
                    throw exception(ErrorCodeConstants.YCRH_FAIL, "返回格式不正确");
                }
            } else {
                log.error("ycapi {} fail, resp：{}", ycrhMethodEnum.getMethodName(), response);
                sendError(ycrhMethodEnum, "HttpResponseStatus:" + response.getStatus());
                throw exception(ErrorCodeConstants.YCRH_FAIL, "结果异常");
            }
        } catch (ServiceException e) {
            sendError(ycrhMethodEnum, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("ycapi {} params: {}", ycrhMethodEnum.getMethodName(), logParams, e);
            sendError(ycrhMethodEnum, e.getMessage());
            throw exception(ErrorCodeConstants.YCRH_FAIL, "系统异常");
        }
    }

    /**
     * 针对base文件参数进行压缩
     * @param ycrhMethodEnum
     * @param paramStr
     * @return
     */
    private String compressParams(YcrhMethodEnum ycrhMethodEnum, String paramStr) {
        if(ObjectUtils.equalsAny(ycrhMethodEnum, YcrhMethodEnum.UPLOAD_VOUCHER_FILE, YcrhMethodEnum.UPLOAD_BPM_ATTACHMENTS)) {
            return StringUtils.abbreviate(paramStr, "...", 200);
        }
        return paramStr;
    }

    @Resource
    private YcrhConfigService ycrhConfigService;
    @Resource
    private ThreadPoolExecutor threadPoolExecutor;

    private void sendError(YcrhMethodEnum ycrhMethodEnum, String msg) {
        YcrhConfigDO ycrhConfigDO = ycrhConfigService.getYcrhConfig();
        if(ycrhConfigDO == null || !ycrhConfigDO.isQwKeyValid()) {
            return;
        }
        threadPoolExecutor.execute(TtlRunnable.get(() -> {
            String key = ycrhConfigDO.getQwKey();
            String str = "[租户ID：%d][调用方法: %s-%s]失败信息：%s";
            String content = String.format(str, TenantContextHolder.getTenantId(), ycrhMethodEnum.getMethodName(), ycrhMethodEnum.getDesc(), msg);
            Map<String, String> contentMap = new HashMap<>();
            contentMap.put("content", content);
            QwRobotUtils.sendTextMsg(key, contentMap);
        }));
    }

}
