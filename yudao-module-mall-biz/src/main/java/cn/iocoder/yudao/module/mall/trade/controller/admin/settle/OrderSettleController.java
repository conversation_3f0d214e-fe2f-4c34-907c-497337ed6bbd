package cn.iocoder.yudao.module.mall.trade.controller.admin.settle;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.basisconfig.InvoiceConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.basisconfig.InvoiceConfigService;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.trade.controller.admin.settle.vo.*;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.trade.service.settle.OrderSettleService;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTask;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 订单结算")
@RestController
@RequestMapping("/trade/orderSettle")
@Validated
@Slf4j
public class OrderSettleController {

    @Resource
    private OrderSettleService orderSettleService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private TradeOrderService tradeOrderService;
    @Resource
    private InvoiceConfigService invoiceConfigService;

    /**
     * 查询结算列表
     *
     * @return
     */
    @GetMapping("/queryOrderSettlePage")
    @Operation(summary = "查询结算列表")
    @PreAuthorize("@ss.hasPermission('trade:settle:query')")
    public CommonResult<PageResult<OrderSettleVO>> queryOrderSettlePage(OrderSettleReq settleReq) {
        supplierService.validateLoginUserSupplier(settleReq.getSupplierId());

        return CommonResult.success(orderSettleService.queryOrderSettlePage(settleReq));
    }

    /**
     * 结算
     * @param list
     * @return
     */
    @PostMapping("/settleByOrderIds")
    @Operation(summary = "结算")
    @PreAuthorize("@ss.hasPermission('trade:settle:update')")
    public CommonResult<Boolean> settleByOrderIds(@RequestBody List<Long> list){
        return CommonResult.success(orderSettleService.settleByOrderIds(list));
    }

    /**
     * 开票申请
     *
     * @param invoiceReq
     * @return
     */
    @PostMapping("/invoiceApply")
    @Operation(summary = "开票申请")
    @PreAuthorize("@ss.hasPermission('trade:settle:invoice')")
    public CommonResult<Boolean> invoiceApply(@RequestBody OrderInvoiceReq invoiceReq) {
        orderSettleService.invoiceApply(invoiceReq, null);
        return CommonResult.success(null);
    }

    @PostMapping("/invoiceUpdate")
    @Operation(summary = "更新订单发票")
    @PreAuthorize("@ss.hasPermission('trade:settle:invoice')")
    public CommonResult<Boolean> updateOrderInvoice(@Valid @RequestBody OrderInvoiceUpdateReq updateReqVO) {
        supplierService.validateLoginUserSupplier(updateReqVO.getSupplierId());
        TradeOrderDO tradeOrderDO = tradeOrderService.getOrder4Supplier(updateReqVO.getSupplierId(), updateReqVO.getOrderId());
        Assert.assertNotNull("orderId错误", tradeOrderDO);
        InvoiceConfigDO invoiceConfig = invoiceConfigService.getInvoiceConfig();
        if(invoiceConfig == null) {
            log.error("发票配置不能为空...");
            throw new ServiceException(ErrorCodeConstants.INVOICE_APPLY_NOT_CONFIG);
        }
        orderSettleService.receiveInvoice4ThirdSupplier(tradeOrderDO, invoiceConfig, updateReqVO);
        return success(true);
    }

    /**
     * 导出结算
     *
     * @param settleReq
     */
    @GetMapping("/exportSettleOrder")
    @Operation(summary = "导出结算订单")
    @PreAuthorize("@ss.hasPermission('trade:settle:export')")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.SETTLE_ORDER_LIST)
    public CommonResult<String> exportSettleOrder(OrderSettleReq settleReq) {
        supplierService.validateLoginUserSupplier(settleReq.getSupplierId());

        String taskId = AsyncFrontTaskContext.getTaskId();
        orderSettleService.exportSettleOrder(settleReq);

        return success(taskId);
    }

    /**
     * 导出发票PDF
     *
     * @param downloadInvoiceReq
     */
    @PostMapping("/downloadInvoice")
    @Operation(summary = "导出发票PDF")
    @PreAuthorize("@ss.hasPermission('trade:settle:query')")
    public void downloadInvoice(@RequestBody DownloadInvoiceReq downloadInvoiceReq) {
        orderSettleService.downloadInvoice(downloadInvoiceReq, null);
    }

    /**
     * 上传发票凭证到业财系统
     * @param list
     * @return
     */
    @PostMapping("/uploadVoucher")
    @Operation(summary = "上传凭证")
    @PreAuthorize("@ss.hasPermission('trade:settle:invoice')")
    public CommonResult<Boolean> uploadVoucher(@RequestBody List<Long> list) {
        return CommonResult.success(orderSettleService.uploadInvoiceFile2YcrhAsync(list));
    }

    /**
     * 通过新方式修复数据，上传发票凭证到业财系统
     * @return
     */
    @PostMapping("/fix-uploadVoucher")
    @Operation(summary = "上传凭证")
    @PreAuthorize("@ss.hasPermission('trade:settle:invoice')")
    public CommonResult<Boolean> fixUploadVoucher() {
        orderSettleService.fixAllOrderInvoiceUpload();
        return CommonResult.success(true);
    }

    /**
     * 发票验真
     * @param list
     * @return
     */
    @PostMapping("/checkInvoice")
    @Operation(summary = "发票验真")
    @PreAuthorize("@ss.hasPermission('trade:settle:invoice')")
    public CommonResult<Boolean> checkInvoice(@RequestBody List<Long> list){
        return CommonResult.success(orderSettleService.checkInvoice(list));
    }


}
