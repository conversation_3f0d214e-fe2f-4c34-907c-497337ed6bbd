package cn.iocoder.yudao.module.mall.member.service.user;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.validation.Mobile;
import cn.iocoder.yudao.module.mall.member.controller.admin.user.vo.MemberUserCreateReqVO;
import cn.iocoder.yudao.module.mall.member.controller.admin.user.vo.MemberUserUpdateReqVO;
import cn.iocoder.yudao.module.mall.member.controller.admin.user.vo.UserQueryReqVO;
import cn.iocoder.yudao.module.mall.member.controller.app.user.vo.AppUserUpdateMobileReqVO;
import cn.iocoder.yudao.module.mall.member.controller.app.user.vo.AppUserUpdatePasswordReqVO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;

/**
 * 会员用户 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberUserService extends IService<MemberUserDO> {

    /**
     * 通过手机查询用户
     *
     * @param mobile 手机
     * @return 用户对象
     */
    MemberUserDO getUserByMobile(String mobile);

    /**
     * 通过员工编号查询用户
     *
     * @param userNo 用户编号
     * @return 用户对象
     */
    MemberUserDO getUserByUserNo(String userNo);

    /**
     * 更新会员手机号
     * @param userId
     * @param mobile
     * @return
     */
    boolean fillUserMobile(Long userId, String mobile);

    /**
     * 基于用户昵称，模糊匹配用户列表
     *
     * @param nickname 用户昵称，模糊匹配
     * @return 用户信息的列表
     */
    List<MemberUserDO> getUserListByNickname(String nickname);

    /**
     * 基于手机号创建用户。
     * 如果用户已经存在，则直接进行返回
     *
     * @param mobile 手机号
     * @param registerIp 注册 IP
     * @return 用户对象
     */
    MemberUserDO createUserIfAbsent(@Mobile String mobile, String registerIp);

    /**
     * 更新用户的最后登陆信息
     *
     * @param id 用户编号
     * @param loginIp 登陆 IP
     */
    void updateUserLogin(Long id, String loginIp);

    /**
     * 通过用户 ID 查询用户
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    MemberUserDO getUser(Long id);

    /**
     * 通过用户 ID 查询用户们
     *
     * @param ids 用户 ID
     * @return 用户对象信息数组
     */
    List<MemberUserDO> getUserList(Collection<Long> ids);

    /**
     * 修改用户昵称
     * @param userId 用户id
     * @param nickname 用户新昵称
     */
    void updateUserNickname(Long userId, String nickname);

    /**
     * 修改用户头像
     * @param userId 用户id
     * @param inputStream 头像文件
     * @return 头像url
     */
    String updateUserAvatar(Long userId, InputStream inputStream) throws Exception;

    /**
     * 修改手机
     * @param userId 用户id
     * @param reqVO 请求实体
     */
    void updateUserMobile(Long userId, AppUserUpdateMobileReqVO reqVO);

    /**
     * 修改登录密码
     * @param userId 用户id
     * @param reqVO 请求实体
     */
    void updateUserPassword(Long userId, AppUserUpdatePasswordReqVO reqVO);

    /**
     * 判断密码是否匹配
     *
     * @param rawPassword 未加密的密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    boolean isPasswordMatch(String rawPassword, String encodedPassword);

    /**
     * 通过条件查询用户列表
     *
     * @param reqVO 入参
     * @return 用户对象信息数组
     */
    PageResult<MemberUserDO> getUserPage(UserQueryReqVO reqVO);

    /**
     * 根据员工编号查询用户其它信息，如部门，姓名等
     * @param userNo
     */
    void fillDetailByUserNo(String userNo);

    /**
     * 删除会员
     *
     * @param id 用户编号
     */
    void deleteById(Long id);

    /**
     * 创建会员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMemberUser(@Valid MemberUserCreateReqVO createReqVO);

    /**
     * 更新会员
     *
     * @param updateReqVO 更新信息
     */
    void updateMemberUser(@Valid MemberUserUpdateReqVO updateReqVO);

    /**
     * 登录用户数
     * @return
     */
    Long getUserCount();

    /**
     * 根据会员构建部门V2
     * @param parentId 上级部门ID
     */
    void buildDeptArchitectureV2(Long parentId);

    /**
     * 根据部门数据构建部门结构V3
     */
    void buildDeptArchitectureV3();

    /**
     * 检查当前所有会员信息并设置部门V4
     */
    void buildDeptArchitectureV4();

    /**
     * 搜索会员
     * @param keyword 模糊匹配姓名和和工号
     * @param limit
     * @return
     */
    List<MemberUserDO> searchUserList(String keyword, Integer limit);

}
