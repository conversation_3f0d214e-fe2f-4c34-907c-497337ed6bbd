package cn.iocoder.yudao.module.mall;

import com.alibaba.ttl.TtlRunnable;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * 项目的启动类
 *
 * <AUTHOR>
@SpringBootApplication
public class MallServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(MallServerApplication.class, args);
    }

    @Bean
    public RestTemplate getRestTemplate() {
        return new RestTemplate();
    }

    @Bean
    @Primary
    public ThreadPoolExecutor taskExecutor() {
        int coreThreads = Runtime.getRuntime().availableProcessors() * 2;
        int maxThreads = coreThreads + 30;
        int keepAliveTime = 60;
        TimeUnit timeUnit = TimeUnit.SECONDS;
        BlockingQueue<Runnable> queue = new LinkedBlockingQueue<>(600);
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(coreThreads, maxThreads, keepAliveTime, timeUnit, queue);
        poolExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        poolExecutor.setThreadFactory((r) -> new Thread(TtlRunnable.get(r)));

        return poolExecutor;
    }

}
