package cn.iocoder.yudao.module.mall.product.service.category;

import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryListReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 商品分类 Service 接口
 *
 * <AUTHOR>
 */
public interface ConfigProductCategoryService {

    /**
     * 创建商品分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCategory(@Valid ProductCategoryCreateReqVO createReqVO);

    /**
     * 更新商品分类
     *
     * @param updateReqVO 更新信息
     */
    void updateCategory(@Valid ProductCategoryUpdateReqVO updateReqVO);

    /**
     * 删除商品分类
     *
     * @param id 编号
     */
    void deleteCategory(Long id);

    /**
     * 获得商品分类
     *
     * @param id 编号
     * @return 商品分类
     */
    ConfigProductCategoryDO getCategory(Long id);

    /**
     * 获得商品分类
     *
     * @param categoryId 外部分类编号
     * @return 商品分类
     */
    ConfigProductCategoryDO getCategoryByCategoryId(Long categoryId);

    /**
     * 获取分类名称路径
     * @param idPath, 逗号分隔
     * @param seperator, 分隔符
     * @return
     */
    String getNamePath(String idPath, String seperator);

    /**
     * 获取分类名称路径
     * @param ids 分类ID
     * @param seperator, 分隔符
     * @return
     */
    String getNamePath(List<Long> ids, String seperator);

    /**
     * 校验商品分类
     *
     * @param id 分类编号
     */
    void validateCategory(Long id);

    /**
     * 获得商品分类列表
     *
     * @param listReqVO 查询条件
     * @return 商品分类列表
     */
    List<ConfigProductCategoryDO> getAllCategoryList(ProductCategoryListReqVO listReqVO);

    /**
     * 获取一级分类列表
     * @param type 类型
     * @param status 状态
     * @return
     */
    List<ConfigProductCategoryDO> getRootCategoryList(Integer type, ProductCategoryStatusEnum status);

    /**
     * 更新分类状态
     * @param id
     * @param status
     */
    void updateCategoryStatus(Long id, ProductCategoryStatusEnum status);

    /**
     * 获取下级分类列表
     * @param parentCategoryId
     * @param status 状态
     * @return
     */
    List<ConfigProductCategoryDO> getChildCategoryList(Long parentCategoryId, Integer status);

    /**
     * 获取下级分类树形列表，会填充所有子节点
     * @param parentCategoryId
     * @param status 状态
     * @return
     */
    List<ConfigProductCategoryDO> getChildCategoryTreeList(Long parentCategoryId, ProductCategoryStatusEnum status);

    /**
     * 查询分类总数量
     * @param type 类型
     * @return
     */
    Long getCategoryTotalCount(Integer type);

    /**
     * 根据分类ID获取分类列表
     * @param categoryIdList 外部分类ID集合
     * @return
     */
    List<ConfigProductCategoryDO> getCategoryListByCategoryId(Collection<Long> categoryIdList);

    /**
     * 同步vop商品分类，支持增量同步
     * @return
     */
    boolean sync();

}
