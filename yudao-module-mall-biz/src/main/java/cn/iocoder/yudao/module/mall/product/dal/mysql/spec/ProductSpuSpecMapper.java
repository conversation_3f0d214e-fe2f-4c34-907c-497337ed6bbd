package cn.iocoder.yudao.module.mall.product.dal.mysql.spec;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSpuSpecDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * spu规格关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSpuSpecMapper extends BaseMapperX<ProductSpuSpecDO> {

    void deleteById2(Long id);

    void deleteBySpuId(Long spuId);

}
