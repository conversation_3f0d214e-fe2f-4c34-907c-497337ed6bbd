package cn.iocoder.yudao.module.mall.external.assets.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/6/23
 */
@Getter
public enum AssetClientTypeEnum {

    LUOBI("luobi","洛比"),
    LUOBI_V20("luobi-v20","洛比-家具类资产"),
    PULUODI("puluodi","普诺迪"),
    SIBIDE_DB("sibide-db","思必得-中间库");

    private String type;

    private String desc;

    AssetClientTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
