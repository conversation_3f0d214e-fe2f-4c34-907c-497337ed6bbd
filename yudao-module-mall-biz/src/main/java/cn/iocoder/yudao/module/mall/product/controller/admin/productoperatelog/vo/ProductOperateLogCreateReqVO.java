package cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商品操作日志记录创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductOperateLogCreateReqVO extends ProductOperateLogBaseVO {

}
