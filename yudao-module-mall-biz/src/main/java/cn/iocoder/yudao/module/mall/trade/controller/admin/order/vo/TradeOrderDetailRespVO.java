package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import cn.iocoder.yudao.module.mall.trade.controller.admin.base.member.user.MemberUserRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.base.product.property.ProductPropertyValueDetailRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 交易订单的详情 Response VO")
@Data
public class TradeOrderDetailRespVO extends TradeOrderBaseVO {


    /**
     * 订单项列表
     */
    private List<TradeOrderItemBaseVO> items;

    /**
     * 用户信息
     */
    private MemberUserRespVO user;



}
