package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * 删除审批单据
 * <AUTHOR>
 * @date 2023/7/27
 */
@Data
public class DeleteVoucherDTO {


    /**
     * 模块代码（由审批系统分配）
     */
    @JsonProperty("mkdm")
    private String moduleName;

    /**
     * 业务编号 自定义2-4位大写英文，提交登记
     */
    @JsonProperty("ywbh")
    private String businessCode;

    /**
     * 业务流水号 自定义不重复的流水号
     */
    @JsonProperty("ywlsh")
    private String businessNo;
}
