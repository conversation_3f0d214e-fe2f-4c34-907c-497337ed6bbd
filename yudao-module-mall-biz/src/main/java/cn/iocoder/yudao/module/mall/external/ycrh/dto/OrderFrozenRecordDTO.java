package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 订单冻结记录
 * <AUTHOR>
 * @date 2024/5/11
 */
@Data
public class OrderFrozenRecordDTO {

    /**
     * 部门编号
     */
    @JsonProperty("bmbh")
    private String departmentNo;
    /**
     * 项目编号
     */
    @JsonProperty("xmbh")
    private String projectNo;
    /**
     * 冻结/解冻金额
     */
    @JsonProperty("je")
    private String frozenAmount;
    /**
     * 冻结/解冻时间
     */
    @JsonProperty("rq")
    private String frozenTime;
    /**
     * 记录类型：
     * “1”:冻结
     * “2”:解冻
     */
    @JsonProperty("type")
    private String type;

}
