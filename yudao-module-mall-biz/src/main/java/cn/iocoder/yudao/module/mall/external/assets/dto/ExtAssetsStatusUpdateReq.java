package cn.iocoder.yudao.module.mall.external.assets.dto;

import cn.iocoder.yudao.module.mall.trade.enums.order.AssetStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 平台提供给洛比调用接口，更新资产建档状态参数
 */
@Data
public class ExtAssetsStatusUpdateReq {

    /**
     * 订单明细编号
     */
    private String orderDetailNo;
    /**
     * 更新状态，0-待处理 1-建档已提交，2-无须建档，3-建档中，4-建档成功 5-建档失败  {@link AssetStatusEnum}
     */
    private Integer approveStatus;
    /**
     * 审批备注
     */
    private String approveMemo;
    /**
     * 审批人
     */
    private String approveBy;
    /**
     * 审批日期，格式为时间戳，毫秒数
     */
    private Long approveTime;
    /**
     * 建档审批通过时返回的资产编号, 支持多个
     */
    private List<String> assetNo;
    /**
     * 数量
     */
    private Integer count;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 验收单编号
     */
    private String acceptNo;
    /**
     * 临时验收单编号
     */
    private String tmpAcceptNo;

    /**
     * 6大类资产类别代码
     */
    private String category6;

    /**
     * 16大类资产类别代码
     */
    private String category16;

    /**
     * 业务分类，依赖各个本地系统而定；如资产登记-1/资产验收-2/低值品登记-3/资产增值-4/以旧换新-5/耗材登记-6
     */
    private String businessClass;

    /**
     * 申购单URL
     */
    private String applyFile;
    /**
     * 验收单URL
     */
    private String acceptFile;

}
