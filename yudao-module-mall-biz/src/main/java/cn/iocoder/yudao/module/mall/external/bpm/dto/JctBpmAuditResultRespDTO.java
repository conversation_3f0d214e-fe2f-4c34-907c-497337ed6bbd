package cn.iocoder.yudao.module.mall.external.bpm.dto;

import lombok.Data;

/**
 * 金采通审批流审批结果数据结构封装
 * <AUTHOR>
 * @date 2024/8/23
 */
@Data
public class JctBpmAuditResultRespDTO {

    /**
     * 审批级别, 从1开始
     */
    private String approvalLevel;

    /**
     * 审批时间格式"2022-06-23 15:06:45"
     */
    private String approvalTime;

    /**
     * 审批日期格式 20220623
     */
    private String approvalDate;

    /**
     * 审批状态 0 未审批 1审批通过 2审批驳回
     */
    private String auditStatus;

    /**
     * 审批意见
     */
    private String auditResult;

    /**
     * 审批人编号
     */
    private String approvalUserNo;

    /**
     * 审批角色名称
     */
    private String approvalRoleName;

    /**
     * 审批人名称
     */
    private String approvalUserName;


}
