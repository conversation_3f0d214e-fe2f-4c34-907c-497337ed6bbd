package cn.iocoder.yudao.module.mall.product.controller.app.sku;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.mall.product.service.sku.VopReviewsComponentService;
import cn.iocoder.yudao.module.mall.trade.service.order.VopComponentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 21:44
 */

@Tag(name = "商品评价接口")
@RestController
@RequestMapping("/product/reviews")
@Validated
@Slf4j
public class VopReviewsComponentController {

    @Autowired
    VopReviewsComponentService vopReviewsComponentService;


    /**
     * 商品评价组件Url
     *
     * @param skuId
     * @return
     */
    @GetMapping(value = "/getReviewsComponentUrl")
    @Operation(summary = "商品评价组件Url")
    public CommonResult<String> getReviewsComponentUrl(@RequestParam(value = "skuId", required = true) Long skuId) {
        return CommonResult.success(vopReviewsComponentService.getReviewsComponentUrl(skuId));
    }


}
