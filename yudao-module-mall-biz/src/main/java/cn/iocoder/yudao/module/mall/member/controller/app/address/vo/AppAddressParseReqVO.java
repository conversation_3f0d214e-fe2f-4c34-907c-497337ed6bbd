package cn.iocoder.yudao.module.mall.member.controller.app.address.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * 用户 APP - 文本地址解析 Request VO
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 文本地址解析 Request VO")
@Data
@ToString(callSuper = true)
public class AppAddressParseReqVO {

    @Schema(description = "地址文本内容")
    @NotBlank(message = "地址文本内容不能为空")
    private String content;

}
