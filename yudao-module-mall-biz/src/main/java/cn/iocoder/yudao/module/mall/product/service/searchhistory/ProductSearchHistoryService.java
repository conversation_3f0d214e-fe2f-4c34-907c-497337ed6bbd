package cn.iocoder.yudao.module.mall.product.service.searchhistory;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.usersearchhistory.vo.UserSearchHistoryPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.searchhistory.ProductSearchHistoryDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员搜索历史 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductSearchHistoryService extends IService<ProductSearchHistoryDO> {

    /**
     * 查询用户的搜索历史
     * @param userId 用户ID
     * @param count 最多返回的数量
     * @return
     */
    List<ProductSearchHistoryDO> getList(Long userId, Integer count);

    /**
     * 增加用户搜索历史
     * @param userId
     * @param keyword
     */
    void addUserSearchHistory(Long userId, String keyword);

    /**
     * 根据用户ID删除搜索历史
     * @param userId
     */
    void deleteByUser(Long userId);

    /**
     * 根据搜索词删除搜索历史
     * @param keyword
     */
    void deleteByKeyWord(Long userId,String keyword);

    List<ProductSearchHistoryDO> getHotList(Integer count);

    /**
     * 后台分页获取用户搜索历史
     * @param reqVO
     */
    PageResult<ProductSearchHistoryDO> getUserSearchHistoryPage(@Valid UserSearchHistoryPageReqVO reqVO);
    /**
     * 根据历史id删除搜索历史
     * @param id
     */
    void deleteById(Long id);
}
