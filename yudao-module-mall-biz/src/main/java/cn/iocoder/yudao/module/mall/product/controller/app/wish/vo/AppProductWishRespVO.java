package cn.iocoder.yudao.module.mall.product.controller.app.wish.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "商城前台 - 心愿单 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppProductWishRespVO extends AppProductWishBaseVO {

    @Schema(description = "id", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    /**
     * 状态: 0-待回复 1-已回复
     */
    @Schema(description = "status", required = true)
    private Integer status;

}
