package cn.iocoder.yudao.module.mall.product.dal.dataobject.skustock;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * sku库存 DO
 *
 * <AUTHOR>
 */
@TableName("product_sku_stock")
@KeySequence("product_sku_stock_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSkuStockDO extends TenantBaseDO {

    /**
     * 自增ID
     */
    @TableId
    private Long id;

    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * SKU ID
     */
    private Long skuId;
    /**
     * 库存
     */
    private Integer stock;

    /**
     * 预警库存
     */
    private Integer warnStock;

    /**
     * 预占库存
     */
    private Integer reserveStock;

}
