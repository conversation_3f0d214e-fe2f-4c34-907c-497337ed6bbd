package cn.iocoder.yudao.module.mall.product.controller.app.spu.vo;

import lombok.Data;

import java.util.List;

/**
 * 商品spu信息
 * <AUTHOR>
 * @date 2023/9/21
 */
@Data
public class ProductSpuBaseRespOpenVO {

    /**
     * 商品 SPU 编号，自增
     */
    private Long spuId;

    /**
     * 供应商内部商品spuId
     */
    private String spuInnerId;

    /**
     * 商品sku基本信息
     */
    private List<SkuRespVO> skus;


    @Data
    public static class SkuRespVO {

        /**
         * 商品skuId
         */
        private Long skuId;

        /**
         * 供应商内部商品skuId
         */
        private String skuInnerId;
    }

}
