package cn.iocoder.yudao.module.mall.member.controller.app.auth;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.common.util.tenant.TenantIdUtils;
import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.framework.security.config.SecurityProperties;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.basisconfig.BasisConfigDO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.SsoConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.basisconfig.BasisConfigService;
import cn.iocoder.yudao.module.mall.basis.service.integration.SsoConfigService;
import cn.iocoder.yudao.module.mall.enums.basis.SsoTypeEnum;
import cn.iocoder.yudao.module.mall.external.sso.cas.CasContextHolder;
import cn.iocoder.yudao.module.mall.external.sso.cas.util.CasUtils;
import cn.iocoder.yudao.module.mall.member.controller.app.auth.vo.*;
import cn.iocoder.yudao.module.mall.member.convert.auth.AuthConvert;
import cn.iocoder.yudao.module.mall.member.service.auth.MemberAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.error;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.mall.member.enums.ErrorCodeConstants.*;


@Tag(name = "用户 APP - 认证")
@RestController
@RequestMapping("/member/auth")
@Validated
@Slf4j
public class AppAuthController {

    @Resource
    private MemberAuthService authService;
    @Resource
    private SecurityProperties securityProperties;
    @Resource
    private SsoConfigService ssoConfigService;
    @Resource
    private BasisConfigService basisConfigService;

    @PostMapping("/sso-login")
    @Operation(summary = "统一身份登录")
    public CommonResult<AppAuthLoginRespVO> ssoLogin4Cas(@RequestBody @Valid AppSsoLoginReqVO reqVO) {
        try {
            log.info("sso-login params:{}", reqVO);
            // 识别租户
            Long tenantId = TenantIdUtils.decryptTenantId(reqVO.getTcode());
            TenantContextHolder.setTenantId(tenantId);
            TenantContextHolder.setIgnore(false);

            if(ObjectUtil.equal(SsoTypeEnum.SSO_YCRH.getType(), reqVO.getEtype())) {
                AppAuthLoginRespVO respVO = authService.ssoLogin(reqVO);
                return success(respVO);
            }

            if(ObjectUtil.equal(SsoTypeEnum.SSO_CAS.getType(), reqVO.getEtype())) {
                Map<String, Object> userInfo = CasContextHolder.getCasUserInfo();
                String reqUid = reqVO.getUid();
                String uid = CasContextHolder.getUserId();
                CasContextHolder.clear();
                log.info("sso-cas userInfo: {}, {}, {}", userInfo, uid, reqUid);
                if (StrUtil.isNotBlank(uid) && ObjectUtil.equal(uid, reqUid)) {
                    AppAuthLoginRespVO respVO = authService.ssoCasLogin(AuthConvert.INSTANCE.convert(userInfo, ssoConfigService.getSsoConfig()));
                    return success(respVO);
                }
            } else {
                log.error("sso-cas登录失败...");
            }
        } catch (Exception e) {
            log.error("sso-cas登录失败: {}", reqVO);
        }

        return error(AUTH_SSO_CAS_ERROR);
    }

    @GetMapping("/sso-logout")
    @Operation(summary = "SSO-CAS退出登录")
    @ResponseBody
    public CommonResult<String> casLogout(HttpServletRequest request, String redirect) {
        request.getSession().invalidate();
        SsoConfigDO configDO = ssoConfigService.getSsoConfig();
        String url = "";
        if (ServletUtils.isMobileRequest() && StringUtils.isNotBlank(configDO.getCasClientHostUrl2())) {
            url = CasUtils.buildCasLogoutUrl(configDO.getCasServerLogoutUrl(), configDO.getCasClientHostUrl2());
        } else {
            url = CasUtils.buildCasLogoutUrl(configDO.getCasServerLogoutUrl(), configDO.getCasClientHostUrl());
        }

        return CommonResult.success(url);
    }

    @GetMapping("/sso-logout-v2")
    @Operation(summary = "SSO-业财接口退出登录")
    @ResponseBody
    public CommonResult<String> ssoLogoutV2(HttpServletRequest request, String redirect) {
        return CommonResult.success(ssoConfigService.getSsoLoginUrl(redirect));
    }

    @PostMapping("/login")
    @Operation(summary = "使用手机/账号 + 密码登录")
    public CommonResult<AppAuthLoginRespVO> login(@RequestBody @Valid AppAuthLoginReqVO reqVO) {
        return success(authService.login(reqVO));
    }

    @PostMapping("/logout")
    @Operation(summary = "登出系统")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request, securityProperties.getTokenHeader());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token);
        }
        return success(true);
    }

    @PostMapping("/refresh-token")
    @Operation(summary = "刷新令牌")
    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<AppAuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return success(authService.refreshToken(refreshToken));
    }

    // ========== 短信登录相关 ==========

    @PostMapping("/sms-login")
    @Operation(summary = "使用手机 + 验证码登录")
    public CommonResult<AppAuthLoginRespVO> smsLogin(@RequestBody @Valid AppAuthSmsLoginReqVO reqVO) {
        return success(authService.smsLogin(reqVO));
    }

    @PostMapping("/sms-create")
    @Operation(summary = "使用手机 + 验证码创建账户")
    public CommonResult<AppAuthLoginRespVO> smsCreate(@RequestBody @Valid AppAuthSmsCreateReqVO reqVO) {
        BasisConfigDO config = basisConfigService.getBasisConfig();
        if (!config.getRegisterSwitch()) {
            return error(AUTH_REGISTER_DISABLED);
        }
        return success(authService.smsCreate(reqVO));
    }

    @PostMapping("/send-sms-code")
    @Operation(summary = "发送手机验证码")
    public CommonResult<Boolean> sendSmsCode(@RequestBody @Valid AppAuthSmsSendReqVO reqVO) {
        authService.sendSmsCode(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/reset-password")
    @Operation(summary = "重置密码", description = "用户忘记密码时使用")
    public CommonResult<Boolean> resetPassword(@RequestBody @Valid AppAuthResetPasswordReqVO reqVO) {
        BasisConfigDO config = basisConfigService.getBasisConfig();
        if (!config.getResetPasswordSwitch()) {
            return error(AUTH_RESET_PASSWORD_DISABLED);
        }
        authService.resetPassword(reqVO);
        return success(true);
    }

    @PostMapping("/update-password")
    @Operation(summary = "修改用户密码", description = "用户修改密码时使用")
    @PreAuthenticated
    public CommonResult<Boolean> updatePassword(@RequestBody @Valid AppAuthUpdatePasswordReqVO reqVO) {
        authService.updatePassword(getLoginUserId(), reqVO);
        return success(true);
    }

    // ========== 社交登录相关 ==========

    @GetMapping("/social-auth-redirect")
    @Operation(summary = "社交授权的跳转")
    @Parameters({
            @Parameter(name = "type", description = "社交类型", required = true),
            @Parameter(name = "redirectUri", description = "跳转地址", required = true)
    })
    public CommonResult<String> socialAuthRedirect(@RequestParam("type") Integer type, @RequestParam("redirectUri") String redirectUri) {
//        String ssoCallbackUrl = String.format("https://jmall.jcy360.cn/bpm/#/pages/ssolink?tcode=%s", TenantIdUtils.encryptTenantId(TenantContextHolder.getTenantId()));
//        ssoCallbackUrl = URLUtil.encode(ssoCallbackUrl);
//        log.info(authService.getSocialAuthorizeUrl(type, ssoCallbackUrl));
//        return CommonResult.success("https://localhost:9090/bpm/#/pages/ssolink?code=fxMxjgyPhjHFR4CKH3jH4TQNwOxKVegxLsdjX9bxWxw&state=da1b15137bd2674255c16e17753cc593&tcode=640f086f8e3341f033edd75e016bc201");
        String res = authService.getSocialAuthorizeUrl(type, redirectUri);
        log.info(res);
        return CommonResult.success(res);
    }

    @PostMapping("/social-login")
    @Operation(summary = "社交快捷登录，使用 code 授权码", description = "适合未登录的用户，但是社交账号已绑定用户")
    public CommonResult<AppAuthLoginRespVO> socialLogin(@RequestBody @Valid AppAuthSocialLoginReqVO reqVO) {
        return success(authService.socialLogin(reqVO));
    }

    @PostMapping("/weixin-mini-app-login")
    @Operation(summary = "微信小程序的一键登录")
    public CommonResult<AppAuthLoginRespVO> weixinMiniAppLogin(@RequestBody @Valid AppAuthWeixinMiniAppLoginReqVO reqVO) {
        return success(authService.weixinMiniAppLogin(reqVO));
    }
}
