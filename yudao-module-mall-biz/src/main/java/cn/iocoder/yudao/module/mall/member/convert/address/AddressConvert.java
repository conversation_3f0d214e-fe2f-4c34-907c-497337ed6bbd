package cn.iocoder.yudao.module.mall.member.convert.address;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.member.api.address.dto.AddressRespDTO;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.AppAddressCreateReqVO;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.AppAddressRespVO;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.AppAddressUpdateReqVO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AddressDO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.AreaDTO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppOpenAddressRespVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 用户收件地址 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AddressConvert {

    AddressConvert INSTANCE = Mappers.getMapper(AddressConvert.class);

    AddressDO convert(AppAddressCreateReqVO bean);

    AddressDO convert(AppAddressUpdateReqVO bean);

    AppAddressRespVO convert(AddressDO bean);

    List<AppAddressRespVO> convertList(List<AddressDO> list);

    PageResult<AppAddressRespVO> convertPage(PageResult<AddressDO> page);

    AddressRespDTO convert02(AddressDO bean);

    AreaDTO convertAreaDTO(AddressDO bean);

    AreaDTO convertAreaDTO(AddressRespDTO bean);


    @Mappings(value = {
            @Mapping(source = "receiverName", target = "name"),
            @Mapping(source = "receiverMobile", target = "mobile"),
            @Mapping(source = "receiverProvince", target = "provinceId"),
            @Mapping(source = "receiverProvinceName", target = "provinceName"),
            @Mapping(source = "receiverCity", target = "cityId"),
            @Mapping(source = "receiverCityName", target = "cityName"),
            @Mapping(source = "receiverCounty", target = "countyId"),
            @Mapping(source = "receiverCountyName", target = "countyName"),
            @Mapping(source = "receiverTown", target = "townId"),
            @Mapping(source = "receiverTownName", target = "townName"),
            @Mapping(source = "receiverConsigneeZip", target = "consigneeZip"),
            @Mapping(source = "receiverDetailAddress", target = "consigneeAddress"),
            @Mapping(target = "userId", ignore = true),
            @Mapping(target = "id", ignore = true)
    })
    AddressRespDTO convertAddress(TradeOrderDO tradeOrderDO);

    @Mappings(value = {
            @Mapping(source = "receiverName", target = "name"),
            @Mapping(source = "receiverMobile", target = "mobile"),
            @Mapping(source = "receiverProvince", target = "provinceId"),
            @Mapping(source = "receiverProvinceName", target = "provinceName"),
            @Mapping(source = "receiverCity", target = "cityId"),
            @Mapping(source = "receiverCityName", target = "cityName"),
            @Mapping(source = "receiverCounty", target = "countyId"),
            @Mapping(source = "receiverCountyName", target = "countyName"),
            @Mapping(source = "receiverTown", target = "townId"),
            @Mapping(source = "receiverTownName", target = "townName"),
            @Mapping(source = "receiverConsigneeZip", target = "consigneeZip"),
            @Mapping(source = "receiverDetailAddress", target = "consigneeAddress")
    })
    AppOpenAddressRespVO convertAddress02(TradeOrderDO tradeOrderDO);


}
