package cn.iocoder.yudao.module.mall.trade.controller.admin.billorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 账单订单 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BillOrderExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("用户id")
    private Long userId;

    @ExcelProperty("账单id")
    private Long billId;

    @ExcelProperty("订单id")
    private Long orderId;

    @ExcelProperty("创建时间")
    private Date createTime;

}
