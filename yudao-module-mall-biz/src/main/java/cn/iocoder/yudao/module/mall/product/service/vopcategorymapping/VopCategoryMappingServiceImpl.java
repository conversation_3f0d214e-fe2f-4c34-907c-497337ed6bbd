package cn.iocoder.yudao.module.mall.product.service.vopcategorymapping;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskUtils;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportRespExcelVO;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportTask;
import com.alibaba.ttl.TtlRunnable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopCategoryMappingDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.module.mall.product.convert.vopcategorymapping.VopCategoryMappingConvert;
import cn.iocoder.yudao.module.mall.product.dal.mysql.vopcategorymapping.VopCategoryMappingMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.VOP_CATEGORY_MAPPING_NOT_EXISTS;

/**
 * 京东分类映射 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class VopCategoryMappingServiceImpl extends ServiceImpl<VopCategoryMappingMapper, VopCategoryMappingDO> implements VopCategoryMappingService {

    @Resource
    private VopCategoryMappingMapper vopCategoryMappingMapper;

    @Resource
    private AsyncFrontTaskUtils asyncFrontTaskUtils;

    @Override
    public Long createVopCategoryMapping(VopCategoryMappingCreateReqVO createReqVO, AsyncImportTask importTask, List<AsyncImportRespExcelVO> importRespList) {
        log.info("租户：{}，createVopCategoryMapping：{}", TenantContextHolder.getTenantId(), createReqVO.getVopLastCategoryId());
        List<VopCategoryMappingDO> vopCategoryMappingDOS = vopCategoryMappingMapper.selectList(
                new LambdaQueryWrapperX<VopCategoryMappingDO>().eq(VopCategoryMappingDO::getVopLastCategoryId, createReqVO.getVopLastCategoryId()));
        if(CollUtil.isNotEmpty(vopCategoryMappingDOS)){
            VopCategoryMappingDO vopCategoryMappingDO = vopCategoryMappingDOS.get(0);
            vopCategoryMappingDO.setLastCategoryId(createReqVO.getLastCategoryId());
            vopCategoryMappingDO.setLastCategoryName(createReqVO.getLastCategoryName());
            vopCategoryMappingDO.setFullCategoryId(createReqVO.getFullCategoryId());
            vopCategoryMappingDO.setFullCategoryName(createReqVO.getFullCategoryName());
            vopCategoryMappingDO.setVopLastCategoryId(createReqVO.getVopLastCategoryId());
            vopCategoryMappingDO.setVopLastCategoryName(createReqVO.getVopLastCategoryName());
            vopCategoryMappingDO.setVopFullCategoryId(createReqVO.getVopFullCategoryId());
            vopCategoryMappingDO.setVopFullCategoryName(createReqVO.getVopFullCategoryName());
            vopCategoryMappingMapper.updateById(vopCategoryMappingDO);
            if(importTask != null && importRespList != null) {
                importRespList.add(importTask.plusUpdated(String.valueOf(createReqVO.getVopLastCategoryId())));
            }
            return vopCategoryMappingDO.getId();
        }

        // 插入
        VopCategoryMappingDO vopCategoryMapping = VopCategoryMappingConvert.INSTANCE.convert(createReqVO);
        vopCategoryMappingMapper.insert(vopCategoryMapping);
        if(importTask != null && importRespList != null) {
            importRespList.add(importTask.plusCreated(String.valueOf(createReqVO.getVopLastCategoryId())   ));
        }
        return vopCategoryMapping.getId();
    }

    @Override
    public void updateVopCategoryMapping(VopCategoryMappingUpdateReqVO updateReqVO) {
        // 校验存在
        validateVopCategoryMappingExists(updateReqVO.getId());
        // 更新
        VopCategoryMappingDO updateObj = VopCategoryMappingConvert.INSTANCE.convert(updateReqVO);
        vopCategoryMappingMapper.updateById(updateObj);
    }

    @Override
    public void deleteVopCategoryMapping(Long id) {
        // 校验存在
        validateVopCategoryMappingExists(id);
        // 删除
        vopCategoryMappingMapper.deleteById(id);
    }

    private void validateVopCategoryMappingExists(Long id) {
        if (vopCategoryMappingMapper.selectById(id) == null) {
            throw exception(VOP_CATEGORY_MAPPING_NOT_EXISTS);
        }
    }

    @Override
    public VopCategoryMappingDO getVopCategoryMapping(Long id) {
        return vopCategoryMappingMapper.selectById(id);
    }

    @Override
    public List<VopCategoryMappingDO> getVopCategoryMappingList(Collection<Long> ids) {
        return vopCategoryMappingMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<VopCategoryMappingDO> getVopCategoryMappingPage(VopCategoryMappingPageReqVO pageReqVO) {
        return vopCategoryMappingMapper.selectPage(pageReqVO);
    }

    @Override
    public List<VopCategoryMappingDO> getVopCategoryMappingList(VopCategoryMappingExportReqVO exportReqVO) {
        return vopCategoryMappingMapper.selectList(exportReqVO);
    }

    @Override
    public void importExcel(List<VopCategoryMappingExcelVO> list) {
        asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), AsyncTaskTypeEnum.PRODUCT_VOP_CATEGORY_MAPPING_IMPORT, TtlRunnable.get(() -> {
            doImportExcel(list);
        }));
    }

    public void doImportExcel(List<VopCategoryMappingExcelVO> list) {
        String taskId = AsyncFrontTaskContext.getTaskId();
        AsyncImportTask importTask = asyncFrontTaskUtils.getAsyncImportTask(taskId);

        if(CollUtil.isEmpty(list)) {
            asyncFrontTaskUtils.taskDone(importTask);
            return;
        }

        try {
            importTask.setTotal(list.size());
            List<AsyncImportRespExcelVO> importRespList = new ArrayList<>();

            AtomicInteger counter = new AtomicInteger(1);
            asyncFrontTaskUtils.updateTask(importTask);

            list.stream().forEach(vopCategoryMappingExcelVO -> {
                counter.incrementAndGet();
                if(counter.get() % 10 == 0) {
                    asyncFrontTaskUtils.updateTask(importTask);
                }

                Boolean categoryIdTypeError = true;
                try {
                    VopCategoryMappingCreateReqVO createReqVO = new VopCategoryMappingCreateReqVO();
                    createReqVO.setLastCategoryId(vopCategoryMappingExcelVO.getLastCategoryId());
                    createReqVO.setLastCategoryName(vopCategoryMappingExcelVO.getLastCategoryName());
                    createReqVO.setFullCategoryId(vopCategoryMappingExcelVO.getLastCategoryName());
                    createReqVO.setFullCategoryName(vopCategoryMappingExcelVO.getLastCategoryName());
                    createReqVO.setVopLastCategoryId(vopCategoryMappingExcelVO.getVopLastCategoryId());
                    createReqVO.setVopLastCategoryName(vopCategoryMappingExcelVO.getVopLastCategoryName());
                    createReqVO.setVopFullCategoryId(vopCategoryMappingExcelVO.getVopFullCategoryId());
                    createReqVO.setVopFullCategoryName(vopCategoryMappingExcelVO.getVopFullCategoryName());
                    categoryIdTypeError = false;
                    createVopCategoryMapping(createReqVO, importTask, importRespList);
                } catch (Exception ex) {
                    if(categoryIdTypeError){
                        importRespList.add(importTask.plusFailed(String.valueOf(vopCategoryMappingExcelVO.getVopLastCategoryId()), "分类ID不是数字"));
                    }
                    else {
                        importRespList.add(importTask.plusFailed(String.valueOf(vopCategoryMappingExcelVO.getVopLastCategoryId()), ex.getMessage()));
                    }
                }
            });

            asyncFrontTaskUtils.updateTask(importTask);
            asyncFrontTaskUtils.importDone(taskId, importRespList);
        } catch (Exception e) {
            log.error("导入京东分类映射失败", e);
            asyncFrontTaskUtils.importFail();
        }
    }

}
