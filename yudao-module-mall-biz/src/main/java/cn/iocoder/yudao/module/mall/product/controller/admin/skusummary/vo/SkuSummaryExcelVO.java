package cn.iocoder.yudao.module.mall.product.controller.admin.skusummary.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 供应商上架商品数统计 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SkuSummaryExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("供应商ID")
    private Long supplierId;

    @ExcelProperty("供应商类型")
    private Integer supplierType;

    @ExcelProperty("供应商名称")
    private String supplierName;

    @ExcelProperty("上架数量")
    private Long listingTotal;

    @ExcelProperty("下架数量")
    private Long delistingTotal;

    @ExcelProperty("截止时间")
    private Date deadline;

    @ExcelProperty("创建时间")
    private Date createTime;

}
