package cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Schema(description = "管理后台 - 运营区域 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSeoCardRespVO extends ProductSeoCardBaseVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "区域内容,json格式")
    private String content;

    @Schema(description = "置顶商品,逗号分隔")
    private String topSku;

    @Schema(description = "创建时间")
    private Date createTime;

}
