package cn.iocoder.yudao.module.mall.product.convert.vopcategorymapping;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopCategoryMappingDO;

/**
 * 京东分类映射 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface VopCategoryMappingConvert {

    VopCategoryMappingConvert INSTANCE = Mappers.getMapper(VopCategoryMappingConvert.class);

    VopCategoryMappingDO convert(VopCategoryMappingCreateReqVO bean);

    VopCategoryMappingDO convert(VopCategoryMappingUpdateReqVO bean);

    VopCategoryMappingRespVO convert(VopCategoryMappingDO bean);

    List<VopCategoryMappingRespVO> convertList(List<VopCategoryMappingDO> list);

    PageResult<VopCategoryMappingRespVO> convertPage(PageResult<VopCategoryMappingDO> page);

    List<VopCategoryMappingExcelVO> convertList02(List<VopCategoryMappingDO> list);

}
