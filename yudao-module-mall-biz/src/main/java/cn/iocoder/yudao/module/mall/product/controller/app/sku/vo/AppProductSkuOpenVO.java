package cn.iocoder.yudao.module.mall.product.controller.app.sku.vo;

import cn.iocoder.yudao.module.mall.product.controller.app.spec.vo.AppProductSpecValueVO;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品sku信息
 * <AUTHOR>
 * @date 2023-09-21
 */
@Data
public class AppProductSkuOpenVO {

    /**
     * 商品skuId
     */
    private Long skuId;

    /**
     * 商品供应商id
     */
    @NotBlank(message = "skuInnerId不能为空")
    @Size(min = 4, max = 50, message = "skuInnerId只允许为4至50个字符")
    private String skuInnerId;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商品skuName
     */
    @NotBlank(message = "skuName不能为空")
    @Size(max = 150, message = "skuName不能超过150个字符")
    private String skuName;

    /**
     * 市场价
     */
    @NotNull(message = "marketPrice不能为空")
    private BigDecimal marketPrice;

    /**
     * 销售价
     */
    @NotNull(message = "salePrice不能为空")
    private BigDecimal salePrice;

    /**
     * 最低起购量
     */
    @NotNull(message = "lowestBuy不能为空")
    @Range(min = 1, max = 9999, message = "lowestBuy必须在1-9999区间")
    private Integer lowestBuy;

    /**
     * 图片地址
     */
    @NotBlank(message = "picUrl不能为空")
    @Size(max = 120, message = "picUrl不能超过200个字符")
    private String picUrl;
    /**
     * 状态：1：上架，0：下架
     */
    @NotNull(message = "status不能为空")
    private Integer status;

    /**
     * sku 属性
     */
    private List<AppProductSpecValueVO> skuSpecValueList;

    /**
     * 商品库存数量
     */
    @NotBlank(message = "stockCount不能为空")
    @Range(min = 1, max = 999999, message = "stockCount必须在1-999999区间")
    private Integer stockCount;

}
