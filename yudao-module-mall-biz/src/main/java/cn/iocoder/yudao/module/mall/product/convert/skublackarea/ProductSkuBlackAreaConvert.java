package cn.iocoder.yudao.module.mall.product.convert.skublackarea;

import java.util.*;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skublackarea.ProductSkuBlackAreaDO;

/**
 * 商品sku禁售区域 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSkuBlackAreaConvert {

    ProductSkuBlackAreaConvert INSTANCE = Mappers.getMapper(ProductSkuBlackAreaConvert.class);

    ProductSkuBlackAreaDO convert(ProductSkuBlackAreaCreateReqVO bean);

    ProductSkuBlackAreaDO convert(ProductSkuBlackAreaUpdateReqVO bean);

    ProductSkuBlackAreaRespVO convert(ProductSkuBlackAreaDO bean);

    List<ProductSkuBlackAreaRespVO> convertList(List<ProductSkuBlackAreaDO> list);

    PageResult<ProductSkuBlackAreaRespVO> convertPage(PageResult<ProductSkuBlackAreaDO> page);

    List<ProductSkuBlackAreaExcelVO> convertList02(List<ProductSkuBlackAreaDO> list);

    default List<ProductSkuBlackAreaDO> convert(List<ProductSkuBlackAreaReqVO> reqVOS, Long supplierId) {
        List<ProductSkuBlackAreaDO> productSkuBlackAreaDOS = new ArrayList<>();
        for (ProductSkuBlackAreaReqVO reqVO : reqVOS) {
            List<ProductSkuBlackAreaReqVO.Area> areas = reqVO.getAreas();
            for (ProductSkuBlackAreaReqVO.Area area : areas) {
                ProductSkuBlackAreaDO productSkuBlackAreaDO = convertDO(reqVO);
                productSkuBlackAreaDO.setProvinceId(area.getProvinceId());
                productSkuBlackAreaDO.setCityId(area.getCityId());
                productSkuBlackAreaDO.setCountyId(area.getCountyId());
                productSkuBlackAreaDO.setSupplierId(supplierId);
                productSkuBlackAreaDOS.add(productSkuBlackAreaDO);
            }
        }
        return productSkuBlackAreaDOS.size() > 0 ? productSkuBlackAreaDOS : null;
    }

    ProductSkuBlackAreaDO convertDO(ProductSkuBlackAreaReqVO reqVO);


}
