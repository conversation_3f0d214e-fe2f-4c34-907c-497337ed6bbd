package cn.iocoder.yudao.module.mall.vop.service;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.mq.producer.product.ClientTenantVopSkuPoolProducer;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuOpenVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuOpenVO;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskUtils;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportRespExcelVO;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportTask;
import cn.iocoder.yudao.module.mall.vop.controller.admin.vopskucategory.vo.*;
import cn.iocoder.yudao.module.mall.vop.convert.vopskucategory.VopSkuCategoryConvert;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopSkuCategoryDO;
import cn.iocoder.yudao.module.mall.vop.dal.mysql.VopSkuCategoryMapper;
import com.alibaba.ttl.TtlRunnable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.recycler.Recycler;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.VOP_SKU_CATEGORY_NOT_EXISTS;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.VOP_SKU_NOT_EXISTS;

/**
 * 京东sku分类关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class VopSkuCategoryServiceImpl  extends ServiceImpl<VopSkuCategoryMapper, VopSkuCategoryDO> implements VopSkuCategoryService {

    @Resource
    private VopSkuCategoryMapper vopSkuCategoryMapper;

    @Lazy
    @Resource
    private VopGoodsBridgeService vopGoodsBridgeService;

    @Resource
    private SupplierService supplierService;

    @Lazy
    @Resource
    private ProductSkuService productSkuService;

    @Resource
    private ClientTenantVopSkuPoolProducer clientTenantVopSkuPoolProducer;

    @Resource
    private AsyncFrontTaskUtils asyncFrontTaskUtils;

    @Override
    public Long createVopSkuCategory(VopSkuCategoryCreateReqVO createReqVO, AsyncImportTask importTask, List<AsyncImportRespExcelVO> importRespList) {
        log.info("租户：{}，createVopSkuCategory：{}", TenantContextHolder.getTenantId(), createReqVO.getSkuInnerId());
        List<VopSkuCategoryDO> vopSkuCategoryDOS = vopSkuCategoryMapper.selectList(new LambdaQueryWrapperX<VopSkuCategoryDO>().eq(VopSkuCategoryDO::getSkuInnerId, createReqVO.getSkuInnerId()));
        if(CollUtil.isNotEmpty(vopSkuCategoryDOS)){
            VopSkuCategoryDO vopSkuCategory = vopSkuCategoryDOS.get(0);
            vopSkuCategory.setSkuName(createReqVO.getSkuName());
            vopSkuCategory.setPicUrl(createReqVO.getPicUrl());
            vopSkuCategory.setCategoryId(createReqVO.getCategoryId());
            vopSkuCategory.setCategoryName(createReqVO.getCategoryName());
            vopSkuCategory.setFullCategoryId(createReqVO.getFullCategoryId());
            vopSkuCategory.setFullCategoryName(createReqVO.getFullCategoryName());
            vopSkuCategoryMapper.updateById(vopSkuCategory);
            if(importTask != null && importRespList != null) {
                importRespList.add(importTask.plusUpdated(createReqVO.getSkuInnerId()));
            }
            return vopSkuCategory.getId();
        }

        SupplierDO supplierDO = supplierService.getSupplierJD();
        ProductSpuOpenVO productSpuOpenVO = vopGoodsBridgeService.goodsDetailSync(Long.valueOf(createReqVO.getSkuInnerId()), supplierDO.getId(), null, createReqVO.getCategoryId());
        if(productSpuOpenVO != null) {
            for(AppProductSkuOpenVO sku : productSpuOpenVO.getSkus()){
                productSkuService.validProductRule(sku.getSkuInnerId(), productSpuOpenVO.getFullCategoryId(), sku.getSalePrice(),
                        Arrays.asList(productSpuOpenVO.getSpuName(), sku.getSkuName()), sku.getStatus());
                if(createReqVO.getSkuInnerId().equals(sku.getSkuInnerId())){
                    createReqVO.setSkuName(sku.getSkuName());
                    createReqVO.setPicUrl(sku.getPicUrl());
                    createReqVO.setFullCategoryId(productSpuOpenVO.getFullCategoryId());
                    createReqVO.setFullCategoryName(productSpuOpenVO.getFullCategoryName());
                    List<String> fullCategoryIdList = Arrays.asList(productSpuOpenVO.getFullCategoryId().split("-"));
                    List<String> fullCategoryNameList = Arrays.asList(productSpuOpenVO.getFullCategoryName().split("/"));
                    createReqVO.setCategoryId(Long.valueOf(fullCategoryIdList.get(fullCategoryIdList.size() - 1)));
                    createReqVO.setCategoryName(fullCategoryNameList.get(fullCategoryNameList.size() - 1));
                }
            }
            productSkuService.saveOrUpdateProductSku(productSpuOpenVO, supplierDO.getId(), null);
            // 插入
            VopSkuCategoryDO vopSkuCategory = vopSkuCategoryMapper.selectOne(new LambdaQueryWrapperX<VopSkuCategoryDO>().eq(VopSkuCategoryDO::getSkuInnerId, createReqVO.getSkuInnerId()));
            if(vopSkuCategory != null){
                vopSkuCategory.setSkuName(createReqVO.getSkuName());
                vopSkuCategory.setPicUrl(createReqVO.getPicUrl());
                vopSkuCategory.setCategoryId(createReqVO.getCategoryId());
                vopSkuCategory.setCategoryName(createReqVO.getCategoryName());
                vopSkuCategory.setFullCategoryId(createReqVO.getFullCategoryId());
                vopSkuCategory.setFullCategoryName(createReqVO.getFullCategoryName());
                vopSkuCategoryMapper.updateById(vopSkuCategory);
                if(importTask != null && importRespList != null) {
                    importRespList.add(importTask.plusUpdated(createReqVO.getSkuInnerId()));
                }
            }
            else {
                vopSkuCategory = VopSkuCategoryConvert.INSTANCE.convert(createReqVO);
                vopSkuCategoryMapper.insert(vopSkuCategory);
                if(importTask != null && importRespList != null) {
                    importRespList.add(importTask.plusCreated(createReqVO.getSkuInnerId()));
                }
            }
            // // 发送vop商品添加消息到vop客户端
            clientTenantVopSkuPoolProducer.sendTenantVopSkuPoolChange(Collections.singleton(Long.valueOf(createReqVO.getSkuInnerId())), 1);
            // 返回
            return vopSkuCategory.getId();
        }
        else {
            throw new ServiceException(VOP_SKU_NOT_EXISTS);
        }
    }

    @Override
    public void updateVopSkuCategory(VopSkuCategoryUpdateReqVO updateReqVO) {
        log.info("租户：{}，updateVopSkuCategory：{}", TenantContextHolder.getTenantId(), updateReqVO.getSkuInnerId());
        // 校验存在
        validateVopSkuCategoryExists(updateReqVO.getId());
        SupplierDO supplierDO = supplierService.getSupplierJD();
        ProductSpuOpenVO productSpuOpenVO = vopGoodsBridgeService.goodsDetailSync(Long.valueOf(updateReqVO.getSkuInnerId()), supplierDO.getId(), null, updateReqVO.getCategoryId());
        if(productSpuOpenVO != null) {
            for (AppProductSkuOpenVO sku : productSpuOpenVO.getSkus()) {
                productSkuService.validProductRule(sku.getSkuInnerId(), productSpuOpenVO.getFullCategoryId(), sku.getSalePrice(),
                        Arrays.asList(productSpuOpenVO.getSpuName(), sku.getSkuName()), sku.getStatus());
                if (updateReqVO.getSkuInnerId().equals(sku.getSkuInnerId())) {
                    updateReqVO.setSkuName(sku.getSkuName());
                    updateReqVO.setPicUrl(sku.getPicUrl());
                }
            }
            productSkuService.saveOrUpdateProductSku(productSpuOpenVO, supplierDO.getId(), null);
            // 更新
            VopSkuCategoryDO updateObj = VopSkuCategoryConvert.INSTANCE.convert(updateReqVO);
            vopSkuCategoryMapper.updateById(updateObj);
        }
    }

    @Override
    public void deleteVopSkuCategory(Long id) {
        // 校验存在
        validateVopSkuCategoryExists(id);
        SupplierDO supplierDO = supplierService.getSupplierJD();
        VopSkuCategoryDO vopSkuCategory = vopSkuCategoryMapper.selectOne(new LambdaQueryWrapperX<VopSkuCategoryDO>().eq(VopSkuCategoryDO::getId, id));
        if(vopSkuCategory != null) {
            vopGoodsBridgeService.deleteGoods(Long.valueOf(vopSkuCategory.getSkuInnerId()), supplierDO.getId());
        }
        // 删除
        vopSkuCategoryMapper.deleteById(id);

        // 发送vop商品移除消息到vop客户端
        clientTenantVopSkuPoolProducer.sendTenantVopSkuPoolChange(Collections.singleton(Long.valueOf(vopSkuCategory.getSkuInnerId())), 0);
    }

    private void validateVopSkuCategoryExists(Long id) {
        if (vopSkuCategoryMapper.selectById(id) == null) {
            throw exception(VOP_SKU_CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public VopSkuCategoryDO getVopSkuCategory(Long id) {
        return vopSkuCategoryMapper.selectById(id);
    }

    @Override
    public List<VopSkuCategoryDO> getVopSkuCategoryList(Collection<Long> ids) {
        return vopSkuCategoryMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<VopSkuCategoryDO> getVopSkuCategoryPage(VopSkuCategoryPageReqVO pageReqVO) {
        return vopSkuCategoryMapper.selectPage(pageReqVO);
    }

    @Override
    public List<VopSkuCategoryDO> getVopSkuCategoryList(VopSkuCategoryExportReqVO exportReqVO) {
        return vopSkuCategoryMapper.selectList(exportReqVO);
    }

    @Override
    public VopSkuCategoryDO getVopSkuCategoryByJdSkuId(String jdSkuId) {
        return vopSkuCategoryMapper.selectOne(VopSkuCategoryDO::getSkuInnerId, jdSkuId);
    }

    @Override
    public List<VopSkuCategoryDO> getList(){
        return vopSkuCategoryMapper.selectList();
    }

    @Override
    public void importExcel(List<VopSkuCategoryImportExcelVO> list) {
        asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), AsyncTaskTypeEnum.VOP_SKU_CATEGORY_IMPORT, TtlRunnable.get(() -> {
            doImportExcel(list);
        }));
    }

    public void doImportExcel(List<VopSkuCategoryImportExcelVO> list) {
        String taskId = AsyncFrontTaskContext.getTaskId();
        AsyncImportTask importTask = asyncFrontTaskUtils.getAsyncImportTask(taskId);

        if(CollUtil.isEmpty(list)) {
            asyncFrontTaskUtils.taskDone(importTask);
            return;
        }

        try {
            importTask.setTotal(list.size());
            List<AsyncImportRespExcelVO> importRespList = new ArrayList<>();

            AtomicInteger counter = new AtomicInteger(1);
            asyncFrontTaskUtils.updateTask(importTask);

            list.stream().forEach(vopSkuCategoryImportExcelVO -> {
                counter.incrementAndGet();
                if(counter.get() % 10 == 0) {
                    asyncFrontTaskUtils.updateTask(importTask);
                }

                Boolean categoryIdTypeError = true;
                try {
                    VopSkuCategoryCreateReqVO createReqVO = new VopSkuCategoryCreateReqVO();
                    createReqVO.setSkuInnerId(vopSkuCategoryImportExcelVO.getSkuInnerId());
                    createReqVO.setCategoryId(Long.valueOf(vopSkuCategoryImportExcelVO.getCategoryId()));
                    categoryIdTypeError = false;
                    createVopSkuCategory(createReqVO, importTask, importRespList);
                } catch (Exception ex) {
                    if(categoryIdTypeError){
                        importRespList.add(importTask.plusFailed(vopSkuCategoryImportExcelVO.getSkuInnerId(), "分类ID不是数字"));
                    }
                    else {
                        importRespList.add(importTask.plusFailed(vopSkuCategoryImportExcelVO.getSkuInnerId(), ex.getMessage()));
                    }
                }
            });

            asyncFrontTaskUtils.updateTask(importTask);
            asyncFrontTaskUtils.importDone(taskId, importRespList);
        } catch (Exception e) {
            log.error("商品池导入失败", e);
            asyncFrontTaskUtils.importFail();
        }
    }

}
