package cn.iocoder.yudao.module.mall.product.dal.dataobject.tag;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 标签分组 DO
 *
 * <AUTHOR>
 */
@TableName("product_tag_group")
@KeySequence("product_tag_group_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductTagGroupDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 分组名称
     */
    private String name;
    /**
     * 分组描述
     */
    private String memo;
    /**
     * 状态, 0-启用，1-禁用, {@link CommonStatusEnum}
     */
    private Integer status;

}
