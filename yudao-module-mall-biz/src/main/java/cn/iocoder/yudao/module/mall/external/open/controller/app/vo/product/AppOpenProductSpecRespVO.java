package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 商品规格 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class AppOpenProductSpecRespVO {

    @Schema(description = "规格ID")
    private Long id;

    @Schema(description = "规格名")
    private String name;

    @Schema(description = "规格类型, 1:spu通用类型, 2:sku销售类型")
    private Integer specType;

}
