package cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "商城前台 - 我的足迹更新 Request VO")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppMyfootprintUpdateReqVO extends AppMyFootprintBaseVO{

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "id不能为空")
    private Long id;
}
