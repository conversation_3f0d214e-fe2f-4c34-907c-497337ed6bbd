package cn.iocoder.yudao.module.mall.basis.controller.admin.basisconfig.vo.basis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

/**
 * 采购方基础配置 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class BasisConfigBaseVO {

    @Schema(description = "采购方名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "采购方名称不能为空")
    private String name;

    @Schema(description = "多供应商开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "多供应商开关不能为空")
    private Boolean multipleShop;

    @Schema(description = "自动审批开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "自动审批开关不能为空")
    private Boolean autoApprove;

    @Schema(description = "前置审批开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "前置审批开关不能为空")
    private Boolean approveSwitch;

    @Schema(description = "项目经费卡开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目经费卡开关不能为空")
    private Boolean projectSwitch;

    @Schema(description = "仪器设备采购表导出开关")
    private Boolean instrumentApplyExport;

    @Schema(description = "送货单导出开关")
    private Boolean orderReceiptExport;

    @Schema(description = "查看价格强制登录开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "查看价格强制登录开关不能为空")
    private Boolean getPriceMustLogin;

    @Schema(description = "资产登记开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "资产登记开关不能为空")
    private Boolean assetsSwitch;

    @Schema(description = "资产手动推送开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "资产手动推送开关不能为空")
    private Boolean assetsManualSwitch;

    @Schema(description = "固资商品混合下单开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "固资商品混合下单不能为空")
    private Boolean assetsMixSwitch;

    @Schema(description = "固资线下结算开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "固资线下结算开关不能为空")
    private Boolean assetsOfflinePaySwitch;

    @Schema(description = "提交订单开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "提交订单开关不能为空")
    private Boolean orderSwitch;

    @Schema(description = "提交订单白名单")
    private String orderWhiteList;

    @Schema(description = "手机号注册开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "手机号注册开关不能为空")
    private Boolean registerSwitch;

    @Schema(description = "手机号重置密码开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "手机号重置密码开关不能为空")
    private Boolean resetPasswordSwitch;

    @Schema(description = "订单完成后自动申请开票开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单完成后自动申请开票开关不能为空")
    private Boolean autoInvoiceSwitch;

    @Schema(description = "发票校验开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "发票校验开关不能为空")
    private Boolean verifyInvoice;

    @Schema(description = "自动开票阈值，如N天后自动发送开票申请")
    private Integer autoInvoiceThreshold;

    @Schema(description = "LOGO地址")
    private String logoUrl;

    @Schema(description = "域名")
    private String domain;

    @Schema(description = "登录类型:1-内部登录,2-外部登录")
    private Integer loginType;

    @Schema(description = "登录方式，20-验证码登录 10-帐号登录")
    private String loginMethod;

    @Schema(description = "登录地址")
    private String loginUrl;

    @Schema(description = "主题颜色")
    private Integer themeCode;

    @Schema(description = "收藏图标地址")
    private String faviconUrl;

    @Schema(description = "退出方式")
    private Integer logoutType;

    @Schema(description = "商城标题")
    private String title;

    @Schema(description = "支付方式")
    private String payMethod;

    @Schema(description = "全局提示开关")
    private Boolean globalTipSwitch;

    @Schema(description = "全局提示内容")
    private String globalTipContent;

    @Schema(description = "商品展示字段, 如商品列表供应商名称，以逗号分隔")
    private String productField;

    @Schema(description = "商品评价开关")
    private Boolean productCommentSwitch;

    @Schema(description = "地址备注开关")
    private Boolean addressRemarkSwitch;

    @Schema(description = "地址备注")
    private String addressRemark;

    @Schema(description = "平台客服配置")
    private String custService;

    @Schema(description = "扩展配置集")
    private String extConfig;

    @Schema(description = "后台入口展示开关")
    private Boolean adminEntrySwitch;

    @Schema(description = "后台入口地址")
    private String adminEntryUrl;

    @Schema(description = "默认区域地址，区域id，以逗号分隔")
    private String address;


}
