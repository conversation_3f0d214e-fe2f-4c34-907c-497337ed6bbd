package cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class AppMyFootprintPageRespVO {

        private Long id;
        /**
         * 商品SKU ID
         */
        @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private String skuId;

        /**
         * 商品图片URL
         */
        private String picUrl;

        /**
         * 商品名称
         */
        private String skuName;

        /**
         * 商品价格
         */
        private BigDecimal salePrice;


        @Schema(description = "售卖状态")
        private Integer saleStatus;

        @Schema(description = "库存状态类型 参考枚举值： 33,39,40,36,34,99", requiredMode = Schema.RequiredMode.REQUIRED, example = "33")
        private Integer stockStateType;

        @Schema(description = "库存状态描述。以下为stockStateId不同时，此字段不同的返回值： 33 有货 现货-下单立即发货 39 有货 在途-正在内部配货，预计2\\\\\\\\x7e6天到达本仓库 40 有货 可配货-下单后从有货仓库配货 36 预订 34 无货 99 无货开预定", requiredMode = Schema.RequiredMode.REQUIRED, example = "有货")
        private String stockStateDesc;
        /**
         * 最新浏览时间
         */
        private LocalDateTime updateTime;

}