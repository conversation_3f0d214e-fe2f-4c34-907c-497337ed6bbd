package cn.iocoder.yudao.module.mall.vop.controller.admin.vopskucategory;

import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTask;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.mall.vop.controller.admin.vopskucategory.vo.*;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopSkuCategoryDO;
import cn.iocoder.yudao.module.mall.vop.convert.vopskucategory.VopSkuCategoryConvert;
import cn.iocoder.yudao.module.mall.vop.service.VopSkuCategoryService;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 京东sku分类关系")
@RestController
@RequestMapping("/mall/vop-sku-category")
@Validated
public class VopSkuCategoryController {

    @Resource
    private VopSkuCategoryService vopSkuCategoryService;

    @PostMapping("/create")
    @Operation(summary = "创建京东sku分类关系")
    @PreAuthorize("@ss.hasPermission('mall:vop-sku-category:create')")
    public CommonResult<Long> createVopSkuCategory(@Valid @RequestBody VopSkuCategoryCreateReqVO createReqVO) {
        return success(vopSkuCategoryService.createVopSkuCategory(createReqVO, null, null));
    }

    @PutMapping("/update")
    @Operation(summary = "更新京东sku分类关系")
    @PreAuthorize("@ss.hasPermission('mall:vop-sku-category:update')")
    public CommonResult<Boolean> updateVopSkuCategory(@Valid @RequestBody VopSkuCategoryUpdateReqVO updateReqVO) {
        vopSkuCategoryService.updateVopSkuCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除京东sku分类关系")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mall:vop-sku-category:delete')")
    public CommonResult<Boolean> deleteVopSkuCategory(@RequestParam("id") Long id) {
        vopSkuCategoryService.deleteVopSkuCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得京东sku分类关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mall:vop-sku-category:query')")
    public CommonResult<VopSkuCategoryRespVO> getVopSkuCategory(@RequestParam("id") Long id) {
        VopSkuCategoryDO vopSkuCategory = vopSkuCategoryService.getVopSkuCategory(id);
        return success(VopSkuCategoryConvert.INSTANCE.convert(vopSkuCategory));
    }

    @GetMapping("/list")
    @Operation(summary = "获得京东sku分类关系列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('mall:vop-sku-category:query')")
    public CommonResult<List<VopSkuCategoryRespVO>> getVopSkuCategoryList(@RequestParam("ids") Collection<Long> ids) {
        List<VopSkuCategoryDO> list = vopSkuCategoryService.getVopSkuCategoryList(ids);
        return success(VopSkuCategoryConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得京东sku分类关系分页")
    @PreAuthorize("@ss.hasPermission('mall:vop-sku-category:query')")
    public CommonResult<PageResult<VopSkuCategoryRespVO>> getVopSkuCategoryPage(@Valid VopSkuCategoryPageReqVO pageVO) {
        PageResult<VopSkuCategoryDO> pageResult = vopSkuCategoryService.getVopSkuCategoryPage(pageVO);
        return success(VopSkuCategoryConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出京东sku分类关系 Excel")
    @PreAuthorize("@ss.hasPermission('mall:vop-sku-category:export')")
    @OperateLog(type = EXPORT)
    public void exportVopSkuCategoryExcel(@Valid VopSkuCategoryExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<VopSkuCategoryDO> list = vopSkuCategoryService.getVopSkuCategoryList(exportReqVO);
        // 导出 Excel
        List<VopSkuCategoryExcelVO> datas = VopSkuCategoryConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "京东sku分类关系.xls", "数据", VopSkuCategoryExcelVO.class, datas);
    }

    /**
     * 获得京东sku导入模板
     * @param response
     * @throws IOException
     */
    @GetMapping("/get-import-template")
    @PreAuthorize("@ss.hasPermission('mall:vop-sku-category:import')")
    @Operation(summary = "获得京东sku导入模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<VopSkuCategoryImportExcelVO> list = Arrays.asList(
                VopSkuCategoryImportExcelVO.builder()
                        .skuInnerId("100007490071")
                        .skuName("得力(deli)记号笔6881黑色粗头油性大头笔马克笔记号笔签到笔相册涂鸦笔办公用品10支装")
                        .categoryId(String.valueOf(6010105L))
                        .categoryName("马克笔")
                        .build()
        );
        // 输出
        ExcelUtils.write(response, "供应商商品sku导入.xls", "Sheet1", VopSkuCategoryImportExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "导入京东sku Excel")
    @PreAuthorize("@ss.hasPermission('mall:vop-sku-category:import')")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.PRODUCT_SKU_SEO_IMPORT, isNested = false)
    public CommonResult<String> importExcel(@RequestParam("file") MultipartFile file) throws Exception {
        String taskId = AsyncFrontTaskContext.getTaskId();
        List<VopSkuCategoryImportExcelVO> list = ExcelUtils.read(file, VopSkuCategoryImportExcelVO.class);
        vopSkuCategoryService.importExcel(list);
        return success(taskId);
    }

}
