package cn.iocoder.yudao.module.mall.product.controller.admin.tag;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.convert.tag.ProductTagGroupConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagGroupDO;
import cn.iocoder.yudao.module.mall.product.service.tag.ProductTagGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 标签分组")
@RestController
@RequestMapping("/product/tag-group")
@Validated
public class ProductTagGroupController {

    @Resource
    private ProductTagGroupService tagGroupService;

    @PostMapping("/create")
    @Operation(summary = "创建标签分组")
    @PreAuthorize("@ss.hasPermission('product:tag-group:create')")
    public CommonResult<Long> createTagGroup(@Valid @RequestBody ProductTagGroupCreateReqVO createReqVO) {
        return success(tagGroupService.createTagGroup(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新标签分组")
    @PreAuthorize("@ss.hasPermission('product:tag-group:update')")
    public CommonResult<Boolean> updateTagGroup(@Valid @RequestBody ProductTagGroupUpdateReqVO updateReqVO) {
        tagGroupService.updateTagGroup(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除标签分组")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:tag-group:delete')")
    public CommonResult<Boolean> deleteTagGroup(@RequestParam("id") Long id) {
        tagGroupService.deleteTagGroup(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得标签分组")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:tag-group:query')")
    public CommonResult<ProductTagGroupRespVO> getTagGroup(@RequestParam("id") Long id) {
        ProductTagGroupDO tagGroup = tagGroupService.getTagGroup(id);
        return success(ProductTagGroupConvert.INSTANCE.convert(tagGroup));
    }

    @GetMapping("/list")
    @Operation(summary = "获得标签分组列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('product:tag-group:query')")
    public CommonResult<List<ProductTagGroupRespVO>> getTagGroupList(@RequestParam("ids") Collection<Long> ids) {
        List<ProductTagGroupDO> list = tagGroupService.getTagGroupList(ids);
        return success(ProductTagGroupConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得标签分组分页")
    @PreAuthorize("@ss.hasPermission('product:tag-group:query')")
    public CommonResult<PageResult<ProductTagGroupRespVO>> getTagGroupPage(@Valid ProductTagGroupPageReqVO pageVO) {
        PageResult<ProductTagGroupDO> pageResult = tagGroupService.getTagGroupPage(pageVO);
        return success(ProductTagGroupConvert.INSTANCE.convertPage(pageResult));
    }

}
