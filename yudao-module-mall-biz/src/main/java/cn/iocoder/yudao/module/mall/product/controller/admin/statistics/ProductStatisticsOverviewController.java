package cn.iocoder.yudao.module.mall.product.controller.admin.statistics;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.external.bigscreen.service.BigScreenService;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenProductCategoryProportionRespVO;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenSupplierProductProportionRespVO;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SkuSummaryRespVO;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 商品概览统计")
@RestController
@RequestMapping("/product/statistics/overview")
@Validated
public class ProductStatisticsOverviewController {
    @Resource
    private BigScreenService bigScreenService;
    @Resource
    ProductSkuService productSkuService;

    /**
     * 商品分类占比
     * @return
     */
    @GetMapping("getProductCategoryProportion")
    @Operation(summary = "商品分类占比")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<List<BigScreenProductCategoryProportionRespVO>> getProductCategoryProportion() {
        List<BigScreenProductCategoryProportionRespVO> bigScreenProductCategoryProportionRespVOS = bigScreenService.getProductCategoryProportion();
        return CommonResult.success(bigScreenProductCategoryProportionRespVOS);
    }

    /**
     * 供应商商品占比
     * @return
     */
    @GetMapping("getSupplierProductProportion")
    @Operation(summary = "供应商商品占比")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<List<BigScreenSupplierProductProportionRespVO>> getSupplierProductProportion() {
        List<BigScreenSupplierProductProportionRespVO> bigScreenSupplierProductProportionRespVOS = bigScreenService.getSupplierProductProportion();
        return CommonResult.success(bigScreenSupplierProductProportionRespVOS);
    }

    /**
     * 统计当前sku总数
     * @return
     */
    @GetMapping("/getSkuTotal")
    @Operation(summary = "统计当前sku总数")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<SkuSummaryRespVO> getSkuTotal() {
        SkuSummaryRespVO skuSummaryRespVO = productSkuService.getSkuTotal();
        return success(skuSummaryRespVO);
    }
}
