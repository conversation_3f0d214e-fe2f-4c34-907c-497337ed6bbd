package cn.iocoder.yudao.module.mall.framework.lock;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.module.mall.annotation.RedissonLock;
import cn.iocoder.yudao.module.mall.util.SpringParseUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/5/19
 */
@Component
@Aspect
@Slf4j
public class RedissonLockAspect {

    @Autowired
    private RedissonClient redissonClient;

    @Around("@annotation(redissonLock)")
    public Object around(ProceedingJoinPoint joinPoint, RedissonLock redissonLock) throws Throwable {
        Assert.notBlank(redissonLock.key(),"key不能为空");
        String key =  SpringParseUtils.parseExpression(joinPoint, redissonLock.key());
        long waitTime = redissonLock.waitTime();
        long leaseTime = redissonLock.leaseTime();
        TimeUnit timeUnit = redissonLock.timeUnit();

        RLock lock = redissonClient.getLock(key);
        boolean acquired = false;
        try {
            acquired = lock.tryLock(waitTime, leaseTime, timeUnit);
            if (acquired) {
                return joinPoint.proceed();
            } else {
                throw new RuntimeException("Could not acquire lock");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Failed to acquire lock", e);
        } finally {
            if (acquired) {
                lock.unlock();
            }
        }
    }
}
