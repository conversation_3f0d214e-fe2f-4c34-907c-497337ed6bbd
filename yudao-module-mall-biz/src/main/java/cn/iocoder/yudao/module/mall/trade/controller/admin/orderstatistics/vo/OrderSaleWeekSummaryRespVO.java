package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderSaleWeekSummaryRespVO {

    @Schema(description = "第几周")
    String week;

    @Schema(description = "开始日期")
    String weekStart;

    @Schema(description = "结束日期")
    String weekEnd;

    @Schema(description = "订单个数")
    private Long count;

    @Schema(description = "订单金额")
    private BigDecimal amount;
}
