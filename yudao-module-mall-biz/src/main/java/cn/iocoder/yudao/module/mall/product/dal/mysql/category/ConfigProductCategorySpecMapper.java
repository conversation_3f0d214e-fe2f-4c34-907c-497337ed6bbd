package cn.iocoder.yudao.module.mall.product.dal.mysql.category;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategorySpecDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商品规格 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ConfigProductCategorySpecMapper extends BaseMapperX<ConfigProductCategorySpecDO> {

    default PageResult<ConfigProductCategorySpecDO> selectPage(ProductSpecPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ConfigProductCategorySpecDO>()
                .likeIfPresent(ConfigProductCategorySpecDO::getName, reqVO.getName())
                .eqIfPresent(ConfigProductCategorySpecDO::getSpecType, reqVO.getSpecType())
                .orderByDesc(ConfigProductCategorySpecDO::getId));
    }

}
