package cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - sku库存创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
public class ProductSkuStockCreateReqVO extends ProductSkuStockBaseVO {

}
