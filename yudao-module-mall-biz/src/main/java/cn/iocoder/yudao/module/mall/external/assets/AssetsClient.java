package cn.iocoder.yudao.module.mall.external.assets;

import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.AssetsConfigDO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsCompleteDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsCreateDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsResp;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsStatusUpdateReq;
import cn.iocoder.yudao.module.mall.external.assets.service.AbstractExtAssetsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/20
 */
@Service
@Slf4j
public class AssetsClient {

    @Resource
    @Lazy
    private List<AbstractExtAssetsService> extAssetsServiceList;

    private AbstractExtAssetsService getTargetService(AssetsConfigDO configDO) {
        if(configDO == null) {
            log.info("资产配置为空");
            return null;
        }
        if(!configDO.isActive()) {
            log.info("资产配置未生效");
            return null;
        }
        String sysCode = configDO.getSysCode();
        AbstractExtAssetsService service =  extAssetsServiceList.stream().filter(item -> item.getClientCode().equals(sysCode)).findFirst().orElse(null);
        if(service == null) {
            log.error("资产配置系统编码不存在:{}", sysCode);
            return null;
        }

        return service;
    }

    /**
     * 推送固资建档信息
     * @param data
     * @return
     */
    public ExtAssetsResp<String> pushAssets(AssetsConfigDO configDO, ExtAssetsCreateDTO data) {
        AbstractExtAssetsService service = getTargetService(configDO);
        if(service == null) {
            return null;
        }
        return service.pushAssets(configDO, data);
    }

    /**
     * 完成固资建档信息
     * @param data
     * @return
     */
    public boolean completeAssets(AssetsConfigDO configDO, ExtAssetsCompleteDTO data) {
        AbstractExtAssetsService service = getTargetService(configDO);
        if(service == null) {
            return false;
        }
        return service.completeAssets(configDO, data);
    }

    /**
     * 查询固资状态
     * @param configDO
     * @param orderNo
     * @param orderItemId
     * @return
     */
    public ExtAssetsStatusUpdateReq queryAssetStatus(AssetsConfigDO configDO, String orderNo, Long orderItemId) {
        AbstractExtAssetsService service = getTargetService(configDO);
        if(service == null || !service.needPollStatus()) {
            return null;
        }
        return service.doPollStatus(orderNo, orderItemId);
    }

}
