package cn.iocoder.yudao.module.mall.product.dal.dataobject.category;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品分类 DO
 *
 * 商品分类一共两类：
 * 1）一级分类：{@link #parentId} 等于 0
 * 2）二级 + 三级分类：{@link #parentId} 不等于 0
 *
 * <AUTHOR>
 */
@TableName("product_category")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCategoryDO extends TenantBaseDO {

    /**
     * 父分类编号 - 根分类
     */
    public static final Long PARENT_ID_NULL = 0L;

    @TableId
    private Long id;
    /**
     * 分类名称
     */
    private String categoryName;
    /**
     * PC端分类图标
     */
    private String icon;
    /**
     * 移动端分类图标
     */
    private String iconH5;
    /**
     * 父级分类ID, 对应的是categoryId字段
     */
    private Long parentId;
    /**
     * 商品分类编号, 以-分隔
     */
    private String fullCategoryId;
    /**
     * 商品分类名称, 以-分隔
     */
    private String fullCategoryName;
    /**
     * 排序, 值越小，排序越靠前
     */
    private Integer orderSort;
    /**
     * 类型：1-京东，2-平台， {@link  cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryTypeEnum}
     */
    private Integer type;
    /**
     * 分类ID
     */
    private Long categoryId;
    /**
     * 分类层级
     */
    private Integer categoryLevel;

    /**
     * 逻辑字段  分类层级
     */
    @TableField(exist = false)
    private Integer cateLevel;

    /**
     * 状态，是否展示 {@link cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum}
     */
    private Integer status;

    /**
     * 低值经济分类
     */
    private String economyClass;
    /**
     * 固资经济分类
     */
    private String economyClass2;
    /**
     * 子分类列表，非持久化字段
     */
    @TableField(exist = false)
    private List<ProductCategoryDO> ChildCategoryList;


    public Integer getCateLevel() {
        return this.categoryLevel + 1;
    }

    @JsonIgnore
    public List<Long> getCategoryIdsByPath() {
        if(StringUtils.isNotBlank(fullCategoryId)) {
            String[] segs = fullCategoryId.split("-");
            return Arrays.asList(segs).stream().map(seg -> Long.valueOf(seg)).collect(Collectors.toList());
        }
        return null;
    }

    @JsonIgnore
    public boolean isEnabled() {
        return ObjectUtil.equal(ProductCategoryStatusEnum.ENABLE.getStatus(), status);
    }
}
