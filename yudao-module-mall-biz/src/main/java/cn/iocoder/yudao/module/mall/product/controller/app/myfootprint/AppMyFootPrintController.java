package cn.iocoder.yudao.module.mall.product.controller.app.myfootprint;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import cn.iocoder.yudao.framework.ratelimiter.core.keyresolver.impl.UserRateLimiterKeyResolver;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo.AppMyFootprintCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo.AppMyFootprintPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo.AppMyFootprintPageRespVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.myfootprint.MyFootprintDO;
import cn.iocoder.yudao.module.mall.product.service.myfootprint.MyFootPrintService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.MY_FOOTPRINT_NOT_EXISTS;

@Tag(name = "用户 App - 我的足迹")
@RestController
@RequestMapping("/product/myFootprint")
@Validated
public class AppMyFootPrintController {

    @Resource
    private MyFootPrintService myFootPrintService;

    /**
     * 分页查询用户足迹
     *
     * @return
     */
    @GetMapping("/getFootprintPage")
    @Operation(summary = "分页查询用户足迹")
    @PreAuthenticated
    public CommonResult<PageResult<AppMyFootprintPageRespVO>> getMyFootPrintPage(@Valid AppMyFootprintPageReqVO pageParam) {
        PageResult<AppMyFootprintPageRespVO> doPageResult = myFootPrintService.getMyFootPrintPage(getLoginUserId(), pageParam);
        return CommonResult.success(doPageResult);
    }

    /**
     * 添加用户商品足迹
     *
     * @param
     * @return
     */
    @PostMapping("/recordFootprint")
    @Operation(summary = "添加我的足迹")
    @PreAuthenticated
    public CommonResult<Long> addMyFootPrint(@Valid @RequestBody AppMyFootprintCreateReqVO addReqVO) {
        Long userId = getLoginUserId();
        addReqVO.setUserId(userId);

        return success(myFootPrintService.addMyFootPrint(addReqVO));
    }

    /**
     * 删除用户商品足迹
     *
     * @param
     * @return
     */
    @PostMapping("/deleteFootprint")
    @Operation(summary = "删除我的足迹")
    @PreAuthenticated
    @RateLimiter(count = 20, timeUnit = TimeUnit.MINUTES, keyResolver = UserRateLimiterKeyResolver.class)
    public CommonResult<Boolean> deleteMyFootPrint(@RequestParam("id") Long id) {
        validatePermission(id);
        myFootPrintService.deleteMyFootPrint(id);
        return success(true);
    }

    private void validatePermission(Long id) {
        MyFootprintDO footPrint = myFootPrintService.getMyFootPrint(id);
        if (footPrint == null) {
            throw exception(MY_FOOTPRINT_NOT_EXISTS);
        }
        Long userId = getLoginUserId();
        if (!footPrint.getUserId().equals(userId)) {
            throw exception(MY_FOOTPRINT_NOT_EXISTS);
        }

    }

    /**
     * 批量删除用户商品足迹
     *
     * @param
     * @return
     */
    @PostMapping("/batchDeleteFootprint")
    @Operation(summary = "批量删除我的足迹")
    @PreAuthenticated
    @RateLimiter(count = 20, timeUnit = TimeUnit.MINUTES, keyResolver = UserRateLimiterKeyResolver.class)
    public CommonResult<Boolean> batchCancelMyFootPrint() {
        myFootPrintService.batchDeleteMyFootPrint(getLoginUserId());
        return success(true);
    }







}
