package cn.iocoder.yudao.module.mall.product.convert.productcomment;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.ProductCommentRespVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentDO;

/**
 * 商品评价 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductCommentConvert {

    ProductCommentConvert INSTANCE = Mappers.getMapper(ProductCommentConvert.class);

    ProductCommentDO convert(AppProductCommentCreateReqVO bean);

    @Mappings(value = {
            @Mapping(source = "memberUser.id", target = "memberId"),
            @Mapping(source = "memberUser.nickname", target = "nickName"),
            @Mapping(source = "memberUser.avatar", target = "avatar"),
            @Mapping(source = "productSkuDO.spuId", target = "spuId"),
            @Mapping(source = "tradeOrderDO.id", target = "orderId"),
            @Mapping(source = "tradeOrderDO.supplierId", target = "supplierId"),
            @Mapping(source = "tradeOrderDO.supplierName", target = "supplierName"),
            @Mapping(target = "id", ignore = true)
    })
    ProductCommentDO convert(AppProductCommentCreateReqVO bean, MemberUserDO memberUser, ProductSkuDO productSkuDO, TradeOrderDO tradeOrderDO);

    ProductCommentDO convert(AppProductCommentUpdateReqVO bean);

    @Mappings(value = {
            @Mapping(source = "nickName", target = "nickName", conditionExpression = "java(bean.getAnonymousFlag() == 0)"),
            @Mapping(source = "avatar", target = "avatar",conditionExpression = "java(bean.getAnonymousFlag() == 0)")
    })
    AppProductCommentRespVO convert(ProductCommentDO bean);

    List<AppProductCommentRespVO> convertList(List<ProductCommentDO> list);

    PageResult<AppProductCommentRespVO> convertPage(PageResult<ProductCommentDO> page);

    List<AppProductCommentExcelVO> convertList02(List<ProductCommentDO> list);

    ProductCommentRespVO convert03(ProductCommentDO bean);

    PageResult<ProductCommentRespVO> convertPage01(PageResult<ProductCommentDO> page);

}
