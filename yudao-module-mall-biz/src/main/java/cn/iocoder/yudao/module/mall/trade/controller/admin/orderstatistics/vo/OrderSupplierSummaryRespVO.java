package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderSupplierSummaryRespVO {

    @Schema(description = "供应商")
    private String supplierName;

    @Schema(description = "销售额")
    private BigDecimal orderAmount;

    @Schema(description = "订单个数")
    private Long orderCount;

}