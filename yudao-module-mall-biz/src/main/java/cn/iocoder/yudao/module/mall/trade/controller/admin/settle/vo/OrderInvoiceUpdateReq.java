package cn.iocoder.yudao.module.mall.trade.controller.admin.settle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 管理后台 - 发票更新请求参数
 */
@Schema(description = "管理后台 - 发票更新请求参数")
@Data
public class OrderInvoiceUpdateReq {

    @NotNull(message = "orderId不能为空")
    private Long orderId;

    private Long supplierId;

    private String orderNo;

    // 发票日期, 格式：yyyy-MM-dd
    private String invoiceDate;

    // 订单总金额
    private BigDecimal totalBatchInvoiceAmount;

    // 订单含税金额
    private BigDecimal invoicePrice;

    // 发票号码
    private String invoiceId;

    // 在线发票链接
    private String fileUrl;

    @NotNull(message = "是否可以开票")
    private Boolean canInvoice;

    // 不能开票的原因
    private String failReason;
}
