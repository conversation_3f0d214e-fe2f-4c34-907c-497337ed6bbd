package cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSkuShowStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商品 SPU 状态更新 Request VO")
@Data
@ToString(callSuper = true)
public class ProductSpuShowStatusUpdateReqVO {

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品编号不能为空")
    private Long id;

    @Schema(description = "商品显示状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品显示状态不能为空")
    @InEnum(ProductSkuShowStatusEnum.class)
    private Integer showStatus;

}
