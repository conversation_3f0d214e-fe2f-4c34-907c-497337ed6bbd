package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupplierSellProportionRespVO {
    @Schema(description = "供应商Id")
    private Long supplierId;

    @Schema(description = "供应商名字")
    private String supplierName;

    @Schema(description = "订单数量")
    private Long orderCount;

    @Schema(description = "销售额")
    private BigDecimal totalAmount;

    @Schema(description = "销售额占比")
    private float proportion;
}
