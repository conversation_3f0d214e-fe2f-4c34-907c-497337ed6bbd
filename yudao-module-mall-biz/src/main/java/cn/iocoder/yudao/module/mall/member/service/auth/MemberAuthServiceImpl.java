package cn.iocoder.yudao.module.mall.member.service.auth;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.monitor.TracerUtils;
import cn.iocoder.yudao.framework.common.util.object.ObjectUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.common.util.string.Sm2Utils;
import cn.iocoder.yudao.framework.common.util.tenant.TenantIdUtils;
import cn.iocoder.yudao.framework.redis.util.RedisUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.external.sso.cas.controller.app.vo.AppSsoCasLoginReqVO;
import cn.iocoder.yudao.module.mall.external.sso.ycrh.dto.SsoYcrhRespDTO;
import cn.iocoder.yudao.module.mall.external.sso.ycrh.service.SsoYcrhService;
import cn.iocoder.yudao.module.mall.member.controller.app.auth.vo.*;
import cn.iocoder.yudao.module.mall.member.convert.auth.AuthConvert;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.member.dal.mysql.user.MemberUserMapper;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.system.api.logger.LoginLogApi;
import cn.iocoder.yudao.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.iocoder.yudao.module.system.api.oauth2.OAuth2TokenApi;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import cn.iocoder.yudao.module.system.api.sms.SmsCodeApi;
import cn.iocoder.yudao.module.system.api.social.SocialUserApi;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserBindReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserRespDTO;
import cn.iocoder.yudao.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.yudao.module.system.enums.logger.LoginResultEnum;
import cn.iocoder.yudao.module.system.enums.oauth2.OAuth2ClientConstants;
import cn.iocoder.yudao.module.system.enums.sms.SmsSceneEnum;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.EXTERNAL_TENANT_ID_PARSE_FAIL;
import static cn.iocoder.yudao.module.mall.member.dal.redis.RedisKeyConstants.MALL_MEMBER_LOGIN_ERROR_COUNT;
import static cn.iocoder.yudao.module.mall.member.enums.ErrorCodeConstants.*;


/**
 * 会员的认证 Service 接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberAuthServiceImpl implements MemberAuthService {

    @Resource
    private MemberUserService userService;
    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private LoginLogApi loginLogApi;
    @Resource
    private SocialUserApi socialUserApi;
    @Resource
    private OAuth2TokenApi oauth2TokenApi;
    @Resource
    private WxMaService wxMaService;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private MemberUserMapper userMapper;
    @Resource
    private SsoYcrhService ssoYcrhService;
    @Resource
    private RedisUtils redisUtils;

    @Override
    public AppAuthLoginRespVO ssoLogin(@Valid AppSsoLoginReqVO reqVO) {
        // 测试时的decryptTenantId用c990f75f8b547698eb4de012cbd0694f，对应的是150；也可以使用TenantIdUtils生成租户ID加密字符串
        SsoYcrhRespDTO respDTO = ssoYcrhService.validateToken(reqVO.getEtoken());
        if (respDTO == null) {
            throw exception(AUTH_SSO_YCRH_ERROR);
        }

        MemberUserDO user = userMapper.selectByUserNo(respDTO.getUserNo());
        if (user == null) {
            // 创建user
            user = new MemberUserDO();
            user.setLoginDate(LocalDateTime.now());
            user.setRegisterIp(ServletUtils.getClientIP());
            user.setStatus(CommonStatusEnum.ENABLE.getStatus());
            user.setUserNo(respDTO.getUserNo());
            user.setNickname(respDTO.getUserName());
            user.setMobile("");
            user.setAvatar("");
            userMapper.insert(user);
        }

        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), user.getMobile(), LoginLogTypeEnum.LOGIN_SSO, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }

        // 创建 Token 令牌
        AppAuthLoginRespVO result = createTokenAfterLoginSuccess(user, user.getMobile(), LoginLogTypeEnum.LOGIN_SSO);
        // 收集其它字段信息
        userService.fillDetailByUserNo(user.getUserNo());
        // 构建返回结果
        return result;
    }

    @Override
    public AppAuthLoginRespVO ssoCasLogin(@Valid AppSsoCasLoginReqVO reqVO) {
        // TODO 请求外部系统查询用户信息
        MemberUserDO user = userMapper.selectByUserNo(reqVO.getUserNo());
        if (user == null) {
            // 创建user
            user = new MemberUserDO();
            user.setLoginDate(LocalDateTime.now());
            user.setRegisterIp(ServletUtils.getClientIP());
            user.setStatus(CommonStatusEnum.ENABLE.getStatus());
            user.setUserNo(reqVO.getUserNo());
            user.setNickname(reqVO.getUserName());
            user.setDeptName(reqVO.getDeptName());
            user.setDeptCode(reqVO.getDeptNo());
            user.setMobile("");
            user.setAvatar("");
            userMapper.insert(user);
        }

        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), user.getMobile(), LoginLogTypeEnum.LOGIN_SSO, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }

        // 创建 Token 令牌
        AppAuthLoginRespVO result = createTokenAfterLoginSuccess(user, user.getMobile(), LoginLogTypeEnum.LOGIN_SSO);
        // 收集其它字段信息
        userService.fillDetailByUserNo(user.getUserNo());

        // 构建返回结果
        return result;
    }

    @Override
    public AppAuthLoginRespVO login(AppAuthLoginReqVO reqVO) {
        String redisKey = String.format(MALL_MEMBER_LOGIN_ERROR_COUNT, TenantContextHolder.getTenantId(), reqVO.getMobile());
        Integer count = redisUtils.get(redisKey);
        // 获取Redis登录次数记录，如果超过5次，则抛出异常；
        if (count != null && count >= 5) {
            throw exception(AUTH_LOGIN_ERROR_OVER_LIMIT);
        }
        MemberUserDO user = null;
        // 如果密码错误，增加错误次数，过期时间为 15 分钟
        try {
            // 解密密码
            String decodePassword = Sm2Utils.decrypt(reqVO.getPassword());
            user = login0(reqVO.getMobile(), decodePassword);
        } catch (ServiceException ex) {
            if (ex.getCode().equals(AUTH_LOGIN_BAD_CREDENTIALS.getCode())) {
                redisUtils.set(redisKey, count == null ? 1 : count + 1, 15, TimeUnit.MINUTES);
            }
            throw ex;
        }

        // 如果 socialType 非空，说明需要绑定社交用户
        if (reqVO.getSocialType() != null) {
            socialUserApi.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(),
                    reqVO.getSocialType(), reqVO.getSocialCode(), reqVO.getSocialState()));
        }

        // 创建 Token 令牌，记录登录日志，清除Redis记录的失败次数
        redisUtils.deleteKeys(redisKey);
        return createTokenAfterLoginSuccess(user, reqVO.getMobile(), LoginLogTypeEnum.LOGIN_USERNAME);
    }

    private String checkSmsCode2Ip(AppAuthSmsBaseReqVO reqVO, SmsSceneEnum scene) {
        // 校验验证码
        String userIp = getClientIP();
        CommonResult<Boolean> result = smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convert(reqVO, scene, userIp));
        if (result == null || result.getData() == null || !result.getData()) {
            log.info("smsLogin smsuse failed: {}", result);
            throw exception(AUTH_SMS_CODE_ERROR);
        }
        return userIp;
    }

    @Override
    @Transactional
    public AppAuthLoginRespVO smsLogin(AppAuthSmsLoginReqVO reqVO) {
        // 校验验证码
        String userIp = checkSmsCode2Ip(reqVO, SmsSceneEnum.MEMBER_LOGIN);
        // 校验该用户是否存在
        checkUserIfExists(reqVO.getMobile());
        // 获得获得注册用户
        MemberUserDO user = userService.createUserIfAbsent(reqVO.getMobile(), userIp);
        Assert.notNull(user, "获取用户失败，结果为空");

        // 如果 socialType 非空，说明需要绑定社交用户
        if (reqVO.getSocialType() != null) {
            socialUserApi.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(),
                    reqVO.getSocialType(), reqVO.getSocialCode(), reqVO.getSocialState()));
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user, reqVO.getMobile(), LoginLogTypeEnum.LOGIN_SMS);
    }

    @Override
    @Transactional
    public AppAuthLoginRespVO smsCreate(AppAuthSmsCreateReqVO reqVO) {
        // 校验验证码
        String userIp = checkSmsCode2Ip(reqVO, SmsSceneEnum.MEMBER_UPDATE_MOBILE);
        // 校验该用户手机号是否被使用
        checkUserIfRepeat(reqVO.getMobile());
        // 获得获得注册用户并修改密码
        MemberUserDO user = userService.createUserIfAbsent(reqVO.getMobile(), userIp);
        Assert.notNull(user, "获取用户失败，结果为空");
        // 解密密码
        String decodePassword = Sm2Utils.decrypt(reqVO.getPassword());
        // 设置密码
        userMapper.updateById(MemberUserDO.builder().id(user.getId())
                .password(passwordEncoder.encode(decodePassword))
                .name(reqVO.getName())
                .nickname(reqVO.getName())
                .userNo(reqVO.getMobile())
                .build());
        // 如果 socialType 非空，说明需要绑定社交用户
        if (reqVO.getSocialType() != null) {
            socialUserApi.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(),
                    reqVO.getSocialType(), reqVO.getSocialCode(), reqVO.getSocialState()));
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user, reqVO.getMobile(), LoginLogTypeEnum.LOGIN_SMS);
    }

    @Override
    public AppAuthLoginRespVO socialLogin(AppAuthSocialLoginReqVO reqVO) {
        buildContext(reqVO.getState());

        SocialUserRespDTO socialUser = socialUserApi.authSocialUser(reqVO.getType(), reqVO.getCode(), reqVO.getState()).getCheckedData();
        Assert.notNull(socialUser, "未注册的用户");

        // 检查用户是否绑定，如果未绑定，则进行绑定
        Boolean isbind = socialUserApi.isBindUserId(socialUser.getType(), socialUser.getId()).getCheckedData();
        if(!isbind){
            socialUserApi.bindSocialUser(new SocialUserBindReqDTO(socialUser.getId(), getUserType().getValue(),
                    socialUser.getType(), socialUser.getCode(), socialUser.getState()));
        }

        MemberUserDO user = userMapper.selectByUserNo(socialUser.getOpenid());
        if (user == null) {
            // 创建user
            user = new MemberUserDO();
            user.setLoginDate(LocalDateTime.now());
            user.setRegisterIp(ServletUtils.getClientIP());
            user.setStatus(CommonStatusEnum.ENABLE.getStatus());
            user.setUserNo(socialUser.getOpenid());
            user.setNickname(socialUser.getNickname());
            user.setMobile("");
            user.setAvatar("");
            userMapper.insert(user);
        }

        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), user.getMobile(), LoginLogTypeEnum.LOGIN_SSO, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }

        // 创建 Token 令牌
        AppAuthLoginRespVO result = createTokenAfterLoginSuccess(user, user.getMobile(), LoginLogTypeEnum.LOGIN_SOCIAL);
        // 收集其它字段信息
        userService.fillDetailByUserNo(user.getUserNo());
        // 构建返回结果
        return result;
    }

    @Override
    public AppAuthLoginRespVO weixinMiniAppLogin(AppAuthWeixinMiniAppLoginReqVO reqVO) {
        // 获得对应的手机号信息
        WxMaPhoneNumberInfo phoneNumberInfo;
        try {
            phoneNumberInfo = wxMaService.getUserService().getNewPhoneNoInfo(reqVO.getPhoneCode());
        } catch (Exception exception) {
            throw exception(AUTH_WEIXIN_MINI_APP_PHONE_CODE_ERROR);
        }
        // 获得获得注册用户
        MemberUserDO user = userService.createUserIfAbsent(phoneNumberInfo.getPurePhoneNumber(), getClientIP());
        Assert.notNull(user, "获取用户失败，结果为空");

        // 绑定社交用户
        socialUserApi.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(),
                SocialTypeEnum.WECHAT_MINI_PROGRAM.getType(), reqVO.getLoginCode(), ""));

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user, user.getMobile(), LoginLogTypeEnum.LOGIN_SOCIAL);
    }

    private AppAuthLoginRespVO createTokenAfterLoginSuccess(MemberUserDO user, String mobile, LoginLogTypeEnum logType) {
        // 插入登陆日志
        createLoginLog(user.getId(), mobile, logType, LoginResultEnum.SUCCESS);
        // 创建 Token 令牌
        CommonResult<OAuth2AccessTokenRespDTO> result = oauth2TokenApi.createAccessToken(new OAuth2AccessTokenCreateReqDTO()
                .setUserId(user.getId()).setUserNo(user.getUserNo()).setUserName(user.getNickname()).setUserType(getUserType().getValue())
                .setClientId(OAuth2ClientConstants.CLIENT_ID_DEFAULT));
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(result.getData());
    }

    @Override
    public String getSocialAuthorizeUrl(Integer type, String redirectUri) {
        CommonResult<String> result = socialUserApi.getAuthorizeUrl(type, redirectUri);
        return result.getData();
    }

    private MemberUserDO login0(String mobile, String password) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_MOBILE;
        // 校验账号是否存在
        MemberUserDO user = userService.getUserByMobile(mobile);
        if (user == null) {
            user = userService.getUserByUserNo(mobile);
            if(user == null){
                createLoginLog(null, mobile, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
                throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
            }
        }
        if (!userService.isPasswordMatch(password, user.getPassword())) {
            createLoginLog(user.getId(), mobile, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), mobile, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        return user;
    }

    private void createLoginLog(Long userId, String mobile, LoginLogTypeEnum logType, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(mobile);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogApi.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
            userService.updateUserLogin(userId, getClientIP());
        }
    }

    @Override
    public void logout(String token) {
        // 删除访问令牌
        CommonResult<OAuth2AccessTokenRespDTO> result = oauth2TokenApi.removeAccessToken(token);
        OAuth2AccessTokenRespDTO accessTokenRespDTO = result.getCheckedData();
        if (accessTokenRespDTO == null) {
            return;
        }
        // 删除成功，则记录登出日志
        createLogoutLog(accessTokenRespDTO.getUserId());
    }

    @Override
    public void updatePassword(Long userId, AppAuthUpdatePasswordReqVO reqVO) {
        // 检验旧密码
        String decodeOldPassword = Sm2Utils.decrypt(reqVO.getOldPassword());
        MemberUserDO userDO = checkOldPassword(userId, decodeOldPassword);
        // 更新用户密码
        String decodePassword = Sm2Utils.decrypt(reqVO.getPassword());
        // TODO 芋艿：需要重构到用户模块
        userMapper.updateById(MemberUserDO.builder().id(userDO.getId())
                .password(passwordEncoder.encode(decodePassword)).build());
    }

    @Override
    public void resetPassword(AppAuthResetPasswordReqVO reqVO) {
        // 校验验证码
        checkSmsCode2Ip(reqVO, SmsSceneEnum.MEMBER_FORGET_PASSWORD);
        // 检验用户是否存在
        MemberUserDO userDO = checkUserIfExists(reqVO.getMobile());
        // 解密密码
        String decodePassword = Sm2Utils.decrypt(reqVO.getPassword());
        // 更新密码
        userMapper.updateById(MemberUserDO.builder().id(userDO.getId())
                .password(passwordEncoder.encode(decodePassword)).build());
    }

    @Override
    public void sendSmsCode(Long userId, AppAuthSmsSendReqVO reqVO) {
        String receiver = null;
        // 情况 1：如果是修改手机场景，需要校验新手机号是否已经注册，说明不能使用该手机了
        if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_UPDATE_MOBILE.getScene())) {
            MemberUserDO user = userService.getUserByMobile(reqVO.getMobile());
            if (user != null && !Objects.equals(user.getId(), userId)) {
                throw exception(USER_MOBILE_EXISTS);
            }
            receiver = "新用户";
        }
        // 情况 2：如果是重置密码或者登录场景，需要校验手机号和用户是存在的
        else if (ObjectUtils.equalsAny(reqVO.getScene(), SmsSceneEnum.MEMBER_FORGET_PASSWORD.getScene(),
                SmsSceneEnum.MEMBER_LOGIN.getScene())) {
            MemberUserDO user = checkUserIfExists(reqVO.getMobile());
            receiver = user.getNameOrNickname();
        }

        smsCodeApi.sendSmsCode(AuthConvert.INSTANCE.convert(reqVO).setCreateIp(getClientIP()).setReceiver(receiver)).checkError();
    }

    @Override
    public AppAuthLoginRespVO refreshToken(String refreshToken) {
        CommonResult<OAuth2AccessTokenRespDTO> result = oauth2TokenApi.refreshAccessToken(refreshToken, OAuth2ClientConstants.CLIENT_ID_DEFAULT);
        return AuthConvert.INSTANCE.convert(result.getCheckedData());
    }

    /**
     * 校验旧密码
     *
     * @param id          用户 id
     * @param oldPassword 旧密码
     * @return MemberUserDO 用户实体
     */
    @VisibleForTesting
    public MemberUserDO checkOldPassword(Long id, String oldPassword) {
        MemberUserDO user = userMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        // 参数：未加密密码，编码后的密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw exception(USER_PASSWORD_FAILED);
        }
        return user;
    }

    public MemberUserDO checkUserIfRepeat(String mobile) {
        MemberUserDO user = null;
        try {
            user = userMapper.selectByMobile(mobile);
        } catch (TooManyResultsException ee) {
            throw exception(USER_MOBILE_REPEAT);
        }
        return user;
    }

    public MemberUserDO checkUserIfExists(String mobile) {
        MemberUserDO user = checkUserIfRepeat(mobile);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return user;
    }

    private void createLogoutLog(Long userId) {
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(LoginLogTypeEnum.LOGOUT_SELF.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(getMobile(userId));
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
        loginLogApi.createLoginLog(reqDTO);
    }

    private String getMobile(Long userId) {
        if (userId == null) {
            return null;
        }
        MemberUserDO user = userService.getUser(userId);
        return user != null ? user.getMobile() : null;
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.MEMBER;
    }

    /**
     * 构建上下文
     * @param tcode
     */
    private void buildContext(String tcode) {
        try {
            Long tenantId = TenantIdUtils.decryptTenantId(tcode);
            TenantContextHolder.setTenantId(tenantId);
            TenantContextHolder.setIgnore(false);
        } catch (Exception e) {
            log.error("构建上下文时解析租户ID错误:", e);
            throw ServiceExceptionUtil.exception(EXTERNAL_TENANT_ID_PARSE_FAIL);
        }
    }
}
