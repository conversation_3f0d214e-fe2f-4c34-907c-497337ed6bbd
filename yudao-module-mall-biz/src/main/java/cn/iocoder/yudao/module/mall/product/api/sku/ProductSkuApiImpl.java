package cn.iocoder.yudao.module.mall.product.api.sku;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.*;
import cn.iocoder.yudao.module.mall.product.convert.sku.ProductSkuConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.product.service.category.ProductCategoryService;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.spu.ProductSpuService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsCategoryService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopOrderService;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopConfigDO;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopSkuCategoryDO;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopSkuPoolDO;
import cn.iocoder.yudao.module.mall.vop.dto.SkuPriceRespDTO;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsGetSkuDetailReq;
import cn.iocoder.yudao.module.mall.vop.service.VopConfigService;
import cn.iocoder.yudao.module.mall.vop.service.VopSkuCategoryService;
import cn.iocoder.yudao.module.mall.vop.service.VopSkuPoolService;
import com.alibaba.ttl.TtlWrappers;
import com.jd.open.api.sdk.domain.vopdd.QueryOrderOpenProvider.request.querySkuFreight.AreaBaseInfoOpenReq;
import com.jd.open.api.sdk.domain.vopdd.QueryOrderOpenProvider.request.querySkuFreight.FreightQueryOpenReq;
import com.jd.open.api.sdk.domain.vopdd.QueryOrderOpenProvider.request.querySkuFreight.SkuInfoOrderOpenReq;
import com.jd.open.api.sdk.domain.vopdd.QueryOrderOpenProvider.response.querySkuFreight.FreightQueryOpenResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.request.getSkusAllSaleState.AreaBaseInfoGoodsReq;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.request.getSkusAllSaleState.GetStockByIdGoodsReq;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.request.getSkusAllSaleState.SkuNumBaseGoodsReq;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.GetSkuPoolInfoGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.OpenRpcResult;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkusAllSaleState.GetSkuCanSaleResp;
import com.jd.open.api.sdk.response.vopdd.VopOrderQuerySkuFreightResponse;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetSkuDetailInfoResponse;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetSkusAllSaleStateResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.SKU_FREIGHT_ERROR;

/**
 * TODO LeeYan9: 类注释;
 *
 * <AUTHOR>
 * @since 2022-09-06
 */
@Service
@Validated
@Slf4j
public class ProductSkuApiImpl implements ProductSkuApi {

    @Resource
    @Lazy
    private ProductSkuService productSkuService;

    @Resource
    private VopGoodsService vopGoodsService;

    @Resource
    private VopOrderService vopOrderService;

    @Resource
    private VopGoodsCategoryService vopGoodsCategoryService;

    @Resource
    private SupplierService supplierService;

    @Resource
    private ProductSpuService productSpuService;

    @Resource
    private VopConfigService vopConfigService;

    @Resource
    private ProductCategoryService productCategoryService;

    @Resource
    private VopSkuPoolService vopSkuPoolService;

    @Resource
    private VopSkuCategoryService vopSkuCategoryService;

    @Value("${jd.image-path:https://img13.360buyimg.com/n12/}")
    private String jdImagePath;

    @Override
    public ProductSkuRespDTO getSku(Long skuId, Boolean isJd) {
        if(isJd) {
            VopGoodsGetSkuDetailReq req = new VopGoodsGetSkuDetailReq();
            ProductSkuDO productSkuDO = productSkuService.getSkuByIdOrInnerId(skuId.toString());
            Assert.notNull(productSkuDO,String.format("商品%s不存在",skuId));
            Long skuInnerId = Long.valueOf(productSkuDO.getSkuInnerId());
            req.setSkuId(skuInnerId);
            VopGoodsGetSkuDetailInfoResponse response = vopGoodsService.getSkuDetailInfo(req);
            OpenRpcResult openRpcResult = response.getOpenRpcResult();
            GetSkuPoolInfoGoodsResp skuResponse = openRpcResult.getResult();
            String categoryCode = null;
            String categoryName = null;
            String fullCategoryId = null;
            VopConfigDO vopConfig = vopConfigService.getVopConfigRequired();
            // 校验商品分类，单供应商模式，可以不用强制匹配本地商品分类；多供应商模式时需要强制匹配；
            if(vopConfig.getFullPoolSwitch()){
                VopSkuCategoryDO vopSkuCategoryDO = vopSkuCategoryService.getVopSkuCategoryByJdSkuId(String.valueOf(skuResponse.getSkuId()));
                if(vopSkuCategoryDO != null){
                    categoryCode = String.valueOf(vopSkuCategoryDO.getCategoryId());
                    categoryName = vopSkuCategoryDO.getCategoryName();
                    fullCategoryId = vopSkuCategoryDO.getFullCategoryId();
                }
            }
            else if(vopConfig.getPoolNameValidate() != null && vopConfig.getPoolNameValidate()) {
                ProductCategoryDO productCategoryDO = null;
                VopSkuPoolDO vopSkuPoolDO = vopSkuPoolService.getPoolBySkuId(String.valueOf(skuResponse.getSkuId()));
                if(StringUtils.isNotBlank(vopSkuPoolDO.getPoolName())) {
                    Long categoryId = productCategoryService.getCategoryIdByPoolName(vopSkuPoolDO.getPoolName());
                    if(categoryId != null) {
                        productCategoryDO = productCategoryService.getByCategoryId(categoryId);
                        categoryCode = String.valueOf(productCategoryDO.getCategoryId());
                        categoryName = productCategoryDO.getCategoryName();
                        fullCategoryId = productCategoryDO.getFullCategoryId();
                    } else {
                        log.info("商品分类id解析为空, {}", vopSkuPoolDO.getPoolName());
                    }
                }

                if(productCategoryDO == null){
                    log.error("商品{}分类查不到", skuId);
                }
            }
            else {
                String category = skuResponse.getCategory();

                if (StringUtils.isNotBlank(category)) {
                    categoryCode = StringUtils.substringAfterLast(category, ";");
                    if(StringUtils.isBlank(categoryCode)) {
                        categoryCode = category;
                    }
                    ConfigProductCategoryDO productCategoryDO = vopGoodsCategoryService.queryConfigCategory(Long.valueOf(categoryCode));
                    if (productCategoryDO != null) {
                        categoryName = productCategoryDO.getCategoryName();
                        fullCategoryId = productCategoryDO.getFullCategoryId();
                    }
                    else {
                        log.error("商品分类没找到：{} {} {}", skuId, skuInnerId, category);
                    }
                }
            }

            Integer lowestBuy = skuResponse.getLowestBuy();
            if(lowestBuy == null){
                lowestBuy = 1;
            }

            if(StringUtils.isBlank(skuResponse.getImagePath())){
                log.error("商品{}图片获取为null", skuId);
            }

            SupplierDO supplierDO = supplierService.getSupplierJD();
            ProductSkuRespDTO productSkuRespDTO = new ProductSkuRespDTO()
                    .setId(productSkuDO.getId())
                    .setSpuId(skuResponse.getSpuId())
                    .setSpuName(skuResponse.getSpuName())
                    .setSkuId(productSkuDO.getId())
                    .setSkuInnerId(skuInnerId.toString())
                    .setSkuName(skuResponse.getSkuName())
                    .setPicUrl(jdImagePath+skuResponse.getImagePath())
                    .setFullCategoryId(fullCategoryId)
                    .setCategoryCode(categoryCode)
                    .setCategoryName(categoryName)
                    .setLowestBuy(lowestBuy)
                    .setStatus(productSkuService.getUnionStatus(skuId, skuResponse.getSkuState()));
            if(supplierDO != null) {
                productSkuRespDTO.setSupplierId(supplierDO.getId());
                productSkuRespDTO.setSupplierName(supplierDO.getName());
            }
            return productSkuRespDTO;
        }
        ProductSkuDO sku = productSkuService.getSku(skuId);
        if(sku == null) {
            log.error("本地商品SKU不存在:{}", skuId);
            return null;
        }
        ProductSkuRespDTO productSkuRespDTO = ProductSkuConvert.INSTANCE.convertResp(sku);
        productSkuRespDTO.setSkuId(skuId);
        ProductSpuDO spu = productSpuService.getSimpleSpu(sku.getSpuId());
        if(spu != null) {
            productSkuRespDTO.setFullCategoryId(spu.getFullCategoryId());
            productSkuRespDTO.setCategoryCode(spu.getEndCategoryId());
            productSkuRespDTO.setCategoryName(spu.getEndCategoryName());
        }
        return productSkuRespDTO;
    }


    @Override
    public List<ProductSkuRespDTO> getSkus(List<Long> skuIds, Boolean isJd) {
        List<ProductSkuRespDTO> productSkuDOS = new ArrayList<>();
        if(CollUtil.isEmpty(skuIds)) {
            return productSkuDOS;
        }
        if(!isJd) {
            return getLocalSkus(skuIds);
        }
        List<Long> errorSkuIds = new ArrayList<>();
        List<CompletableFuture<?>> futures = new ArrayList<>();
        for (Long skuId : skuIds) {
            futures.add(CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
                try {
                    ProductSkuRespDTO sku = getSku(skuId,isJd);
                    if(sku != null) {
                        productSkuDOS.add(sku);
                    } else {
                        errorSkuIds.add(skuId);
                        log.error("商品查询为空，sku:{}", skuId);
                    }
                    return true;
                } catch (Exception e) {
                    log.error("同步VOP商品异常, sku:{}", skuId, e);
                    errorSkuIds.add(skuId);
                }
                return false;
            })));
        }
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{})).get();
        } catch(Exception e) {
            log.error("getSkus 多线程执行错误:", e);
        }

        if(CollUtil.isNotEmpty(errorSkuIds)) {
            log.error("商品sku查询结果异常，失败sku：", errorSkuIds);
            exception(ErrorCodeConstants.SKU_NOT_EXISTS);
        }

        return productSkuDOS;
    }

    @Override
    public List<ProductSkuRespDTO> getLocalSkus(List<Long> skuIds) {
        List<ProductSkuRespDTO> productList = new ArrayList<>();
        List<ProductSkuDO> skus = productSkuService.getSkuByIdOrInnerIds(skuIds.stream().map(String::valueOf).collect(Collectors.toList()));
        skus.forEach(sku -> {
            ProductSkuRespDTO productSkuRespDTO = ProductSkuConvert.INSTANCE.convertResp(sku);
            ProductSpuDO spu = productSpuService.getSimpleSpu(sku.getSpuId());
            if(spu != null) {
                productSkuRespDTO.setFullCategoryId(spu.getFullCategoryId());
                productSkuRespDTO.setFullCategoryName(spu.getFullCategoryName());
                productSkuRespDTO.setCategoryCode(spu.getEndCategoryId());
                productSkuRespDTO.setCategoryName(spu.getEndCategoryName());
            }

            productList.add(productSkuRespDTO);
        });

        return productList;
    }

    @Override
    public List<ProductSkuRespDTO> getLocalSkusByIds(Collection<Long> skuIds) {
        List<ProductSkuRespDTO> productList = new ArrayList<>();
        List<ProductSkuDO> skus = productSkuService.getSkuList(skuIds);
        skus.forEach(sku -> {
            ProductSkuRespDTO productSkuRespDTO = ProductSkuConvert.INSTANCE.convertResp(sku);
            ProductSpuDO spu = productSpuService.getSimpleSpu(sku.getSpuId());
            if(spu != null) {
                productSkuRespDTO.setFullCategoryId(spu.getFullCategoryId());
                productSkuRespDTO.setFullCategoryName(spu.getFullCategoryName());
                productSkuRespDTO.setCategoryCode(spu.getEndCategoryId());
                productSkuRespDTO.setCategoryName(spu.getEndCategoryName());
            }

            productList.add(productSkuRespDTO);
        });

        return productList;
    }

    @Override
    public ProductSkuRespDTO getLocalSku(Long skuId) {
        ProductSkuDO sku = productSkuService.getSku(skuId);
        ProductSkuRespDTO productSkuRespDTO = ProductSkuConvert.INSTANCE.convertResp(sku);

        ProductSpuDO spu = productSpuService.getSimpleSpu(sku.getSpuId());
        if(spu != null) {
            productSkuRespDTO.setFullCategoryId(spu.getFullCategoryId());
            productSkuRespDTO.setCategoryCode(spu.getEndCategoryId());
            productSkuRespDTO.setCategoryName(spu.getEndCategoryName());
        }

        return productSkuRespDTO;
    }

    @Override
    public List<ProductSkuRespDTO> getSimpleSkusByInnerIds(List<String> innerIds, Long supplierId) {
        List<ProductSkuDO> skuDOList = productSkuService.getSkuListByInnerIdAndSupplierId(innerIds, supplierId);
        List<ProductSkuRespDTO> skuDTOList = Lists.newArrayList();
        skuDOList.forEach(productSkuDO -> {
            ProductSkuRespDTO productSkuRespDTO = new ProductSkuRespDTO()
                    .setId(productSkuDO.getId())
                    .setSpuId(productSkuDO.getSpuId())
                    .setSpuName(productSkuDO.getSpuName())
                    .setSkuId(productSkuDO.getId())
                    .setSkuInnerId(productSkuDO.getSkuInnerId())
                    .setSkuName(productSkuDO.getSkuName())
                    .setLowestBuy(productSkuDO.getLowestBuy());
            skuDTOList.add(productSkuRespDTO);
        });

        return skuDTOList;
    }

    @Override
    public List<ProductSkuRespDTO> getSkuList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<ProductSkuDO> skus = productSkuService.getSkuList(ids);
        return ProductSkuConvert.INSTANCE.convertList04(skus);
    }

    @Override
    public void updateSkuStock(ProductSkuUpdateStockReqDTO updateStockReqDTO) {
        productSkuService.updateSkuStock(updateStockReqDTO);
    }

    @Override
    public List<CanSaleRespDTO> getJdSkusAllSaleState(List<SkuBaseReqDTO> reqs, AreaDTO area) {
        GetStockByIdGoodsReq getStockReq = new GetStockByIdGoodsReq();
        AreaBaseInfoGoodsReq areaBaseInfoGoodsReq = new AreaBaseInfoGoodsReq();
        areaBaseInfoGoodsReq.setProvinceId(area.getProvinceId());
        areaBaseInfoGoodsReq.setCityId(area.getCityId());
        areaBaseInfoGoodsReq.setCountyId(area.getCountyId());
        areaBaseInfoGoodsReq.setTownId(area.getTownId());
        getStockReq.setAreaInfo(areaBaseInfoGoodsReq);

        List<SkuNumBaseGoodsReq> skuNumBaseGoodsReqs = reqs.stream().map(req -> {
            SkuNumBaseGoodsReq skuNumBaseGoodsReq = new SkuNumBaseGoodsReq();
            skuNumBaseGoodsReq.setSkuId(req.getSkuId());
            skuNumBaseGoodsReq.setSkuNumber(req.getSkuNumber());
            return skuNumBaseGoodsReq;
        }).collect(Collectors.toList());
        getStockReq.setSkuNumInfoList(skuNumBaseGoodsReqs);
        VopGoodsGetSkusAllSaleStateResponse response = vopGoodsService.getSkusAllSaleState(getStockReq);
        if (response == null || response.getOpenRpcResult() == null || CollectionUtils.isEmpty(response.getOpenRpcResult().getResult())) {
            return null;
        }
        List<GetSkuCanSaleResp> result = response.getOpenRpcResult().getResult();
        List<CanSaleRespDTO> canSaleRespDTOS = result.stream().map(getSkuCanSaleResp -> {
            CanSaleRespDTO canSaleRespDTO = new CanSaleRespDTO();
            BeanUtils.copyProperties(getSkuCanSaleResp, canSaleRespDTO);
            return canSaleRespDTO;
        }).collect(Collectors.toList());

        // 查询促销价限购数量
        List<SkuPriceRespDTO> skuPriceRespDTOS = vopGoodsService.getVopSellPrice(reqs.stream().map(SkuBaseReqDTO::getSkuId).collect(Collectors.toList()));
        Map<Long, SkuPriceRespDTO> skuPriceMap = convertMap(skuPriceRespDTOS, SkuPriceRespDTO::getSkuId);
        Map<Long, SkuBaseReqDTO> skuBaseReqDTOMap = convertMap(reqs, SkuBaseReqDTO::getSkuId);
        for (CanSaleRespDTO canSaleRespDTO : canSaleRespDTOS) {
            SkuBaseReqDTO skuBaseReqDTO = skuBaseReqDTOMap.get(canSaleRespDTO.getSkuId());
            SkuPriceRespDTO skuPriceRespDTO = skuPriceMap.get(canSaleRespDTO.getSkuId());
            if (skuBaseReqDTO != null
                    && skuPriceRespDTO != null
                    && skuPriceRespDTO.getHasPromotion() != null
                    && skuPriceRespDTO.getHasPromotion()
                    && skuPriceRespDTO.getRemainNum() < skuBaseReqDTO.getSkuNumber()) {
                canSaleRespDTO.setCanPurchase(false);
                if(skuPriceRespDTO.getRemainNum() == 0){
                    canSaleRespDTO.setMessage("限售商品，已售罄");
                }
                else {
                    canSaleRespDTO.setMessage(String.format("限售商品，限购%d件", skuPriceRespDTO.getRemainNum()));
                }
            }
        }

        return canSaleRespDTOS;
    }

    @Override
    public FreightRespDTO querySkuFreight(List<SkuBaseReqDTO> reqs, AreaDTO area, Integer paymentType) {
        FreightQueryOpenReq req = new FreightQueryOpenReq();
        req.setPaymentType(paymentType);
        AreaBaseInfoOpenReq areaBaseInfoOpenReq = new AreaBaseInfoOpenReq();
        BeanUtils.copyProperties(area, areaBaseInfoOpenReq);
        req.setAreaInfo(areaBaseInfoOpenReq);
        List<SkuInfoOrderOpenReq> skuInfos = reqs.stream().map(skuBaseReqDTO -> {
                    SkuInfoOrderOpenReq skuInfoOrderOpenReq = new SkuInfoOrderOpenReq();
                    skuInfoOrderOpenReq.setSkuNum(skuBaseReqDTO.getSkuNumber());
                    skuInfoOrderOpenReq.setSkuId(skuBaseReqDTO.getSkuId());
                    return skuInfoOrderOpenReq;
                })
                .collect(Collectors.toList());
        req.setSkuInfoList(skuInfos);
        VopOrderQuerySkuFreightResponse response = vopOrderService.querySkuFreight(req);
        if (response != null && response.getOpenRpcResult() != null && response.getOpenRpcResult().getResult() != null) {
            FreightQueryOpenResp result = response.getOpenRpcResult().getResult();
            return BeanUtil.copyProperties(result, FreightRespDTO.class);
        } else {
            String errorMsg = response != null ? response.getMsg() : "系统异常";
            throw exception(SKU_FREIGHT_ERROR, errorMsg);
        }
    }

}
