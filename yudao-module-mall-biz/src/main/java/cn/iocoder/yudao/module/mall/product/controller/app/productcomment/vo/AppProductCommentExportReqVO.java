package cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "用户 APP - 商品评价 Excel 导出 Request VO，参数和 ProductCommentPageReqVO 是一致的")
@Data
public class AppProductCommentExportReqVO {

    @Schema(description = "订单id")
    private Long orderId;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "spuId")
    private Long spuId;

    @Schema(description = "供应商id")
    private Long supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "会员id")
    private Long memberId;

    @Schema(description = "会员昵称")
    private String nickName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "评价星数 1->5")
    private Integer score;

    @Schema(description = "评价内容")
    private String content;

    @Schema(description = "上传图片地址，以逗号隔开")
    private String pics;

    @Schema(description = "是否匿名 1-是 0 否")
    private Integer anonymousFlag;

    @Schema(description = "审核状态 0-待审核 1-审核通过 2-审核驳货")
    private Integer auditStatus;

    @Schema(description = "评价者ip")
    private String clientIp;

    @Schema(description = "评价者地区")
    private String clientArea;

    @Schema(description = "评价者点赞数")
    private Integer likeCount;

    @Schema(description = "评价回复数")
    private Integer replyCount;

    @Schema(description = "举报次数")
    private Integer reportCount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
