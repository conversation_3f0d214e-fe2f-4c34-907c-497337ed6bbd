package cn.iocoder.yudao.module.mall.trade.controller.admin.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.service.SecurityFrameworkService;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.mall.annotation.OrderOperateLog;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.SupplierSimpleRespVO;
import cn.iocoder.yudao.module.mall.basis.convert.supplier.SupplierConvert;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.basisconfig.BasisConfigDO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.basisconfig.BasisConfigService;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.*;
import cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo.PurchaseRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.req.OrderIdReq;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.req.OrderNoReq;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.req.OrderOfflineSettlementReq;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.req.OrderRemarkReq;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.ApprovalResultRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.OrderDetailRespVO;
import cn.iocoder.yudao.module.mall.trade.convert.order.TradeOrderConvert;
import cn.iocoder.yudao.module.mall.trade.convert.purchase.PurchaseConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import cn.iocoder.yudao.module.mall.trade.enums.order.OrderOperateTypeEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderCancelTypeEnum;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderAssetsService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.trade.service.order.VopOrderBridgeService;
import cn.iocoder.yudao.module.mall.trade.service.purchase.PurchaseService;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTask;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskUtils;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.ttl.TtlRunnable;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 交易订单
 */
@Tag(name = "管理后台 - 交易订单")
@RestController
@RequestMapping("/trade/order")
@Validated
@Slf4j
public class TradeOrderController {

    @Resource
    private TradeOrderService tradeOrderService;
    @Resource
    private PurchaseService purchaseService;
    @Resource
    private VopOrderBridgeService vopOrderBridgeService;
    @Resource
    private TradeOrderAssetsService orderAssetsService;
    @Resource
    private AsyncFrontTaskUtils asyncFrontTaskUtils;
    @Resource
    private SupplierService supplierService;
    @Resource
    private SecurityFrameworkService securityFrameworkService;
    @Resource
    private BasisConfigService basisConfigService;

    /**
     * 获得交易订单分页
     * @param reqVO
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "获得交易订单分页")
    @PreAuthorize("@ss.hasPermission('trade:order:query')")
    public CommonResult<PageResult<TradeOrderPageItemRespVO>> getOrderPageV2(TradeOrderPageReqVO reqVO) {
        reqVO.setParentType(0);
        PageResult<TradeOrderDO> pageResult = tradeOrderService.getOrderPage2(reqVO);
        if(pageResult.getTotal() == 0) {
            return success(null);
        }
        handleTagPermission(pageResult.getList());
        // 最终组合
        return success(TradeOrderConvert.INSTANCE.convertPage2(pageResult));
    }

    /**
     * 导出交易订单
     * @param reqVO
     * @return
     */
    @GetMapping("/export")
    @Operation(summary = "导出交易订单")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.ORDER_LIST)
    @PreAuthorize("@ss.hasPermission('trade:order:export')")
    public CommonResult<String> exportOrders(TradeOrderPageReqVO reqVO) {
        final String taskId = AsyncFrontTaskContext.getTaskId();
        BasisConfigDO basisConfigDO = basisConfigService.getBasisConfig();
        boolean needProject = basisConfigDO.needProject();
        asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), AsyncTaskTypeEnum.ORDER_LIST,
            TtlRunnable.get(() -> {
                int pageSize = 1000, pageNo = 1;
                reqVO.setParentType(0);
                reqVO.setPageNo(pageNo);
                reqVO.setPageSize(pageSize);
                List<TradeOrderDO> dataList = new ArrayList<>();
                try {
                    asyncFrontTaskUtils.plusExportProgress(20);
                    PageResult<TradeOrderDO> pageResult = tradeOrderService.getOrderPage2(reqVO);
                    log.info("任务{}导出交易订单，总数:{}", taskId, pageResult.getTotal());

                    while(CollUtil.isNotEmpty(pageResult.getList()) && pageResult.getPages() >= pageNo) {
                        dataList.addAll(pageResult.getList());
                        reqVO.setPageNo(++pageNo);
                        pageResult = tradeOrderService.getOrderPage2(reqVO);
                        asyncFrontTaskUtils.plusExportProgress(dataList.size(), pageResult.getTotal(), 40);
                    }
                    if (CollUtil.isEmpty(dataList)) {
                        asyncFrontTaskUtils.exportDone(taskId, null);
                        return;
                    }

                    List<? extends CommonOrderExportVO> exportList = null;
                    if(needProject) {
                        exportList = TradeOrderConvert.INSTANCE.convertExportVO2(dataList, WithProjectOrderExportVO.class);
                    } else {
                        exportList = TradeOrderConvert.INSTANCE.convertExportVO2(dataList, CommonOrderExportVO.class);
                    }

                    if (CollUtil.isEmpty(exportList)) {
                        asyncFrontTaskUtils.exportDone(taskId, null);
                        return;
                    }

                    int batchSize = 500;
                    for (int i = 0; i < exportList.size(); i += batchSize) {
                        List<?> batchOrderExportVOS = exportList.subList(i, Math.min(i + batchSize, exportList.size()));
                        if(needProject) {
                            tradeOrderService.fillExtraInfo4OrderExportV2((List<WithProjectOrderExportVO>) batchOrderExportVOS);
                        } else {
                            tradeOrderService.fillExtraInfo4OrderExportV1((List<CommonOrderExportVO>) batchOrderExportVOS);
                        }

                        asyncFrontTaskUtils.plusExportProgress(exportList.size(), pageResult.getTotal(), 30);
                    }

                    List<CommonOrderExportVO> exportList2 = new ArrayList<>();
                    for (CommonOrderExportVO commonOrderExportVO : exportList) {
                        List<CommonOrderExportVO> exportVOS = TradeOrderConvert.INSTANCE.convertExportVO3(commonOrderExportVO.getItemList());
                        for (CommonOrderExportVO exportVO : exportVOS) {
                            TradeOrderConvert.INSTANCE.convertExportVO3(exportVO, commonOrderExportVO);
                        }
                        exportList2.addAll(exportVOS);
                    }

                    // 导出 Excel 文件
                    asyncFrontTaskUtils.exportDone(taskId, exportList2);
                } catch (Exception e) {
                    asyncFrontTaskUtils.exportFail();
                    log.error("任务{}导出订单失败：", taskId, e);
                }
            })
        );

        return success(taskId);
    }

    /**
     * 获得订单状态总数
     * @return
     */
    @GetMapping("/get-order-status")
    @Operation(summary = "获得订单状态总数")
    @PreAuthorize("@ss.hasPermission('trade:order:query')")
    public CommonResult<List<TradeOrderStatusResult>> getOrderStatusResult() {
        return success(tradeOrderService.getOrderStatusResult());
    }

    /**
     * 获得交易订单详情
     * @param id
     * @return
     */
    @GetMapping("/get-detail")
    @Operation(summary = "获得交易订单详情")
    @Parameter(name = "id", description = "订单编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('trade:order:query')")
    public CommonResult<AdminOrderDetailRespVO> getOrderDetail(@RequestParam("id") Long id) {
        OrderDetailRespVO orderDetail = tradeOrderService.getOrderDetail4Admin(id);
        AdminOrderDetailRespVO adminOrderDetailRespVO = BeanUtil.copyProperties(orderDetail, AdminOrderDetailRespVO.class);
        //增加采购信息
        PurchaseDO purchase = purchaseService.getPurchaseByOrder(orderDetail.getId());
        if (purchase != null) {
            PurchaseRespVO purchaseRespVO = PurchaseConvert.INSTANCE.convert(purchase);
            if(StringUtils.isNotBlank(purchaseRespVO.getAuditResult())) {
                List<ApprovalResultRespVO> auditResults = JSON.parseArray(purchaseRespVO.getAuditResult(), ApprovalResultRespVO.class);
                purchaseRespVO.setAuditResults(auditResults);
            }
            adminOrderDetailRespVO.setPurchaseInfo(purchaseRespVO);
        }
        handleTagPermission(adminOrderDetailRespVO);
        return success(adminOrderDetailRespVO);
    }

    /**
     * 判断是否有标签运营数据权限后清除运营标签
     * @param list
     */
    private void handleTagPermission(List<TradeOrderDO> list) {
        if(!securityFrameworkService.hasPermission("product:tag:stats")) {
            if(CollUtil.isNotEmpty(list)) {
                list.forEach(order -> {
                    if(CollUtil.isNotEmpty(order.getItemList())) {
                        order.getItemList().forEach(item -> {
                            item.setSkuTags(null);
                        });
                    }
                });
            }
        }
    }

    /**
     * 判断是否有标签运营数据权限后清除运营标签
     * @param detailRespVO
     */
    private void handleTagPermission(AdminOrderDetailRespVO detailRespVO) {
        if(!securityFrameworkService.hasPermission("product:tag:stats")) {
            if(CollUtil.isNotEmpty(detailRespVO.getSkuInfoList())) {
                detailRespVO.getSkuInfoList().forEach(item -> {
                    item.setSkuTags(null);
                });
            }
        }
    }

    /**
     * 获得交易订单父子订单详情
     * @param parentOrderId
     * @return
     */
    @GetMapping("/get-parent-detail")
    @Operation(summary = "获得交易订单父子订单详情")
    @Parameter(name = "parentOrderId", description = "父订单ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('trade:order:query')")
    public CommonResult<TradeOrderParentChildRespVO> getOrderParentDetail(@RequestParam("parentOrderId") Long parentOrderId) {
        return success(tradeOrderService.getParentAndChildOrders(parentOrderId));
    }

    /**
     * 获取订单审批信息
     * @param id
     * @return
     */
    @GetMapping("/get-audit-result")
    @Operation(summary = "获取订单审批信息")
    @Parameter(name = "id", description = "订单编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('trade:order:query')")
    public CommonResult<List<ApprovalResultRespVO>> getAuditResultByOrderId(@RequestParam("id") Long id) {
        List<ApprovalResultRespVO> auditResults = tradeOrderService.getAuditResultByOrderId(id);
        return success(auditResults);
    }

    /**
     * 确认订单
     *
     * @param orderIdReq
     * @return
     */
    @PostMapping("/confirmOrder")
    @Operation(summary = "确认订单")
    @OrderOperateLog(content = "确认订单",type = OrderOperateTypeEnum.CONFIRM,orderId = "#orderIdReq.orderId")
	@PreAuthorize("@ss.hasPermission('trade:order:confirm')")
    public CommonResult<Boolean> confirmOrder(@Valid @RequestBody OrderIdReq orderIdReq) {
        tradeOrderService.confirmOrder(orderIdReq.getOrderId());
        return CommonResult.success(true);
    }

    @PostMapping("/receiveOrder")
    @Operation(summary = "确认收货")
    @OrderOperateLog(content = "确认收货",type = OrderOperateTypeEnum.COMPLETED,orderId = "#orderIdReq.orderId")
	@PreAuthorize("@ss.hasPermission('trade:order:receive')")
    public CommonResult<Boolean> receiveOrder(@Valid @RequestBody OrderIdReq orderIdReq) {
        tradeOrderService.receiveOrder(orderIdReq.getOrderId());
        return success(true);
    }

    @PostMapping("/deliveryOrder")
    @Operation(summary = "订单发货")
	@PreAuthorize("@ss.hasAnyPermissions('trade:delivery:create')")
    public CommonResult<Boolean> deliveryOrder(@Valid @RequestBody TradeOrderDeliveryReqVO deliveryReqVO) {
        tradeOrderService.deliveryOrder(deliveryReqVO);
        return success(true);
    }

    /**
     * 已完成
     * @param orderIdReq
     * @return
     */
    @PostMapping("/completeOrder")
    @Operation(summary = "已完成")
    @OrderOperateLog(content = "完成订单",type = OrderOperateTypeEnum.COMPLETED,orderId = "#orderIdReq.orderId")
	@PreAuthorize("@ss.hasPermission('trade:order:complete')")
    public CommonResult<Boolean> completeOrder(@Valid  @RequestBody OrderIdReq orderIdReq) {
        tradeOrderService.completeOrder(orderIdReq.getOrderId());
        return success(true);
    }

    /**
     * 已取消
     * @param orderIdReq
     * @return
     */
    @PostMapping("/cancelOrder")
    @Operation(summary = "已取消")
    @OrderOperateLog(content = "取消订单",type = OrderOperateTypeEnum.CANCELED,orderId = "#orderIdReq.orderId")
	@PreAuthorize("@ss.hasPermission('trade:order:cancel')")
    public CommonResult<Boolean> cancelOrder(@Valid  @RequestBody OrderIdReq orderIdReq) {
        tradeOrderService.cancelOrder(orderIdReq.getOrderId(), TradeOrderCancelTypeEnum.ADMIN_CANCEL, "和客户协商一致后管理员取消", false);
        return success(true);
    }

    /**
     * 打开订单项售后
     * @param orderIdReq
     * @return
     */
    @PostMapping("/openAfterSale")
    @Operation(summary = "打开订单项售后")
    @OrderOperateLog(content = "打开订单项售后",type = OrderOperateTypeEnum.OPEN_AFTER_SALE,orderId = "#orderIdReq.orderId")
	@PreAuthorize("@ss.hasPermission('trade:after-sale:create')")
    public CommonResult<Boolean> openAfterSale4Item(@Valid  @RequestBody OrderIdReq orderIdReq) {
        tradeOrderService.openAfterSale4Item(orderIdReq.getOrderItemId());
        return success(true);
    }

    /**
     * 手动拆单，vop拆单消息异常情况下手动补偿
     * @param jdOrderId
     * @return
     */
    @GetMapping("/splitOrder")
    @Parameter(name = "jdOrderId", description = "京东父订单编号", required = true, example = "1L")
	@PreAuthorize("@ss.hasPermission('trade:order:split')")
    public CommonResult<Boolean> splitOrder(@RequestParam("jdOrderId") Long jdOrderId) {
        vopOrderBridgeService.splitOrder(jdOrderId);
        return success(true);
    }

    /**
     * 手动拆单，针对父订单相关的采购进行拆单
     * @param parentOrderId
     * @return
     */
    @GetMapping("/splitPurchaseOrder")
    @Parameter(name = "parentOrderId", description = "父订单编号", required = true, example = "1L")
	@PreAuthorize("@ss.hasPermission('trade:order:split')")
    public CommonResult<Boolean> splitOrder4Purchase(@RequestParam("parentOrderId") Long parentOrderId) {
        return success(purchaseService.splitOrder4Purchase(parentOrderId));
    }

    /**
     * 手动进行固资建档处理
     * @param orderIdReq
     * @return
     */
    @PostMapping("/createOrderAssets")
    @Operation(summary = "手动固资建档")
	@PreAuthorize("@ss.hasPermission('trade:order-item-assets:update')")
    public CommonResult<Boolean> createOrderAssets(@Valid  @RequestBody OrderIdReq orderIdReq) {
        Boolean result = orderAssetsService.handleOrderAsset(orderIdReq.getOrderId(), false);
        return success(result);
    }

    /**
     * 固资建档回滚
     * @param orderIdReq
     * @return
     */
    @PostMapping("/resetOrderAssets")
    @Operation(summary = "固资建档回滚")
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:update')")
    public CommonResult<Boolean> resetOrderAssets(@Valid  @RequestBody OrderIdReq orderIdReq) {
        orderAssetsService.orderAssetRollback(orderIdReq.getOrderId());
        return success(true);
    }

    @PostMapping("/setRemark")
    @Operation(summary = "运营备注")
	@PreAuthorize("@ss.hasPermission('trade:order:remark')")
    public CommonResult<Boolean> setRemark(@Valid  @RequestBody OrderRemarkReq orderRemarkReq) {
        tradeOrderService.setRemark(orderRemarkReq.getOrderId(), orderRemarkReq.getRemark());
        return success(true);
    }

    @PostMapping("/setOfflineSettlement")
    @Operation(summary = "设置线下结算")
    @PreAuthorize("@ss.hasPermission('trade:order:offline-settlement')")
    public CommonResult<Boolean> setOfflineSettlement(@Valid  @RequestBody OrderOfflineSettlementReq orderOfflineSettlementReq) {
        tradeOrderService.setOfflineSettlement(orderOfflineSettlementReq.getOrderId(), true);
        return success(true);
    }

    /**
     * 同步订单业财状态，会处理补推或取消
     * @param orderNoReq
     * @return
     */
    @PostMapping("/syncYcrhStatus")
    @Operation(summary = "同步业财订单状态")
    @PreAuthorize("@ss.hasPermission('trade:order:ycrh')")
    public CommonResult<Boolean> syncYcrhStatus(@Valid @RequestBody OrderNoReq orderNoReq) {
        purchaseService.syncOrderInYcrhManual(orderNoReq.getOrderNo());
        return success(true);
    }

    /**
     * 刷新订单业财状态，不会补偿
     * @param orderNoReq
     * @return
     */
    @PostMapping("/refreshYcrhStatus")
    @Operation(summary = "刷新订单业财状态")
    @PreAuthorize("@ss.hasPermission('trade:order:ycrh')")
    public CommonResult<Boolean> refreshYcrhStatus(@Valid @RequestBody OrderNoReq orderNoReq) {
        purchaseService.refreshOrderInYcrh(orderNoReq.getOrderNo());
        return success(true);
    }

    /**
     * 取消业财订单,经费解冻
     * @param orderNoReq
     * @return
     */
    @PostMapping("/cancel4Ycrh")
    @Operation(summary = "取消业财订单")
    @PreAuthorize("@ss.hasPermission('trade:order:ycrh')")
    public CommonResult<Boolean> cancel4Ycrh(@Valid @RequestBody OrderNoReq orderNoReq) {
        purchaseService.cancelOrderInYcrhManual(orderNoReq.getOrderNo(), "1");
        return success(true);
    }

    /**
     * 强制同步订单消息，如果订单存在则取消后补推，然后同步售后状态
     * @return
     */
    @PostMapping("/forceSyncOrder4Ycrh")
    @Operation(summary = "强制同步订单至业财融合")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:ycrh', 'trade:bill:update')")
    public CommonResult<Boolean> forceSyncOrder4Ycrh(@Valid @RequestBody OrderNoReq orderNoReq) {
        purchaseService.forceSyncOrder4Ycrh(orderNoReq.getOrderNo());
        return success(true);
    }

    /**
     * 修复采购单信息
     * @param orderNoReq
     * @return
     */
    @PostMapping("/fixPurchaseOrder")
    @Operation(summary = "修复采购单信息")
    @PreAuthorize("@ss.hasPermission('trade:order:fix-purchase')")
    public CommonResult<Boolean> fixPurchaseOrder(@Valid @RequestBody OrderNoReq orderNoReq) {
        purchaseService.fixPurchaseOrder(orderNoReq.getOrderNo());
        return success(true);
    }

    /**
     * 售后状态同步至业财
     * @return
     */
    @PostMapping("/afterSaleOrder4Ycrh")
    @Operation(summary = "售后状态同步至业财融合，可以指定售后单同步，不指定同步所有售后单")
	@PreAuthorize("@ss.hasPermission('trade:order:fix-purchase')")
    public CommonResult<Boolean> afterSaleOrder4Ycrh(@Valid @RequestBody TradeAfterSaleReqVO tradeAfterSaleReqVO) {
        purchaseService.afterSaleOrder4Ycrh(tradeAfterSaleReqVO);
        return success(true);
    }

    @GetMapping("/handlePurchaseAuditStatus")
    @Operation(summary = "补偿处理采购单审批状态")
    @PreAuthorize("@ss.hasPermission('trade:order:fix-purchase')")
    public CommonResult<Boolean> handlePurchaseAuditStatus() {
        purchaseService.handlePurchaseAuditStatus();
        return success(true);
    }

    @GetMapping("/simple-supplier-list")
    @Operation(summary = "获得供应商精简信息列表")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:query', 'product:spu:query')")
    public CommonResult<List<SupplierSimpleRespVO>> getSimpleSupplierList(String name) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Long supplierId = loginUser.getSupplierId();
        List<SupplierDO> list = null;
        if(supplierId == null) {
            list = supplierService.queryEnabledSupplierList(name);
        } else {
            list = Arrays.asList(supplierService.getSupplier(supplierId));
        }
        return success(SupplierConvert.INSTANCE.convertList04(list));
    }


}
