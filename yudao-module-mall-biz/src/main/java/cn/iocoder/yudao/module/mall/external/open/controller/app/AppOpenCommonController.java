package cn.iocoder.yudao.module.mall.external.open.controller.app;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.mall.external.open.annotation.OpenAuth;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.brand.AppOpenProductBrandDetailRespVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.brand.AppOpenProductBrandPageReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.brand.AppOpenProductBrandRespVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.common.AppBrandCreateReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.common.AppOpenAreaReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.common.AppOpenAreaRespVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.common.AppOpenIdReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenProductCategoryReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenProductCategoryRespVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenProductSpecReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenProductSpecRespVO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AreaDO;
import cn.iocoder.yudao.module.mall.member.service.address.AreaService;
import cn.iocoder.yudao.module.mall.product.convert.area.AreaConvert;
import cn.iocoder.yudao.module.mall.product.convert.brand.ProductBrandConvert;
import cn.iocoder.yudao.module.mall.product.convert.category.ProductCategoryConvert;
import cn.iocoder.yudao.module.mall.product.convert.spec.ProductSpecConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.brand.ProductBrandDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategorySpecDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.brand.ProductBrandService;
import cn.iocoder.yudao.module.mall.product.service.category.ConfigProductCategorySpecService;
import cn.iocoder.yudao.module.mall.product.service.category.ProductCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "开放API - 商品 SPU")
@RestController
@RequestMapping("/mall/open/common")
@Validated
@OpenAuth
public class AppOpenCommonController {

    @Resource
    private AreaService areaService;

    @Resource
    private ProductBrandService brandService;

    @Resource
    private ProductCategoryService categoryService;

    @Resource
    private ConfigProductCategorySpecService configProductCategorySpecService;

    @PostMapping("/brand/createByName")
    @Operation(summary = "根据品牌名称生成")
    public CommonResult<Long> createBrandByName(@Valid @RequestBody AppBrandCreateReqVO createReq) {
        Long id  = brandService.createFromName(createReq.getName());
        return success(id);
    }

    @PostMapping("/brand/get")
    @Operation(summary = "获得品牌详情")
    public CommonResult<AppOpenProductBrandDetailRespVO> getBrand(@Valid @RequestBody AppOpenIdReqVO commonIdReq) {
        ProductBrandDO brand = brandService.getBrand(commonIdReq.getId());
        return success(ProductBrandConvert.INSTANCE.convert2(brand));
    }

    @PostMapping("/brand/list")
    @Operation(summary = "获得品牌列表")
    public CommonResult<PageResult<AppOpenProductBrandRespVO>> getBrandList(@Valid @RequestBody AppOpenProductBrandPageReqVO pageVO) {
        PageResult<ProductBrandDO> page = brandService.getBrandPage(pageVO);
        return success(ProductBrandConvert.INSTANCE.convertPage2(page));
    }

    /**
     * 省市区镇四级区域接口
     * @param param
     * @return
     */
    @PostMapping("/area/list")
    @Operation(summary = "省市区镇四级区域接口")
    public CommonResult<List<AppOpenAreaRespVO>> getAreas(@RequestBody @Valid AppOpenAreaReqVO param) {
        List<AreaDO> list = (areaService.getAreas(param));
        return success(AreaConvert.INSTANCE.convertList(list));
    }

    /**
     * 查询顶级大类
     *
     * @return
     */
    @PostMapping("/productCategory/getRootList")
    @Operation(summary = "查询顶级大类")
    public CommonResult<List<AppOpenProductCategoryRespVO>> getRootCategoryList() {
        List<ProductCategoryDO> list = categoryService.getRootCategoryList(ProductCategoryStatusEnum.ENABLE);
        return CommonResult.success(ProductCategoryConvert.INSTANCE.convertList04(list));
    }

    /**
     * 查询父类下所有级别的分类
     *
     * @param reqVO
     * @return
     */
    @PostMapping("/productCategory/getChildList")
    @Operation(summary = "查询父类下所有级别的分类")
    public CommonResult<List<AppOpenProductCategoryRespVO>> getAllChildCategoryList(@Valid @RequestBody AppOpenProductCategoryReqVO reqVO) {
        List<ProductCategoryDO> list = categoryService.getChildCategoryList(reqVO.getParentCategoryId(), ProductCategoryStatusEnum.ENABLE);
        return CommonResult.success(ProductCategoryConvert.INSTANCE.convertList04(list));
    }

    /**
     * 查询商品规格
     *
     * @param reqVO
     * @return
     */
    @PostMapping("/productSpec/list")
    @Operation(summary = "查询商品分类下的规格列表")
    @TenantIgnore
    public CommonResult<List<AppOpenProductSpecRespVO>> getProductSpecList(@Valid @RequestBody AppOpenProductSpecReqVO reqVO) {
        List<ConfigProductCategorySpecDO> list = configProductCategorySpecService.getSpecList4product(reqVO.getProductCategoryId());
        return CommonResult.success(ProductSpecConvert.INSTANCE.convertList04(list));
    }

}
