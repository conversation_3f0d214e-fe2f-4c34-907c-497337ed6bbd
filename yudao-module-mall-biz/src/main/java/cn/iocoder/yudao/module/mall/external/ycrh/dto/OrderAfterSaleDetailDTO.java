package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class OrderAfterSaleDetailDTO {

    /**
     * 售后类型 "1":退货
     */
    private Integer afsType;

    /**
     * 包裹单号
     */
    private String packageNo;

    /**
     * 售后单号(售后类型1时必填)
     */
    private String afsNo;

    /**
     * 商品ID
     */
    private String skuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 售后原因
     */
    private String afsReason;

    /**
     * 售后商品数量
     */
    private Integer afsSkuNum;

    /**
     * 退款账号
     */
    private String refundNo;

    /**
     * 退款商品金额
     */
    private BigDecimal afsSkuPrice;

    /**
     * 退款运费(如果因退货而产生运费请填负数)
     */
    private BigDecimal afsFreightPrice;

    /**
     * 退款总金额
     */
    private BigDecimal afsTotalPrice;

    /**
     * 下单时间(yyyy-MM-dd HH:mm:ss)
     */
    private String afsFinishTime;

}
