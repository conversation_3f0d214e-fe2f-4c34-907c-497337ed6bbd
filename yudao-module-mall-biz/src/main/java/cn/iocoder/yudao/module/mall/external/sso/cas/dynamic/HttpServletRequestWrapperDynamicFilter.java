package cn.iocoder.yudao.module.mall.external.sso.cas.dynamic;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.mall.external.sso.cas.CasContextHolder;
import cn.iocoder.yudao.module.mall.external.sso.cas.util.CasUtils;
import lombok.extern.slf4j.Slf4j;
import org.jasig.cas.client.util.HttpServletRequestWrapperFilter;
import org.jasig.cas.client.validation.Assertion;

import javax.servlet.Filter;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Map;

import static org.jasig.cas.client.util.AbstractCasFilter.CONST_CAS_ASSERTION;

/**
 * @author: HF
 * @description: TODO
 * @date: 2024/4/13 20:31
 */
@Slf4j
public class HttpServletRequestWrapperDynamicFilter extends AbstractDynamicCasFilter {

    public HttpServletRequestWrapperDynamicFilter(CasConfigService casConfigService) {
        super(casConfigService);
    }

    @Override
    protected Filter genTargetFilter(FilterConfig config, Map<String, String> casConfigMap) throws ServletException {
        DummyFilterConfig newConfig = new DummyFilterConfig("", config.getServletContext(), casConfigMap);
        Filter targetFilter = new HttpServletRequestWrapperFilter();
        targetFilter.init(newConfig);
        return targetFilter;
    }

    @Override
    protected void doBefore(ServletRequest servletRequest,  Long tenantId, Map<String, Object> bodyJson) {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        CasContextHolder.clear();
        if(isUidInBody(bodyJson)) {
            String uid = bodyJson.get("uid").toString();
            log.info("--assertionRedisKey---tenantId:{}--uid:{}-", tenantId, uid);
            if (StrUtil.isNotBlank(uid) && tenantId != null) {
                String assertionRedisKey = casConfigService.getAssertionRedisKey(tenantId, uid);
                Assertion assertion = casConfigService.getAssertionFromRedis(assertionRedisKey);
                log.info("--assertionRedisKey key: {}", assertionRedisKey);
                if (assertion != null) {
                    log.info("---assertionRedisKey--put assertion to session=, {}", assertion);
                    HttpSession session = httpServletRequest.getSession(true);
                    session.setAttribute(CONST_CAS_ASSERTION, assertion);
                    CasContextHolder.setUserId(uid);
                    CasContextHolder.setCasUserInfo(assertion.getPrincipal().getAttributes());
                }
            }
        } else {
            HttpSession session = httpServletRequest.getSession(true);
            Assertion assertion = (Assertion) session.getAttribute(CONST_CAS_ASSERTION);
            if (assertion != null) {
                log.info("assertion from session: {}", assertion.getPrincipal().getAttributes());
                String uid = CasUtils.generateUuid();
                CasContextHolder.setUserId(uid);
                CasContextHolder.setCasUserInfo(assertion.getPrincipal().getAttributes());
                String assertionRedisKey = casConfigService.getAssertionRedisKey(tenantId, uid);
                casConfigService.putAssertionToRedis(assertionRedisKey, assertion);
            }
        }
    }

    @Override
    public void destroy() {
        CasContextHolder.clear();
        super.destroy();
    }

    private boolean isUidInBody(Map<String, Object> bodyJson) {
        return bodyJson!= null && bodyJson.containsKey("uid");
    }

}
