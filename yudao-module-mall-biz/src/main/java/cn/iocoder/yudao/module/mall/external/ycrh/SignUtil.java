package cn.iocoder.yudao.module.mall.external.ycrh;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.util.DigestUtils;

import java.util.*;

@Slf4j
public class SignUtil {

    public static String createSign(Map<String, Object> params, String privateKey) {
        // 移除签名
        params.keySet().removeIf(key -> key.equals("sign"));

        // map转json去转义
        String jsonStr = JSON.toJSONString(params);
        // 参数排序
        Map<String, Object> sortMap = jsonToMap(jsonStr);
        // 将参数以参数名的字典升序排序
        jsonStr = StringEscapeUtils.unescapeJson(JSON.toJSONString(sortMap));

        String signTempStr = privateKey + jsonStr;
        log.info("signTempStr::{}", StringUtils.abbreviate(signTempStr, "...", 500));
        String encrypt = DigestUtils.md5DigestAsHex(signTempStr.getBytes());
        encrypt = encrypt.toUpperCase();
        System.out.println("md5 :: " + encrypt);
        return encrypt;
    }

    private static Map<String, Object> jsonToMap(String jsonStr) {
        Map<String, Object> treeMap = new TreeMap<String, Object>();
        JSONObject json = JSONObject.parseObject(jsonStr, Feature.OrderedField);// Feature.OrderedField实现解析后保存不乱序
        Iterator<String> keys = json.keySet().iterator();
        while (keys.hasNext()) {
            String key = keys.next();
            Object value = json.get(key);
            // 判断传入kay-value中是否含有""或null
            if (json.get(key) == null || value == null || value.toString().length() == 0) {
                // 当JSON字符串存在null时,不将该kay-value放入Map中,即显示的结果不包括该kay-value
                continue;
            }
            // 判断是否为JSONArray(json数组)
            if (value instanceof JSONArray) {
                JSONArray jsonArray = (JSONArray) value;
                List<Object> arrayList = new ArrayList<>();
                for (Object object : jsonArray) {
                    // 判断是否为JSONObject，如果是 转化成TreeMap
                    if (object instanceof JSONObject) {
                        object = jsonToMap(object.toString());
                    }
                    arrayList.add(object);
                }
                treeMap.put(key, arrayList);
            } else {
                // 判断该JSON中是否嵌套JSON
                boolean flag = isJSONValid(value.toString());
                if (flag) {
                    // 若嵌套json了,通过递归再对嵌套的json(即子json)进行排序
                    value = jsonToMap(value.toString());
                }
                // 其他基础类型直接放入treeMap
                // JSONObject可进行再次解析转换
                treeMap.put(key, value);
            }
        }
        return treeMap;
    }

    private final static boolean isJSONValid(String json) {
        try {
            JSONObject.parseObject(json);
        } catch (Exception ex) {
            return false;
        }
        return true;
    }

}
