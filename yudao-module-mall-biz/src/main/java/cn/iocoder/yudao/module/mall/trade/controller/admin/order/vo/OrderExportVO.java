package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 订单主信息导出
 *
 * <AUTHOR>
 * @Date 2024/6/13
 */
@Schema(description = "管理后台 - 订单导出excel VO")
@Data
public class OrderExportVO {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "订单号")
    @Excel(needMerge = true, name = "订单号", orderNum = "1",width = 24)
    private String no;

    @Schema(description = "外部订单编号")
    @Excel(needMerge = true, name = "外部订单编号", orderNum = "2",width = 24, replace = {"--_null"})
    private String thirdOrderId;

    @Schema(description = "供应商名称")
    @Excel(needMerge = true, name = "供应商名称", orderNum = "3",width = 20)
    private String supplierName;

    @Schema(description = "下单人")
    @Excel(needMerge = true, name = "下单人", orderNum = "4",width = 15)
    private String userName;

    @Schema(description = "下单人工号")
    @Excel(needMerge = true, name = "下单人工号", orderNum = "5",width = 15)
    private String userNo;

    @Schema(description = "所在部门")
    @Excel(needMerge = true, name = "所在部门", orderNum = "6",width = 15)
    private String deptName;

    @Schema(description = "经济分类")
    @Excel(needMerge = true, name = "经济分类", orderNum = "7",width = 15)
    private String ecnomyClass;

    @Schema(description = "项目类型")
    @Excel(needMerge = true, name = "项目类型", orderNum = "8",width = 15)
    private String projectType;

    @Schema(description = "项目编号")
    @Excel(needMerge = true, name = "项目编号（经费卡卡号）", orderNum = "9",width = 15)
    private String projectNo;

    @Schema(description = "项目名称")
    @Excel(needMerge = true, name = "项目名称（经费卡名称）", orderNum = "10",width = 15)
    private String projectName;

    @Schema(description = "项目所属部门")
    @Excel(needMerge = true, name = "项目所属部门（经费卡所属部门）", orderNum = "11",width = 15)
    private String projectDepartment;

    @Schema(description = "收货人")
    @Excel(needMerge = true, name = "收货人", orderNum = "12",width = 15)
    private String receiverName;

    @Schema(description = "收货地址")
    @Excel(needMerge = true, name = "收货地址", orderNum = "13",width = 15)
    private String receiverAddress;

    @Schema(description = "订单状态")
    @Excel(needMerge = true, name = "订单状态", orderNum = "14", replace = {"未确认_1", "已确认_2", "已发货_3", "已送达_4", "已签收_5", "已完成_8", "已取消_9"}, width = 15)
    private Integer status;

    @Schema(description = "开票状态")
    @Excel(needMerge = true, name = "开票状态", orderNum = "15", replace = {"未开票_0", "已申请开票_1", "开票完成_2", "开票失败_3", "验真处理中_4", "验真完成_5", "验真失败_6"}, width = 15)
    private Integer invoiceStatus;

    @Schema(description = "账单编号")
    @Excel(needMerge = true, name = "账单编号", orderNum = "16",width = 15)
    private Long billNo;

    @Schema(description = "下单时间")
    @Excel(needMerge = true, name = "下单时间", orderNum = "17", exportFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", replace = {"--_null"}, width = 24)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime submitTime;

    @Schema(description = "完成时间")
    @Excel(needMerge = true, name = "完成时间", orderNum = "18", exportFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", replace = {"--_null"}, width = 24)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime finishTime;

    @Schema(description = "商品金额")
    @Excel(needMerge = true, name = "商品金额", orderNum = "19",width = 15)
    private BigDecimal productPrice;

    @Schema(description = "运费金额")
    @Excel(needMerge = true, name = "运费金额", orderNum = "20",width = 15, replace = {"0_null"})
    private BigDecimal deliveryPrice;

    @Schema(description = "订单总金额")
    @Excel(needMerge = true, name = "订单总金额", orderNum = "21",width = 15)
    private BigDecimal orderPrice;

    @Schema(description = "退款金额")
    @Excel(needMerge = true, name = "退款金额", orderNum = "22",width = 15, replace = {"0_null"})
    private BigDecimal refundPrice;

    @Schema(description = "账期支付金额")
    @Excel(needMerge = true, name = "账期支付金额", orderNum = "23", width = 20)
    private BigDecimal periodPayPrice;

    @Schema(description = "个人支付金额")
    @Excel(needMerge = true, name = "个人支付金额", orderNum = "24", width = 20)
    private BigDecimal personalPayPrice;

    @Schema(description = "下单终端")
    @Excel(needMerge = true, name = "下单终端", orderNum = "25", replace = {"PC端_1", "移动端H5_20"}, width = 15)
    private Integer platform;

    @Schema(description = "订单明细")
    @ExcelCollection(name = "订单明细", orderNum = "26")
    private List<OrderItemExportVO> itemList;

}
