package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecValueRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 商品 SKU Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSkuRespVO extends ProductSkuBaseVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "供应商skuId")
    private String skuInnerId;

    @Schema(description = "库存", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "库存不能为空")
    private Integer stock;

    @Schema(description = "预警库存", example = "1")
    private Integer warnStock;

    /**
     * 属性数组
     */
    private List<ProductSpecValueRespVO> specValueList;

}
