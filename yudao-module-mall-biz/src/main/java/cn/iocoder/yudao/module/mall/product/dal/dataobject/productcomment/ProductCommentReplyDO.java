package cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 商品评价回复 DO
 *
 * <AUTHOR>
 */
@TableName("product_comment_reply")
@KeySequence("product_comment_reply_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCommentReplyDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 评价id
     */
    private Long commentId;
    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 会员id
     */
    private Long memberId;
    /**
     * 会员昵称
     */
    private String nickName;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 评价内容
     */
    private String content;
    /**
     * 审核状态 0-待审核 1-审核通过 2-审核驳回
     */
    private Integer auditStatus;
    /**
     * 回复者ip
     */
    private String clientIp;
    /**
     * 回复者地区
     */
    private String clientArea;
    /**
     * 举报次数
     */
    private Integer reportCount;

}
