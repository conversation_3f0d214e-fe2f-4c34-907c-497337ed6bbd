package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
public class StatsSupplierSaleRespVO {

    private Long supplierId;
    private String supplierName;
    private Long totalCount;
    private BigDecimal totalPrice;
    private BigDecimal deliveryPrice;
    private BigDecimal skuPrice;
    private BigDecimal afterSaleTotalPrice;
    private Long afterSaleTotalCount;
    private BigDecimal netTotalPrice;

}
