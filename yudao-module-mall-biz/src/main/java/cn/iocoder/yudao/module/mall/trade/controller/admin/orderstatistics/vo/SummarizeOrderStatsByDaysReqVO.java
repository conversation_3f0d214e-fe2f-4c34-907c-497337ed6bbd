package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Data
public class SummarizeOrderStatsByDaysReqVO {
    @Schema(description = "类型，新增订单-1、 确认订单-2、 发货订单-3、完成订单-4")
    private Long type;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    public LocalDateTime getStartTime() {
        return startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public LocalDateTime getEndTime() {
        return endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
}
