package cn.iocoder.yudao.module.mall.external.ycrh.dto;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */

import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 业财融合统一返回类
 *
 * <AUTHOR>
 */
@Data
public class YcrhResp<T> {

    private String resultCode;

    private String resultMsg;

    private T data;


    @JsonIgnore
    public boolean isSuccess() {
        return "1".equals(this.resultCode);
    }


    /**
     * 如果失败 抛出异常
     *
     * @param checkFail
     * @return
     */
    public T getData(boolean checkFail) {
        if (!isSuccess()) {
            throw exception(ErrorCodeConstants.YCRH_FAIL, this.resultMsg);
        }
        return data;
    }
}
