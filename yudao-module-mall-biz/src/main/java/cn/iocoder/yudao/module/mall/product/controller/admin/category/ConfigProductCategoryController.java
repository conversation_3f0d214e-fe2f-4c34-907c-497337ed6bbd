package cn.iocoder.yudao.module.mall.product.controller.admin.category;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.category.ProductCategoryConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.category.ConfigProductCategoryService;
import cn.iocoder.yudao.module.mall.product.service.category.ProductCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 平台配置商品分类")
@RestController
@RequestMapping("/product/config/category")
@Validated
public class ConfigProductCategoryController {

    @Resource
    private ConfigProductCategoryService configCategoryService;

    @PostMapping("/create")
    @Operation(summary = "创建商品分类")
    @PreAuthorize("@ss.hasPermission('config:product:category:create')")
    public CommonResult<Long> createCategory(@Valid @RequestBody ProductCategoryCreateReqVO createReqVO) {
        return success(configCategoryService.createCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品分类")
    @PreAuthorize("@ss.hasPermission('config:product:category:update')")
    public CommonResult<Boolean> updateCategory(@Valid @RequestBody ProductCategoryUpdateReqVO updateReqVO) {
        configCategoryService.updateCategory(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新商品分类状态")
    @PreAuthorize("@ss.hasPermission('config:product:category:update')")
    public CommonResult<Boolean> updateCategoryStatus(@RequestBody ProductCategoryUpdateReqVO updateReqVO) {
        configCategoryService.updateCategoryStatus(updateReqVO.getId(), ProductCategoryStatusEnum.fromStatus(updateReqVO.getStatus()));
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('config:product:category:delete')")
    public CommonResult<Boolean> deleteCategory(@RequestParam("id") Long id) {
        configCategoryService.deleteCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasAnyPermissions('config:product:category:query', 'product:category:query')")
    public CommonResult<ProductCategoryRespVO> getCategory(@RequestParam("id") Long id) {
        ConfigProductCategoryDO category = configCategoryService.getCategory(id);
        return success(ProductCategoryConvert.INSTANCE.convert2(category));
    }

    @GetMapping("/list-by-parent")
    @Operation(summary = "根据上级分类ID获得下级商品分类列表")
    @PreAuthorize("@ss.hasAnyPermissions('config:product:category:query', 'product:category:query')")
    public CommonResult<List<ProductCategoryRespVO>> getCategoryListByParent(Long parentCategoryId, Integer status) {
        List<ConfigProductCategoryDO> list = configCategoryService.getChildCategoryList(parentCategoryId, status);
        return success(ProductCategoryConvert.INSTANCE.convertList02(list));
    }

    @GetMapping("/list-root")
    @Operation(summary = "获得一级商品分类列表")
	@PreAuthorize("@ss.hasAnyPermissions('config:product:category:query', 'product:category:query')")
    public CommonResult<List<ProductCategoryRespVO>> getRootCategoryList(Integer type) {
        List<ConfigProductCategoryDO> list = configCategoryService.getRootCategoryList(type, ProductCategoryStatusEnum.ENABLE);
        return success(ProductCategoryConvert.INSTANCE.convertList07(list));
    }

    @GetMapping("/list-children-tree")
    @Operation(summary = "获得下级商品分类树形列表")
    @PreAuthorize("@ss.hasAnyPermissions('config:product:category:query', 'product:category:query')")
    public CommonResult<List<ProductCategoryTreeRespVO>> getChildCategoryTreeList(@RequestParam(value = "parentCategoryId", required = true) Long parentCategoryId) {
        List<ConfigProductCategoryDO> list = configCategoryService.getChildCategoryTreeList(parentCategoryId, ProductCategoryStatusEnum.ENABLE);
        return success(ProductCategoryConvert.INSTANCE.convertList08(list));
    }

    @GetMapping("/get-total-count")
    @Operation(summary = "查询分数总数量")
    @PreAuthorize("@ss.hasAnyPermissions('config:product:category:query', 'product:category:query')")
    public CommonResult<Long> getTotalCount(Integer type) {
        return success(configCategoryService.getCategoryTotalCount(type));
    }

    @GetMapping("/sync")
    @Operation(summary = "同步vop商品分类，支持增量同步")
	@PreAuthorize("@ss.hasPermission('config:product:category:sync')")
    public CommonResult sync(){
        boolean result = configCategoryService.sync(); 
        return success(result);
    }
}
