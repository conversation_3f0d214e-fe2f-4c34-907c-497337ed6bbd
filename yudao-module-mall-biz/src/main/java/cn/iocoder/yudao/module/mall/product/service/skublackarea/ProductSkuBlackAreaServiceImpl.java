package cn.iocoder.yudao.module.mall.product.service.skublackarea;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.sku.ProductSkuMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.mall.product.dal.dataobject.skublackarea.ProductSkuBlackAreaDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.module.mall.product.convert.skublackarea.ProductSkuBlackAreaConvert;
import cn.iocoder.yudao.module.mall.product.dal.mysql.skublackarea.ProductSkuBlackAreaMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 商品sku禁售区域 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductSkuBlackAreaServiceImpl extends ServiceImpl<ProductSkuBlackAreaMapper,ProductSkuBlackAreaDO> implements ProductSkuBlackAreaService {

    @Resource
    private ProductSkuBlackAreaMapper skuBlackAreaMapper;
    @Lazy
    @Resource
    private ProductSkuMapper productSkuMapper;

    @Override
    public Long createSkuBlackArea(ProductSkuBlackAreaCreateReqVO createReqVO) {
        // 插入
        ProductSkuBlackAreaDO skuBlackArea = ProductSkuBlackAreaConvert.INSTANCE.convert(createReqVO);
        skuBlackAreaMapper.insert(skuBlackArea);
        // 返回
        return skuBlackArea.getId();
    }

    @Override
    public void updateSkuBlackArea(ProductSkuBlackAreaUpdateReqVO updateReqVO) {
        // 校验存在
        validateSkuBlackAreaExists(updateReqVO.getId());
        // 更新
        ProductSkuBlackAreaDO updateObj = ProductSkuBlackAreaConvert.INSTANCE.convert(updateReqVO);
        skuBlackAreaMapper.updateById(updateObj);
    }

    @Override
    public void deleteSkuBlackArea(Long id) {
        // 校验存在
        validateSkuBlackAreaExists(id);
        // 删除
        skuBlackAreaMapper.deleteById(id);
    }

    private void validateSkuBlackAreaExists(Long id) {
        if (skuBlackAreaMapper.selectById(id) == null) {
        }
    }

    @Override
    public ProductSkuBlackAreaDO getSkuBlackArea(Long id) {
        return skuBlackAreaMapper.selectById(id);
    }

    @Override
    public List<ProductSkuBlackAreaDO> getSkuBlackAreaList(Collection<Long> ids) {
        return skuBlackAreaMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ProductSkuBlackAreaDO> getSkuBlackAreaPage(ProductSkuBlackAreaPageReqVO pageReqVO) {
        return skuBlackAreaMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProductSkuBlackAreaDO> getSkuBlackAreaList(ProductSkuBlackAreaExportReqVO exportReqVO) {
        return skuBlackAreaMapper.selectList(exportReqVO);
    }

    @Override
    public void setProductSkuBlackArea(List<ProductSkuBlackAreaReqVO> reqVOs, Long supplierId) {
        if (CollectionUtil.isNotEmpty(reqVOs)) {
            //1.删除原商品禁售区域信息
            List<Long> skuIds = reqVOs.stream().map(ProductSkuBlackAreaReqVO::getSkuId).collect(Collectors.toList());
            List<ProductSkuDO> skuList = productSkuMapper.selectList(Wrappers.lambdaQuery(ProductSkuDO.class)
                    .in(ProductSkuDO::getId, skuIds)
                    .eq(ProductSkuDO::getSupplierId, supplierId)
                    .select(ProductSkuDO::getId));
            if(CollectionUtil.isEmpty(skuList)) {
                return;
            }
            skuIds = skuList.stream().map(ProductSkuDO::getId).collect(Collectors.toList());

            LambdaQueryWrapper<ProductSkuBlackAreaDO> wrapper = Wrappers.lambdaQuery(ProductSkuBlackAreaDO.class).in(ProductSkuBlackAreaDO::getSkuId,skuIds).eq(ProductSkuBlackAreaDO::getSupplierId, supplierId);
            this.remove(wrapper);
            //2.添加商品禁售区域信息
            List<ProductSkuBlackAreaDO> productSkuBlackAreaDOS = ProductSkuBlackAreaConvert.INSTANCE.convert(reqVOs, supplierId);
            this.saveBatch(productSkuBlackAreaDOS);
        }
    }

    @Override
    public List<ProductSkuBlackAreaDO> getProductSkuBlackArea(List<Long> skuIds, Long supplierId) {
        LambdaQueryWrapper<ProductSkuBlackAreaDO> wrapper = Wrappers.lambdaQuery(ProductSkuBlackAreaDO.class).in(ProductSkuBlackAreaDO::getSkuId,skuIds).eq(ProductSkuBlackAreaDO::getSupplierId, supplierId);
        return skuBlackAreaMapper.selectList(wrapper);
    }

}
