package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.YearMonth;

@Data
public class OrderSaleMonthSummaryReqVO {

    @NotNull(message = "开始时间不能为空")
    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM")
    private YearMonth startMonth;

    @NotNull(message = "结束时间不能为空")
    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM")
    private YearMonth endMonth;
}
