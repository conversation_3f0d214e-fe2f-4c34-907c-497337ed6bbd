package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductCategorySalesAndRevenueSummaryRespVO {

    @Schema(description = "分类id")
    private Long categoryId;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "商品销量")
    private Long salesVolume;

    @Schema(description = "商品销售额")
    private BigDecimal salesRevenue;

    @Data
    public static class ProductSellCountSummaryRespVO {

        @Schema(description = "商品sku")
        Long skuId;

        @Schema(description = "商品名称")
        String productName;

        @Schema(description = "商品销量")
        Long sellCount;

    }
}
