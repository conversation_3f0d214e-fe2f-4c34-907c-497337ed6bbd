package cn.iocoder.yudao.module.mall.product.controller.admin.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Schema(description = "管理后台 - 商品分类克隆 Request VO")
@Data
@ToString(callSuper = true)
public class ProductCategoryCloneReqVO {

    @Schema(description = "分类ID")
    @NotEmpty(message = "分类ID不能为空")
    private List<Long> categoryIdList;

}
