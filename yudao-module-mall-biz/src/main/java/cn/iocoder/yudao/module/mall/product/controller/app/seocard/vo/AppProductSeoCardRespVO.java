package cn.iocoder.yudao.module.mall.product.controller.app.seocard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "APP前台 - 运营区域 Response VO")
@Data
@ToString(callSuper = true)
public class AppProductSeoCardRespVO {

    @Schema(description = "id", required = true)
    private Long id;

    @Schema(description = "区域名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "区域名称不能为空")
    private String name;

    @Schema(description = "显示标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "显示标题不能为空")
    private String title;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "背景图")
    private String imageUrl;

    @Schema(description = "区域类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "区域类型不能为空")
    private Integer type;

    @Schema(description = "区域布局", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "区域布局不能为空")
    private Integer layout;

    @Schema(description = "区域备注")
    private String memo;

    @Schema(description = "区域排序")
    private Integer sort;

    @Schema(description = "区域内容,json格式")
    private String content;

    @Schema(description = "置顶商品,逗号分隔")
    private String topSku;

}
