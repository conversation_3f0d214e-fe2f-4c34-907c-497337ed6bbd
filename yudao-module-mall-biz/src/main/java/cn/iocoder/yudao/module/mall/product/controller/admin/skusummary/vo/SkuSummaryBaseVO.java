package cn.iocoder.yudao.module.mall.product.controller.admin.skusummary.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
 * 供应商上架商品数统计 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class SkuSummaryBaseVO {

    @Schema(description = "供应商ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;

    @Schema(description = "供应商类型")
    private Integer supplierType;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "上架数量")
    private Long listingTotal;

    @Schema(description = "下架数量")
    private Long delistingTotal;

    @Schema(description = "截止时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "截止时间不能为空")
    private Date deadline;

}
