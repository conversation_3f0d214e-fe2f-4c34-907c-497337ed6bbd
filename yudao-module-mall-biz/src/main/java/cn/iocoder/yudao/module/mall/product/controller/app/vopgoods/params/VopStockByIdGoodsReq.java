package cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.params;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class VopStockByIdGoodsReq {

    /**
     * 商品skuIds
     */
    @Schema(description = "商品skuIds")
    @NotNull(message = "商品skuIds不能为空")
    private List<Long> skuIds;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    @NotNull(message = "供应商id不能为空")
    private Long supplierId;

    /**
     * 省ID
     */
    @Schema(description = "省ID")
    private Long provinceId;

    /**
     * 城市ID
     */
    @Schema(description = "城市ID")
    private Long cityId;

    /**
     * 区ID
     */
    @Schema(description = "区ID")
    private Long countyId;

    /**
     * 街道/乡镇ID
     */
    @Schema(description = "街道/乡镇ID")
    private Long townId;
}
