package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class TradeAfterSaleReqVO {

    @Schema(description = "订单id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "订单id不能为空")
    private Long orderId;

    @Schema(description = "售后单号")
    private List<String> afterSaleNOS;
}
