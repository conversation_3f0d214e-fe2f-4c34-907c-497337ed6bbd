package cn.iocoder.yudao.module.mall.product.service.productcomment;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.productcomment.ProductCommentReplyConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentReplyDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.productcomment.ProductCommentReplyMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.PRODUCT_COMMENT_REPLY_NOT_EXISTS;

/**
 * 商品评价回复 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductCommentReplyServiceImpl implements ProductCommentReplyService {

    @Resource
    private ProductCommentReplyMapper productCommentReplyMapper;

    @Override
    public Long createProductCommentReply(ProductCommentReplyCreateReqVO createReqVO) {
        // 插入
        ProductCommentReplyDO productCommentReply = ProductCommentReplyConvert.INSTANCE.convert(createReqVO);
        productCommentReplyMapper.insert(productCommentReply);
        // 返回
        return productCommentReply.getId();
    }

    @Override
    public void updateProductCommentReply(ProductCommentReplyUpdateReqVO updateReqVO) {
        // 校验存在
        validateProductCommentReplyExists(updateReqVO.getId());
        // 更新
        ProductCommentReplyDO updateObj = ProductCommentReplyConvert.INSTANCE.convert(updateReqVO);
        productCommentReplyMapper.updateById(updateObj);
    }

    @Override
    public void deleteProductCommentReply(Long id) {
        // 校验存在
        validateProductCommentReplyExists(id);
        // 删除
        productCommentReplyMapper.deleteById(id);
    }

    private void validateProductCommentReplyExists(Long id) {
        if (productCommentReplyMapper.selectById(id) == null) {
            throw exception(PRODUCT_COMMENT_REPLY_NOT_EXISTS);
        }
    }

    @Override
    public ProductCommentReplyDO getProductCommentReply(Long id) {
        return productCommentReplyMapper.selectById(id);
    }

    @Override
    public List<ProductCommentReplyDO> getProductCommentReplyList(Collection<Long> ids) {
        return productCommentReplyMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ProductCommentReplyDO> getProductCommentReplyPage(ProductCommentReplyPageReqVO pageReqVO) {
        return productCommentReplyMapper.selectPage(pageReqVO);
    }

}
