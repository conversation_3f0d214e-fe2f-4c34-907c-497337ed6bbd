package cn.iocoder.yudao.module.mall.external.hrms.impl.hzau.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 部门信息接口响应结果
 * <AUTHOR>
 */
@Data
public class HzauDeptPageRespDTO extends HzauPageDTO {

    @JsonProperty("data_struct")
    private Map<String, String> dataStruct;

    @JsonProperty("encrypted_field")
    private String encryptedField;

    @JsonProperty("data")
    private List<HzauDeptDTO> list;

}
