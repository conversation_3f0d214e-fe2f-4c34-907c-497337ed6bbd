package cn.iocoder.yudao.module.mall.product.service.skusearch;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.product.controller.app.skusearch.vo.SkuContentDemoPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.SkuContentDemoDO;
import cn.iocoder.yudao.module.mall.product.dal.es.SkuContentDemoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.nested.Nested;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品规格 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SkuContentDemoServiceImpl implements SkuContentDemoService {

    @Resource
    private SkuContentDemoRepository skuContentDemoRepository;
    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Override
    public void save(SkuContentDemoDO entity) {
        skuContentDemoRepository.save(entity);
    }

    @Override
    public void delete(Long id) {
        skuContentDemoRepository.deleteById(id);
    }

    @Override
    public SkuContentDemoDO getById(Long id) {
        return skuContentDemoRepository.findById(id).orElse(null);
    }

    @Override
    public PageResult<SkuContentDemoDO> seachByPage(@NotNull SkuContentDemoPageReqVO pageReqVO) {
        NativeSearchQueryBuilder searchQueryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolFilterBuilder = QueryBuilders.boolQuery();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        Long tenantId = TenantContextHolder.getTenantId();
        if(tenantId != null) {
            boolFilterBuilder.must(QueryBuilders.termQuery("tenantId", tenantId));
        }
        if(pageReqVO.getBrandId() != null) {
            boolFilterBuilder.must(QueryBuilders.termQuery("brandId", pageReqVO.getBrandId()));
        }
        if(pageReqVO.getSupplierId() != null) {
            boolFilterBuilder.must(QueryBuilders.termQuery("supplierId", pageReqVO.getSupplierId()));
        }
        if(StringUtils.isNotBlank(pageReqVO.getCategoryIdPath())) {
            boolFilterBuilder.must(QueryBuilders.prefixQuery("categoryIdPath", pageReqVO.getCategoryIdPath()));
        }
        if(pageReqVO.getSpecValueVo() != null) {
            boolFilterBuilder.must(QueryBuilders.nestedQuery("specValueList",
                    QueryBuilders.boolQuery().must(QueryBuilders.termQuery("specValueList.specId", pageReqVO.getSpecValueVo().getSpecId()))
                    .must(QueryBuilders.termQuery("specValueList.specValue", pageReqVO.getSpecValueVo().getSpecValue())),
                    ScoreMode.None));
        }
        boolQueryBuilder.must(boolFilterBuilder);

        if(StringUtils.isNotBlank(pageReqVO.getKeyword())) {
            BoolQueryBuilder subBoolBuilder = QueryBuilders.boolQuery();
            subBoolBuilder.should(QueryBuilders.matchQuery("spuName", pageReqVO.getKeyword()));
            subBoolBuilder.should(QueryBuilders.matchQuery("categoryName1", pageReqVO.getKeyword()));
            boolQueryBuilder.must(subBoolBuilder);
        } else {
            boolQueryBuilder.must(QueryBuilders.matchAllQuery());
        }

        searchQueryBuilder.withQuery(boolQueryBuilder);

        TermsAggregationBuilder brandAggsBuilder = AggregationBuilders.terms("brandIds").field("brandId").size(3);
        NestedAggregationBuilder specAggsBuilder = AggregationBuilders.nested("specValueList", "specValueList").subAggregation(
                AggregationBuilders.terms("specId").field("specValueList.specId").subAggregation(AggregationBuilders.terms("specValue").field("specValueList.specValue")));
        searchQueryBuilder.withAggregations(brandAggsBuilder);
        searchQueryBuilder.withAggregations(specAggsBuilder);

        searchQueryBuilder.withSorts(SortBuilders.scoreSort().order(SortOrder.DESC));

        PageResult<SkuContentDemoDO> pageResult = new PageResult<>();
        // 这里分页从0开始
        PageRequest pageReq = PageRequest.of(pageReqVO.getPageNo() - 1, pageReqVO.getPageSize());
        searchQueryBuilder.withPageable(pageReq);

        SearchHits<SkuContentDemoDO> searchHits = elasticsearchTemplate.search(searchQueryBuilder.build(), SkuContentDemoDO.class);
        pageResult.setList(searchHits.getSearchHits().stream().map(item -> item.getContent()).collect(Collectors.toList()));
        pageResult.setTotal(searchHits.getTotalHits());
        pageResult.setPageSize(pageReq.getPageSize());
        pageResult.setPageNum(pageReq.getPageNumber());

        // 这里的聚合计划搜索结果的分组后得 分类，供应商，品牌，规格，方便进一步筛选 TODO...
        AggregationsContainer aggContainer = searchHits.getAggregations();
        Aggregations aggs = (Aggregations)aggContainer.aggregations();
        ParsedLongTerms longTerms = (ParsedLongTerms)aggs.asMap().get("brandIds");
        List<ParsedLongTerms.ParsedBucket> buckets = (List<ParsedLongTerms.ParsedBucket>) longTerms.getBuckets();
        List<Long> ids = buckets.stream().map(item -> (Long)item.getKey()).collect(Collectors.toList());
        log.info("商品搜索聚合品牌ID： {}", ids);

        Nested nestedAggs = (Nested)aggs.asMap().get("specValueList");
        Terms specIdAggs = nestedAggs.getAggregations().get("specId");
        Map<Long, List<String>> specValMap = new HashMap<>();
        // 遍历
        for (Terms.Bucket bucket : specIdAggs.getBuckets()) {
            Long specId = Long.valueOf(bucket.getKeyAsString());
            Terms specValueTerms = bucket.getAggregations().get("specValue");
            List<String> values = specValueTerms.getBuckets().stream().map(item -> item.getKeyAsString()).collect(Collectors.toList());
            specValMap.put(specId, values);
        }
        log.info("商品搜索聚合规格： {}", JsonUtils.toJsonString(specValMap));

        return pageResult;
    }
}
