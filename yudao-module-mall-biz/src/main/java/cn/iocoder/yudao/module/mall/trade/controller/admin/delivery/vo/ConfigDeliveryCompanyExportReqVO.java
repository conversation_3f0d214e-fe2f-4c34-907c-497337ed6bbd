package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 平台配置物流公司 Excel 导出 Request VO，参数和 ConfigDeliveryCompanyPageReqVO 是一致的")
@Data
public class ConfigDeliveryCompanyExportReqVO {

    @Schema(description = "物流公司编码")
    private String com;

    @Schema(description = "物流公司名称")
    private String name;

    @Schema(description = "物流公司类型")
    private String type;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
