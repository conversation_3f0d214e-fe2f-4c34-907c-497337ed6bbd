package cn.iocoder.yudao.module.mall.product.service.spu;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * 商品 SPU Service 接口
 *
 * <AUTHOR>
 */
public interface ProductSpuService extends IService<ProductSpuDO> {

    /**
     * 创建商品 SPU
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSpu(@Valid ProductSpuCreateReqVO createReqVO);

    void saveOrUpdateBatchV2(List<ProductSpuDO> list);

    /**
     * 更新商品 SPU
     *
     * @param updateReqVO 更新信息
     */
    void updateSpu(@Valid ProductSpuUpdateReqVO updateReqVO);

    /**
     * 更新商品 SPU 状态
     *
     * @param statusUpdateReqVO 更新信息
     */
    void updateSpuStatus(@Valid ProductSpuStatusUpdateReqVO statusUpdateReqVO);

    /**
     * 更新商品 SPU 列出状态
     * @param showStatusUpdateReqVOS
     */
    void updateSpuShowStatus (@Valid List<ProductSpuShowStatusUpdateReqVO> showStatusUpdateReqVOS);

    /**
     * 更新商品 SPU 平台上下架状态
     *
     * @param statusUpdateReqVOS 更新信息
     */
    void updateSpuPlatformStatus(@Valid List<ProductSpuStatusUpdateReqVO> statusUpdateReqVOS);

    /**
     * 删除商品 SPU
     *
     * @param id 编号
     */
    void deleteSpu(Long id);

    /**
     * 下架
     * @param id
     */
    void disableSpu(Long id);

    /**
     * 获得商品 SPU
     *
     * @param id 编号
     * @return 商品 SPU
     */
    ProductSpuDO getSpu(Long id);

    /**
     * 获得商品 SPU, 不包含商详
     * @param id
     * @return
     */
    ProductSpuDO getSimpleSpu(Long id);

    /**
     * 获得商品 SPU
     *
     * @param id 编号
     * @return 商品 SPU
     */
    ProductSpuDetailRespVO getSpuDetail(Long id);

    /**
     * 获得商品 SPU 列表
     *
     * @param ids 编号数组
     * @param needDescription 是否要返回商品描述
     * @return 商品 SPU 列表
     */
    List<ProductSpuDO> getSpuList(Collection<Long> ids, boolean needDescription);

    /**
     * 获得商品 SPU 映射
     *
     * @param ids 编号数组
     * @return 商品 SPU 映射
     */
    default Map<Long, ProductSpuDO> getSpuMap(Collection<Long> ids) {
        return convertMap(getSpuList(ids, false), ProductSpuDO::getId);
    }

    /**
     * 获得所有商品 SPU 列表
     *
     * @return 商品 SPU 列表
     */
    List<ProductSpuDO> getSpuList();

    /**
     * 获得商品 SPU 分页
     *
     * @param pageReqVO 分页查询
     * @return 商品spu分页
     */
    PageResult<ProductSpuDO> getSpuPage(ProductSpuPageReqVO pageReqVO);


    /**
     * 更新商品 SPU 库存（增量）
     *
     * @param stockIncrCounts SPU 编号与库存变化（增量）的映射
     */
    void updateSpuStock(Map<Long, Integer> stockIncrCounts);

    /**
     * 根据innerId获取商品SPU信息
     * @param innerId
     * @param supplierId
     * @return
     */
    ProductSpuDO getSpuByInnerIdAndSupplierId(String innerId, Long supplierId);

    /**
     * 根据innerId获取商品SPU的商品分类信息
     * @param innerId
     * @param supplierId
     * @return
     */
    ProductSpuDO getSpuCategoryByInnerIdAndSupplierId(String innerId, Long supplierId);

    /**
     * 根据供应商ID及内部商品ID查询SPU
     * @param supplierId
     * @param spuInnerId
     * @return
     */
    ProductSpuDO getSimpleSpuBySupplierInnerId(Long supplierId, String spuInnerId);

    /**
     * 根据供应商ID及内部商品ID查询SPU列表
     * @param supplierId
     * @param spuInnerIds
     * @return
     */
    List<ProductSpuDO> getSimpleSpuBySupplierInnerId(Long supplierId, List<String> spuInnerIds);

    List<ProductSpuDO> getSimpleSpuById(Collection<Long> ids);


}
