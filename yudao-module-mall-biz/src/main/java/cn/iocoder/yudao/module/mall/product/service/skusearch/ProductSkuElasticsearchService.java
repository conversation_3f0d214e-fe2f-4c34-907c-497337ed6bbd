package cn.iocoder.yudao.module.mall.product.service.skusearch;

import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.GoodsSearchPageResultResp;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.GoodsSearchReq;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.ProductSkuES;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/7
 */
public interface ProductSkuElasticsearchService {

    /**
     * 保存商品信息到es
     * @param productSkuES
     */
    void save(ProductSkuES productSkuES);


    /**
     * 批量保存商品信息到es
     * @param productSkuESList
     */
    void saveAll(List<ProductSkuES> productSkuESList);

    /**
     * 分页搜索商品信息
     * @param req
     * @return
     */
    GoodsSearchPageResultResp goodsSearchPageList(GoodsSearchReq req);

    /**
     * 批量删除商品
     * @param skuIds
     */
    void deleteAll(List<Long> skuIds);

    /**
     * 删除某个供应商的索引
     * @param supplierId
     */
    void deleteBySupplierId(Long supplierId);

    /**
     * 删除租户内全部商品
     * @param tenantId
     */
    void deleteAllOfTenant(Long tenantId);


}
