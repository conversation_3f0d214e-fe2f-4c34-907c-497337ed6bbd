package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.delivery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "更新订单对应的物流信息 - Request VO")
@Data
public class AppOpenDeliveryBindReqVO {
    /**
     * 订单编号
     */
    @Schema(description = "订单id")
    @NotNull(message = "订单id不能为空")
    private Long orderId;

    /**
     * skuIds
     */
    @Schema(description = "订单包含的商品skuIds字符串，多个sku由逗号隔开")
    private String skuIds;

    /**
     * 物流公司名称
     */
    @Schema(description = "物流公司名称")
    @NotNull(message = "物流公司名称不能为空")
    private String companyName;

    /**
     * 物流公司编码
     */
    @Schema(description = "物流公司编码")
    @NotNull(message = "物流公司编码不能为空")
    private String companyCode;

    /**
     * 物流单号
     */
    @Schema(description = "物流单号")
    @NotNull(message = "物流单号不能为空")
    private String num;
}
