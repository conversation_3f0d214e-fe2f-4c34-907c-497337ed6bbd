package cn.iocoder.yudao.module.mall.trade.controller.admin.order;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.acceptance.TradeOrderAcceptanceSaveReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.acceptance.TradeOrderItemAcceptancePageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.acceptance.TradeOrderItemAcceptanceRespVO;
import cn.iocoder.yudao.module.mall.trade.convert.order.TradeOrderAcceptanceConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemAcceptanceDO;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderItemAcceptanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 交易订单商品验收单
 */
@Tag(name = "管理后台 - 交易订单")
@RestController
@RequestMapping("/trade/order-acceptance")
@Validated
@Slf4j
public class TradeOrderAcceptanceController {

    @Resource
    private TradeOrderItemAcceptanceService orderItemAcceptanceService;

    /**
     * 获得交易订单商品验收单分页
     * @param reqVO
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "获得交易订单分页")
    @PreAuthorize("@ss.hasPermission('trade:order:query')")
    public CommonResult<PageResult<TradeOrderItemAcceptanceRespVO>> getPage(TradeOrderItemAcceptancePageReqVO reqVO) {
        PageResult<TradeOrderItemAcceptanceDO> pageResult = orderItemAcceptanceService.getOrderItemAcceptancePage(reqVO);
        return success(TradeOrderAcceptanceConvert.INSTANCE.convertPage02(pageResult));
    }

    /**
     * 获得交易订单商品验收单
     * @param id
     * @return
     */
    @GetMapping("/get")
    @Operation(summary = "获得交易订单商品验收单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:order:query')")
    public CommonResult<TradeOrderItemAcceptanceRespVO> getInfo(@RequestParam("id") Long id) {
        TradeOrderItemAcceptanceDO orderItemAcceptanceDO = orderItemAcceptanceService.getById(id);
        return success(TradeOrderAcceptanceConvert.INSTANCE.convert02(orderItemAcceptanceDO));
    }

    /**
     * 获得交易订单商品验收单
     * @param orderItemId
     * @return
     */
    @GetMapping("/get-by-item")
    @Operation(summary = "获得交易订单商品验收单")
    @Parameter(name = "orderItemId", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:query', 'trade:order-item-assets:query')")
    public CommonResult<TradeOrderItemAcceptanceRespVO> getListByItem(@RequestParam("orderItemId") Long orderItemId) {
        TradeOrderItemAcceptanceDO orderItemAcceptanceDO = orderItemAcceptanceService.getByOrderItem(orderItemId);
        return success(TradeOrderAcceptanceConvert.INSTANCE.convert02(orderItemAcceptanceDO));
    }

    /**
     * reqVO
     * @param reqVO
     * @return
     */
    @PostMapping("/save-by-item")
    @Operation(summary = "保存订单商品验收单")
    @Parameter(name = "orderItemId", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order-item-assets:update')")
    public CommonResult<Long> saveByItem(@Valid @RequestBody TradeOrderAcceptanceSaveReqVO reqVO) {
        return success(orderItemAcceptanceService.saveOrUpdate(reqVO));
    }

    /**
     * 获得交易订单商品验收单
     * @param orderId
     * @return
     */
    @GetMapping("/list-by-order")
    @Operation(summary = "获得交易订单商品验收单")
    @Parameter(name = "orderId", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:query', 'trade:order-item-assets:query')")
    public CommonResult<List<TradeOrderItemAcceptanceRespVO>> getListByOrder(@RequestParam("orderId") Long orderId) {
        List<TradeOrderItemAcceptanceDO> list = orderItemAcceptanceService.getByOrder(orderId);
        return success(TradeOrderAcceptanceConvert.INSTANCE.convertList02(list));
    }

    /**
     * 上传验收材料
     * @param id
     * @return
     */
    @PostMapping("/upload-files")
    @Operation(summary = "上传验收材料")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:order-item-assets:update')")
    public CommonResult<Boolean> uploadFile(@RequestParam("id") Long id) {
        return success(orderItemAcceptanceService.retryUploadAcceptanceFiles(id));
    }

}
