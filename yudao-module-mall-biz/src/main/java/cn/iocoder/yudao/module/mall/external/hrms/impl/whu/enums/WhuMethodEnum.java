package cn.iocoder.yudao.module.mall.external.hrms.impl.whu.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Getter
public enum WhuMethodEnum {

    // 武大相关接口
    GET_ACCESS_TOKEN("/open_api/authentication/get_access_token","获取接口访问Token"),
    GET_STAFF_LIST("/open_api/customization/tsjzsjzgjbxx/full","获取教职工基本信息"),
    GET_DEPT_LIST("/open_api/customization/tgxxxdwjbxx/full","获取部门信息"),
    GET_GH_INFO_LIST("/open_api/customization/view_echo_yankee/full","获取教职工会籍信息");

    private String methodName;

    private String desc;

    WhuMethodEnum(String methodName, String desc) {
        this.methodName = methodName;
        this.desc = desc;
    }
}
