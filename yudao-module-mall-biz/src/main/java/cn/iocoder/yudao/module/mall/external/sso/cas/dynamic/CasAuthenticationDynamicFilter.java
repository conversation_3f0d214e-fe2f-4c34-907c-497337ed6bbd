package cn.iocoder.yudao.module.mall.external.sso.cas.dynamic;

import lombok.extern.slf4j.Slf4j;
import org.jasig.cas.client.authentication.AuthenticationFilter;
import org.jasig.cas.client.authentication.Saml11AuthenticationFilter;

import javax.servlet.Filter;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import java.util.Map;

/**
 * @author: HF
 * @description: TODO
 * @date: 2024/4/13 20:08
 */
@Slf4j
public class CasAuthenticationDynamicFilter extends AbstractDynamicCasFilter {

    public CasAuthenticationDynamicFilter(CasConfigService casConfigService) {
        super(casConfigService);
    }

    @Override
    protected Filter genTargetFilter(FilterConfig config, Map<String, String> casConfigMap) throws ServletException {
        DummyFilterConfig newConfig = new DummyFilterConfig("", config.getServletContext(), casConfigMap);
        String casType = casConfigMap.get("validation-type");
        if (casType != null) {
            casType = casType.toUpperCase();
        }
        Filter targetCasAuthnFilter = (!"CAS".equals(casType)) && (!"CAS3".equals(casType)) ? new Saml11AuthenticationFilter() : new AuthenticationFilter();
        targetCasAuthnFilter.init(newConfig);
        return targetCasAuthnFilter;
    }

    @Override
    protected void doBefore(ServletRequest servletRequest,  Long tenantId, Map<String, Object> bodyJson) {
//        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
//        HttpSession session = httpServletRequest.getSession(true);
//        Assertion assertion = (Assertion) session.getAttribute(CONST_CAS_ASSERTION);
//        CasContextHolder.clear();
//        //如果session中有asserton， 放入redis
//        if (assertion != null) {
//            String uid = CasUtils.generateUuid();
//            CasContextHolder.setUserId(uid);
//            CasContextHolder.setCasUserInfo(assertion.getPrincipal().getAttributes());
//            String assertionRedisKey = casConfigService.getAssertionRedisKey(tenantId, uid);
//            casConfigService.putAssertionToRedis(assertionRedisKey, assertion);
//        }
    }

}
