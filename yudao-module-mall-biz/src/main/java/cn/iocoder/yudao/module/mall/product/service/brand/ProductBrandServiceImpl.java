package cn.iocoder.yudao.module.mall.product.service.brand;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.brand.AppOpenProductBrandPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.brand.vo.ProductBrandCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.brand.vo.ProductBrandListReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.brand.vo.ProductBrandPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.brand.vo.ProductBrandUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.convert.brand.ProductBrandConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.brand.ProductBrandDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.brand.ProductBrandMapper;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.basis.dal.redis.RedisKeyConstants.MALL_PRODUCT_BRAND;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.*;
import static org.springframework.transaction.annotation.Propagation.NOT_SUPPORTED;

/**
 * 品牌 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProductBrandServiceImpl implements ProductBrandService {

    @Resource
    private ProductBrandMapper brandMapper;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public Long createBrand(ProductBrandCreateReqVO createReqVO) {
        // 校验
        validateBrandNameUnique(null, createReqVO.getName());

        // 插入
        ProductBrandDO brand = ProductBrandConvert.INSTANCE.convert(createReqVO);
        brandMapper.insert(brand);
        // 返回
        return brand.getId();
    }

    @Override
    public void updateBrand(ProductBrandUpdateReqVO updateReqVO) {
        // 校验存在
        validateBrandExists(updateReqVO.getId());
        validateBrandNameUnique(updateReqVO.getId(), updateReqVO.getName());
        // 更新
        ProductBrandDO updateObj = ProductBrandConvert.INSTANCE.convert(updateReqVO);
        brandMapper.updateById(updateObj);
    }

    @Override
    public void deleteBrand(Long id) {
        // 校验存在
        validateBrandExists(id);
        // 删除
        brandMapper.deleteById(id);
    }

    private void validateBrandExists(Long id) {
        if (brandMapper.selectById(id) == null) {
            throw exception(BRAND_NOT_EXISTS);
        }
    }

    @VisibleForTesting
    public void validateBrandNameUnique(Long id, String name) {
        ProductBrandDO brand = brandMapper.selectByName(name);
        if (brand == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的字典类型
        if (id == null) {
            throw exception(BRAND_NAME_EXISTS);
        }
        if (!brand.getId().equals(id)) {
            throw exception(BRAND_NAME_EXISTS);
        }
    }

    @Override
    public ProductBrandDO getBrand(Long id) {
        return brandMapper.selectById(id);
    }

    /**
     * 在高并发情况时，如果支持事务，原子块执行完时数据可能并未持久化
     * @param name 品牌名称
     * @return
     */
    @Override
    @Transactional(propagation = NOT_SUPPORTED)
    public Long createFromName(String name) {
        if(StringUtils.isBlank(name)) {
            return null;
        }
        String key = MALL_PRODUCT_BRAND + ":lock:" + TenantContextHolder.getTenantId();
        RLock lock = redissonClient.getLock(key);
        try {
            if(lock.tryLock(10, 20, TimeUnit.SECONDS)) {
                ProductBrandDO brand = brandMapper.selectByName(name);
                if (brand == null) {
                    brand = new ProductBrandDO();
                    brand.setName(name);
                    brand.setStatus(CommonStatusEnum.ENABLE.getStatus());
                    brandMapper.insert(brand);
                }

                return brand.getId();
            }
            log.error("创建品牌获取同步锁失败:{}", name);
        } catch(Exception e) {
            log.error("品牌名称处理异常:", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return null;
    }

    @Override
    public ProductBrandDO getBrandByName(String name) {
        return brandMapper.selectByName(name);
    }

    @Override
    public List<ProductBrandDO> getBrandList(Collection<Long> ids) {
        return brandMapper.selectBatchIds(ids);
    }

    @Override
    public List<ProductBrandDO> getBrandList(ProductBrandListReqVO listReqVO) {
        return brandMapper.selectList(listReqVO);
    }

    @Override
    public void validateProductBrand(Long id) {
        ProductBrandDO brand = brandMapper.selectById(id);
        if (brand == null) {
            throw exception(BRAND_NOT_EXISTS);
        }
        if (brand.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(BRAND_DISABLED);
        }
    }

    @Override
    public PageResult<ProductBrandDO> getBrandPage(ProductBrandPageReqVO pageReqVO) {
        return brandMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<ProductBrandDO> getBrandPage(AppOpenProductBrandPageReqVO pageReqVO) {
        return brandMapper.selectPage(pageReqVO);
    }

}
