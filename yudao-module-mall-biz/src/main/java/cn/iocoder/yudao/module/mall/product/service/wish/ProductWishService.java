package cn.iocoder.yudao.module.mall.product.service.wish;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo.ProductWishExportReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo.ProductWishPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.wish.vo.AppProductWishCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.wish.vo.AppProductWishUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.wish.ProductWishDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 心愿单 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductWishService {

    /**
     * 创建心愿单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWish(@Valid AppProductWishCreateReqVO createReqVO);

    /**
     * 更新心愿单
     *
     * @param updateReqVO 更新信息
     */
    void updateWish(@Valid AppProductWishUpdateReqVO updateReqVO);

    /**
     * 删除心愿单
     *
     * @param id 编号
     */
    void deleteWish(Long id);

    /**
     * 修改心愿单状态
     * @param bean
     */
    void updateWithStatus(ProductWishDO bean);

    /**
     * 获得心愿单
     *
     * @param id 编号
     * @return 心愿单
     */
    ProductWishDO getWish(Long id);

    /**
     * 获得心愿单列表
     *
     * @param ids 编号
     * @return 心愿单列表
     */
    List<ProductWishDO> getWishList(Collection<Long> ids);

    /**
     * 获得心愿单分页
     *
     * @param pageReqVO 分页查询
     * @return 心愿单分页
     */
    PageResult<ProductWishDO> getWishPage(ProductWishPageReqVO pageReqVO);

    /**
     * 获得心愿单列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 心愿单列表
     */
    List<ProductWishDO> getWishList(ProductWishExportReqVO exportReqVO);

}
