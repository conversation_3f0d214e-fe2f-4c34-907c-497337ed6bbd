package cn.iocoder.yudao.module.mall.product.dal.dataobject.searchhistory;


import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 会员搜索历史 DO
 *
 * <AUTHOR>
 */
@TableName(value = "product_search_history", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSearchHistoryDO extends TenantBaseDO {

    /**
     * 用户ID
     */
    @TableId
    private Long id;

    /**
     * 用户昵称（来自member_user表的关联字段）
     */
    @TableField(exist = false) // 非表字段需要标记
    private String nickname;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 搜索次数
     */
    private Integer count;

}
