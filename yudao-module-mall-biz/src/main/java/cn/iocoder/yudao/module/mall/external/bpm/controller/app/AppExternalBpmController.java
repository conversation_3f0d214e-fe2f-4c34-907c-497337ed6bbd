package cn.iocoder.yudao.module.mall.external.bpm.controller.app;

import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.tenant.TenantIdUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.external.bpm.BpmClient;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmCallbackResultDTO;
import cn.iocoder.yudao.module.mall.mq.producer.order.TradeOrderProducer;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.BPM_NOTIFY_FAIL;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.EXTERNAL_TENANT_ID_PARSE_FAIL;

/**
 * BPM外部开放接口
 * <AUTHOR>
 */
@Tag(name = "外部API - BPM")
@RestController
@RequestMapping("/mall/external/bpm")
@Validated
@Slf4j
public class AppExternalBpmController {

    @Resource
    private BpmClient bpmClient;
    @Resource
    private TradeOrderProducer orderProducer;

    /**
     * 构建上下文
     * @param tcode
     */
    private void buildContext(String tcode) {
        try {
            Long tenantId = TenantIdUtils.decryptTenantId(tcode);
            TenantContextHolder.setTenantId(tenantId);
            TenantContextHolder.setIgnore(false);
        } catch (Exception e) {
            log.error("构建上下文时解析租户ID错误:", e);
            throw ServiceExceptionUtil.exception(EXTERNAL_TENANT_ID_PARSE_FAIL);
        }
    }

    /**
     * 审批流回调
     * @param body
     * @return
     */
    @PostMapping("/notify/{tcode}")
    public Object notify(@PathVariable String tcode,
                         @RequestBody(required = false) String body,
                         @RequestParam(required = false) Map<String, String> params) {
        log.info("bpm notify tcode: {}, body: {} params：{}", tcode, body, params);
        try {
            buildContext(tcode);
            BpmCallbackResultDTO resultDTO = bpmClient.handleBpmCallback(body, params);
            orderProducer.sendBpmStatus(resultDTO.getBpmNo());
            return resultDTO.getResult();
        } catch (Exception e) {
            log.error("bpm notify fail ",e);
            return CommonResult.error(BPM_NOTIFY_FAIL);
        }
    }

}
