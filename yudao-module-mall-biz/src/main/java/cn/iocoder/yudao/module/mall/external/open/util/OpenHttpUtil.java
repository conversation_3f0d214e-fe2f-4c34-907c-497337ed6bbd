package cn.iocoder.yudao.module.mall.external.open.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.common.OpenAppDO;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.external.open.aop.OpenAuthAspectV2;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Component
@Slf4j
public class OpenHttpUtil {

    private static final int REQ_TIMEOUT = 10 * 1000;
    @Resource
    private ObjectMapper objectMapper;

    /**
     * post请求
     * @param url
     * @param param
     * @param openAppDO
     * @param clazz
     * @return
     * @param <T>
     */
    public <T> T post(String url, Object param, OpenAppDO openAppDO, Class<T> clazz) {
        JavaType javaType = objectMapper.getTypeFactory().constructType(clazz);
        return post(url, param, openAppDO, javaType);
    }

    /**
     * post请求
     * @param url
     * @param param
     * @param openAppDO
     * @param javaType
     * @return
     * @param <T>
     */
    public <T> T post(String url, Object param, OpenAppDO openAppDO, JavaType javaType) {
        String paramStr = null;
        try {
            HttpRequest req = HttpUtil.createPost(url);
            paramStr = JSON.toJSONString(param);
            // 填充头部信息
            fillHeader(req, paramStr, openAppDO);
            HttpResponse response = req.body(paramStr).timeout(REQ_TIMEOUT).execute();
            log.info("open call {} params: {} resp：{}", url, paramStr, response);
            if (response.isOk()) {
                String body = response.body();
                JSONObject respObj = JSONObject.parseObject(body);
                if (respObj.containsKey("code")) {
                    Integer respCode = respObj.getIntValue("code");
                    String errMsg = respObj.getString("msg");
                    if (ObjectUtil.equal(respCode, 0)) {
                        JSONObject dataObj = respObj.getJSONObject("data");
                        return objectMapper.readValue(dataObj.toJSONString(), javaType);
                    } else {
                        throw exception(ErrorCodeConstants.OPEN_API_CALL_FAIL, errMsg);
                    }
                } else {
                    throw exception(ErrorCodeConstants.OPEN_API_CALL_FAIL, "返回格式不正确");
                }
            } else {
                log.error("open call {} fail, resp：{}", url, response);
                throw exception(ErrorCodeConstants.OPEN_API_CALL_FAIL, "结果异常");
            }
        } catch (Exception e) {
            log.error("open call error {} params: {}", url, paramStr, e);
            throw exception(ErrorCodeConstants.OPEN_API_CALL_FAIL, "系统异常");
        }
    }

    /**
     * 填充请求头部信息
     * @param req
     * @param body
     * @param openAppDO
     */
    public void fillHeader(HttpRequest req, String body, OpenAppDO openAppDO) {
        String nonce = RandomUtil.randomString(10);
        long timestamp = System.currentTimeMillis();
        String sign = generateSign(body, nonce, timestamp, openAppDO);

        req.header(OpenAuthAspectV2.HEADER_TOKEN,  openAppDO.getAppKey());
        req.header(OpenAuthAspectV2.HEADER_NONCE,  nonce);
        req.header(OpenAuthAspectV2.HEADER_TIMESTAMP,  timestamp + "");
        req.header(OpenAuthAspectV2.HEADER_SIGN,  sign);
    }

    /**
     * 生成签名
     * @param body
     * @param nonce
     * @param timestamp
     * @param openApp
     * @return
     */
    public String generateSign(String body, String nonce, long timestamp, OpenAppDO openApp) {
        // 生成签名
        String signSource = openApp.getAppKey()
                + nonce
                + timestamp
                + body
                + openApp.getAppSecret();

        return DigestUtil.sha256Hex(signSource);
    }

}
