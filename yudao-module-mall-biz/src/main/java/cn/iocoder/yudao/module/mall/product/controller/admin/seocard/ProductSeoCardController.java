package cn.iocoder.yudao.module.mall.product.controller.admin.seocard;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.seocard.ProductSeoCardConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.seocard.ProductSeoCardDO;
import cn.iocoder.yudao.module.mall.product.service.seocard.ProductSeoCardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 运营区域")
@RestController
@RequestMapping("/product/seo-card")
@Validated
public class ProductSeoCardController {

    @Resource
    private ProductSeoCardService seoCardService;

    @PostMapping("/create")
    @Operation(summary = "创建运营区域")
    @PreAuthorize("@ss.hasPermission('product:seo-card:create')")
    public CommonResult<Long> createSeoCard(@Valid @RequestBody ProductSeoCardCreateReqVO createReqVO) {
        return success(seoCardService.createSeoCard(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新运营区域")
    @PreAuthorize("@ss.hasPermission('product:seo-card:update')")
    public CommonResult<Boolean> updateSeoCard(@Valid @RequestBody ProductSeoCardUpdateReqVO updateReqVO) {
        seoCardService.updateSeoCard(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新运营区域状态")
    @PreAuthorize("@ss.hasPermission('product:seo-card:update')")
    public CommonResult<Boolean> updateSeoCardStatus(@Valid @RequestBody ProductSeoCardStatusUpdateReqVO updateReqVO) {
        seoCardService.updateSeoCardStatus(updateReqVO.getId(), updateReqVO.getStatus());
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除运营区域")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:seo-card:delete')")
    public CommonResult<Boolean> deleteSeoCard(@RequestParam("id") Long id) {
        seoCardService.deleteSeoCard(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得运营区域")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:seo-card:query')")
    public CommonResult<ProductSeoCardRespVO> getSeoCard(@RequestParam("id") Long id) {
        ProductSeoCardDO seoCard = seoCardService.getSeoCard(id);
        return success(ProductSeoCardConvert.INSTANCE.convert(seoCard));
    }

    @GetMapping("/list")
    @Operation(summary = "获得运营区域列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('product:seo-card:query')")
    public CommonResult<List<ProductSeoCardRespVO>> getSeoCardList(@RequestParam("ids") Collection<Long> ids) {
        List<ProductSeoCardDO> list = seoCardService.getSeoCardList(ids);
        return success(ProductSeoCardConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得运营区域分页")
    @PreAuthorize("@ss.hasAnyPermissions('product:seo-card:query', 'mall:home-config')")
    public CommonResult<PageResult<ProductSeoCardRespVO>> getSeoCardPage(@Valid ProductSeoCardPageReqVO pageVO) {
        PageResult<ProductSeoCardDO> pageResult = seoCardService.getSeoCardPage(pageVO);
        return success(ProductSeoCardConvert.INSTANCE.convertPage(pageResult));
    }


}
