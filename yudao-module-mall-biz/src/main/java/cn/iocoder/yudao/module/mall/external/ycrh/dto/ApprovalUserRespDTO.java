package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/24
 */
@Data
public class ApprovalUserRespDTO {

    /**
     * 	审批级别
     */
    private String rank;

    /**
     * 	角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    @JsonProperty("roleNm")
    private String roleName;

    /**
     * 员工编号
     */
    @JsonProperty("ygbh")
    private String ygNo;

    /**
     * 员工名称
     */
    @JsonProperty("ygmc")
    private String ygName;

    /**
     * 能否指定审批人  (1:能, 0或其他:不能)
     */
    @JsonProperty("zdspr")
    private String isDesignatedApprover;

    /**
     * 能否删除审批人 1:能, 0或其他:不能
     */
    @JsonProperty("scspr")
    private String isDeleteApprover;

    /**
     * 是否发送短信 1:发, 0或其他:不发
     */
    @JsonProperty("tsdx")
    private String isSendMessage;

    /**
     * 是否签章 1:证书签章, 2:无证书签章, 0:不签
     */
    @JsonProperty("sfqz")
    private String isSignature;

    /**
     * 是否签名 1:签名, 0或其他:不签
     */
    @JsonProperty("sfqm")
    private String isSign;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 审批方式 2:会签, 1或其他:或签
     */
    @JsonProperty("spfs")
    private String approvalType;

    /**
     * 能否编辑审批流程 1:能, 0或其他:不能
     */
    @JsonProperty("bjlc")
    private String isUpdateApproval;
}
