package cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Schema(description = "商城前台 - 我的足迹 Response VO")
@EqualsAndHashCode(callSuper = true)
public class AppMyFootprintRespVO extends AppMyFootprintBaseVO {

    @Schema(description = "id", required = true)
    private Long id;


    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;


}
