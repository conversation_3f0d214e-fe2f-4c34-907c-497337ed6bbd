package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/28
 */
@Data
public class BillCancelReqDTO {

    /**
     * 账单编号
     */
    private String billNo;
    /**
     * 撤销原因(1:用户取消 2:系统取消3:审批拒绝 4:退货5:其他)
     */
    private String cancelReason;
    /**
     * 取消时间(yyyy-MM-dd HH:mm:ss)
     */
    private String cancelTime;
    /**
     * 订单编号列表
     */
    private List<String> orderNoList;
}
