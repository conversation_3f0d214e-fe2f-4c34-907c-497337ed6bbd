package cn.iocoder.yudao.module.mall.product.controller.admin.skusummary;

import cn.iocoder.yudao.module.mall.external.bigscreen.vo.BigScreenProductCategoryProportionRespVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.mall.product.controller.admin.skusummary.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusummary.SkuSummaryDO;
import cn.iocoder.yudao.module.mall.product.convert.skusummary.SkuSummaryConvert;
import cn.iocoder.yudao.module.mall.product.service.skusummary.SkuSummaryService;

@Tag(name = "管理后台 - 供应商上架商品数统计")
@RestController
@RequestMapping("/product/sku-summary")
@Validated
public class SkuSummaryController {

    @Resource
    private SkuSummaryService skuSummaryService;

    @PostMapping("/create")
    @Operation(summary = "创建供应商上架商品数统计")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:create')")
    public CommonResult<Long> createSkuSummary(@Valid @RequestBody SkuSummaryCreateReqVO createReqVO) {
        return success(skuSummaryService.createSkuSummary(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新供应商上架商品数统计")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:update')")
    public CommonResult<Boolean> updateSkuSummary(@Valid @RequestBody SkuSummaryUpdateReqVO updateReqVO) {
        skuSummaryService.updateSkuSummary(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除供应商上架商品数统计")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:delete')")
    public CommonResult<Boolean> deleteSkuSummary(@RequestParam("id") Long id) {
        skuSummaryService.deleteSkuSummary(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得供应商上架商品数统计")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:query')")
    public CommonResult<SkuSummaryRespVO> getSkuSummary(@RequestParam("id") Long id) {
        SkuSummaryDO skuSummary = skuSummaryService.getSkuSummary(id);
        return success(SkuSummaryConvert.INSTANCE.convert(skuSummary));
    }

    @GetMapping("/list")
    @Operation(summary = "获得供应商上架商品数统计列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:query')")
    public CommonResult<List<SkuSummaryRespVO>> getSkuSummaryList(@RequestParam("ids") Collection<Long> ids) {
        List<SkuSummaryDO> list = skuSummaryService.getSkuSummaryList(ids);
        return success(SkuSummaryConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得供应商上架商品数统计分页")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:query')")
    public CommonResult<PageResult<SkuSummaryRespVO>> getSkuSummaryPage(@Valid SkuSummaryPageReqVO pageVO) {
        PageResult<SkuSummaryDO> pageResult = skuSummaryService.getSkuSummaryPage(pageVO);
        return success(SkuSummaryConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出供应商上架商品数统计 Excel")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:export')")
    @OperateLog(type = EXPORT)
    public void exportSkuSummaryExcel(@Valid SkuSummaryExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SkuSummaryDO> list = skuSummaryService.getSkuSummaryList(exportReqVO);
        // 导出 Excel
        List<SkuSummaryExcelVO> datas = SkuSummaryConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "供应商上架商品数统计.xls", "数据", SkuSummaryExcelVO.class, datas);
    }

    @GetMapping("/saveSupplierSkuSummary")
    @Operation(summary = "统计截止到今天0点的供应商sku上下架总数量")
	@PreAuthorize("@ss.hasPermission('product:sku-statistics:update')")
    public CommonResult<List<SkuSummaryRespVO>> saveSupplierSkuSummary() {
        List<SkuSummaryDO> list = skuSummaryService.saveSupplierSkuSummary();
        return success(SkuSummaryConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/supplierSkuSummary")
    @Operation(summary = "查询日期范围内供应商每日上架状态sku总数量")
	@PreAuthorize("@ss.hasPermission('product:sku-statistics:query')")
    public CommonResult<List<SkuSummaryDaysRespVO>> supplierSkuSummary(@Valid @RequestBody SkuSummaryDaysReqVO skuSummaryDaysReqVO) {
        return success(skuSummaryService.supplierSkuSummary(skuSummaryDaysReqVO));
    }
}
