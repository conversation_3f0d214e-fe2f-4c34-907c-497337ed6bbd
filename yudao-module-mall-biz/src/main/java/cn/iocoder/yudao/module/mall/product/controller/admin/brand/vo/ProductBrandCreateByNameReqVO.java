package cn.iocoder.yudao.module.mall.product.controller.admin.brand.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商品品牌创建 Request VO")
@Data
@ToString(callSuper = true)
public class ProductBrandCreateByNameReqVO {

    @Schema(description = "品牌名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    @NotNull(message = "品牌名称不能为空")
    private String name;

}
