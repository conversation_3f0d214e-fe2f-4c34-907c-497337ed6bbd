package cn.iocoder.yudao.module.mall.external.bpm.service;

import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmAuditResultDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmCallbackResultDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmReqDTO;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseSubmitReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseValidateReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import com.alibaba.fastjson.JSONObject;

import java.util.Map;

/**
 * 审批流封装
 * <AUTHOR>
 * @date 2024/08/20
 */
public interface BpmService {

    /**
     * 查询客户端类型
     * @return
     */
    String getClientType();

    /**
     * 推送审批流信息
     * @param bpmReqDTO
     */
    String pushBpmInfo(BpmReqDTO bpmReqDTO);

    /**
     * 推送前置校验，校验不通过则抛业务异常
     * @param reqVO
     */
    void validateBeforePush(PurchaseValidateReqVO reqVO);

    /**
     * 审批流数据回调
     * @param body
     * @param params
     */
    BpmCallbackResultDTO handleBpmCallback(String body, Map<String, String> params);

    /**
     * 拉取审批流的审批状态
     * @param bpmNo
     */
    BpmAuditResultDTO pullBpmStatus(String bpmNo);

    /**
     * 取消审批流
     * @param bpmNo
     * @param userId
     */
    void cancelBpmInfo(String bpmNo, String reason, Long userId);

}
