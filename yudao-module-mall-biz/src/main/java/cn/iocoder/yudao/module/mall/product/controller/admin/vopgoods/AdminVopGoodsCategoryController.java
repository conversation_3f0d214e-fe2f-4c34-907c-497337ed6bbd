//package cn.iocoder.yudao.module.mall.product.controller.admin.vopgoods;
//
//import cn.iocoder.yudao.framework.common.pojo.CommonResult;
//import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsCategoryItem;
//import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsCategoryService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.security.PermitAll;
//import java.util.List;
//
///**
// * VOP商品分类相关接口
// * @Description
// * <AUTHOR>
// * @Date 2023/7/3 20:35
// */
//
//@RestController
//@RequestMapping("/product/vopGoodsCategory")
//@Slf4j
//@Tag(name = "VOP商品分类相关接口")
//public class AdminVopGoodsCategoryController {
//
//    @Autowired
//    VopGoodsCategoryService vopGoodsCategoryService;
//
//    /**
//     * 查询顶级大类
//     * @return
//     */
//    @GetMapping("getBigCategoryList")
//    @Operation(summary = "查询顶级大类")
//    @PermitAll
//    public CommonResult<List<VopGoodsCategoryItem>> getBigCategoryList() {
//        return CommonResult.success(vopGoodsCategoryService.getBigCategoryList());
//    }
//
//    /**
//     * 查询父类下所有级别的分类
//     * @param parentCategoryId
//     * @return
//     */
//    @GetMapping("getAllChildCategoryList")
//    @Operation(summary = "查询父类下所有级别的分类")
//    @PermitAll
//    public CommonResult<List<VopGoodsCategoryItem>> getAllChildCategoryList(@RequestParam(value = "parentCategoryId", required = true) Long parentCategoryId) {
//        return CommonResult.success(vopGoodsCategoryService.getAllChildCategoryList(parentCategoryId));
//    }
//
//}
