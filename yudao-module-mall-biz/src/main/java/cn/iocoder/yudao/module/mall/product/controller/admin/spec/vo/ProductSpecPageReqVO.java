package cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 商品规格分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSpecPageReqVO extends PageParam {

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "规格名")
    private String name;

    @Schema(description = "规格类型：1-spu通用参数类型 2-sku特有参数类型")
    private Integer specType;

}
