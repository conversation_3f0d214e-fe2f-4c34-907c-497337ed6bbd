package cn.iocoder.yudao.module.mall.trade.controller.admin.statistics;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderAfterSaleSummaryRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderSupplierSummaryRespVO;
import cn.iocoder.yudao.module.mall.trade.service.orderstatistics.OrderStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 订单供应商统计")
@RestController
@RequestMapping("/trade/statistics/supplier")
@Validated
public class TradeStatisticsSupplierController {
    @Resource
    private OrderStatisticsService orderStatisticsService;

    @GetMapping("/getOrderSummaryBySupplier")
    @Operation(summary = "供应商销售额排行")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:supplier')")
    public CommonResult<List<OrderSupplierSummaryRespVO>> getOrderSummaryBySupplier() {
        List<OrderSupplierSummaryRespVO> orderSupplierSummaryRespVOS = orderStatisticsService.getOrderSummaryBySupplier();
        return success(orderSupplierSummaryRespVOS);
    }

    @GetMapping("/getAfterSaleOrderSummary")
    @Operation(summary = "供应商售后订单量排行")
    @PreAuthorize("@ss.hasPermission('product:sku-statistics:supplier')")
    public CommonResult<List<OrderAfterSaleSummaryRespVO>> getAfterSaleOrderSummary() {
        List<OrderAfterSaleSummaryRespVO> orderAfterSaleSummaryRespVOS = orderStatisticsService.getAfterSaleOrderSummary();
        return success(orderAfterSaleSummaryRespVOS);
    }
}
