package cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo;

import cn.iocoder.yudao.module.mall.product.enums.spu.ProductOperateTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 商品操作日志记录 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ProductOperateLogBaseVO {

    /**
     * spuId
     */
    @Schema(description = "spuId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "spuId不能为空")
    private Long spuId;

    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "供应商id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "供应商id不能为空")
    private Long supplierId;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "用户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户名称")
    private String userName;

    @Schema(description = "操作分类", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "操作分类不能为空")
    private Integer operateType;

    @Schema(description = "操作内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "操作内容不能为空")
    private String content;

    public String getOperateTypeDesc() {
        if (this.operateType == null) {
            return null;
        }
        ProductOperateTypeEnum orderOperateTypeEnum = ProductOperateTypeEnum.fromCode(this.operateType);
        if (orderOperateTypeEnum == null) {
            return null;
        }
        return orderOperateTypeEnum.getDesc();
    }
}
