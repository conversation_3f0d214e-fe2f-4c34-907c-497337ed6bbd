package cn.iocoder.yudao.module.mall.trade.controller.admin.orderoperatelog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;

@Schema(description = "管理后台 - 订单操作日志记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderOperateLogRespVO extends OrderOperateLogBaseVO {

    @Schema(description = "日志主键", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
