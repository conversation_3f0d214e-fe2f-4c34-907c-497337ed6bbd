package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/5
 */
@Data
public class ProjectInfoRespDTO {

    /**
     * 部门编号
     */
    @JsonProperty("bmbh")
    private String departmentNo;

    /**
     * 部门名称
     */
    @JsonProperty("bmmc")
    private String departmentName;

    /**
     * 项目编号
     */
    @JsonProperty("xmbh")
    private String projectNo;

    /**
     * 项目名称
     */
    @JsonProperty("xmmc")
    private String projectName;

    /**
     * 项目类型
     */
    @JsonProperty("xmlx")
    private String projectType;

    /**
     * 项目类型名称
     */
    @JsonProperty("xmlxmc")
    private String projectTypeName;

    /**
     * 是否国库
     * 1:是 0:否
     */
    @JsonProperty("isgk")
    private String isTreasury ;

    /**
     * 国库信息码
     */
    @JsonProperty("gkxxm")
    private String treasuryNo;

    /**
     * 项目大类 01-预算 02-科研 03-专项 04-基建 05-收入 06-基金 07-代管 99-其他
     */
    @JsonProperty("cclass")
    private String projectClass;

    /**
     * 主负责人编号
     */
    @JsonProperty("zfzrbh")
    private String chargeNo;

    /**
     * 主负责人姓名
     */
    @JsonProperty("zfzrxm")
    private String chargeName;

    /**
     * 允许支出经济分类 逗号分割
     */
    @JsonProperty("jjflzckm")
    private String economyClass;

    /**
     * 不允许支出经济分类 逗号分割
     */
    @JsonProperty("nojjflzckm")
    private String noEconomyClass;

    /**
     * 财政支出
     */
    @JsonProperty("isczzc")
    private String isFinanceExpense;

    /**
     * 是否启用 1:是 0:否
     */
    @JsonProperty("qyf")
    private String isEnable;

    /**
     * 绑定的商品分类
     */
    @JsonProperty("skuList")
    private List<ProjectSkuCategoryDTO> skuCategoryList;

    /**
     * 绑定的商品SKU
     */
    @JsonIgnore
    private List<String> skuIdList;

    /**
     * 总余额阈值，默认为0
     */
    @JsonIgnore
    private Integer totalBalanceThreshold;

    /**
     * 授权余额阈值，默认为0
     */
    @JsonIgnore
    private Integer authorizedBalanceThreshold;


}
