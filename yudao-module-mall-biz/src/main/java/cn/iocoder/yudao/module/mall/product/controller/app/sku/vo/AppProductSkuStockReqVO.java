package cn.iocoder.yudao.module.mall.product.controller.app.sku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AppProductSkuStockReqVO {

    @Schema(description = "商品skuId")
    @NotNull(message = "商品skuId不能为空")
    private Long skuId;

    @Schema(description = "兄弟商品skuId")
    private List<Long> brotherSkuIds;

    /**
     * 省ID
     */
    @Schema(description = "省ID")
    private Long provinceId;

    /**
     * 城市ID
     */
    @Schema(description = "城市ID")
    private Long cityId;

    /**
     * 区ID
     */
    @Schema(description = "区ID")
    private Long countyId;

    /**
     * 街道/乡镇ID
     */
    @Schema(description = "街道/乡镇ID")
    private Long townId;
}
