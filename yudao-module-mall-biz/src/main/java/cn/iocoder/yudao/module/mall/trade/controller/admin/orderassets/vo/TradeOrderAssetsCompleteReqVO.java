package cn.iocoder.yudao.module.mall.trade.controller.admin.orderassets.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class TradeOrderAssetsCompleteReqVO {

    @Schema(description = "订单固资建档明细ID")
    @NotNull(message = "订单固资建档明细ID不能为空")
    private Long orderItemId;

    @Schema(description = "6大类资产类别代码")
    @NotBlank(message = "6大类资产类别代码不能为空")
    private String category6;

    @Schema(description = "16大类资产类别代码")
    @NotBlank(message = "16大类资产类别代码不能为空")
    private String category16;

    @Schema(description = "资产编号")
    @NotEmpty(message = "资产编号不能为空")
    private List<String> assetNo;

    @Schema(description = "验收单号")
    @NotBlank(message = "验收单号不能为空")
    private String acceptNo;

    @Schema(description = "审批人")
    private String approveBy;

    @Schema(description = "审批日期")
    private Date approveTime;

    @Schema(description = "验收单文件URL")
    private String acceptFile;

    @Schema(description = "业务分类")
    private String businessClass;

}
