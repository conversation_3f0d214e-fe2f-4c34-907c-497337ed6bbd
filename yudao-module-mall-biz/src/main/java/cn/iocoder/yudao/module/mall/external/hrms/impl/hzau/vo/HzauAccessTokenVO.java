package cn.iocoder.yudao.module.mall.external.hrms.impl.hzau.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@Data
public class HzauAccessTokenVO implements Serializable {

    private String accessToken;
    private Long expiresIn;
    private LocalDateTime createTime;

    public boolean checkValidTime() {
        LocalDateTime now = LocalDateTime.now();
        if(createTime == null) {
            return false;
        }

        long diffSeconds = ChronoUnit.SECONDS.between(createTime, now);
        if(expiresIn == null || expiresIn <= 0) {
            return false;
        }

        return diffSeconds <= (expiresIn - 10);
    }

    public static void main(String[] args) {
        LocalDateTime n1 = LocalDateTime.now();
        LocalDateTime n2 = n1.plusDays(2);

        System.out.println("======" + ChronoUnit.SECONDS.between(n1, n2));
    }

}
