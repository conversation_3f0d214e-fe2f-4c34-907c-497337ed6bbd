package cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;

@Schema(description = "管理后台 - 商品操作日志记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductOperateLogRespVO extends ProductOperateLogBaseVO {

    @Schema(description = "日志主键", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
