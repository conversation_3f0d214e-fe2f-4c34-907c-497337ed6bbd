package cn.iocoder.yudao.module.mall.product.util;

import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryExcelVO;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

/**
 * 商品分类Excel合并单元格处理器
 * 
 * <AUTHOR>
 */
public class CategoryExcelMergeHandler implements SheetWriteHandler {

    private final List<ProductCategoryExcelVO> data;

    public CategoryExcelMergeHandler(List<ProductCategoryExcelVO> data) {
        this.data = data;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        
        if (data == null || data.isEmpty()) {
            return;
        }

        // 合并一级分类
        mergeCategory1(sheet);
        
        // 合并二级分类
        mergeCategory2(sheet);
    }

    /**
     * 合并一级分类相同的单元格
     */
    private void mergeCategory1(Sheet sheet) {
        int startRow = 1; // 从第二行开始（第一行是表头）
        Long currentCategory1Id = null;
        String currentCategory1Name = null;
        int mergeStartRow = startRow;

        for (int i = 0; i < data.size(); i++) {
            ProductCategoryExcelVO item = data.get(i);
            int currentRow = startRow + i;

            if (currentCategory1Id == null || !currentCategory1Id.equals(item.getCategory1Id())) {
                // 合并前一组
                if (currentCategory1Id != null && currentRow - 1 > mergeStartRow) {
                    // 合并一级分类ID
                    sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, currentRow - 1, 0, 0));
                    // 合并一级分类名称
                    sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, currentRow - 1, 1, 1));
                }
                
                // 开始新的一组
                currentCategory1Id = item.getCategory1Id();
                currentCategory1Name = item.getCategory1Name();
                mergeStartRow = currentRow;
            }
        }

        // 处理最后一组
        if (currentCategory1Id != null && data.size() > mergeStartRow - startRow + 1) {
            int lastRow = startRow + data.size() - 1;
            if (lastRow > mergeStartRow) {
                // 合并一级分类ID
                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, lastRow, 0, 0));
                // 合并一级分类名称
                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, lastRow, 1, 1));
            }
        }
    }

    /**
     * 合并二级分类相同的单元格
     */
    private void mergeCategory2(Sheet sheet) {
        int startRow = 1; // 从第二行开始（第一行是表头）
        Long currentCategory1Id = null;
        Long currentCategory2Id = null;
        String currentCategory2Name = null;
        int mergeStartRow = startRow;

        for (int i = 0; i < data.size(); i++) {
            ProductCategoryExcelVO item = data.get(i);
            int currentRow = startRow + i;

            // 检查是否是同一个一级分类下的同一个二级分类
            boolean isSameCategory2 = currentCategory1Id != null && 
                                    currentCategory1Id.equals(item.getCategory1Id()) &&
                                    currentCategory2Id != null && 
                                    currentCategory2Id.equals(item.getCategory2Id());

            if (!isSameCategory2) {
                // 合并前一组二级分类
                if (currentCategory2Id != null && currentRow - 1 > mergeStartRow) {
                    // 合并二级分类ID
                    sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, currentRow - 1, 2, 2));
                    // 合并二级分类名称
                    sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, currentRow - 1, 3, 3));
                }
                
                // 开始新的一组
                currentCategory1Id = item.getCategory1Id();
                currentCategory2Id = item.getCategory2Id();
                currentCategory2Name = item.getCategory2Name();
                mergeStartRow = currentRow;
            }
        }

        // 处理最后一组
        if (currentCategory2Id != null && data.size() > mergeStartRow - startRow + 1) {
            int lastRow = startRow + data.size() - 1;
            if (lastRow > mergeStartRow) {
                // 合并二级分类ID
                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, lastRow, 2, 2));
                // 合并二级分类名称
                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, lastRow, 3, 3));
            }
        }
    }
}
