package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import cn.iocoder.yudao.module.mall.trade.enums.order.PaymentMethodEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeAuditStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderAfterSaleStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderDeliveryStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* 交易订单 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class TradeOrderBaseVO {

    // ========== 订单基本信息 ==========

    /**
     * 订单编号
     */
    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    /**
     * 订单流水号
     */
    @Schema(description = "订单流水号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1146347329394184195")
    private String no;

    /**
     * 订单入库时间
     */
    @Schema(description = "订单入库时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    /**
     * 用户编号
     */
    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    private Long userId;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    private String userName;

    @Schema(description = "用户 IP", requiredMode = Schema.RequiredMode.REQUIRED, example = "127.0.0.1")
    private String userIp;

    /**
     * 用户备注
     */
    @Schema(description = "用户备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String userRemark;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    /**
     * 购买的商品数量
     */
    @Schema(description = "购买的商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Integer productCount;

    /**
     * 订单提交时间
     */
    @Schema(description = "订单提交时间")
    private LocalDateTime submitTime;

    /**
     * 订单提交时间
     */
    @Schema(description = "订单提交时间")
    private LocalDateTime confirmTime;

    /**
     * 订单完成时间
     */
    @Schema(description = "订单完成时间")
    private LocalDateTime finishTime;

    /**
     * 订单修改时间
     */
    @Schema(description = "订单修改时间")
    private LocalDateTime updateTime;

    /**
     * 订单取消时间
     */
    @Schema(description = "订单取消时间")
    private LocalDateTime cancelTime;

    /**
     * 取消原因
     */
    @Schema(description = "取消原因", example = "你猜一下")
    private String cancelReason;

    /**
     * 取消类型
     */
    @Schema(description = "取消类型", example = "10")
    private Integer cancelType;

    /**
     * 商家备注
     */
    @Schema(description = "商家备注", example = "你猜一下")
    private String remark;

    /**
     * 支付方式
     * {@link PaymentMethodEnum}
     */
    @Schema(description = "支付方式", example = "1")
    private Integer paymentMethod;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id", example = "123")
    private Long supplierId;

    @Schema(description = "供应商类型", example = "1")
    private Integer supplierType;
    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称", example = "京东")
    private String supplierName;

    // ========== 价格 + 支付基本信息 ==========

    /**
     * 支付订单编号
     */
    @Schema(description = "支付订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long payOrderId;

    /**
     * 是否已支付
     */
    @Schema(description = "是否已支付", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean payed;

    /**
     * 付款时间
     */
    @Schema(description = "付款时间")
    private LocalDateTime payTime;

    /**
     * 支付渠道
     */
    @Schema(description = "支付渠道", requiredMode = Schema.RequiredMode.REQUIRED, example = "wx_lite")
    private String payChannelCode;

    /**
     * 商品金额
     *
     */
    @Schema(description = "商品金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000")
    private BigDecimal productPrice;

    /**
     * 运费金额
     */
    @Schema(description = "运费金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000")
    private BigDecimal deliveryPrice;

    /**
     * 订单金额
     *
     */
    @Schema(description = "订单金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000")
    private BigDecimal orderPrice;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000")
    private BigDecimal payPrice;

    // ========== 收件 + 物流基本信息 ==========
    /**
     * 发货状态
     *
     * 枚举 {@link TradeOrderDeliveryStatusEnum}
     */
    @Schema(description = "发货状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer deliveryStatus;

    /**
     * 发货时间
     */
    @Schema(description = "发货时间")
    private LocalDateTime deliveryTime;

    /**
     * 收货时间
     */
    @Schema(description = "收货时间")
    private LocalDateTime receiveTime;

    /**
     * 收件人名称
     */
    @Schema(description = "收件人名称")
    private String receiverName;

    /**
     * 收件人手机
     */
    @Schema(description = "收件人手机")
    private String receiverMobile;

    /**
     * 收件人邮编
     */
    @Schema(description = "收件人邮编")
    private String receiverConsigneeZip;

    /**
     * 收件人地址id
     */
    @Schema(description = "收件人地址id")
    private Long receiverAddressId;

    /**
     * 收件人详细地址
     */
    @Schema(description = "收件人详细地址")
    private String receiverDetailAddress;

    // ========== 售后基本信息 ==========
    /**
     * 售后状态
     *
     * 枚举 {@link TradeOrderAfterSaleStatusEnum}
     */
    @Schema(description = "售后状态")
    private Integer afterSaleStatus;

    /**
     * 退款金额，单位：元
     *
     * 注意，退款并不会影响 {@link #payPrice} 实际支付金额
     * 也就说，一个订单最终产生多少金额的收入 = payPrice - refundPrice
     */
    @Schema(description = "退款金额")
    private BigDecimal refundPrice;

    /**
     * 发票状态
     */
    @Schema(description = "发票状态")
    private Integer invoiceStatus;

    /**
     * 发票验真状态
     */
    @Schema(description = "发票验真状态")
    private Boolean invoiceCheckStatus;

    /**
     * 固资建档状态：0-建档待处理, 1-建档已提交，2-无须建档，3-建档中 4-建档成功 5-建档失败
     * {@link cn.iocoder.yudao.module.mall.trade.enums.order.AssetStatusEnum}
     */
    @Schema(description = "固资建档状态")
    private Integer assetStatus;

    @Schema(description = "固资建档接口失败备注")
    private String assetMemo;

    @Schema(description = "用户删除状态")
    private Boolean userDeleted;

    /**
     * 来源平台
     * {@link cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderPlatformEnum}
     */
    @Schema(description = "来源平台")
    private Integer platform;

    @Schema(description = "审批单号")
    private String bpmNo;
    /**
     * 审核状态 1-未审批 2-审批中 3-审批驳回 4-审批通过
     * 枚举 {@link TradeAuditStatusEnum}
     */
    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer auditStatus;

    @Schema(description = "审批完成时间")
    private LocalDateTime auditCompleteTime;

    @Schema(description = "是否需要提交采购")
    private Boolean needPurchase;

    @Schema(description = "是否线下结算")
    private Boolean offlineSettlement;

    @Schema(description = "采购单ID")
    private Long purchaseId;

    @Schema(description = "项目编号")
    private String projectNo;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目部门")
    private String projectDept;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "是否需要推送到业财融合")
    private Boolean ycrhNeed;

    @Schema(description = "业财融合中的状态")
    private String ycrhStatus;

}
