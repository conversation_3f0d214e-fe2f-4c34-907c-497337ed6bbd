package cn.iocoder.yudao.module.mall.external.bigscreen.service;

import cn.iocoder.yudao.module.mall.external.bigscreen.vo.*;

import java.util.List;

public interface BigScreenService {

    public BigScreenUserSummaryRespVO getUserSummary();

    public BigScreenOrderSummaryRespVO getOrderSummary();

    public List<BigScreenRealTimeOrderRespVO> getRealTimeOrderList();

    public BigScreenProductSummaryRespVO getProductSummary();

    public List<BigScreenSupplierProductSummaryRespVO> getSupplierProductSummary();

    public List<BigScreenSupplierProductProportionRespVO> getSupplierProductProportion();

    public List<BigScreenProductCategoryProportionRespVO> getProductCategoryProportion();

    public List<BigScreenSellProductCategorySummaryRespVO> getSellProductCategorySummary();

    public BigScreenApprovalOrderSummaryRespVO getApprovalOrderSummary();

    public BigScreenAssetArchiveSummaryRespVO getAssetArchiveSummary();

}
