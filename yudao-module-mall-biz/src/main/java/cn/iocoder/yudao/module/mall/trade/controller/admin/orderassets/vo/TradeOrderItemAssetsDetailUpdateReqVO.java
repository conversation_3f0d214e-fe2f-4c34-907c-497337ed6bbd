package cn.iocoder.yudao.module.mall.trade.controller.admin.orderassets.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class TradeOrderItemAssetsDetailUpdateReqVO {

    @Schema(description = "订单固资建档明细ID")
    @NotNull(message = "订单固资建档明细ID不能为空")
    private Long id;

    @Schema(description = "6大类资产类别代码")
    @NotBlank(message = "6大类资产类别代码不能为空")
    private String category6;

    @Schema(description = "16大类资产类别代码")
    @NotBlank(message = "16大类资产类别代码不能为空")
    private String category16;

}
