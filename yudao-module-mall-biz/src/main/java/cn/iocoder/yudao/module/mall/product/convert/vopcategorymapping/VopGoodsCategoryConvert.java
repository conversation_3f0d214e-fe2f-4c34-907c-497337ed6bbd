package cn.iocoder.yudao.module.mall.product.convert.vopcategorymapping;

import cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo.VopGoodsCategoryRespVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopGoodsCategoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface VopGoodsCategoryConvert {
    VopGoodsCategoryConvert INSTANCE = Mappers.getMapper(VopGoodsCategoryConvert.class);

    List<VopGoodsCategoryRespVO> convertList(List<VopGoodsCategoryDO> vopGoodsCategoryDO);
}
