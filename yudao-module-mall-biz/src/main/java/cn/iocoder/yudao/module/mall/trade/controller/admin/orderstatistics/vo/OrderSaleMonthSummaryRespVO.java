package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderSaleMonthSummaryRespVO {

    @Schema(description = "月份")
    private String month;

    @Schema(description = "订单个数")
    private Long count;

    @Schema(description = "订单金额")
    private BigDecimal amount;
}
