package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/18
 */
@Data
public class BalanceInfoRespDTO {

    /**
     * 项目余额
     */
    @JsonProperty("xmye")
    private BigDecimal projectBalance;

    /**
     * 项目额度
     */
    @JsonProperty("xmed")
    private BigDecimal projectThreshold;

    /**
     * 额度控制编码
     */
    @JsonProperty("edkzbm")
    private String controlCode;

    /**
     * 项目编号
     */
    @JsonProperty("xmbh")
    private String projectNo;

    /**
     * 经济分类编码
     */
    @JsonProperty("jjflkmbh")
    private String economyClass;

}
