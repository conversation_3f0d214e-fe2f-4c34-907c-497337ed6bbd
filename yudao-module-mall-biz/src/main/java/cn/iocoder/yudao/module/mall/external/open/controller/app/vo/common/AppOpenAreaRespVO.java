package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/6/17
 */
@Data
public class AppOpenAreaRespVO {

    /**
     * 区域名称
     */
    @Schema(description = "区域名称")
    private String name;

    /**
     * 区域id
     */
    @Schema(description = "区域id")
    private Long id;

    /**
     * 父级Id
     */
    private Long parentId;

    /**
     * 级别
     */
    private Integer level;

}
