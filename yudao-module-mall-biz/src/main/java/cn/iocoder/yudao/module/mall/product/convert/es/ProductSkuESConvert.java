package cn.iocoder.yudao.module.mall.product.convert.es;

import cn.hutool.core.util.RandomUtil;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuDetailRespVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.ProductSkuES;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/8
 */
@Mapper
public interface ProductSkuESConvert {

    ProductSkuESConvert INSTANCE = Mappers.getMapper(ProductSkuESConvert.class);

    /**
     * 商详转换为es对象
     * @param skuDetailRespVOs
     * @return
     */
    @IterableMapping(qualifiedByName = "convertES")
    List<ProductSkuES> convertList(List<AppProductSkuDetailRespVO> skuDetailRespVOs);

    default List<ProductSkuES> convertList02(List<AppProductSkuDetailRespVO> skuDetailRespVOs, Map<Long, SupplierDO> supplierMap, Map<Long, List<Long>> skuTagIdMap) {
        List<ProductSkuES> list = new ArrayList<>();
        skuDetailRespVOs.forEach(skuDetail -> {
            ProductSkuES skuES = convertES(skuDetail);
            if(supplierMap.containsKey(skuES.getSupplierId())) {
                skuES.setSupplierWeight(supplierMap.get(skuES.getSupplierId()).getRecommendIndex());
            }
            skuES.setSalesCount(skuDetail.getInitSalesCount() + skuDetail.getSalesCount());
            skuES.setTagIds(null);
            if(skuTagIdMap != null && skuTagIdMap.containsKey(skuDetail.getSkuId())) {
                skuES.setTagIds(skuTagIdMap.get(skuDetail.getSkuId()));
            }

            list.add(skuES);
        });

        return list;
    }


    /**
     * 商详转换为es对象
     * @param productSkuDetailRespVO
     * @return
     */
    @Named(value = "convertES")
     default ProductSkuES convertES(AppProductSkuDetailRespVO productSkuDetailRespVO) {
         ProductSkuES productSkuES = convert(productSkuDetailRespVO);
         productSkuES.setRandSeq(RandomUtil.randomInt(99999));
        //List<AppProductSpecValueVO> skuSpecValueList = productSkuDetailRespVO.getSkuSpecValueList();
        //if(CollectionUtils.isNotEmpty(skuSpecValueList)) {
        //    List<SkuSpecValueDO> skuSpecValueDOS = skuSpecValueList
        //            .stream()
        //            .map(appProductSpecValueVO -> new SkuSpecValueDO()
        //                    .setSpecId(appProductSpecValueVO.getSpecId())
        //                    .setSpecValue(appProductSpecValueVO.getSpecValue()))
        //            .collect(Collectors.toList());
        //    productSkuES.setSpecValueList(skuSpecValueDOS);
        //}
         return productSkuES;
     }


    /**
     * 商详转换为es对象
     * @param productSkuDetailRespVO
     * @return
     */
    @Mappings(value = {
            @Mapping(source = "category1Id", target = "categoryId1"),
            @Mapping(source = "category2Id", target = "categoryId2"),
            @Mapping(source = "category3Id",target = "categoryId3"),
            @Mapping(source = "category1Name", target = "categoryName1"),
            @Mapping(source = "category2Name", target = "categoryName2"),
            @Mapping(source = "category3Name",target = "categoryName3"),
            @Mapping(target = "skuId", expression = "java(productSkuDetailRespVO.getSkuId() == null ? null : String.valueOf(productSkuDetailRespVO.getSkuId()))")
    })
    ProductSkuES convert(AppProductSkuDetailRespVO productSkuDetailRespVO);


}
