package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SummarizeOrderStatsByDaysRespVO {
    @Schema(description = "日期")
    private String date;

    @Schema(description = "订单数量")
    private Long orderCount;

    @Schema(description = "订单金额")
    private BigDecimal orderAmount;
}
