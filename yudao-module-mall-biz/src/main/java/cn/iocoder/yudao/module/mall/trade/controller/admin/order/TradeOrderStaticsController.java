package cn.iocoder.yudao.module.mall.trade.controller.admin.order;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.*;
import cn.iocoder.yudao.module.mall.trade.convert.orderstatistics.OrderStatisticsConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.orderstatistics.OrderStatisticsDO;
import cn.iocoder.yudao.module.mall.trade.service.orderstatistics.OrderStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@RestController
public class TradeOrderStaticsController {


    @Resource
    private OrderStatisticsService orderStatisticsService;

    @PostMapping("/create")
    @Operation(summary = "创建订单统计")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:create')")
    public CommonResult<Long> createOrderStatistics(@Valid @RequestBody OrderStatisticsCreateReqVO createReqVO) {
        return success(orderStatisticsService.createOrderStatistics(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新订单统计")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:update')")
    public CommonResult<Boolean> updateOrderStatistics(@Valid @RequestBody OrderStatisticsUpdateReqVO updateReqVO) {
        orderStatisticsService.updateOrderStatistics(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除订单统计")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:delete')")
    public CommonResult<Boolean> deleteOrderStatistics(@RequestParam("id") Long id) {
        orderStatisticsService.deleteOrderStatistics(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得订单统计")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<OrderStatisticsRespVO> getOrderStatistics(@RequestParam("id") Long id) {
        OrderStatisticsDO orderStatistics = orderStatisticsService.getOrderStatistics(id);
        return success(OrderStatisticsConvert.INSTANCE.convert(orderStatistics));
    }

    @GetMapping("/list")
    @Operation(summary = "获得订单统计列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderStatisticsRespVO>> getOrderStatisticsList(@RequestParam("ids") Collection<Long> ids) {
        List<OrderStatisticsDO> list = orderStatisticsService.getOrderStatisticsList(ids);
        return success(OrderStatisticsConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得订单统计分页")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<PageResult<OrderStatisticsRespVO>> getOrderStatisticsPage(@Valid OrderStatisticsPageReqVO pageVO) {
        PageResult<OrderStatisticsDO> pageResult = orderStatisticsService.getOrderStatisticsPage(pageVO);
        return success(OrderStatisticsConvert.INSTANCE.convertPage(pageResult));
    }

}
