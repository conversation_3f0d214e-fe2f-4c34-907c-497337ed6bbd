package cn.iocoder.yudao.module.mall.trade.controller.admin.settle.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单明细
 *
 * <AUTHOR>
 * @Date 2024/5/7
 */
@Schema(description = "管理后台 - 订单结算导出excel VO")
@Data
public class OrderItemSettleExportVO {

    @Schema(description = "订单ID")
    @Excel(name = "订单明细ID", orderNum = "0", width = 20)
    private Long id;

    @Schema(description = "商品SKU")
    @Excel(name = "商品SKU", orderNum = "1", width = 20)
    private Long skuId;

    @Schema(description = "商品名称")
    @Excel(name = "商品名称", orderNum = "2", width = 45)
    private String skuName;

    @Schema(description = "商品分类")
    @Excel(name = "商品分类", orderNum = "3", width = 20)
    private String categoryName;

    @Schema(description = "商品数量")
    @Excel(name = "商品数量", orderNum = "4", width = 15)
    private Integer count;

    @Schema(description = "商品单价")
    @Excel(name = "商品单价", orderNum = "5", width = 20)
    private BigDecimal skuPrice;

    @Schema(description = "商品总价")
    @Excel(name = "商品总价", orderNum = "6", width = 20)
    private BigDecimal skuTotalPrice;

    @Schema(description = "固资状态")
    @Excel(name = "固资状态", orderNum = "7", replace = {"建档待处理_0", "建档已提交_1", "无须建档_2", "建档中_3", "建档成功_4", "建档失败_5"}, width = 20)
    private Integer assetStatus;

    @Schema(description = "售后状态")
    @Excel(name = "售后状态", orderNum = "8", replace = {"未售后_0", "售后中_1", "已退款_2"}, width = 20)
    private Integer afterSaleStatus;


}
