package cn.iocoder.yudao.module.mall.product.dal.mysql.spu;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo.ProductSpuExportReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo.ProductSpuPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 商品spu Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSpuMapper extends BaseMapperX<ProductSpuDO> {

    default PageResult<ProductSpuDO> selectPage(ProductSpuPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductSpuDO>()
                .eqIfPresent(ProductSpuDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(ProductSpuDO::getBrandId, reqVO.getBrandId())
                .eqIfPresent(ProductSpuDO::getBrandName, reqVO.getBrandName())
                .likeIfPresent(ProductSpuDO::getSpuName, reqVO.getSpuName())
                .eqIfPresent(ProductSpuDO::getCategory1Id, reqVO.getCategory1Id())
                .eqIfPresent(ProductSpuDO::getCategory2Id, reqVO.getCategory2Id())
                .eqIfPresent(ProductSpuDO::getCategory3Id, reqVO.getCategory3Id())
                .eqIfPresent(ProductSpuDO::getCategory1Name, reqVO.getCategory1Name())
                .eqIfPresent(ProductSpuDO::getCategory2Name, reqVO.getCategory2Name())
                .eqIfPresent(ProductSpuDO::getCategory3Name, reqVO.getCategory3Name())
                .geIfPresent(ProductSpuDO::getSalesCount, reqVO.getSalesCountMin())
                .leIfPresent(ProductSpuDO::getSalesCount, reqVO.getSalesCountMax())
                .geIfPresent(ProductSpuDO::getSalePrice, reqVO.getSalePriceMin())
                .leIfPresent(ProductSpuDO::getSalePrice, reqVO.getSalePriceMax())
                .eqIfPresent(ProductSpuDO::getSpecType, reqVO.getSpecType())
                .eqIfPresent(ProductSpuDO::getStatus, reqVO.getSpuStatus())
                .eqIfPresent(ProductSpuDO::getPlatformStatus, reqVO.getPlatformStatus())
                .betweenIfPresent(ProductSpuDO::getCreateTime, reqVO.getCreateTime())
                .likeRight(StringUtils.isNotBlank(reqVO.getFullCategoryId()), ProductSpuDO::getFullCategoryId, reqVO.getFullCategoryId())
                .orderByDesc(ProductSpuDO::getUpdateTime, ProductSpuDO::getSort));
    }


    default List<ProductSpuDO> selectList(ProductSpuExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ProductSpuDO>()
                .eqIfPresent(ProductSpuDO::getBrandId, reqVO.getBrandId())
                .likeIfPresent(ProductSpuDO::getBrandName, reqVO.getBrandName())
                .eqIfPresent(ProductSpuDO::getSpecType, reqVO.getSpecType())
                .likeIfPresent(ProductSpuDO::getSpuName, reqVO.getSpuName())
                .eqIfPresent(ProductSpuDO::getSellPoint, reqVO.getSellPoint())
                .eqIfPresent(ProductSpuDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(ProductSpuDO::getCategory1Id, reqVO.getCategory1Id())
                .eqIfPresent(ProductSpuDO::getCategory2Id, reqVO.getCategory2Id())
                .eqIfPresent(ProductSpuDO::getCategory3Id, reqVO.getCategory3Id())
                .likeIfPresent(ProductSpuDO::getCategory1Name, reqVO.getCategory1Name())
                .likeIfPresent(ProductSpuDO::getCategory2Name, reqVO.getCategory2Name())
                .likeIfPresent(ProductSpuDO::getCategory3Name, reqVO.getCategory3Name())
                .orderByDesc(ProductSpuDO::getUpdateTime, ProductSpuDO::getSort));
    }
    /**
     * 更新商品 SPU 库存
     *
     * @param id 商品 SPU 编号
     * @param incrCount 增加的库存数量
     */
    default void updateStock(Long id, Integer incrCount) {
        LambdaUpdateWrapper<ProductSpuDO> updateWrapper = new LambdaUpdateWrapper<ProductSpuDO>()
                .setSql(" total_stock = total_stock +" + incrCount) // 负数，所以使用 + 号
                .eq(ProductSpuDO::getId, id);
        update(null, updateWrapper);
    }

}
