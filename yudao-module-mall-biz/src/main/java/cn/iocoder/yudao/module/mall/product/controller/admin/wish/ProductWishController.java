package cn.iocoder.yudao.module.mall.product.controller.admin.wish;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.module.mall.product.controller.admin.wish.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.wish.ProductWishConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.wish.ProductWishDO;
import cn.iocoder.yudao.module.mall.product.enums.wish.ProductWishStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.wish.ProductWishService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;


@Tag(name = "管理后台 - 心愿单")
@RestController
@RequestMapping("/product/wish")
@Validated
public class ProductWishController {

    @Resource
    private ProductWishService wishService;

    @DeleteMapping("/delete")
    @Operation(summary = "删除心愿单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:wish:delete')")
    public CommonResult<Boolean> deleteWish(@RequestParam("id") Long id) {
        wishService.deleteWish(id);
        return success(true);
    }

    @PostMapping("/update-status")
    @Operation(summary = "修改心愿单")
    @PreAuthorize("@ss.hasPermission('product:wish:update')")
    public CommonResult<Boolean> updateWishStatus(@Valid @RequestBody ProductWishStausReqVO statusReqVO) {
        ProductWishDO wish = ProductWishConvert.INSTANCE.convert(statusReqVO);
        wishService.updateWithStatus(wish);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得心愿单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:wish:query')")
    public CommonResult<ProductWishRespVO> getWish(@RequestParam("id") Long id) {
        ProductWishDO wish = wishService.getWish(id);
        return success(ProductWishConvert.INSTANCE.convert(wish));
    }

    @GetMapping("/list")
    @Operation(summary = "获得心愿单列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('product:wish:query')")
    public CommonResult<List<ProductWishRespVO>> getWishList(@RequestParam("ids") Collection<Long> ids) {
        List<ProductWishDO> list = wishService.getWishList(ids);
        return success(ProductWishConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得心愿单分页")
    @PreAuthorize("@ss.hasPermission('product:wish:query')")
    public CommonResult<PageResult<ProductWishRespVO>> getWishPage(@Valid ProductWishPageReqVO pageVO) {
        PageResult<ProductWishDO> pageResult = wishService.getWishPage(pageVO);
        return success(ProductWishConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出心愿单 Excel")
    @PreAuthorize("@ss.hasPermission('product:wish:export')")
    @OperateLog(type = EXPORT)
    public void exportWishExcel(@Valid ProductWishExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ProductWishDO> list = wishService.getWishList(exportReqVO);
        // 导出 Excel
        List<ProductWishExcelVO> datas = ProductWishConvert.INSTANCE.convertList03(list);
        datas.forEach(item -> {
            item.setStatusInfo(ProductWishStatusEnum.INIT.getName());
            if(item.getStatus() != null && ProductWishStatusEnum.REPLIED.getValue().equals(item.getStatus())) {
                item.setStatusInfo(ProductWishStatusEnum.REPLIED.getName());
            }
        });
        ExcelUtils.write(response, "心愿单.xls", "数据", ProductWishExcelVO.class, datas);
    }

}
