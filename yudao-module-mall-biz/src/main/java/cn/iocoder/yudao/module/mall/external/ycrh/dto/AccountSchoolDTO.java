package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class AccountSchoolDTO {

    /**
     * 转入部门编号
     */
    @JsonProperty("ZRBMBH")
    private String departmentNo;

    /**
     * 转入项目编号
     */
    @JsonProperty("ZRXMBH")
    private String projectNo;

    /**
     * 转入主科目
     */
    @JsonProperty("ZRZKM")
    private String majorSubject;

    /**
     * 转入经济分类
     */
    @JsonProperty("ZRJJFL")
    private String economyClass;

    /**
     * 转入金额
     */
    @JsonProperty("JE")
    private BigDecimal amount;

}
