package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 物流轨迹 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class DeliveryTrackBaseVO {

    @Schema(description = "物流信息表id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物流信息表id不能为空")
    private Long deliveryId;

    @Schema(description = "时间，原始格式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "时间，原始格式不能为空")
    private String deliveryTime;

    @Schema(description = "内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "内容不能为空")
    private String content;

    @Schema(description = "本数据元对应的行政区域的编码，实时查询接口中提交resultv2=1或者resultv2=4标记后才会出现")
    private String areaCode;

    @Schema(description = "本数据元对应的行政区域的名称，实时查询接口中提交resultv2=1或者resultv2=4标记后才会出现")
    private String areaName;

    @Schema(description = "本数据元对应的物流状态名称或者高级状态名称，实时查询接口中提交resultv2=1或者resultv2=4标记后才会出现", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "本数据元对应的物流状态名称或者高级状态名称，实时查询接口中提交resultv2=1或者resultv2=4标记后才会出现不能为空")
    private String status;

    @Schema(description = "本数据元对应的行政区域经纬度，实时查询接口中提交resultv2=4标记后才会出现")
    private String areaCenter;

    @Schema(description = "本数据元对应的行政区域拼音，实时查询接口中提交resultv2=4标记后才会出现")
    private String areaPinyin;

    @Schema(description = "本数据元对应的高级物流状态值，实时查询接口中提交resultv2=4标记后才会出现")
    private String statusCode;

}
