package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class PayOrderDTO {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 费用名称
     */
    private String payName;

    /**
     * 缴费类型 1:年费 2:专利费 3:服务费  4:预算  5:电费 6:仪器使用费
     */
    private String payType;

    /**
     * 缴费金额
     */
    private BigDecimal payAmount;

    /**
     * 缴费时间 (yyyy-MM-dd HH:mm:ss)
     */
    private String orderTime;

    /**
     * 操作人工号
     */
    @JsonProperty("ygbh")
    private String operatorNo;

    /**
     * 经济分类
     */
    @JsonProperty("jjfl")
    private String economyClass;

    /**
     * 使用单一项目 1:是(默认)  2:否(多个项目)
     */
    private String singleProject;

    /**
     * 部门编号 (singleProject为1时必填)
     */
    @JsonProperty("bmbh")
    private String departmentNo;

    /**
     * 项目编号 (singleProject为1时必填)
     */
    @JsonProperty("xmbh")
    private String projectNo;

    /**
     * 项目负责人编号 (singleProject为1时必填)
     */
    @JsonProperty("xmfzrbh")
    private String chargeNo;

    /**
     * 项目信息列表  (singleProject为2时必填)
     */
    private List<XmlInfoDTO> xmInfoList;

    /**
     * 备注
     */
    @JsonProperty("bz")
    private String remark;
}
