package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 物流信息 Excel 导出 Request VO，参数和 DeliveryPageReqVO 是一致的")
@Data
public class DeliveryExportReqVO {

    @Schema(description = "是否签收标记，0未签收，1已签收，请忽略，明细状态请参考state字段")
    private Integer isCheck;

    @Schema(description = "出发行政区域编码")
    private String fromNumber;

    @Schema(description = "出发行政区域名字")
    private String fromName;

    @Schema(description = "目的地行政区域编码")
    private String toNumber;

    @Schema(description = "目的地行政区域名字")
    private String toName;

    @Schema(description = "当前行政区域编码")
    private String curNumber;

    @Schema(description = "当前行政区域名字")
    private String curName;

    @Schema(description = "订阅状态，默认为0未订阅，1订阅")
    private Integer subscribe;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "供应商编号")
    private Long supplierId;

    @Schema(description = "物流来源")
    private String source;

    @Schema(description = "物流公司编码,一律用小写字母")
    private String com;

    @Schema(description = "物流单号")
    private String num;

    @Schema(description = "物流单当前状态，默认为0在途，1揽收，2疑难，3签收，4退签，5派件，8清关，14拒签等10个基础物流状态，如需要返回高级物流状态，请参考 resultv2 传值")
    private Integer state;

    @Schema(description = "物流公司名称")
    private String name;

}
