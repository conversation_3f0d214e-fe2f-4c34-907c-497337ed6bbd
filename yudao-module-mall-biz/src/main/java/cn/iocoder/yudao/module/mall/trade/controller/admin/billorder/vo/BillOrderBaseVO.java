package cn.iocoder.yudao.module.mall.trade.controller.admin.billorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
 * 账单订单 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class BillOrderBaseVO {

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "账单id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "账单id不能为空")
    private Long billId;

    @Schema(description = "订单id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单id不能为空")
    private Long orderId;

}
