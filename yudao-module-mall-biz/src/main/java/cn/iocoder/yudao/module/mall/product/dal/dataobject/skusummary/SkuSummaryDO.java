package cn.iocoder.yudao.module.mall.product.dal.dataobject.skusummary;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 供应商上架商品数统计 DO
 *
 * <AUTHOR>
 */
@TableName("product_sku_summary")
@KeySequence("product_sku_summary_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuSummaryDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 供应商ID
     */
    private Long supplierId;
    /**
     * 供应商类型
     */
    private Integer supplierType;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 上架数量
     */
    private Long listingTotal;
    /**
     * 下架数量
     */
    private Long delistingTotal;
    /**
     * 截止时间
     */
    private Date deadline;

}
