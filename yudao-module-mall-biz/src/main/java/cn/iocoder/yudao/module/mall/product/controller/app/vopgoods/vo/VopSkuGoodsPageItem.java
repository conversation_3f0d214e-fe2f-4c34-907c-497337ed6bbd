package cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo;

import com.jd.open.api.sdk.domain.vopsp.SearchGoodsProvider.response.searchSku.SkuHitSearchGoodsResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/6 15:50
 */

@Data
public class VopSkuGoodsPageItem extends SkuHitSearchGoodsResp {

    /**
     * 市场价
     */
    @Schema(description = "市场价")
    private BigDecimal marketPrice;
    /**
     * 售卖价
     */
    @Schema(description = "售卖价")
    private BigDecimal salePrice;

    /**
     * 售卖状态
     */
    @Schema(description = "售卖状态")
    private Integer saleStatus;

    /**
     * 剩余数量。
     */
    @Schema(description = "剩余数量。")
    private int remainNum;

    /**
     * 最低购买量
     */
    @Schema(description = "最低购买量")
    private Integer lowestBuy;

    /**
     * 到货天数，可能为空
     */
    @Schema(description = "到货天数，可能为空")
    private Integer arrivalDays;

    /**
     * 库存状态类型，参考枚举值： 33,39,40,36,34,99
     */
    @Schema(description = "库存状态类型，参考枚举值： 33,39,40,36,34,99")
    private int stockStateType;

    /**
     * 库存状态描述。以下为stockStateId不同时，此字段不同的返回值：
     * 33 有货 现货-下单立即发货
     * 39 有货 在途-正在内部配货，预计2-6天到达本仓库
     * 40 有货 可配货-下单后从有货仓库配货
     * 36 预订
     * 34 无货
     * 99 无货开预定
     */
    @Schema(description = "库存状态描述。以下为stockStateId不同时，此字段不同的返回值： 33 有货 现货-下单立即发货 39 有货 在途-正在内部配货，预计2-6天到达本仓库 40 有货 可配货-下单后从有货仓库配货 36 预订 34 无货 99 无货开预定")
    private String stockStateDesc;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商logo
     */
    private String logoUrl;

}
