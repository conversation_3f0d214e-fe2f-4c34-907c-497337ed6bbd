package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 商品状态更新
 * <AUTHOR>
 * @date 2024-04-30
 */
@Data
public class AppOpenSkuUpdateStatusReqVO {

    /**
     * 商品skuId
     */
    @Schema(description = "商品skuId")
    @NotNull(message = "商品skuId不能为空")
    private Long skuId;

    /**
     * 状态： 1-正常 0-禁用
     */
    @Schema(description = "状态： 1-正常 0-禁用", example = "1")
    private Integer status;

}
