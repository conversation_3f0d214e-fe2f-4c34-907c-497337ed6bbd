package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade;

import cn.iocoder.yudao.module.mall.trade.controller.app.message.vo.AppTradeMessageContentVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.message.vo.AppTradeMessageReqVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 发票消息体
 */
@Data
public class AppTradeInvoiceVO extends AppTradeMessageContentVO {
    /**
     * 发票金额
     */
    private BigDecimal invoicePrice;
}
