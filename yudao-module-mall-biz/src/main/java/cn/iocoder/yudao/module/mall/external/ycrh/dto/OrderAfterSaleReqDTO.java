package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class OrderAfterSaleReqDTO {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 是否已结算,待处理 (默认为"0") "0":否  "1":是
     */
    private String waitAfter;

    /**
     * 售后详情列表
     */
    private List<OrderAfterSaleDetailDTO>  afsDetailList;

    /**
     * 退款项目列表 (当使用多项目时必填)
     */
    private List<XmlInfoDTO> afsXmList;
}
