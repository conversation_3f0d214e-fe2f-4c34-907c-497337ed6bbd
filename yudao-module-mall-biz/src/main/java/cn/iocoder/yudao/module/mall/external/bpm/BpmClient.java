package cn.iocoder.yudao.module.mall.external.bpm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmAuditResultDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmCallbackResultDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmReqDTO;
import cn.iocoder.yudao.module.mall.external.bpm.service.BpmService;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseValidateReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import cn.iocoder.yudao.module.mall.trade.service.purchase.PurchaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.EXTERNAL_BPM_NOT_EXIST;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.EXTERNAL_BPM_NOT_OPEN;

/**
 * BPM业务层基类
 * <AUTHOR>
 * @date 2024/08/20
 */
@Service
@Slf4j
public class BpmClient {

    @Resource
    private List<BpmService> bpmServiceList;
    @Resource
    private BpmClientConfig bpmClientConfig;
    @Resource
    @Lazy
    private PurchaseService purchaseService;

    private BpmService getValidService() {
        String clientCode = bpmClientConfig.getBpmSysCode();
        if(StringUtils.isBlank(clientCode) || CollUtil.isEmpty(bpmServiceList)) {
            throw ServiceExceptionUtil.exception(EXTERNAL_BPM_NOT_EXIST);
        }
        BpmService service = bpmServiceList.stream().filter(item -> item.getClientType().equals(clientCode)).findFirst().orElse(null);
        Assert.notNull(service, "BPM客户端不存在");
        return service;
    }

    /**
     * 审批推送前置校验
     * @param reqVO
     */
    public void validateBeforePush(PurchaseValidateReqVO reqVO) {
        if(!bpmClientConfig.isBpmOpen()) {
            throw ServiceExceptionUtil.exception(EXTERNAL_BPM_NOT_OPEN);
        }
        BpmService service = getValidService();
        service.validateBeforePush(reqVO);
    }

    /**
     * 推送审批流信息
     * @param bpmReqDTO
     */
    public String pushBpmInfo(BpmReqDTO bpmReqDTO) {
        if(!bpmClientConfig.isBpmOpen()) {
            throw ServiceExceptionUtil.exception(EXTERNAL_BPM_NOT_OPEN);
        }
        BpmService service = getValidService();
        return service.pushBpmInfo(bpmReqDTO);
    }

    /**
     * 审批流数据回调
     * @param body
     * @param params
     */
    public BpmCallbackResultDTO handleBpmCallback(String body, Map<String, String> params) {
        BpmService service = getValidService();
        return service.handleBpmCallback(body, params);
    }

    /**
     * 拉取审批流的审批状态
     * @param bpmNo
     */
    public void pullBpmStatus(String bpmNo) {
        PurchaseDO purchaseDO = purchaseService.getPurchaseByBpmNo(bpmNo);
        if(purchaseDO == null) {
            log.info("审批流单号{}不存在", bpmNo);
            return;
        }
        if(purchaseDO.isBpmFinished()) {
            log.info("审批流{}已经处理并完成", bpmNo);
            return;
        }
        BpmService service = getValidService();
        BpmAuditResultDTO bpmAuditResultDTO = service.pullBpmStatus(bpmNo);
        if(bpmAuditResultDTO == null) {
            log.info("审批流{}状态查询结果为空", bpmNo);
            return;
        }
        purchaseService.updatePurchaseAuditStatus(purchaseDO, bpmAuditResultDTO);
    }

    /**
     * 获取审批状态
     * @param bpmNo
     * @return
     */
    public BpmAuditResultDTO getBpmAuditResult(String bpmNo) {
        BpmService service = getValidService();
        BpmAuditResultDTO bpmAuditResultDTO = service.pullBpmStatus(bpmNo);
        if(bpmAuditResultDTO == null) {
            log.info("审批流{}状态查询结果为空", bpmNo);
            return null;
        }

        return bpmAuditResultDTO;
    }

    /**
     * 取消审批流
     * @param bpmNo
     * @param userId
     */
    public void cancelBpmInfo(String bpmNo, String reason, Long userId) {
        BpmService service = getValidService();
        service.cancelBpmInfo(bpmNo, reason, userId);
    }

}
