package cn.iocoder.yudao.module.mall.product.controller.admin.productcomment;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.productcomment.ProductCommentConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentDO;
import cn.iocoder.yudao.module.mall.product.service.productcomment.ProductCommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

/**
 * 管理后台 - 商品评价
 */
@Tag(name = "管理后台 - 商品评价")
@RestController
@RequestMapping("/product/comment")
@Validated
public class ProductCommentController {

    @Resource
    private ProductCommentService productCommentService;

    /**
     * 删除商品评价
     * @param id
     * @return
     */
    @PostMapping("/delete")
    @Operation(summary = "删除商品评价")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:comment:delete')")
    public CommonResult<Boolean> deleteComment(@RequestParam("id") Long id) {
        productCommentService.deleteComment(id);
        return success(true);
    }

    /**
     * 获得商品评价
     * @param id
     * @return
     */
    @GetMapping("/get")
    @Operation(summary = "获得商品评价")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:comment:query')")
    public CommonResult<ProductCommentRespVO> getComment(@RequestParam("id") Long id) {
        ProductCommentDO productComment = productCommentService.getComment(id);
        return success(ProductCommentConvert.INSTANCE.convert03(productComment));
    }

    /**
     * 获得商品评价分页
     * @param pageVO
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "获得商品评价分页")
    @PreAuthorize("@ss.hasPermission('product:comment:query')")
    public CommonResult<PageResult<ProductCommentRespVO>> getCommentPage(@Valid ProductCommentPageReqVO pageVO) {
        PageResult<ProductCommentDO> pageResult = productCommentService.getCommentPage(pageVO);
        return success(ProductCommentConvert.INSTANCE.convertPage01(pageResult));
    }

    /**
     * 审核评论
     * @param reqVO
     * @return
     */
    @PostMapping("/audit")
    @Operation(summary = "审核评论")
    @PreAuthorize("@ss.hasPermission('product:comment:audit')")
    public CommonResult<PageResult<ProductCommentRespVO>> audit(@Valid ProductCommentAuditReqVO reqVO) {
        productCommentService.updateAuditStatus(reqVO);
        return success(null);
    }


}
