package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo.*;
import cn.iocoder.yudao.module.mall.trade.convert.delivery.ConfigDeliveryCompanyConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.ConfigDeliveryCompanyDO;
import cn.iocoder.yudao.module.mall.trade.service.delivery.ConfigDeliveryCompanyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 平台配置物流公司")
@RestController
@RequestMapping("/trade/config/delivery-company")
@Validated
public class ConfigDeliveryCompanyController {

    @Resource
    private ConfigDeliveryCompanyService configDeliveryCompanyService;

    @PostMapping("/create")
    @Operation(summary = "创建平台配置物流公司")
	@PreAuthorize("@ss.hasPermission('trade:deliverycompany:create')")
    public CommonResult<Long> createConfigDeliveryCompany(@Valid @RequestBody ConfigDeliveryCompanyCreateReqVO createReqVO) {
        return success(configDeliveryCompanyService.createConfigDeliveryCompany(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新平台配置物流公司")
	@PreAuthorize("@ss.hasPermission('trade:deliverycompany:update')")
    public CommonResult<Boolean> updateConfigDeliveryCompany(@Valid @RequestBody ConfigDeliveryCompanyUpdateReqVO updateReqVO) {
        configDeliveryCompanyService.updateConfigDeliveryCompany(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除平台配置物流公司")
    @Parameter(name = "id", description = "编号", required = true)
	@PreAuthorize("@ss.hasPermission('trade:deliverycompany:delete')")
    public CommonResult<Boolean> deleteConfigDeliveryCompany(@RequestParam("id") Long id) {
        configDeliveryCompanyService.deleteConfigDeliveryCompany(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得平台配置物流公司")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
	@PreAuthorize("@ss.hasPermission('trade:deliverycompany:query')")
    public CommonResult<ConfigDeliveryCompanyRespVO> getConfigDeliveryCompany(@RequestParam("id") Long id) {
        ConfigDeliveryCompanyDO configDeliveryCompany = configDeliveryCompanyService.getConfigDeliveryCompany(id);
        return success(ConfigDeliveryCompanyConvert.INSTANCE.convert(configDeliveryCompany));
    }

    @GetMapping("/list")
    @Operation(summary = "获得平台配置物流公司列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
	@PreAuthorize("@ss.hasPermission('trade:deliverycompany:query')")
    public CommonResult<List<ConfigDeliveryCompanyRespVO>> getConfigDeliveryCompanyList(@RequestParam("ids") Collection<Long> ids) {
        List<ConfigDeliveryCompanyDO> list = configDeliveryCompanyService.getConfigDeliveryCompanyList(ids);
        return success(ConfigDeliveryCompanyConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得平台配置物流公司分页")
	@PreAuthorize("@ss.hasPermission('trade:deliverycompany:query')")
    public CommonResult<PageResult<ConfigDeliveryCompanyRespVO>> getConfigDeliveryCompanyPage(@Valid ConfigDeliveryCompanyPageReqVO pageVO) {
        PageResult<ConfigDeliveryCompanyDO> pageResult = configDeliveryCompanyService.getConfigDeliveryCompanyPage(pageVO);
        return success(ConfigDeliveryCompanyConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出平台配置物流公司 Excel")
    @OperateLog(type = EXPORT)
	@PreAuthorize("@ss.hasPermission('trade:deliverycompany:export')")
    public void exportConfigDeliveryCompanyExcel(@Valid ConfigDeliveryCompanyExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ConfigDeliveryCompanyDO> list = configDeliveryCompanyService.getConfigDeliveryCompanyList(exportReqVO);
        // 导出 Excel
        List<ConfigDeliveryCompanyExcelVO> datas = ConfigDeliveryCompanyConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "平台配置物流公司.xls", "数据", ConfigDeliveryCompanyExcelVO.class, datas);
    }

    @GetMapping("/get-types")
    @Operation(summary = "物流公司类型")
	@PreAuthorize("@ss.hasPermission('trade:deliverycompany:sync')")
    public CommonResult<List<String>> getTypes() {
        List<String> types = configDeliveryCompanyService.getTypes();
        return success(types);
    }

    /**
     * 同步快递100物流公司
     * @return
     */
    @GetMapping("/sync")
    @Operation(summary = "同步快递100物流公司")
	@PreAuthorize("@ss.hasPermission('trade:deliverycompany:update')")
    public CommonResult<Boolean> syncCompany() {
        Boolean ret = configDeliveryCompanyService.syncCompany();
        return success(ret);
    }
}
