package cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 京东分类映射 DO
 *
 * <AUTHOR>
 */
@TableName("mall_vop_category_mapping")
@KeySequence("mall_vop_category_mapping_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VopCategoryMappingDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 末级分类ID
     */
    private Long lastCategoryId;
    /**
     * 末级分类名称
     */
    private String lastCategoryName;
    /**
     * 完整分类id -分隔
     */
    private String fullCategoryId;
    /**
     * 完整分类名称
     */
    private String fullCategoryName;
    /**
     * vop末级分类ID
     */
    private Long vopLastCategoryId;
    /**
     * vop末级分类名称
     */
    private String vopLastCategoryName;
    /**
     * vop完整分类id -分隔
     */
    private String vopFullCategoryId;
    /**
     * vop完整分类名称
     */
    private String vopFullCategoryName;

}
