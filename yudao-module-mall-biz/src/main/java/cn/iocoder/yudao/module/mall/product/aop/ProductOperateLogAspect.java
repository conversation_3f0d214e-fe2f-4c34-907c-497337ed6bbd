package cn.iocoder.yudao.module.mall.product.aop;

import cn.iocoder.yudao.module.mall.annotation.ProductOperateLog;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productoperatelog.ProductOperateLogDO;
import cn.iocoder.yudao.module.mall.product.service.productoperatelog.ProductOperateLogService;
import cn.iocoder.yudao.module.mall.util.SpringParseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserName;

/**
 * <AUTHOR>
 * @date 2024/1/30
 */
@Component
@Aspect
@Slf4j
public class ProductOperateLogAspect {

    @Autowired
    private ProductOperateLogService productOperateLogService;

    @Around("@annotation(productOperateLog)")
    public Object around(ProceedingJoinPoint joinPoint, ProductOperateLog productOperateLog) throws Throwable {
        Object result = joinPoint.proceed();
        saveLog(joinPoint, productOperateLog);
        return result;
    }

    private void  saveLog(ProceedingJoinPoint joinPoint, ProductOperateLog productOperateLog) {
        String spuIdStr = SpringParseUtils.parseExpression(joinPoint, productOperateLog.spuId());
        Long userId = getLoginUserId();
        String userName = getLoginUserName();
        ProductOperateLogDO operateLog = new ProductOperateLogDO();
        if(StringUtils.isNotBlank(spuIdStr)) {
            operateLog.setSpuId(Long.valueOf(spuIdStr));
        }
        operateLog.setContent(productOperateLog.content());
        operateLog.setUserId(userId);
        operateLog.setUserName(userName);
        operateLog.setOperateType(productOperateLog.type().getCode());
        productOperateLogService.saveProductOperateLog(operateLog);
    }

}
