package cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商品规格更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ConfigProductCategorySpecUpdateReqVO extends ConfigProductCategorySpecBaseVO {

    @Schema(description = "规格Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "规格Id不能为空")
    private Long id;

}
