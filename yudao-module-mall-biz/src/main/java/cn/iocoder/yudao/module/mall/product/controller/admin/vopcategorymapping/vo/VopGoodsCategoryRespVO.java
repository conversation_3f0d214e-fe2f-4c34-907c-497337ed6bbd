package cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo;

import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsCategoryItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 京东商品分类 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VopGoodsCategoryRespVO extends VopGoodsCategoryBaseVO{
    /**
     * 分类子类集合
     */
    @Schema(description = "分类子类集合")
    private List<VopGoodsCategoryItem> childCategoryList;
}
