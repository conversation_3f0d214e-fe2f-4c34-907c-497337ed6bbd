package cn.iocoder.yudao.module.mall.basis.dal.dataobject.basisconfig;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.mall.enums.basis.LoginTypeEnum;
import cn.iocoder.yudao.module.mall.enums.basis.ThemeCodeEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.PaymentMethodEnum;
import cn.iocoder.yudao.module.mall.util.ConvertUtils;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;

import java.util.Arrays;
import java.util.Map;

/**
 * 采购方基础配置 DO
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("mall_basis_config")
@KeySequence("mall_basis_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasisConfigDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 采购方名称
     */
    private String name;
    /**
     * LOGO地址
     */
    private String logoUrl;
    /**
     * 多供应商开关
     */
    private Boolean multipleShop;
    /**
     *  前置审批开关
     */
    private Boolean approveSwitch;
    /**
     *  项目经费卡开关
     */
    private Boolean projectSwitch;
    /**
     *  仪器设备采购表导出开关
     */
    private Boolean instrumentApplyExport;
    /**
     * 送货单导出开关
     */
    private Boolean orderReceiptExport;
    /**
     *  查看价格强制登录开关
     */
    private Boolean getPriceMustLogin;
    /**
     *  资产登记开关
     */
    private Boolean assetsSwitch;
    /**
     * 验收开关
     */
    private Boolean acceptSwitch;
    /**
     *  资产登记手动推送开关
     */
    private Boolean assetsManualSwitch;
    /**
     * 固资商品混合下单开关, 默认true
     */
    private Boolean assetsMixSwitch;
    /**
     * 固资线下结算开关，默认为false
     */
    private Boolean assetsOfflinePaySwitch;
    /**
     *  自动审批开关
     */
    private Boolean autoApprove;
    /**
     *  提交订单开关
     */
    private Boolean orderSwitch;
    /**
     * 提交订单白名单
     */
    private String orderWhiteList;
    /**
     *  手机号注册开关
     */
    private Boolean registerSwitch;
    /**
     *  手机号重置密码开关
     */
    private Boolean resetPasswordSwitch;
    /**
     * 订单完成后自动申请开票开关
     */
    private Boolean autoInvoiceSwitch;
    /**
     * 发票校验开关
     */
    private Boolean verifyInvoice;
    /**
     * 自动开票阈值，如N天后自动发送开票申请
     */
    private Integer autoInvoiceThreshold;
    /**
     * 域名,支持多个域名，以逗号分隔
     */
    private String domain;
    /**
     * 登录类型,参考数据字典, {@link LoginTypeEnum}
     */
    private Integer loginType;
    /**
     * 登录方式，20-验证码登录 10-帐号登录
     */
    private String loginMethod;
    /**
     * 登录地址
     */
    private String loginUrl;
    /**
     * 商城主题颜色，参考数据字典, {@link ThemeCodeEnum}
     */
    private Integer themeCode;
    /**
     * 收藏图标地址
     */
    private String faviconUrl;
    /**
     * 退出方式, 参考数据字典, {@link cn.iocoder.yudao.module.mall.enums.basis.LogoutTypeEnum}
     */
    private Integer logoutType;
    /**
     * 商城标题
     */
    private String title;
    /**
     * 支付方式，逗号分隔
     * 参考 {@link PaymentMethodEnum}
     */
    private String payMethod;
    /**
     * 全局提示开关
     */
    private Boolean globalTipSwitch;
    /**
     * 全局提示内容
     */
    private String globalTipContent;
    /**
     * 商品展示字段, 如商品列表供应商名称，以逗号分隔
     */
    private String productField;
    /**
     * 商品评价开关
     */
    private Boolean productCommentSwitch;
    /**
     * 地址备注开关
     */
    private Boolean addressRemarkSwitch;
    /**
     * 地址备注
     */
    private String addressRemark;
    /**
     * 平台客服配置，格式：k=v&k2=v2
     */
    private String custService;
    /**
     * 扩展参数，企微key
     */
    private String extQwKey;
    /**
     * 扩展参数，通知内容模板
     */
    private String extNoticeTpl;
    /**
     * 扩展参数集，JSON格式存储
     */
    private String extConfig;
    /**
     * 是否显示后台登录入口
     */
    private Boolean adminEntrySwitch;
    /**
     * 后台登录地址
     */
    private String adminEntryUrl;
    /**
     * 默认区域地址，区域id，以逗号分隔
     */
    private String address;

    /**
     * 是否自动确认
     * @return
     */
    public boolean needAutoApprove() {
        if(approveSwitch == null || projectSwitch == null || autoApprove == null) {
            return false;
        }
        // 前置审批开关关闭, 经费卡开关关闭，自动审批开关打醒开
        if(!approveSwitch && !projectSwitch && autoApprove) {
            return true;
        }

        return false;
    }

    public boolean needAutoApproveAfterPurchase() {
        // 当只开通经费卡时，未开通前置审批时，订单应该自动审批；
        if(projectSwitch && !approveSwitch) {
            return true;
        }

        return false;
    }

    public Map<String, Object> extractExtConfigMap() {
        return ConvertUtils.jsonToMap(extConfig);
    }

    public <T> T extractExtConfigItem(String key, Class<T> type) {
        Map<String, Object> map = extractExtConfigMap();
        Object value = map.get(key);
        if (value == null || !type.isInstance(value)) {
            return null;
        }
        return type.cast(value);
    }

    public boolean showPriceOnLogin() {
        return getPriceMustLogin != null && getPriceMustLogin;
    }

    public boolean needApprove() {
        return approveSwitch != null && approveSwitch;
    }

    public boolean needProject() {
        return projectSwitch != null && projectSwitch;
    }

    public boolean needPurchase() {
        return needApprove() || needProject();
    }

    public boolean needAssetsPush() { return assetsSwitch != null && assetsSwitch; }

    public boolean needAutoInvoice() {
        return autoInvoiceSwitch != null && autoInvoiceSwitch;
    }

    public boolean needVerifyInvoice() {
        return verifyInvoice != null && verifyInvoice;
    }

    public boolean allowAssetsMixInOrder() { return assetsMixSwitch == null || assetsMixSwitch; }

    public boolean needAssetsOfflinePay() { return assetsOfflinePaySwitch == null || assetsOfflinePaySwitch; }

    public boolean autoInvoiceThresholdGreaterZero() {
        return autoInvoiceThreshold != null && autoInvoiceThreshold > 0;
    }

    public boolean needAssetsManualSwitch() {
        return assetsManualSwitch != null && assetsManualSwitch;
    }

    public boolean addressRemarkSwitch() {
        return addressRemarkSwitch != null && addressRemarkSwitch;
    }

    public boolean needAdminEntry() {
        return adminEntrySwitch != null && adminEntrySwitch;
    }

    @JsonIgnore
    public String getFirstDomain() {
        if(StringUtils.isNotBlank(domain)) {
            String[] segs = StringUtils.split(domain, ",");
            return segs[0];
        }

        return null;
    }

    /**
     * 是否可以提交订单
     * @param userId
     * @return
     */
    public boolean canSubmitOrder(Long userId) {
        if(orderSwitch) {
            return true;
        }
        else if(userId != null
                && StringUtils.isNotBlank(orderWhiteList)
                && Arrays.asList(orderWhiteList.split(",")).contains(String.valueOf(userId))) {
            return true;
        }
        return false;
    }
}
