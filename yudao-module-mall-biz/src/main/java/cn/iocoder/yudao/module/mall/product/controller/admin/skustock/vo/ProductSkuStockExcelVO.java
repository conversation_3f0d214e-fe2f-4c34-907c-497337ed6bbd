package cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * sku库存 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ProductSkuStockExcelVO {

    @ExcelProperty("自增ID")
    private Long id;

    @ExcelProperty("供应商id")
    private Long supplierId;

    @ExcelProperty("SKU ID")
    private Integer skuId;

    @ExcelProperty("库存")
    private Integer stock;

    @ExcelProperty("预警库存")
    private Integer warnStock;

    @ExcelProperty("预占库存")
    private Integer reserveStock;

    @ExcelProperty("创建时间")
    private Date createTime;

}
