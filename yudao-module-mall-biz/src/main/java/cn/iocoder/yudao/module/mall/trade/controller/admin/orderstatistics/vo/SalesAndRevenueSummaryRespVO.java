package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SalesAndRevenueSummaryRespVO {

    @Schema(description = "商品id")
    private Long skuId;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品销量")
    private Long salesVolume;

    @Schema(description = "商品销售额")
    private BigDecimal salesRevenue;
}
