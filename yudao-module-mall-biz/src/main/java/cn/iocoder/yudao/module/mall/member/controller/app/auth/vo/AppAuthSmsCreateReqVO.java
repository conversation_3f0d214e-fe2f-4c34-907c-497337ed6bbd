package cn.iocoder.yudao.module.mall.member.controller.app.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;


@Schema(description = "用户 APP - 手机 + 验证码创建并登录 Request VO,如果登录并绑定社交用户，需要传递 social 开头的参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppAuthSmsCreateReqVO extends AppAuthSmsLoginReqVO {

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "buzhidao")
    @NotEmpty(message = "密码不能为空")
    private String password;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "小王")
    private String name;
}
