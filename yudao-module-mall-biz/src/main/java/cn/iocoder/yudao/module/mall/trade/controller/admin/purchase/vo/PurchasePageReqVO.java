package cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 采购分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchasePageReqVO extends PageParam {

    @Schema(description = "采购单id")
    private Long id;

    @Schema(description = "审批流单据号")
    private String bpmNo;

    @Schema(description = "状态，0-生效，1-用户取消，2-系统取消，3-后台取消，4-被驳回", example = "1")
    private Integer status;

    @Schema(description = "采购人工号")
    private String userNo;

    @Schema(description = "采购人姓名")
    private String userName;

    @Schema(description = "采购人用户ID（外键）")
    private Long userId;

    @Schema(description = "项目编号")
    private String projectNo;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "采购状态")
    private Integer auditStatus;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "商品SKU")
    private String skuId;

    @Schema(description = "商品SKU名称")
    private String skuName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
