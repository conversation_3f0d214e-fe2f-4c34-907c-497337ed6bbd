package cn.iocoder.yudao.module.mall.product.convert.skusummary;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.yudao.module.mall.product.controller.admin.skusummary.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusummary.SkuSummaryDO;

/**
 * 供应商上架商品数统计 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SkuSummaryConvert {

    SkuSummaryConvert INSTANCE = Mappers.getMapper(SkuSummaryConvert.class);

    SkuSummaryDO convert(SkuSummaryCreateReqVO bean);

    SkuSummaryDO convert(SkuSummaryUpdateReqVO bean);

    SkuSummaryRespVO convert(SkuSummaryDO bean);

    List<SkuSummaryRespVO> convertList(List<SkuSummaryDO> list);

    PageResult<SkuSummaryRespVO> convertPage(PageResult<SkuSummaryDO> page);

    List<SkuSummaryExcelVO> convertList02(List<SkuSummaryDO> list);

}
