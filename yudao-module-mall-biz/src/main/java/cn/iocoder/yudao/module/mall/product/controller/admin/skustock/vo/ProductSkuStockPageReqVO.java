package cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - sku库存分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSkuStockPageReqVO extends PageParam {

    @Schema(description = "供应商id")
    private Long supplierId;

    @Schema(description = "SKU ID")
    private Long skuId;

    @Schema(description = "库存")
    private Integer stock;

    @Schema(description = "预警库存")
    private Integer warnStock;

    @Schema(description = "预占库存")
    private Integer reserveStock;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
