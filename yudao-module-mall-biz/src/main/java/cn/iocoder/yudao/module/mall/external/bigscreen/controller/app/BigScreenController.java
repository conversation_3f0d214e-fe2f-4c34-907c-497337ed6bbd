package cn.iocoder.yudao.module.mall.external.bigscreen.controller.app;


import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.external.bigscreen.service.BigScreenService;
import cn.iocoder.yudao.module.mall.external.bigscreen.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "大屏")
@RestController
@RequestMapping("/mall/bigscreen")
@Validated
public class BigScreenController {

    @Resource
    private BigScreenService bigScreenService;

    /**
     * 用户统计
     * @return
     */
    @GetMapping("getUserSummary")
    @Operation(summary = "用户统计：用户总数、已登录用户数")
    public CommonResult<BigScreenUserSummaryRespVO> getUserSummary() {
        BigScreenUserSummaryRespVO bigScreenUserSummaryRespVO = bigScreenService.getUserSummary();
        return CommonResult.success(bigScreenUserSummaryRespVO);
    }

    /**
     * 订单统计
     * @return
     */
    @GetMapping("getOrderSummary")
    @Operation(summary = "订单统计：今日订单数量、今日订单金额、总订单数量、总订单金额")
    public CommonResult<BigScreenOrderSummaryRespVO> getOrderSummary() {
        BigScreenOrderSummaryRespVO bigScreenOrderSummaryRespVO = bigScreenService.getOrderSummary();
        return CommonResult.success(bigScreenOrderSummaryRespVO);
    }

    /**
     * 财务审批单统计
     * @return
     */
    @GetMapping("getApprovalOrderSummary")
    @Operation(summary = "财务审批单统计：待审批订单数量、审批通过订单数量、审核驳回订单数量、待结算订单数、待结算金额")
    public CommonResult<BigScreenApprovalOrderSummaryRespVO> getApprovalOrderSummary() {
        BigScreenApprovalOrderSummaryRespVO bigScreenApprovalOrderSummaryRespVO = bigScreenService.getApprovalOrderSummary();
        return CommonResult.success(bigScreenApprovalOrderSummaryRespVO);
    }

    /**
     * 实时订单列表
     * @return
     */
    @GetMapping("getRealTimeOrderList")
    @Operation(summary = "实时订单列表，统计最近20条订单订单")
    public CommonResult<List<BigScreenRealTimeOrderRespVO>> getRealTimeOrderList() {
        List<BigScreenRealTimeOrderRespVO> bigScreenServiceRealTimeOrderList = bigScreenService.getRealTimeOrderList();
        return CommonResult.success(bigScreenServiceRealTimeOrderList);
    }

    /**
     * 商品统计
     * @return
     */
    @GetMapping("getProductSummary")
    @Operation(summary = "商品统计：商品数量、当天采购商品数、总采购商品数")
    public CommonResult<BigScreenProductSummaryRespVO> getProductSummary() {
        BigScreenProductSummaryRespVO bigScreenServiceProductSummary = bigScreenService.getProductSummary();
        return CommonResult.success(bigScreenServiceProductSummary);
    }

    /**
     * 供应商商品数量排行
     * @return
     */
    @GetMapping("getSupplierProductSummary")
    @Operation(summary = "供应商商品数量排行")
    public CommonResult<List<BigScreenSupplierProductSummaryRespVO>> getSupplierProductSummary() {
        List<BigScreenSupplierProductSummaryRespVO> bigScreenSupplierProductSummaries = bigScreenService.getSupplierProductSummary();
        return CommonResult.success(bigScreenSupplierProductSummaries);
    }

    /**
     * 供应商商品占比
     * @return
     */
    @GetMapping("getSupplierProductProportion")
    @Operation(summary = "供应商商品占比")
    public CommonResult<List<BigScreenSupplierProductProportionRespVO>> getSupplierProductProportion() {
        List<BigScreenSupplierProductProportionRespVO> bigScreenSupplierProductProportionRespVOS = bigScreenService.getSupplierProductProportion();
        return CommonResult.success(bigScreenSupplierProductProportionRespVOS);
    }

    /**
     * 商品分类占比
     * @return
     */
    @GetMapping("getProductCategoryProportion")
    @Operation(summary = "商品分类占比")
    public CommonResult<List<BigScreenProductCategoryProportionRespVO>> getProductCategoryProportion() {
        List<BigScreenProductCategoryProportionRespVO> bigScreenProductCategoryProportionRespVOS = bigScreenService.getProductCategoryProportion();
        return CommonResult.success(bigScreenProductCategoryProportionRespVOS);
    }

    /**
     * 采购商品品类排行
     * @return
     */
    @GetMapping("getSellProductCategorySummary")
    @Operation(summary = "采购商品品类排行")
    public CommonResult<List<BigScreenSellProductCategorySummaryRespVO>> getSellProductCategorySummary() {
        List<BigScreenSellProductCategorySummaryRespVO> bigScreenSellProductCategorySummaryRespVOS = bigScreenService.getSellProductCategorySummary();
        return CommonResult.success(bigScreenSellProductCategorySummaryRespVOS);
    }

    /**
     * 资产建档统计
     * @return
     */
    @GetMapping("getAssetArchiveSummary")
    @Operation(summary = "资产建档统计")
    public CommonResult<BigScreenAssetArchiveSummaryRespVO> getAssetArchiveSummary() {
        BigScreenAssetArchiveSummaryRespVO bigScreenAssetArchiveSummaryRespVO = bigScreenService.getAssetArchiveSummary();
        return CommonResult.success(bigScreenAssetArchiveSummaryRespVO);
    }
}
