package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 订单导出excel VO
 */
@Schema(description = "管理后台 - 账单导出excel VO")
@Data
public class BillExportVO {
    @Schema(description = "账单编号")
    private Long billId;

    @Schema(description = "平台订单号")
    @Excel(needMerge = true, name = "平台订单号", orderNum = "1",width = 24)
    private String orderNo;

    @Schema(description = "外部订单号")
    @Excel(needMerge = true, name = "外部订单号", orderNum = "2",width = 24, replace = {"--_null"})
    private String thirdOrderId;

    @Schema(description = "供应商名称")
    @Excel(name = "供应商名称", orderNum = "3",width = 20)
    private String supplierName;

    @Schema(description = "学院部门")
    @Excel(needMerge = true, name = "学院部门", orderNum = "4",width = 15)
    private String deptName;

    @Schema(description = "订单时间")
    @Excel(needMerge = true, name = "订单时间", orderNum = "5", exportFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", replace = {"--_null"}, width = 24)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime submitTime;

    @Schema(description = "完成时间")
    @Excel(needMerge = true, name = "完成时间", orderNum = "6", exportFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", replace = {"--_null"}, width = 24)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime finishTime;

    @Schema(description = "商品金额")
    @Excel(needMerge = true, name = "商品金额", orderNum = "7",width = 15)
    private BigDecimal productPrice;

    @Schema(description = "运费")
    @Excel(needMerge = true, name = "运费", orderNum = "8",width = 15, replace = {"0_null"})
    private BigDecimal deliveryPrice;

    @Schema(description = "退款金额")
    @Excel(needMerge = true, name = "退款金额", orderNum = "9",width = 15, replace = {"0_null"})
    private BigDecimal refundPrice;

    @Schema(description = "账期退款支付")
    @Excel(name = "账期退款支付", orderNum = "10", width = 20)
    private BigDecimal periodRefundPrice;

    @Schema(description = "账期支付")
    @Excel(name = "账期支付", orderNum = "11", width = 20)
    private BigDecimal periodPayPrice;

    @Schema(description = "个人支付退款金额")
    @Excel(name = "个人支付退款金额", orderNum = "12", width = 20)
    private BigDecimal personalRefundPrice;

    @Schema(description = "个人支付金额")
    @Excel(name = "个人支付金额", orderNum = "13", width = 20)
    private BigDecimal personalPayPrice;

    @Schema(description = "实际金额")
    @Excel(needMerge = true, name = "实际金额", orderNum = "14",width = 15, replace = {"0_null"})
    private BigDecimal realPrice;

    @Schema(description = "发票状态")
    @Excel(needMerge = true, name = "发票状态", orderNum = "15", replace = {"未开票_0", "已申请开票_1", "开票完成_2", "开票失败_3", "验真处理中_4", "验真完成_5", "验真失败_6"}, width = 15)
    private Integer invoiceStatus;

    @Schema(description = "固资状态")
    @Excel(name = "固资状态", orderNum = "16", replace = {"建档待处理_0", "建档已提交_1", "无须建档_2", "建档中_3", "建档成功_4", "建档失败_5"}, width = 20)
    private Integer assetStatus;

    @Schema(description = "支付类型")
    @Excel(name = "支付类型", orderNum = "17", replace = {"账期支付_1", "VOP余额支付_2", "线下支付_3", "货到付款_4", "线上支付_5", "积分支付_6", "混合支付_7"}, width = 20)
    private Integer paymentType;

    @Schema(description = "预约单号")
    @Excel(name = "预约单号", orderNum = "18", width = 20)
    private String voucherNo;

    @Schema(description = "结算状态")
    @Excel(name = "结算状态", orderNum = "19", replace = {"未推送_-2", "已取消_0", "已推送_1", "审批中_2", "已审批_3", "已下单_4", "待结算_5", "结算中_6", "已结算_7", "已拆分_11", "已驳回_21"}, width = 20)
    private Integer ycrhStatus;
}
