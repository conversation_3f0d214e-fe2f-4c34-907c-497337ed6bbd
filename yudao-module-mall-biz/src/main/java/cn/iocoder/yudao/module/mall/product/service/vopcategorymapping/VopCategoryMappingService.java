package cn.iocoder.yudao.module.mall.product.service.vopcategorymapping;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopCategoryMappingDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportRespExcelVO;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportTask;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 京东分类映射 Service 接口
 *
 * <AUTHOR>
 */
public interface VopCategoryMappingService extends IService<VopCategoryMappingDO> {

    /**
     * 创建京东分类映射
     * @param createReqVO
     * @param importTask
     * @param importRespList
     * @return
     */
    Long createVopCategoryMapping(VopCategoryMappingCreateReqVO createReqVO, AsyncImportTask importTask, List<AsyncImportRespExcelVO> importRespList);

    /**
     * 更新京东分类映射
     *
     * @param updateReqVO 更新信息
     */
    void updateVopCategoryMapping(@Valid VopCategoryMappingUpdateReqVO updateReqVO);

    /**
     * 删除京东分类映射
     *
     * @param id 编号
     */
    void deleteVopCategoryMapping(Long id);

    /**
     * 获得京东分类映射
     *
     * @param id 编号
     * @return 京东分类映射
     */
    VopCategoryMappingDO getVopCategoryMapping(Long id);

    /**
     * 获得京东分类映射列表
     *
     * @param ids 编号
     * @return 京东分类映射列表
     */
    List<VopCategoryMappingDO> getVopCategoryMappingList(Collection<Long> ids);

    /**
     * 获得京东分类映射分页
     *
     * @param pageReqVO 分页查询
     * @return 京东分类映射分页
     */
    PageResult<VopCategoryMappingDO> getVopCategoryMappingPage(VopCategoryMappingPageReqVO pageReqVO);

    /**
     * 获得京东分类映射列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 京东分类映射列表
     */
    List<VopCategoryMappingDO> getVopCategoryMappingList(VopCategoryMappingExportReqVO exportReqVO);

    /**
     * 导入京东分类映射
     * @param file
     */
    void importExcel(MultipartFile file);
}
