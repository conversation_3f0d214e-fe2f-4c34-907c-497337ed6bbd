package cn.iocoder.yudao.module.mall.external.bpm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.mall.enums.external.enums.BpmAuditStatusEnum;
import cn.iocoder.yudao.module.mall.external.bpm.BpmClientConfig;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmAuditResultDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmCallbackResultDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmReqDTO;
import cn.iocoder.yudao.module.mall.external.bpm.dto.JctBpmAuditResultRespDTO;
import cn.iocoder.yudao.module.mall.external.bpm.enums.BpmClientTypeEnum;
import cn.iocoder.yudao.module.mall.external.xy2.Xy2Client;
import cn.iocoder.yudao.module.mall.external.xy2.dto.*;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseValidateReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 湘雅2审批流封装
 */
@Service
@Slf4j
public class Xy2BpmServiceImpl implements BpmService {

    @Resource
    private Xy2Client xy2Client;
    @Resource
    private BpmClientConfig bpmClientConfig;
    @Resource
    private MemberUserService memberUserService;
    @Resource
    private TradeOrderService tradeOrderService;

    @Override
    public String getClientType() {
        return BpmClientTypeEnum.XY2.getType();
    }

    @Override
    public void validateBeforePush(PurchaseValidateReqVO reqVO) {

    }

    @Override
    public String pushBpmInfo(BpmReqDTO bpmReqDTO) {
        PurchaseDO purchaseDO = bpmReqDTO.getPurchaseDO();
        Xy2PushBpmInfoReqDTO xy2PushBpmInfoReqDTO = createPurchaseOrder(bpmReqDTO);
        xy2Client.pushBpmInfo(xy2PushBpmInfoReqDTO);
        return purchaseDO.getBpmNo();
    }

    @Override
    public BpmCallbackResultDTO handleBpmCallback(String body, Map<String, String> params) {
        log.info("处理湘雅2审批流回调：{}", params);
        BpmCallbackResultDTO bpmCallbackResultDTO = new BpmCallbackResultDTO();
        bpmCallbackResultDTO.setBpmNo(params.get("bpmNo"));
        bpmCallbackResultDTO.setResult(true);
        return bpmCallbackResultDTO;
    }

    @Override
    public BpmAuditResultDTO pullBpmStatus(String bpmNo) {
        Xy2PullBpmInfoRespDTO xy2PullBpmInfoRespDTO = xy2Client.pullBpmStatus(bpmNo);
        if(xy2PullBpmInfoRespDTO != null) {
            BpmAuditResultDTO bpmAuditResultDTO = new BpmAuditResultDTO();
            if(xy2PullBpmInfoRespDTO.getAuditStatus().equals("-1")) {
                bpmAuditResultDTO.setAuditStatus(BpmAuditStatusEnum.PENDING);
            } else if(xy2PullBpmInfoRespDTO.getAuditStatus().equals("1")) {
                bpmAuditResultDTO.setAuditStatus(BpmAuditStatusEnum.PASS);
            } else if(xy2PullBpmInfoRespDTO.getAuditStatus().equals("2")) {
                bpmAuditResultDTO.setAuditStatus(BpmAuditStatusEnum.REJECT);
            } else  {
                return null;
            }

            bpmAuditResultDTO.setAuditResult(processAuditResult(xy2PullBpmInfoRespDTO.getAuditResult()));
            return bpmAuditResultDTO;
        }
        return null;
    }

    @Override
    public void cancelBpmInfo(String bpmNo, String reason, Long userId) {
        xy2Client.cancelBpmInfo(bpmNo, reason);
    }

    /**
     * 构建采购单信息
     * @param bpmReqDTO
     * @return
     */
    private Xy2PushBpmInfoReqDTO createPurchaseOrder(BpmReqDTO bpmReqDTO) {
        List<Xy2OrderDataDTO> ordersData = new ArrayList<>();
        List<Long> orderIds = bpmReqDTO.getOrderIds();
        PurchaseDO purchaseDO = bpmReqDTO.getPurchaseDO();
        // 根据采购单获取订单列表
        List<TradeOrderDO> orders = tradeOrderService.getOrders(orderIds);
        List<TradeOrderItemDO> allOrderItems = tradeOrderService.getOrderItemListByOrderId(orderIds);
        allOrderItems = allOrderItems.stream().filter(item -> item.getSkuPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        Map<Long, List<TradeOrderItemDO>> orderItemMap = CollectionUtils.convertMultiMap(allOrderItems, TradeOrderItemDO::getOrderId);

        for(TradeOrderDO tradeOrderDO : orders) {
            Xy2OrderDataDTO orderData = new Xy2OrderDataDTO();
            List<TradeOrderItemDO> orderItems = orderItemMap.get(tradeOrderDO.getId());
            if(CollUtil.isEmpty(orderItems)) {
                continue;
            }
            List<Xy2SkuDataDTO> skuData = new ArrayList<>();
            for(TradeOrderItemDO tradeOrderItemDO : orderItems){
                Xy2SkuDataDTO skuDataDTO = new Xy2SkuDataDTO();
                skuDataDTO.setSkuId(String.valueOf(tradeOrderItemDO.getSkuId()));
                skuDataDTO.setSkuInnerId(tradeOrderItemDO.getSkuInnerId());
                skuDataDTO.setSkuImg(tradeOrderItemDO.getPicUrl());
                skuDataDTO.setSkuName(tradeOrderItemDO.getSkuName());
                skuDataDTO.setCount(Long.valueOf(tradeOrderItemDO.getCount()));
                skuDataDTO.setSkuPrice(tradeOrderItemDO.getSkuPrice().toPlainString());
                skuData.add(skuDataDTO);
            }
            orderData.setOrderId(tradeOrderDO.getNo());
            if(StringUtils.isNotBlank(tradeOrderDO.getThirdOrderId())){
                orderData.setThirdOrderId(tradeOrderDO.getThirdOrderId());
            } else {
                orderData.setThirdOrderId(tradeOrderDO.getNo());
            }
            orderData.setCreateTime(tradeOrderDO.getSubmitTime());
            orderData.setTotalPrice(tradeOrderDO.getOrderPrice().toPlainString());
            orderData.setReceiverName(tradeOrderDO.getReceiverName());
            orderData.setReceiverMobile(tradeOrderDO.getReceiverMobile());
            orderData.setReceiverAddress(String.format("%s%s%s", tradeOrderDO.getReceiverCityName(), tradeOrderDO.getReceiverCountyName(), tradeOrderDO.getReceiverDetailAddress()));
            orderData.setSupplierId(tradeOrderDO.getSupplierId());
            orderData.setSupplierName(tradeOrderDO.getSupplierName());
            orderData.setSkuData(skuData);
            ordersData.add(orderData);
        }

        Xy2PushBpmInfoReqDTO pushBpmInfoReqDTO = new Xy2PushBpmInfoReqDTO();
        pushBpmInfoReqDTO.setBpmNo(purchaseDO.getBpmNo());

        MemberUserDO memberUserDO = memberUserService.getUser(SecurityFrameworkUtils.getLoginUserId());
        pushBpmInfoReqDTO.setApplicantName(memberUserDO.getName());
        pushBpmInfoReqDTO.setApplicantNo(memberUserDO.getUserNo());

        if(StrUtil.isNotBlank(purchaseDO.getProjectNo())) {
            pushBpmInfoReqDTO.setProjectNo(purchaseDO.getProjectNo());
            pushBpmInfoReqDTO.setProjectName(purchaseDO.getProjectName());
            pushBpmInfoReqDTO.setProjectType(purchaseDO.getProjectType());
            pushBpmInfoReqDTO.setEconomyClass(purchaseDO.getEconomicCode());
            pushBpmInfoReqDTO.setEconomyClassName(purchaseDO.getEconomicName());
        }

        pushBpmInfoReqDTO.setOrdersData(ordersData);
        pushBpmInfoReqDTO.setPurchaseTotalPrice(purchaseDO.getTotalAmount().toPlainString());
        pushBpmInfoReqDTO.setReason(purchaseDO.getPurchaseReason());
        pushBpmInfoReqDTO.setFiles(purchaseDO.getAttachmentList());

        pushBpmInfoReqDTO.setCallbackUrl(bpmClientConfig.buildVoucherNotifyUrl());
        return pushBpmInfoReqDTO;
    }

    /**
     * 审批流结果字段转换
     * @param auditResultDTOS
     * @return
     */
    private String processAuditResult(List<Xy2AuditResultDTO> auditResultDTOS) {
        if(CollUtil.isEmpty(auditResultDTOS)) {
            return null;
        }

        List<JctBpmAuditResultRespDTO> jctDTOS = new ArrayList<>();
        Integer level = 1;
        for (int i = auditResultDTOS.size() - 1; i >= 0; i--) {
            Xy2AuditResultDTO respDTO = auditResultDTOS.get(i);
            JctBpmAuditResultRespDTO jctDTO = new JctBpmAuditResultRespDTO();

            // 审批状态
            jctDTO.setAuditStatus(respDTO.getAuditStatus());
            jctDTO.setAuditResult(respDTO.getAuditResult());

            // 审批人
            jctDTO.setApprovalUserName(respDTO.getApprovalUserName());
            jctDTO.setApprovalUserNo(respDTO.getApprovalUserNo());

            // 审批时间
            jctDTO.setApprovalDate(respDTO.getApprovalTime());
            jctDTO.setApprovalTime(respDTO.getApprovalTime());

            jctDTO.setApprovalLevel(String.valueOf(level++));

            jctDTOS.add(jctDTO);
        }

        return JSON.toJSONString(jctDTOS);
    }

}
