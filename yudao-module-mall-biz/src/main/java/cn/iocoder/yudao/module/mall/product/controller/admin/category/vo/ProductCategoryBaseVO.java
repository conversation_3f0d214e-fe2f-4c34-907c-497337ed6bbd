package cn.iocoder.yudao.module.mall.product.controller.admin.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 商品分类 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ProductCategoryBaseVO {

    @Schema(description = "分类名称")
    @NotNull(message = "分类名称不能为空")
    private String categoryName;

    @Schema(description = "分类图标")
    private String icon;

    @Schema(description = "移动端分类图标")
    private String iconH5;

    @Schema(description = "父级分类ID")
    @NotNull(message = "父分类编号不能为空")
    private Long parentId;

    @Schema(description = "商品分类编号, 以-分隔")
    private String fullCategoryId;

    @Schema(description = "商品分类名称, 以-分隔")
    private String fullCategoryName;

    @Schema(description = "排序")
    private Integer orderSort;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "分类级别")
    private Integer categoryLevel;

    @Schema(description = "分类状态")
    private Integer status;

    @Schema(description = "分类类型")
    private Integer type;

    @Schema(description = "经济分类编码")
    private String economyClass;

    @Schema(description = "固资经济分类编码")
    private String economyClass2;

}
