package cn.iocoder.yudao.module.mall.product.convert.myfootprint;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.Stock;
import cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo.AppMyFootprintCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo.AppMyFootprintPageRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuDetailRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.SkuGoodsPageItem;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.myfootprint.MyFootprintDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Mapper
public interface MyFootprintConvert {

    MyFootprintConvert INSTANCE = Mappers.getMapper(MyFootprintConvert.class);

    MyFootprintDO convert(AppMyFootprintCreateReqVO createReqVO);

    @Mappings(value = {
            @Mapping(source = "skuDetail.skuId", target = "skuId"),
            @Mapping(source = "skuDetail.skuPicUrl", target = "picUrl"),
            @Mapping(source = "skuDetail.skuName", target = "skuName"),
            @Mapping(source = "productSkuStock.stockStateType", target = "stockStateType"),
            @Mapping(source = "productSkuStock.stockStateDesc", target = "stockStateDesc"),
            @Mapping(source = "myFootprint.updateTime", target = "updateTime"),
            @Mapping(source = "myFootprint.id",target = "id")
    })
    AppMyFootprintPageRespVO convert0(AppProductSkuDetailRespVO skuDetail, Stock productSkuStock,MyFootprintDO myFootprint);

    default List<AppMyFootprintPageRespVO> convertList(List<AppProductSkuDetailRespVO> skuDetails, List<Stock> productSkuStocks, List<MyFootprintDO> myFootprints) {
        if (skuDetails == null || myFootprints == null) {
            return null;
        }
        Map<Long, MyFootprintDO> footprintMap = myFootprints.stream().collect(Collectors.toMap(MyFootprintDO::getSkuId, f -> f));
        List<AppMyFootprintPageRespVO> list = new ArrayList<AppMyFootprintPageRespVO>(skuDetails.size());
        for (AppProductSkuDetailRespVO productSkuDetailRespVO : skuDetails) {
            Map<Long, Stock> stockMap = CollectionUtils.convertMap(productSkuStocks, Stock::getSkuId);
            MyFootprintDO myFootprint = footprintMap.get(productSkuDetailRespVO.getSkuId());
            list.add(convert0(productSkuDetailRespVO, stockMap.get(productSkuDetailRespVO.getSkuId()), myFootprint));
        }

        return list;
    }
}