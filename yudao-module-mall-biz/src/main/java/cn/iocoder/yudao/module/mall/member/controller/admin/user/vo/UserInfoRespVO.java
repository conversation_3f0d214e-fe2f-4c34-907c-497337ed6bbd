package cn.iocoder.yudao.module.mall.member.controller.admin.user.vo;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 管理后台 - 用户个人信息 Response VO
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 用户个人信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoRespVO {

    /**
     * 员工id
     */
    private Long id;

    /**
     * 员工编号 三方系统唯一标识
     */
    private String userNo;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 部门编号
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 职务名称
     */
    private String jobTitle;

    /**
     *  人员类型
     */
    private String userType;

    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 帐号状态 0-开启 1-关闭
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 注册 IP
     */
    private String registerIp;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private LocalDateTime loginDate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
