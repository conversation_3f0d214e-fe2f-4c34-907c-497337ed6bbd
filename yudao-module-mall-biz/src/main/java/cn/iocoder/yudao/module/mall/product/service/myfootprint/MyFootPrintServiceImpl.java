package cn.iocoder.yudao.module.mall.product.service.myfootprint;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.member.service.address.AreaService;
import cn.iocoder.yudao.module.mall.product.api.sku.ProductSkuApi;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.AreaDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.CanSaleRespDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.SkuBaseReqDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.Stock;
import cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo.AppMyFootprintCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo.AppMyFootprintPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo.AppMyFootprintPageRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuDetailRespVO;
import cn.iocoder.yudao.module.mall.product.convert.area.AreaConvert;
import cn.iocoder.yudao.module.mall.product.convert.myfootprint.MyFootprintConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.myfootprint.MyFootprintDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.myfootprint.MyFootprintMapper;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.skustock.ProductSkuStockService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.dto.SkuPriceRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.MY_FOOTPRINT_NOT_EXISTS;


@Service
@Validated
public class MyFootPrintServiceImpl extends ServiceImpl<MyFootprintMapper,MyFootprintDO> implements MyFootPrintService {

    @Resource
    private MyFootprintMapper myFootprintMapper;
    @Resource
    private ProductSkuService  productSkuService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private AreaService areaService;
    @Resource
    private ProductSkuApi productSkuApi;
    @Resource
    private VopGoodsService vopGoodsService;
    @Resource
    private ProductSkuStockService productSkuStockService;


    @Override
    public Long addMyFootPrint(AppMyFootprintCreateReqVO addReqVO) {
        //插入前需要去判断 如果当日有相同的sku商品 则不插入
        QueryWrapper<MyFootprintDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sku_id", addReqVO.getSkuId())
                .eq("user_id", addReqVO.getUserId())
                .ge("update_time", LocalDateTime.now().minusMonths(3));
        MyFootprintDO existingFootprint = myFootprintMapper.selectOne(queryWrapper);
        if (existingFootprint != null) {
            existingFootprint.setUpdateTime(LocalDateTime.now());
            myFootprintMapper.updateById(existingFootprint);
            return existingFootprint.getId();
        }
        else{
            MyFootprintDO myFootprintDO = MyFootprintConvert.INSTANCE.convert(addReqVO);
            myFootprintMapper.insert(myFootprintDO);
            return myFootprintDO.getId();
        }
    }

    @Override
    public void deleteMyFootPrint(Long id) {
        validateWishExists(id);
        myFootprintMapper.deleteById(id);
    }

    private void validateWishExists(Long id) {
        if(myFootprintMapper.selectById(id) == null) {
            throw exception(MY_FOOTPRINT_NOT_EXISTS);
        }
    }

    @Override
    public MyFootprintDO getMyFootPrint(Long id) {
        return myFootprintMapper.selectById(id);
    }

    @Override
    public PageResult<AppMyFootprintPageRespVO> getMyFootPrintPage(Long loginUserId, AppMyFootprintPageReqVO pageParam) {
        PageResult<AppMyFootprintPageRespVO> pageResult = new PageResult<>();
        // 构建查询条件
        QueryWrapper<MyFootprintDO> queryWrapper = new QueryWrapper<>();
        //默认查询用户三个月内的数据
        queryWrapper.eq("user_id", loginUserId)
                .ge("update_time", LocalDate.now().minusMonths(3).atStartOfDay())
                .orderByDesc("update_time");

        // 使用MyBatis Plus的分页功能
        Page<MyFootprintDO> page =
                new Page<>(pageParam.getPageIndex(), pageParam.getPageSize());

        // 执行分页查询
        page = myFootprintMapper.selectPage(page, queryWrapper);
        List<MyFootprintDO> respVOList = page.getRecords();

        pageResult.setPageSize(pageParam.getPageSize());
        pageResult.setPageNum(pageParam.getPageIndex());

        pageResult.setTotal(page.getTotal());


        List<Long> skuIds = respVOList.stream().map(MyFootprintDO::getSkuId).collect(Collectors.toList());



        if(CollUtil.isNotEmpty(skuIds)) {
            List<AppProductSkuDetailRespVO> productSkuDetails = productSkuService.getProductSkuDetailListNoSort(skuIds);
            if(CollUtil.isNotEmpty(productSkuDetails)) {
                List<Stock> productSkuStocks = null;
                List<AppProductSkuDetailRespVO> enableProductSkuDetails = productSkuDetails.stream()
                        .filter(productSkuDetailRespVO -> ProductSpuStatusEnum.isEnable(productSkuDetailRespVO.getStatus()))
                        .collect(Collectors.toList());
                if(CollUtil.isNotEmpty(enableProductSkuDetails)) {
                    Map<String, AppProductSkuDetailRespVO> productSkuDetailMap = convertMap(enableProductSkuDetails, AppProductSkuDetailRespVO::getSkuInnerId);
                    Set<Long> enableSkuIds = enableProductSkuDetails.stream().map(AppProductSkuDetailRespVO::getSkuId).collect(Collectors.toSet());

                    // 查询京东商品可售状态以及价格
                    List<AppProductSkuDetailRespVO> jdProductSkus = null;
                    Set<String> enableJdSkuInnerIds = null;
                    //1.先查商品是否可购买 过滤掉不可购买的商品
                    AreaDTO areaDTO = AreaConvert.INSTANCE.convertArea(pageParam.getAreaIds());
                    SupplierDO jdSupplier = supplierService.getSupplierJD();
                    if(jdSupplier != null && jdSupplier.getId() != null) {
                        jdProductSkus = productSkuDetails.stream().filter(productSkuDetailRespVO -> jdSupplier.getId().equals(productSkuDetailRespVO.getSupplierId())).collect(Collectors.toList());
                        enableJdSkuInnerIds = jdProductSkus.stream().map(AppProductSkuDetailRespVO::getSkuInnerId).collect(Collectors.toSet());

                        //京东商品需要再次判断是否可购买
                        if(CollectionUtils.isNotEmpty(jdProductSkus)) {
                            List<SkuBaseReqDTO> skuBaseReqDTOS = jdProductSkus.stream()
                                    .map(productSkuDetailRespVO -> new SkuBaseReqDTO().setSkuId(Long.valueOf(productSkuDetailRespVO.getSkuInnerId())).setSkuNumber(1)).collect(Collectors.toList());
                            List<CanSaleRespDTO> skusAllSaleState = productSkuApi.getJdSkusAllSaleState(skuBaseReqDTOS, areaDTO);
                            List<CanSaleRespDTO> noCanSaleRespDTOS = Optional.ofNullable(skusAllSaleState)
                                    .orElse(Lists.newArrayList()).stream().filter(canSaleRespDTO -> !canSaleRespDTO.getCanPurchase()).collect(Collectors.toList());
                            for (CanSaleRespDTO noCanSaleRespDTO : noCanSaleRespDTOS) {
                                String noCanSaleSkuId = noCanSaleRespDTO.getSkuId().toString();
                                enableJdSkuInnerIds.remove(noCanSaleSkuId);
                                AppProductSkuDetailRespVO productSkuDetailRespVO = productSkuDetailMap.get(noCanSaleSkuId);
                                if(productSkuDetailRespVO == null) {
                                    continue;
                                }
                                productSkuDetailRespVO.setSaleStatus(0);
                                productSkuDetailRespVO.setSaleStatus(0);

                                enableSkuIds.remove(productSkuDetailRespVO.getSkuId());
                            }
                        }
                    }

                    // 实时查询京东商品价格
                    if(CollUtil.isNotEmpty(enableJdSkuInnerIds)) {
                        List<Long> enableSkuInnerIdList = enableJdSkuInnerIds.stream().map(Long::valueOf).collect(Collectors.toList());
                        List<SkuPriceRespDTO> vopGoodsPriceList = vopGoodsService.getVopSellPrice(enableSkuInnerIdList);
                        Map<Long, SkuPriceRespDTO> priceMap = convertMap(vopGoodsPriceList, SkuPriceRespDTO::getSkuId);
                        jdProductSkus.forEach(productSkuDetail -> {
                            if(!NumberUtil.isNumber(productSkuDetail.getSkuInnerId())) {
                                Long innerId = Long.valueOf(productSkuDetail.getSkuInnerId());
                                if(priceMap.containsKey(innerId)) {
                                    productSkuDetail.setSalePrice(priceMap.get(innerId).getSalePrice());
                                    productSkuDetail.setMarketPrice(priceMap.get(innerId).getMarketPrice());
                                }
                            }
                        });
                    }

                    productSkuStocks = productSkuStockService.getProductSkuStocks(new ArrayList<>(enableSkuIds), areaDTO);
                }

                List<AppMyFootprintPageRespVO> skuGoodsPageItems = MyFootprintConvert.INSTANCE.convertList(productSkuDetails, productSkuStocks, respVOList);
                pageResult.setList(skuGoodsPageItems);
            }
        }

        // 返回分页结果
        return pageResult;
    }

    @Override
    public void batchDeleteMyFootPrint(Long loginUserId) {
        QueryWrapper<MyFootprintDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", loginUserId);
        //执行删除操作
        myFootprintMapper.delete(queryWrapper);
    }

}
