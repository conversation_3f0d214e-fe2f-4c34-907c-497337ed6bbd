package cn.iocoder.yudao.module.mall.member.controller.admin.user.vo;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class MemberUserBaseVO {

    @Schema(description = "员工编号", example = "1")
    @NotEmpty(message = "员工编号不能为空")
    private String userNo;

    @Schema(description = "员工姓名", example = "张三")
    @NotEmpty(message = "员工姓名不能为空")
    private String name;

    @Schema(description = "部门编号", example = "002")
    private String deptCode;

    @Schema(description = "部门名称", example = "行政部")
    private String deptName;

    @Schema(description = "部门ID", example = "123")
    private Long deptId;

    @Schema(description = "职务名称", example = "总经理")
    private String jobTitle;

    @Schema(description = "人员类型", example = "1")
    private String userType;

    @Schema(description = "用户昵称", example = "行政部")
    @NotEmpty(message = "用户昵称不能为空")
    private String nickname;

    @Schema(description = "用户头像", example = "")
    private String avatar;
    /**
     * 帐号状态  0-开启 1-关闭
     *
     * 枚举 {@link CommonStatusEnum}
     */
    @Schema(description = "用户状态", example = "1")
    @NotNull(message = "用户状态不能为空")
    private Integer status;

    @Schema(description = "手机号", example = "13566668888")
    private String mobile;

    @Schema(description = "注册IP", example = "************")
    private String registerIp;

    @Schema(description = "最近登录IP", example = "************")
    private String loginIp;

    @Schema(description = "最近登录时间", example = "13566668888")
    private LocalDateTime loginDate;

}
