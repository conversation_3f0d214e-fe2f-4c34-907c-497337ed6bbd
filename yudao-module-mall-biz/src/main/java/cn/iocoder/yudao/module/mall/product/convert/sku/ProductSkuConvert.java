package cn.iocoder.yudao.module.mall.product.convert.sku;

import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.ProductSkuRespDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.ProductSkuUpdateStockReqDTO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuCreateOrUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuOptionRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuDetailRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppSkuDetailInfo;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppAppProductSkuOpenRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuOpenVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spec.vo.AppProductSpecValueVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skustock.ProductSkuStockDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSkuSpecDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSpuSpecDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.GetSkuPoolInfoGoodsResp;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * 商品 SKU Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSkuConvert {

    ProductSkuConvert INSTANCE = Mappers.getMapper(ProductSkuConvert.class);

    ProductSkuDO convert(ProductSkuCreateOrUpdateReqVO bean);

    default ProductSkuDO convert02(ProductSkuCreateOrUpdateReqVO bean, ProductSpuDO spu) {
        ProductSkuDO item = convert(bean);
        item.setSupplierId(spu.getSupplierId());
        item.setSupplierName(spu.getSupplierName());
        item.setSupplierType(spu.getSupplierType());
        item.setSpuId(spu.getId()).setSpuName(spu.getSpuName());

        return item;
    }

    default List<ProductSkuDO> convert02List(List<ProductSkuCreateOrUpdateReqVO> beans, ProductSpuDO spu) {
        if(CollectionUtils.isEmpty(beans)) {
            return null;
        }
        List<ProductSkuDO> productSkuDOS = new ArrayList<>();
        for (ProductSkuCreateOrUpdateReqVO bean : beans) {
            ProductSkuDO productSkuDO = convert02(bean, spu);
            productSkuDOS.add(productSkuDO);
        }
        return productSkuDOS;
    }
    ProductSkuRespVO convert(ProductSkuDO bean);

    default ProductSkuRespVO convert03(ProductSkuDO bean, ProductSkuStockDO stockDO) {
        ProductSkuRespVO skuRespVO = convert(bean);
        if (stockDO != null) {
            skuRespVO.setStock(stockDO.getStock());
            skuRespVO.setWarnStock(stockDO.getWarnStock());
        }

        return skuRespVO;
    }

    List<ProductSkuRespVO> convertList(List<ProductSkuDO> list);

    List<ProductSkuDO> convertList06(List<ProductSkuCreateOrUpdateReqVO> list);

    default List<ProductSkuDO> convertList06(List<ProductSkuCreateOrUpdateReqVO> list, ProductSpuDO spu) {
        List<ProductSkuDO> result = convertList06(list);
        result.forEach(item -> {
            item.setSupplierId(spu.getSupplierId());
            item.setSpuId(spu.getId()).setSpuName(spu.getSpuName());
        });
        return result;
    }

    List<ProductSkuRespDTO> convertList04(List<ProductSkuDO> list);

    List<ProductSkuOptionRespVO> convertList05(List<ProductSkuDO> skus);

    /**
     * 获得 SPU 的库存变化 Map
     *
     * @param items SKU 库存变化
     * @param skus  SKU 列表
     * @return SPU 的库存变化 Map
     */
    default Map<Long, Integer> convertSpuStockMap(List<ProductSkuUpdateStockReqDTO.Item> items,
                                                  List<ProductSkuDO> skus) {
        Map<Long, Long> skuIdAndSpuIdMap = convertMap(skus, ProductSkuDO::getId, ProductSkuDO::getSpuId); // SKU 与 SKU 编号的 Map 关系
        Map<Long, Integer> spuIdAndStockMap = new HashMap<>(); // SPU 的库存变化 Map 关系
        items.forEach(item -> {
            Long spuId = skuIdAndSpuIdMap.get(item.getId());
            if (spuId == null) {
                return;
            }
            Integer stock = spuIdAndStockMap.getOrDefault(spuId, 0) + item.getIncrCount();
            spuIdAndStockMap.put(spuId, stock);
        });
        return spuIdAndStockMap;
    }


    default List<ProductSkuDO> convertDOList(List<AppProductSkuOpenVO> list, SupplierDO supplierDO, Long spuId, String spuName) {
        if (list == null || list.size() == 0) {
            return null;
        }
        List<ProductSkuDO> productSkuDOS = new ArrayList<>();
        for (AppProductSkuOpenVO appProductSkuOpenVO : list) {
            productSkuDOS.add(convertDO2(appProductSkuOpenVO, supplierDO, spuId, spuName));
        }
        return productSkuDOS;
    }

    default ProductSkuDO convertDO2(AppProductSkuOpenVO vo, SupplierDO supplierDO, Long spuId, String spuName) {
        if (vo == null) {
            return null;
        }
        if(vo.getSkuId() != null && vo.getSkuId() <= 0) {
            vo.setSkuId(null);
        }
        ProductSkuDO productSkuDO = convertDO(vo);
        productSkuDO.setSupplierId(supplierDO.getId());
        productSkuDO.setSupplierType(supplierDO.getType());
        productSkuDO.setSupplierName(supplierDO.getName());
        productSkuDO.setSpuName(spuName);
        productSkuDO.setSpuId(spuId);
        return productSkuDO;
    }

    @Mappings({
            @Mapping(source = "skuId", target = "id")
    })
    ProductSkuDO convertDO(AppProductSkuOpenVO appProductSkuOpenVO);

    @Mappings({
            @Mapping(source = "id", target = "skuId")
    })
    AppAppProductSkuOpenRespVO convertVO(ProductSkuDO productSkuDO);


    @Mappings({
            @Mapping(source = "id", target = "skuId")
    })
    ProductSkuRespDTO convertResp(ProductSkuDO productSkuDO);


    AppSkuDetailInfo convertSkuDetailResp(GetSkuPoolInfoGoodsResp skuPoolInfoGoodsResp);


    @Mappings({
            @Mapping(source = "productSkuDO.id", target = "skuId"),
            @Mapping(source = "productSkuDO.salePrice", target = "salePrice"),
            @Mapping(source = "productSkuDO.marketPrice", target = "marketPrice"),
            @Mapping(source = "productSkuDO.status", target = "status"),
            @Mapping(source = "productSpuDO.spuName", target = "spuName"),
            @Mapping(source = "productSkuDO.supplierId", target = "supplierId"),
            @Mapping(source = "productSkuDO.salesCount", target = "salesCount"),
            @Mapping(source = "productSkuDO.initSalesCount", target = "initSalesCount"),
            @Mapping(source = "productSkuDO.lowestBuy", target = "lowestBuy"),
            @Mapping(source = "productSkuDO.supplierName", target = "supplierName"),
            @Mapping(source = "productSkuDO.supplierType", target = "supplierType"),
            @Mapping(source = "productSkuDO.tenantId", target = "tenantId"),
            @Mapping(source = "productSkuDO.picUrl", target = "skuPicUrl"),
            @Mapping(source = "productSpuDO.picUrl", target = "spuPicUrl"),
            @Mapping(source = "productSkuDO.platformStatus", target = "platformStatus"),
            @Mapping(source = "productSkuDO.showStatus", target = "showStatus"),
    })
    AppProductSkuDetailRespVO convertSkuDetailResp(ProductSkuDO productSkuDO, ProductSpuDO productSpuDO, List<ProductSpuSpecDO> spuSpecValueList, List<ProductSkuSpecDO> skuSpecValueList);


    AppProductSpecValueVO  convertSpecValue(ProductSpuSpecDO productSpuSpecDO);

}
