package cn.iocoder.yudao.module.mall.product.service.myfootprint;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo.AppMyFootprintCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo.AppMyFootprintPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.myfootprint.vo.AppMyFootprintPageRespVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.myfootprint.MyFootprintDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

public interface MyFootPrintService extends IService<MyFootprintDO> {

    Long addMyFootPrint(@Valid AppMyFootprintCreateReqVO addReqVO);


    /**
     * 获得删除足迹
     *
     * @param id 编号
     */
    void deleteMyFootPrint(Long id);


    /**
     * 获得足迹
     *
     * @param id 编号
     * @return 足迹
     */
    MyFootprintDO getMyFootPrint(Long id);

    PageResult<AppMyFootprintPageRespVO> getMyFootPrintPage(Long loginUserId, @Valid AppMyFootprintPageReqVO pageParam);

    void batchDeleteMyFootPrint(Long loginUserId);



}

