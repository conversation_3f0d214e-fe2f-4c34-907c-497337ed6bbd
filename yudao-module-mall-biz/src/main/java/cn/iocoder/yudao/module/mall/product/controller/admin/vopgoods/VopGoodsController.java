package cn.iocoder.yudao.module.mall.product.controller.admin.vopgoods;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.product.controller.admin.vopgoods.vo.SyncVopProductReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.params.GoodsStockInfoReq;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.AreaStockInfoResp;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsDetailResp;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsSearchPageResultResp;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.req.*;
import cn.iocoder.yudao.module.mall.vop.service.VopConfigService;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.checkSkuSaleList.CheckSkuSaleGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getCanCodList.GetCanCodGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getGiftInfoSkuList.GetSkuGiftGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSellPrice.GetSellPriceGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSimilarSkuList.GetSimilarSkuGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.GetSkuPoolInfoGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuImageList.GetSkuImageGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuStateList.GetSkuStateGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getYanbaoSkuList.GetYanbaoSkuGoodsResp;
import com.jd.open.api.sdk.response.vopsp.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * VOP商品相关接口
 * @Description
 * <AUTHOR>
 * @Date 2023/6/9 17:08
 */

@RestController
@RequestMapping("/product/vopgoods")
@Slf4j
@Tag(name = "VOP商品相关接口")
public class VopGoodsController {

    @Autowired
    VopGoodsService vopGoodsService;

    @Autowired
    VopGoodsBridgeService vopGoodsBridgeService;

    @Resource
    private VopConfigService vopConfigService;

    /**
     * 搜索商品
     *
     * @param searchReq
     * @return
     */
    @GetMapping("goodsSearchPageList")
    @Operation(summary = "搜索商品")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<VopGoodsSearchPageResultResp> goodsSearchPageList(VopGoodsSearchReq searchReq) {
        return CommonResult.success(vopGoodsBridgeService.goodsSearchPageList(searchReq));
    }

    /**
     * 查询单个商品的详细信息，包含商品售卖单位、重量、产地、包装清单、主图、规格参数等
     * @param skuDetailReq
     * @return
     */
    @GetMapping("getSkuDetailInfo")
    @Operation(summary = "查询单个商品的详细信息，包含商品售卖单位、重量、产地、包装清单、主图、规格参数等")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<GetSkuPoolInfoGoodsResp> getSkuDetailInfo(VopGoodsGetSkuDetailReq skuDetailReq) {
        VopGoodsGetSkuDetailInfoResponse response = vopGoodsService.getSkuDetailInfo(skuDetailReq);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 查询商品上架、详情图片、可售、权益等
     *
     * @param skuId
     * @return
     */
    @GetMapping("getAssociationSkuDetailInfo")
    @Operation(summary = "查询商品上架、详情图片、可售、权益等")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<VopGoodsDetailResp> getAssociationSkuDetailInfo(@RequestParam(value = "skuId", required = true) Long skuId) {
        return CommonResult.success(vopGoodsBridgeService.getAssociationSkuDetailInfo(skuId));
    }

    /**
     * 查询某个商品的全地址库存
     * @param stockInfoReq
     * @return
     */
    @GetMapping("queryGoodsStockInfo")
    @Operation(summary = "查询商品库存状态等")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<AreaStockInfoResp> queryGoodsStockInfo(@Valid GoodsStockInfoReq stockInfoReq) {
        return CommonResult.success(vopGoodsBridgeService.queryGoodsStockInfo(stockInfoReq));
    }


    /**
     * 查询商品图片，包含商品主图、图片路径等
     * @param skuId
     * @return
     */
    @GetMapping("getSkuImageList")
    @Operation(summary = "查询商品图片，包含商品主图、图片路径等")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<List<GetSkuImageGoodsResp>> getSkuImageList(@RequestParam(value = "skuId", required = true) String skuId) {
        VopGoodsGetSkuImageListResponse response = vopGoodsService.getSkuImageList(skuId);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 验证商品可售性，同时可查询商品是否可开专票、无理由退货类型等
     * @param skuId
     * @return
     */
    @GetMapping("checkSkuSaleList")
    @Operation(summary = "验证商品可售性，同时可查询商品是否可开专票、无理由退货类型等")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<List<CheckSkuSaleGoodsResp>> checkSkuSaleList(@RequestParam(value = "skuId", required = true) String skuId) {
        VopGoodsCheckSkuSaleListResponse response = vopGoodsService.checkSkuSaleList(skuId);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 查询商品上下架状态
     * @param skuId
     * @return
     */
    @GetMapping("getSkuStateList")
    @Operation(summary = "查询商品上下架状态")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<List<GetSkuStateGoodsResp>> getSkuStateList(@RequestParam(value = "skuId", required = true) String skuId) {
        VopGoodsGetSkuStateListResponse response = vopGoodsService.getSkuStateList(skuId);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 批量查询商品售卖价
     * @param skuId
     * @return
     */
    @GetMapping("getSellPrice")
    @Operation(summary = "批量查询商品售卖价")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<List<GetSellPriceGoodsResp>> getSellPrice(@RequestParam(value = "skuId", required = true) String skuId) {
        VopGoodsGetSellPriceResponse response = vopGoodsService.getSellPrice(skuId, null);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 查询相似商品，例如统一款商品的不同颜色
     * @param skuId
     * @return
     */
    @GetMapping("getSimilarSkuList")
    @Operation(summary = "查询相似商品，例如统一款商品的不同颜色")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<List<GetSimilarSkuGoodsResp>> getSimilarSkuList(@RequestParam(value = "skuId", required = true) Long skuId) {
        VopGoodsGetSimilarSkuListResponse response = vopGoodsService.getSimilarSkuList(skuId);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 查询赠品信息，此内容与企业享受权益相关
     * @param goodsGetGiftInfoSkuListReq
     * @return
     */
    @GetMapping("getGiftInfoSkuList")
    @Operation(summary = "查询赠品信息，此内容与企业享受权益相关")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<List<GetSkuGiftGoodsResp>> getGiftInfoSkuList(VopGoodsGetGiftInfoSkuListReq goodsGetGiftInfoSkuListReq) {
        VopGoodsGetGiftInfoSkuListResponse response = vopGoodsService.getGiftInfoSkuList(goodsGetGiftInfoSkuListReq);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 查询延保信息
     * @param yanbaoSkuListReq
     * @return
     */
    @GetMapping("getYanbaoSkuList")
    @Operation(summary = "查询延保信息")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<List<GetYanbaoSkuGoodsResp>> getYanbaoSkuList(VopGoodsGetYanbaoSkuListReq yanbaoSkuListReq) {
        VopGoodsGetYanbaoSkuListResponse response = vopGoodsService.getYanbaoSkuList(yanbaoSkuListReq);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }

    /**
     * 根据四级地址，查询商品在此地区是否支持货到付款，可进行批量查询
     * @param canCodListReq
     * @return
     */
    @GetMapping("getCanCodList")
    @Operation(summary = "根据四级地址，查询商品在此地区是否支持货到付款，可进行批量查询")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<List<GetCanCodGoodsResp>> getCanCodList(VopGoodsGetCanCodListReq canCodListReq) {
        VopGoodsGetCanCodListResponse response = vopGoodsService.getCanCodList(canCodListReq);
        return CommonResult.success(response.getOpenRpcResult().getResult());
    }
    
    /** 
    * 同步商品池内商品总数量
    * @return
    */
    @GetMapping("getSyncTotal")
    @Operation(summary = "同步商品池内商品总数量")
	@PreAuthorize("@ss.hasPermission('vop:query')")
    public CommonResult<Long> goodsPoolSyncCount() {
        Long count = vopGoodsBridgeService.goodsPoolSyncCount("");
        return CommonResult.success(count);
    }

    /**
     * 商品池全量同步
     * @return
     */
    @PostMapping("syncAllGoods")
    @Operation(summary = "同步商品池")
    @PreAuthorize("@ss.hasPermission('vop:sync')")
    public CommonResult<Boolean> goodsPoolSync() {
        Long tenantId = TenantContextHolder.getTenantId();
        if(vopConfigService.getVopConfig().getFullPoolSwitch()){
            vopGoodsBridgeService.fullGoodsPoolSync(tenantId, true);
        }
        else {
            vopGoodsBridgeService.customGoodsPoolSync(tenantId, true);
        }
        return CommonResult.success(true);
    }

    /** 
    * 单个商品同步
    * @Param: [skuId]
    * @return
    */
    @PostMapping("syncSinglleGood")
    @Operation(summary = "单个商品同步")
    @PreAuthorize("@ss.hasPermission('vop:sync')")
    public CommonResult<Boolean> goodsUpdate(@RequestParam(value = "skuId", required = true) Long skuId, String poolName) {
        List<Long> successList = vopGoodsBridgeService.goodsUpdate(false, poolName, skuId);
        return CommonResult.success(successList.contains(skuId));
    }

    /**
     * 批量同步商品
     * @param skuIds
     * @param poolName
     * @return
     */
    @PostMapping("batchSyncGoods")
    @Operation(summary = "批量同步商品")
    @PreAuthorize("@ss.hasPermission('vop:sync')")
    public CommonResult<List<Long>> batchSyncGoods(@RequestParam(value = "skuIds", required = true) List<Long> skuIds, String poolName) {
        List<Long> successList = vopGoodsBridgeService.goodsUpdate(false, poolName, skuIds.toArray(new Long[]{}));
        List<Long> failList = skuIds.stream().filter(skuId -> !successList.contains(skuId)).collect(Collectors.toList());
        return CommonResult.success(failList);
    }

    /**
     * 按分类同步商品
     * categoryIds为空，则同步所有分类
     * @param reqVO
     * @return
     */
    @PostMapping("syncVopProductByCategory")
    @Operation(summary = "按分类同步商品")
    @PreAuthorize("@ss.hasPermission('vop:sync')")
    public CommonResult<Boolean> syncVopProductByCategory(@RequestBody SyncVopProductReqVO reqVO) {
        vopGoodsBridgeService.syncVopProductByCategory(reqVO.getCategoryIds(), reqVO.getMaxPageSize());
        return CommonResult.success(null);
    }
}
