package cn.iocoder.yudao.module.mall.external.assets.mapper;

import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsCreateDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.sibide.ExtSiBiDeAssetsDetailDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.sibide.ExtSiBiDeAssetsStatusDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ExtSibideAssetMapper {

    long selectOrderCount(@Param("orderNo") String orderNo);

    void insertOrder(@Param("data") ExtAssetsCreateDTO data);

    void updateOrder(@Param("data") ExtAssetsCreateDTO data);

    void insertOrderItem(@Param("data") ExtAssetsCreateDTO data);

    void deleteOrderItems(@Param("orderNo") String orderNo, @Param("orderDetailNo") String orderDetailNo);

    List<ExtSiBiDeAssetsStatusDTO> selectAssetStatus(@Param("params") Map<String, Object> params);

    List<ExtSiBiDeAssetsStatusDTO> selectAssetStatusV2(@Param("params") Map<String, Object> params);

    List<ExtSiBiDeAssetsDetailDTO> selectAssetList(@Param("params") Map<String, Object> params);

}
