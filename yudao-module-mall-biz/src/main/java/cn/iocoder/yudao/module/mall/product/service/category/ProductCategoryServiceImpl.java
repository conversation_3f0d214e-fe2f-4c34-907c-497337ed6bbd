package cn.iocoder.yudao.module.mall.product.service.category;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.category.ProductCategoryConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.category.ProductCategoryMapper;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryLevelEnum;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryTypeEnum;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskUtils;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportRespExcelVO;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportTask;
import cn.iocoder.yudao.module.system.enums.CommonConstants;
import com.alibaba.ttl.TtlRunnable;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.yudao.module.mall.basis.dal.redis.RedisKeyConstants.MALL_PRODUCT_CATEGORY;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.ASYNC_TASK_BUSY_FAIL;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.*;

/**
 * 商品分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProductCategoryServiceImpl extends ServiceImpl<ProductCategoryMapper, ProductCategoryDO> implements ProductCategoryService {

    @Resource
    private ProductCategoryMapper productCategoryMapper;
    @Resource
    private ConfigProductCategoryService configProductCategoryService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private ThreadPoolExecutor threadPoolExecutor;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    @Lazy
    private ProductSkuService productSkuService;
    @Resource
    private AsyncFrontTaskUtils asyncFrontTaskUtils;

    private String getCacheKey() {
        return MALL_PRODUCT_CATEGORY + ":" + TenantContextHolder.getTenantId();
    }

    private void deleteCache() {
        String lockKey = "lock:" + getCacheKey();
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        try {
            redisTemplate.delete(getCacheKey());
        } catch(Exception e) {
            log.error("商品分类缓存删除异常:", e);
        } finally {
            lock.unlock();
        }
    }

    private List<ProductCategoryDO> getCacheList() {
        String lockKey = "lock:" + getCacheKey();
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        try {
            String dataKey = getCacheKey();
            long size = redisTemplate.opsForList().size(dataKey);
            List<ProductCategoryDO> list = null;
            if(size > 0) {
                list = redisTemplate.opsForList().range(dataKey, 0 , size - 1);
            } else {
                list = productCategoryMapper.selectList();
                if(CollUtil.isNotEmpty(list)) {
                    redisTemplate.opsForList().rightPushAll(dataKey, list);
                    redisTemplate.expire(dataKey, 5, TimeUnit.DAYS);
                }
            }
            return list;
        } catch(Exception e) {
            log.error("getCacheList error", e);
        } finally {
            lock.unlock();
        }

        return new ArrayList<>();
    }


    @Override
    public Long createCategory(ProductCategoryCreateReqVO createReqVO) {
        // 校验父分类存在
        ProductCategoryDO parentCategory = validateParentProductCategory(createReqVO.getParentId());
        // 校验分类业务ID是否唯一
        validateUniqueCategoryId(null, createReqVO.getCategoryId());
        // 插入
        ProductCategoryDO category = ProductCategoryConvert.INSTANCE.convert(createReqVO);
        if(createReqVO.getType() == null) {
            category.setType(ProductCategoryTypeEnum.SELF.getType());
        }
        // 通过上级分类处理上下级字段
        buildFullCategoryIdAndName(parentCategory, category);
        if(parentCategory == null) {
            category.setParentId(0L);
            category.setCategoryLevel(0);
        } else {
            category.setParentId(parentCategory.getCategoryId());
            category.setCategoryLevel(parentCategory.getCategoryLevel() + 1);
        }
        productCategoryMapper.insert(category);

        this.deleteCache();
        // 返回
        return category.getId();
    }

    private void buildFullCategoryIdAndName(ProductCategoryDO parentCategory, ProductCategoryDO curCategory) {
        if(parentCategory == null) {
            curCategory.setFullCategoryName(curCategory.getCategoryName());
            curCategory.setFullCategoryId(curCategory.getCategoryId().toString());
        } else {
            curCategory.setFullCategoryName(parentCategory.getFullCategoryName() + "-" + curCategory.getCategoryName());
            curCategory.setFullCategoryId(parentCategory.getFullCategoryId() + "-" + curCategory.getCategoryId());
        }
    }

    @Override
    public void cloneCategory(@Valid ProductCategoryCloneReqVO cloneReqVO) {
        List<ProductCategoryDO> cacheList = getCacheList();
        List<Long> persistIdList = cacheList.stream().map(ProductCategoryDO::getCategoryId).collect(Collectors.toList());
        Set<Long> freshCategoryIdSet = cloneReqVO.getCategoryIdList().stream().filter(id -> !CollectionUtil.contains(persistIdList, id)).collect(
                Collectors.toSet());
        List<ConfigProductCategoryDO> configList = configProductCategoryService.getCategoryListByCategoryId(freshCategoryIdSet);
        configList.forEach(item -> {
            item.setId(null);
            item.setCreateTime(null);
            item.setCreator(null);
            item.setUpdateTime(null);
            item.setUpdater(null);
        });

        List<ProductCategoryDO> insertList = ProductCategoryConvert.INSTANCE.convertList09(configList);
        productCategoryMapper.insertBatch(insertList);
        this.deleteCache();
    }

    @Override
    public void updateCategory(ProductCategoryUpdateReqVO updateReqVO) {
        // 校验父分类存在
        ProductCategoryDO parentCategory = validateParentProductCategory(updateReqVO.getParentId());
        // 校验分类是否存在
        validateProductCategoryExists(updateReqVO.getId());
        // 校验分类业务ID是否唯一
        validateUniqueCategoryId(updateReqVO.getId(), updateReqVO.getCategoryId());
        // 更新
        ProductCategoryDO updateObj = ProductCategoryConvert.INSTANCE.convert(updateReqVO);
        buildFullCategoryIdAndName(parentCategory, updateObj);

        productCategoryMapper.updateById(updateObj);

        this.deleteCache();
    }

    @Override
    public void deleteCategory(Long id) {
        // 校验分类是否存在
        validateProductCategoryExists(id);

        // TODO 芋艿 补充只有不存在商品才可以删除
        // 删除
        productCategoryMapper.deleteById(id);
        this.deleteCache();
    }

    @Override
    public void deleteCategoryWithChildren(Long categoryId) {
        Set<Long> categoryIdSet = new HashSet<>();
        List<ProductCategoryDO> cacheList = getCacheList();
        computeChildrenIds(categoryId, categoryIdSet, cacheList);
        categoryIdSet.add(categoryId);
        // 删除
        productCategoryMapper.delete(Wrappers.lambdaQuery(ProductCategoryDO.class).in(ProductCategoryDO::getCategoryId, categoryIdSet));
        this.deleteCache();
    }

    private ProductCategoryDO validateParentProductCategory(Long id) {
        // 如果是根分类，无需验证
        if (Objects.equals(id, ProductCategoryDO.PARENT_ID_NULL)) {
            return null;
        }
        // 父分类不存在
        ProductCategoryDO category = productCategoryMapper.selectByCategoryId(id);
        if (category == null) {
            throw exception(CATEGORY_PARENT_NOT_EXISTS);
        }
        return category;
    }

    private void validateProductCategoryExists(Long id) {
        ProductCategoryDO category = productCategoryMapper.selectById(id);
        if (category == null) {
            throw exception(CATEGORY_NOT_EXISTS);
        }
    }

    private void validateUniqueCategoryId(Long id, Long categoryId) {
        // 父分类不存在
        ProductCategoryDO category = productCategoryMapper.selectByCategoryId(categoryId);
        if(category == null) {
            return;
        }
        if(id == null || !category.getId().equals(id)) {
            throw exception(CATEGORY_ID_EXISTS);
        }
    }

    @Override
    public ProductCategoryDO getCategory(Long id) {
        return productCategoryMapper.selectById(id);
    }

    @Override
    public List<ProductCategoryDO> getByCategoryIds(Collection<Long> categoryIds) {
        return productCategoryMapper.getByCategoryIds(categoryIds);
    }

    @Override
    public ProductCategoryDO getByCategoryId(Long categoryId) {
        return productCategoryMapper.selectByCategoryId(categoryId);
    }

    @Override
    public String getEconomyClassByCategoryId(Long categoryId) {
        ProductCategoryDO categoryDO = getByCategoryId(categoryId);
        if(categoryDO != null) {
            return categoryDO.getEconomyClass();
        }

        return null;
    }

    @Override
    public Map<Long, String> getEconomyClassMapByCategoryId(Collection<Long> categoryIds) {
        if(CollUtil.isEmpty(categoryIds)) {
            return MapUtil.empty();
        }
        List<ProductCategoryDO> categoryDOList = getByCategoryIds(categoryIds);
        categoryDOList = categoryDOList.stream().filter(item -> StrUtil.isNotBlank(item.getEconomyClass())).collect(Collectors.toList());
        if(CollUtil.isEmpty(categoryDOList)) {
            return MapUtil.empty();
        }
        return convertMap(categoryDOList, ProductCategoryDO::getCategoryId, ProductCategoryDO::getEconomyClass);
    }

    @Override
    public Map<Long, String> getEconomyClassMapByCategoryIdV2(Collection<SimpleCategoryVO> categoryVOS) {
        if(CollUtil.isEmpty(categoryVOS)) {
            return MapUtil.empty();
        }
        List<Long> categoryIds  = categoryVOS.stream().map(SimpleCategoryVO::getCategoryId).collect(Collectors.toList());
        List<ProductCategoryDO> categoryDOList = getByCategoryIds(categoryIds);
        if(CollUtil.isEmpty(categoryDOList)) {
            return MapUtil.empty();
        }

        Map<Long, Boolean> categoryAssetMap = convertMap(categoryVOS, SimpleCategoryVO::getCategoryId, SimpleCategoryVO::getIsAsset);
        Map<Long, String> map = new HashMap<>();
        categoryDOList.forEach(categoryDO -> {
            Boolean isAsset = categoryAssetMap.get(categoryDO.getCategoryId());
            if(isAsset != null && isAsset && StrUtil.isNotBlank(categoryDO.getEconomyClass2())) {
                map.put(categoryDO.getCategoryId(), categoryDO.getEconomyClass2());
            } else {
                map.put(categoryDO.getCategoryId(), categoryDO.getEconomyClass());
            }
        });

        return map;
    }

    @Override
    public String getNameByCategoryId(Long categoryId) {
        if(categoryId == null) {
            return null;
        }
        ProductCategoryDO categoryDO = getByCategoryId(categoryId);
        if(categoryDO != null) {
            return categoryDO.getCategoryName();
        }
        ConfigProductCategoryDO configCategoryDO = configProductCategoryService.getCategoryByCategoryId(categoryId);
        if(configCategoryDO != null) {
            return configCategoryDO.getCategoryName();
        }

        return categoryId.toString();
    }

    @Override
    public ProductCategoryDO getByCategoryName(Integer type, String categoryName) {
        List<ProductCategoryDO> list = productCategoryMapper.selectList(Wrappers.lambdaQuery(ProductCategoryDO.class)
                .eq(ProductCategoryDO::getCategoryName, categoryName)
                .eq(ProductCategoryDO::getType, type).orderByAsc(ProductCategoryDO::getId));
        return list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public List<ProductCategoryDO> getParentsByCategoryIds(List<Long> categoryIds) {
        Set<Long> currentCategoryIds = new HashSet<>(categoryIds);
        List<ProductCategoryDO> productCategoryDOS = new ArrayList<>();
        while (CollectionUtils.isNotEmpty(currentCategoryIds)) {
            if(CollUtil.isNotEmpty(productCategoryDOS)) {
                Set<Long> foundIds = productCategoryDOS.stream().map(cate -> cate.getCategoryId()).collect(Collectors.toSet());
                currentCategoryIds.removeAll(foundIds);
            }
            if(CollUtil.isEmpty(currentCategoryIds)) {
                break;
            }
            List<ProductCategoryDO> productCategoryDOList = productCategoryMapper.getByCategoryIds(currentCategoryIds);
            if(CollectionUtils.isEmpty(productCategoryDOList)) {
                break;
            }
            productCategoryDOS.addAll(productCategoryDOList);
            currentCategoryIds = productCategoryDOList.stream()
                    .filter(productCategoryDO -> !Long.valueOf(0).equals(productCategoryDO.getParentId()))
                    .map(ProductCategoryDO::getParentId)
                    .collect(Collectors.toSet());
        }
        return productCategoryDOS;
    }

    // 缓存可能需要更新，不从缓存获取数据
    public void getParentCategoryList(Long categoryId, List<ProductCategoryDO> parentCategoryList) {
        ProductCategoryDO categoryDO = getByCategoryId(categoryId);
        if(categoryDO.getCategoryLevel() == 0){
            return;
        }
        ProductCategoryDO parentCategory = getByCategoryId(categoryDO.getParentId());
        parentCategoryList.add(parentCategory);
        getParentCategoryList(parentCategory.getCategoryId(), parentCategoryList);
    }

    @Override
    public List<ProductCategoryDO> getListFromCurrent2Root(Long categoryId) {
        List<ProductCategoryDO> cacheList = getCacheList();
        List<ProductCategoryDO> list = new ArrayList<>();

        ProductCategoryDO currentCategory = cacheList.stream().filter(cate -> cate.getCategoryId().equals(categoryId)).findFirst().orElse(null);
        if(currentCategory == null) {
            return list;
        }
        list.add(currentCategory);
        Long parentId = currentCategory.getParentId();
        while(parentId != null && parentId > 0) {
            Long finalParentId = parentId;
            ProductCategoryDO parent = cacheList.stream().filter(cate -> cate.getCategoryId().equals(finalParentId)).findFirst().orElse(null);
            if(parent != null) {
                list.add(parent);
                parentId = parent.getParentId();
            } else {
                break;
            }
        }

        return list;
    }

    @Override
    public boolean isAllEnable(Set<Long> categoryIds) {
        List<ProductCategoryDO> cacheList = getCacheList();
        List<ProductCategoryDO> hitList = cacheList.stream().filter(cate -> categoryIds.contains(cate.getCategoryId())).collect(Collectors.toList());
        return categoryIds.size() == hitList.size() && hitList.stream().allMatch(cate -> ProductCategoryStatusEnum.ENABLE.getStatus().equals(cate.getStatus()));
    }

    @Override
    public Set<Long> getDisabledIdList() {
        List<ProductCategoryDO> cacheList = getCacheList();
        return cacheList.stream().filter(cate -> !ProductCategoryStatusEnum.isEnable(cate.getStatus())).map(ProductCategoryDO::getCategoryId).collect(Collectors.toSet());
    }

    @Override
    public void validateCategory(Long categoryId) {
        ProductCategoryDO category = productCategoryMapper.selectByCategoryId(categoryId);
        if (category == null) {
            throw exception(CATEGORY_NOT_EXISTS);
        }
        if (Objects.equals(category.getStatus(), ProductCategoryStatusEnum.DISABLE.getStatus())) {
            throw exception(CATEGORY_DISABLED, category.getCategoryName());
        }
    }

    @Override
    public Integer getCategoryLevel(Long id) {
        if (Objects.equals(id, ProductCategoryDO.PARENT_ID_NULL)) {
            return 0;
        }
        int level = 1;
        for (int i = 0; i < 100; i++) {
            ProductCategoryDO category = productCategoryMapper.selectById(id);
            // 如果没有父节点，break 结束
            if (category == null
                    || Objects.equals(category.getParentId(), ProductCategoryDO.PARENT_ID_NULL)) {
                break;
            }
            // 继续递归父节点
            level++;
            id = category.getParentId();
        }
        return level;
    }

    @Override
    public List<ProductCategoryDO> getAllCategoryList(ProductCategoryListReqVO listReqVO) {
        List<ProductCategoryDO> cacheList = getCacheList();
        List<ProductCategoryDO> list = cacheList.stream().filter((item) -> {
            boolean result = true;
            if(listReqVO.getStatus() !=  null) {
                result = item.getStatus().equals(listReqVO.getStatus());
                if(!result) {
                    return false;
                }
            }
            if(StrUtil.isNotBlank(listReqVO.getName())) {
                result = result && StrUtil.contains(item.getCategoryName(), listReqVO.getName());
                if(!result) {
                    return false;
                }
            }
            if(listReqVO.getCategoryId() != null) {
                result = result && item.getCategoryId().equals(listReqVO.getCategoryId());
                if(!result) {
                    return false;
                }
            }
            if(StrUtil.isNotBlank(listReqVO.getEconomyClass())) {
                result = result && StrUtil.contains(item.getEconomyClass(), listReqVO.getEconomyClass());
                if(!result) {
                    return false;
                }
            }
            if(listReqVO.getHaveEconomyClass() != null) {
                if(listReqVO.getHaveEconomyClass()) {
                    result = result && StrUtil.isNotBlank(item.getEconomyClass());
                } else {
                    result = result && StrUtil.isBlank(item.getEconomyClass());
                }
            }
            return result;
        }).collect(Collectors.toList());

        CollectionUtil.sort(list, (obj1 ,obj2) -> obj1.getOrderSort() - obj2.getOrderSort());
        return list;
    }

    /**
     * 查询一级大类
     *
     * @return
     */
    @Override
    public List<ProductCategoryDO> getRootCategoryList(ProductCategoryStatusEnum status) {
        List<ProductCategoryDO> cacheList = getCacheList();
        List<ProductCategoryDO> list = cacheList.stream().filter(item -> Objects.equals(item.getCategoryLevel(), ProductCategoryLevelEnum.LAVEL1.getLevel()))
                .collect(Collectors.toList());
        if(status != null) {
            list = list.stream().filter(item -> item.getStatus().equals(status.getStatus())).collect(Collectors.toList());
        }
        CollectionUtil.sort(list, Comparator.comparingInt(ProductCategoryDO::getOrderSort));
        return list;
    }

    @Override
    public Integer getCategoryTotalCount() {
        return getCacheList().size();
    }

    @Override
    public void updateCategoryStatus(Long id, ProductCategoryStatusEnum status) {
        // 校验分类是否存在
        validateProductCategoryExists(id);

        String lockKey = String.format("lock:product-category-status:%d:%d", TenantContextHolder.getTenantId(), id);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            Assert.isTrue(lock.tryLock(2, 60, TimeUnit.SECONDS), "当前任务正在处理中，请稍后再试");
            ProductCategoryDO updateObj = new ProductCategoryDO();
            updateObj.setId(id);
            updateObj.setStatus(status.getStatus());
            productCategoryMapper.updateById(updateObj);
            this.deleteCache();

            threadPoolExecutor.execute(TtlRunnable.get(() -> {
                try {
                    productSkuService.updateSkuIndexByCategoryStatus(productCategoryMapper.selectById(id));
                } catch (Exception e) {
                    log.error("updateCategoryStatus task error: ", e);
                }
            }));
        } catch (Exception e) {
            throw exception(ASYNC_TASK_BUSY_FAIL, e.getMessage());
        }
    }

    @Override
    public String getNamePath(String idPath, String seperator) {
        if(StringUtils.isBlank(idPath)) {
            return null;
        }

        String[] ids = StringUtils.split(idPath, ",");
        List<Long> idList = Arrays.stream(ids).map(item -> Long.valueOf(item)).collect(Collectors.toList());

        return getNamePath(idList, seperator);
    }

    @Override
    public String getNamePath(List<Long> ids, String seperator) {
        if(CollUtil.isEmpty(ids)) {
            return null;
        }
        if(StringUtils.isBlank(seperator)) {
            seperator = "/";
        }
        List<ProductCategoryDO> cacheList = getCacheList();
        List<String> nameList = Lists.newArrayList();
        for(Long id : ids) {
            Optional<ProductCategoryDO> opt = cacheList.stream().filter(item -> ObjectUtil.equal(id, item.getCategoryId())).findFirst();
            if(opt.isPresent()) {
                nameList.add(opt.get().getCategoryName());
            } else {
                // 本地分类没有时，尝试从全局分类池查找
                ConfigProductCategoryDO configCategory = configProductCategoryService.getCategoryByCategoryId(Long.valueOf(id));
                if(configCategory != null) {
                    nameList.add(configCategory.getCategoryName());
                } else {
                    nameList.add(id.toString());
                }
            }
        }

        return CollectionUtil.join(nameList, seperator);
    }

    @Override
    public boolean validateCategoryIds(List<Long> ids) {
        List<ProductCategoryDO> cacheList = getCacheList();
        long hitSize = cacheList.stream().filter(item -> ids.contains(item.getCategoryId())).count();
        if(hitSize != ids.size()) {
            return false;
        }

        List<ProductCategoryDO> productCategoryDOS = new ArrayList<>();
        for (int i = ids.size() - 1; i >= 1; i--) {
            Long categoryId = ids.get(i);
            Long parentCategoryId = ids.get(i - 1);
            for (ProductCategoryDO productCategoryDO : cacheList) {
                if(productCategoryDO.getCategoryId().equals(categoryId)
                        && !parentCategoryId.equals(productCategoryDO.getParentId())){
                    return false;
                }
            }
        }

        return true;
    }

    @Override
    public List<ProductCategoryDO> getListByEconomyClass(List<String> economyClassList) {
        if (CollUtil.isEmpty(economyClassList)) {
            return Collections.emptyList();
        }

        Set<String> economyClassSet = new HashSet<>(economyClassList);
        List<ProductCategoryDO> list = getCacheList();

        return list.stream().filter(item -> StrUtil.isNotBlank(item.getEconomyClass()) &&
                        economyClassSet.stream().anyMatch(prefix -> item.getEconomyClass().startsWith(prefix.trim())))
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductCategoryDO> getListByNotEconomyClass(List<String> notEconomyClassList) {
        List<ProductCategoryDO> list = getCacheList();
        if (CollUtil.isEmpty(notEconomyClassList)) {
            return list;
        }

        Set<String> notEconomyClassSet = new HashSet<>(notEconomyClassList);

        return list.stream().filter(item -> StrUtil.isBlank(item.getEconomyClass()) ||
                        notEconomyClassSet.stream().noneMatch(prefix -> item.getEconomyClass().startsWith(prefix.trim())))
                .collect(Collectors.toList());
    }

    /**
     * 查询一个分类下全部子分类
     * @param parentCategoryId
     * @return
     */
    @Override
    public List<ProductCategoryDO> getChildCategoryList(Long parentCategoryId, ProductCategoryStatusEnum status) {
        if (null == parentCategoryId) {
            return Collections.emptyList();
        }

        List<ProductCategoryDO> cacheList = getCacheList();
        List<ProductCategoryDO> list = cacheList.stream().filter(item -> Objects.equals(item.getParentId(), parentCategoryId)).collect(Collectors.toList());
        if(status != null) {
            list = list.stream().filter(item -> item.getStatus().equals(status.getStatus())).collect(Collectors.toList());
        }
        CollectionUtil.sort(list, (obj1 ,obj2) -> obj1.getOrderSort() - obj2.getOrderSort());
        return list;
    }

    /**
     * 根据父级分类ID进行递归构建分类树
     * @param parentCategoryId
     * @return
     */
    @Override
    public List<ProductCategoryDO> getChildCategoryTreeList(Long parentCategoryId, ProductCategoryStatusEnum status) {
        List<ProductCategoryDO> cacheList = getCacheList();
        return travelChildCategory(parentCategoryId, status, cacheList);
    }

    private List<ProductCategoryDO> travelChildCategory(Long parentCategoryId, ProductCategoryStatusEnum status, List<ProductCategoryDO> dicList) {
        List<ProductCategoryDO> list = dicList.stream().filter(item -> Objects.equals(item.getParentId(), parentCategoryId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if(status != null) {
            list = list.stream().filter(item -> item.getStatus().equals(status.getStatus())).collect(Collectors.toList());
        }
        for (ProductCategoryDO categoryDO : list) {
            categoryDO.setChildCategoryList(this.travelChildCategory(categoryDO.getCategoryId(), status, dicList));
        }

        CollectionUtil.sort(list, (obj1 ,obj2) -> obj1.getOrderSort() - obj2.getOrderSort());
        return list;
    }

    private void computeChildrenIds(Long parentCategoryId, Set<Long> categoryIdList, List<ProductCategoryDO> dicList) {
        List<Long> childrenIdList = dicList.stream().filter(item -> Objects.equals(item.getParentId(), parentCategoryId)).map(ProductCategoryDO::getCategoryId).collect(Collectors.toList());
        childrenIdList.forEach(categoryId -> {
            computeChildrenIds(categoryId, categoryIdList, dicList);
        });
        categoryIdList.addAll(childrenIdList);
    }

    @Override
    @TenantIgnore
    public void initCategory4Tenant(Long tenantId) {
        if(CommonConstants.SUPER_TENANT.equals(tenantId)) {
            log.info("超管租户不用初始化");
            return;
        }
        // 如果存在商品分类，则不进行初始化
        List<ProductCategoryDO> categoryList = productCategoryMapper.selectList(Wrappers.lambdaQuery(ProductCategoryDO.class).eq(ProductCategoryDO::getTenantId, tenantId));
        if(CollUtil.isNotEmpty(categoryList)) {
            log.info("租户：{} 已经存在商品分类，不用初始化", tenantId);
            return;
        }

        // 数据准备
        categoryList = productCategoryMapper.selectList(Wrappers.lambdaQuery(ProductCategoryDO.class).eq(ProductCategoryDO::getTenantId, CommonConstants.SUPER_TENANT));

        // 分类数据复制及加工
        // 分类的parentId即categoryId，无须单独处理parentId
        List<ProductCategoryDO> categoryList2 = BeanUtil.copyToList(categoryList, ProductCategoryDO.class);
        for(ProductCategoryDO cate2 : categoryList2) {
            cate2.setId(null).setTenantId(tenantId).setCreateTime(null)
                    .setCreator(null).setUpdateTime(null).setUpdater(null);
        }
        productCategoryMapper.insertBatch(categoryList2);

        // 设置多租户上下文
        TenantContextHolder.setTenantId(tenantId);
        TenantContextHolder.setIgnore(false);
        // 清除缓存
        this.deleteCache();
    }

    /**
     * 根据分类池名称取出商品分类名称
     * {分类编号}{分类名称}, 分类编号长度为7位
     * @param poolName
     * @return
     */
    @Override
    public String getCategoryNameByPoolName(String poolName) {
        if(StringUtils.isBlank(poolName) || poolName.length() <= 7) {
            return poolName;
        }
        String categoryId = poolName.substring(0, 7);
        if(NumberUtil.isNumber(categoryId)) {
            return poolName.substring(7);
        };
        return poolName;
    }

    /**
     * 根据分类池名称取出商品分类Id
     * {分类编号}{分类名称}, 分类编号长度为7位
     * @param poolName
     * @return
     */
    @Override
    public Long getCategoryIdByPoolName(String poolName) {
        if(StringUtils.isBlank(poolName) || poolName.length() <= 7) {
            return null;
        }
        String categoryId = poolName.substring(0, 7);
        if(NumberUtil.isNumber(categoryId)) {
            return Long.valueOf(categoryId);
        };

        return null;
    }

    @Override
    public Long getCategoryCount() {
        return productCategoryMapper.selectCount(
                new LambdaQueryWrapperX<ProductCategoryDO>().eq(ProductCategoryDO::getCategoryLevel, 0)
                .eq(ProductCategoryDO::getStatus, ProductCategoryStatusEnum.ENABLE.getStatus()));
    }

    @Override
    public Boolean validLeafCategory(Long categoryId) {
        ProductCategoryDO productCategoryDO = productCategoryMapper.selectByCategoryId(categoryId);
        if(productCategoryDO == null || !productCategoryDO.getStatus().equals(ProductCategoryStatusEnum.ENABLE.getStatus())){
            log.error("商品分类id无效：{}", categoryId);
            return false;
        }

        return productCategoryMapper.selectCountByParentId(categoryId) == 0;
    }

    @Override
    public void importList(List<ProductCategoryImportExcelVO> list) {
        asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), AsyncTaskTypeEnum.PRODUCT_CATEGORY_IMPORT, TtlRunnable.get(() -> {
            doImportList(list);
        }));
    }

    public void doImportList(List<ProductCategoryImportExcelVO> list) {
        String taskId = AsyncFrontTaskContext.getTaskId();
        AsyncImportTask importTask = asyncFrontTaskUtils.getAsyncImportTask(taskId);

        try {
            if(CollUtil.isEmpty(list)) {
                asyncFrontTaskUtils.taskDone(importTask);
                return;
            }

            importTask.setTotal(list.size());
            List<AsyncImportRespExcelVO> importRespList = new ArrayList<>();

            AtomicInteger counter = new AtomicInteger(1);
            asyncFrontTaskUtils.updateTask(importTask);

            List<ProductCategoryDO> updates = new ArrayList<>();
            list.stream().forEach(category -> {
                counter.incrementAndGet();
                if(counter.get() % 10 == 0) {
                    asyncFrontTaskUtils.updateTask(importTask);
                }

                String categoryCode = category.getCode();
                try {
                    // 字段校验
                    validateCategoryImportVO(category);
                } catch (Exception ex) {
                    importRespList.add(importTask.plusFailed(categoryCode, ex.getMessage()));
                    return;
                }

                ProductCategoryDO categoryBean = buildCategoryDO(category);
                ProductCategoryDO persistDO = getByCategoryId(Long.valueOf(categoryCode));
                if(persistDO != null) {
                    categoryBean.setId(persistDO.getId());
                    updates.add(categoryBean);
                } else {
                    try {
                        validateCategoryImportVO4Insert(category);
                        ProductCategoryCreateReqVO createReqVO = new ProductCategoryCreateReqVO();
                        BeanUtil.copyProperties(categoryBean, createReqVO);
                        createReqVO.setStatus(ProductCategoryStatusEnum.ENABLE.getStatus());
                        this.createCategory(createReqVO);
                        importRespList.add(importTask.plusCreated(categoryCode));
                    } catch (Exception e) {
                        importRespList.add(importTask.plusFailed(categoryCode, e.getMessage()));
                    }
                }
            });
            log.info("商品分类导入更新数量：{}", updates.size());
            if(CollUtil.isNotEmpty(updates)) {
                productCategoryMapper.updateBatch(updates, 1000);
                asyncFrontTaskUtils.updateTask(importTask);
            }
            asyncFrontTaskUtils.importDone(taskId, importRespList);
            this.deleteCache();
        } catch (Exception e) {
            log.error("导入商品分类异常", e);
            asyncFrontTaskUtils.importFail();
        }
    }

    private void validateCategoryImportVO(ProductCategoryImportExcelVO bean) {
        if(bean.getCode() == null) {
            throw ServiceExceptionUtil.exception(PRODUCT_CATEGORY_IMPORT_VALIDATION_FAIL, "商品分类编码为空");
        }
        if(!NumberUtil.isLong(bean.getCode())) {
            throw ServiceExceptionUtil.exception(PRODUCT_CATEGORY_IMPORT_VALIDATION_FAIL, "商品分类编码不是数字");
        }
    }

    private void validateCategoryImportVO4Insert(ProductCategoryImportExcelVO bean) {
        if(bean.getCode() == null) {
            throw ServiceExceptionUtil.exception(PRODUCT_CATEGORY_IMPORT_VALIDATION_FAIL, "商品分类编码为空");
        }
        if(StrUtil.isBlank(bean.getName())) {
            throw ServiceExceptionUtil.exception(PRODUCT_CATEGORY_IMPORT_VALIDATION_FAIL, "商品分类名称为空");
        }
    }

    private ProductCategoryDO buildCategoryDO(ProductCategoryImportExcelVO bean) {
        ProductCategoryDO categoryDO = new ProductCategoryDO();
        categoryDO.setParentId(bean.getParentCode());
        categoryDO.setCategoryId(Long.valueOf(bean.getCode()));
        categoryDO.setCategoryName(bean.getName());
        categoryDO.setIcon(bean.getIconUrl());
        categoryDO.setIconH5(bean.getH5IconUrl());
        categoryDO.setEconomyClass(bean.getEconomyClass());
        categoryDO.setEconomyClass2(bean.getEconomyClass2());
        categoryDO.setOrderSort(bean.getSort());

        return categoryDO;
    }

    @Override
    public List<Long> convertToLeafCategoryIds(List<Long> categoryIds) {
        if (CollUtil.isEmpty(categoryIds)) {
            return new ArrayList<>();
        }

        List<ProductCategoryDO> cacheList = getCacheList();
        Set<Long> leafCategoryIds = new HashSet<>();

        for (Long categoryId : categoryIds) {
            // 检查分类是否存在
            ProductCategoryDO category = cacheList.stream()
                    .filter(item -> Objects.equals(item.getCategoryId(), categoryId))
                    .findFirst()
                    .orElse(null);

            if (category == null) {
                log.warn("分类ID不存在: {}", categoryId);
                continue;
            }

            // 检查是否为末级分类（叶子分类）
            boolean isLeaf = cacheList.stream()
                    .noneMatch(item -> Objects.equals(item.getParentId(), categoryId));

            if (isLeaf) {
                // 如果是末级分类，直接添加
                leafCategoryIds.add(categoryId);
            } else {
                // 如果不是末级分类，递归获取所有末级子分类
                collectLeafCategoryIds(categoryId, cacheList, leafCategoryIds);
            }
        }

        return new ArrayList<>(leafCategoryIds);
    }

    /**
     * 递归收集指定分类下的所有末级分类ID
     * @param parentCategoryId 父分类ID
     * @param cacheList 所有分类列表
     * @param leafCategoryIds 收集末级分类ID的集合
     */
    private void collectLeafCategoryIds(Long parentCategoryId, List<ProductCategoryDO> cacheList, Set<Long> leafCategoryIds) {
        // 获取直接子分类
        List<ProductCategoryDO> childCategories = cacheList.stream()
                .filter(item -> Objects.equals(item.getParentId(), parentCategoryId))
                .collect(Collectors.toList());

        for (ProductCategoryDO childCategory : childCategories) {
            Long childCategoryId = childCategory.getCategoryId();

            // 检查子分类是否为末级分类
            boolean isLeaf = cacheList.stream()
                    .noneMatch(item -> Objects.equals(item.getParentId(), childCategoryId));

            if (isLeaf) {
                // 如果是末级分类，添加到结果集
                leafCategoryIds.add(childCategoryId);
            } else {
                // 如果不是末级分类，继续递归
                collectLeafCategoryIds(childCategoryId, cacheList, leafCategoryIds);
            }
        }
    }

    @Override
    public List<ProductCategoryExcelVO> getCategoryExcelData(ProductCategoryListReqVO listReqVO) {
        // 获取所有分类数据
        List<ProductCategoryDO> allCategories = getAllCategoryList(listReqVO);

        log.info("getCategoryExcelData: 获取到分类总数 = {}", allCategories.size());

        // 打印前几个分类的信息用于调试
        for (int i = 0; i < Math.min(5, allCategories.size()); i++) {
            ProductCategoryDO category = allCategories.get(i);
            log.info("分类[{}]: ID={}, Name={}, Level={}, ParentId={}",
                i, category.getCategoryId(), category.getCategoryName(),
                category.getCategoryLevel(), category.getParentId());
        }

        // 按层级分组
        Map<Integer, List<ProductCategoryDO>> categoryLevelMap = allCategories.stream()
                .collect(Collectors.groupingBy(ProductCategoryDO::getCategoryLevel));

        log.info("分类层级分组: {}", categoryLevelMap.keySet());

        List<ProductCategoryDO> level1Categories = categoryLevelMap.getOrDefault(0, new ArrayList<>());
        List<ProductCategoryDO> level2Categories = categoryLevelMap.getOrDefault(1, new ArrayList<>());
        List<ProductCategoryDO> level3Categories = categoryLevelMap.getOrDefault(2, new ArrayList<>());

        log.info("一级分类数量: {}, 二级分类数量: {}, 三级分类数量: {}",
            level1Categories.size(), level2Categories.size(), level3Categories.size());

        // 构建父子关系映射
        Map<Long, List<ProductCategoryDO>> level2ByParent = level2Categories.stream()
                .collect(Collectors.groupingBy(ProductCategoryDO::getParentId));
        Map<Long, List<ProductCategoryDO>> level3ByParent = level3Categories.stream()
                .collect(Collectors.groupingBy(ProductCategoryDO::getParentId));

        List<ProductCategoryExcelVO> excelData = new ArrayList<>();

        // 遍历一级分类
        for (ProductCategoryDO level1 : level1Categories) {
            List<ProductCategoryDO> level2List = level2ByParent.getOrDefault(level1.getCategoryId(), new ArrayList<>());

            if (level2List.isEmpty()) {
                // 如果没有二级分类，只显示一级分类
                ProductCategoryExcelVO excelVO = new ProductCategoryExcelVO();
                excelVO.setCategory1Id(level1.getCategoryId());
                excelVO.setCategory1Name(level1.getCategoryName());
                excelData.add(excelVO);
            } else {
                // 遍历二级分类
                for (ProductCategoryDO level2 : level2List) {
                    List<ProductCategoryDO> level3List = level3ByParent.getOrDefault(level2.getCategoryId(), new ArrayList<>());

                    if (level3List.isEmpty()) {
                        // 如果没有三级分类，只显示到二级分类
                        ProductCategoryExcelVO excelVO = new ProductCategoryExcelVO();
                        excelVO.setCategory1Id(level1.getCategoryId());
                        excelVO.setCategory1Name(level1.getCategoryName());
                        excelVO.setCategory2Id(level2.getCategoryId());
                        excelVO.setCategory2Name(level2.getCategoryName());
                        excelData.add(excelVO);
                    } else {
                        // 遍历三级分类
                        for (ProductCategoryDO level3 : level3List) {
                            ProductCategoryExcelVO excelVO = new ProductCategoryExcelVO();
                            excelVO.setCategory1Id(level1.getCategoryId());
                            excelVO.setCategory1Name(level1.getCategoryName());
                            excelVO.setCategory2Id(level2.getCategoryId());
                            excelVO.setCategory2Name(level2.getCategoryName());
                            excelVO.setCategory3Id(level3.getCategoryId());
                            excelVO.setCategory3Name(level3.getCategoryName());
                            excelData.add(excelVO);
                        }
                    }
                }
            }
        }

        log.info("getCategoryExcelData: 生成Excel数据行数 = {}", excelData.size());

        // 打印前几行数据用于调试
        for (int i = 0; i < Math.min(3, excelData.size()); i++) {
            ProductCategoryExcelVO item = excelData.get(i);
            log.info("Excel数据[{}]: {}|{}|{}|{}|{}|{}", i,
                item.getCategory1Id(), item.getCategory1Name(),
                item.getCategory2Id(), item.getCategory2Name(),
                item.getCategory3Id(), item.getCategory3Name());
        }

        return excelData;
    }

}
