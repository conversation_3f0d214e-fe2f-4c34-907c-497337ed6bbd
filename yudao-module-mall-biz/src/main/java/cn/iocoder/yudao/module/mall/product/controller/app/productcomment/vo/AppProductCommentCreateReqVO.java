package cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "用户 APP - 商品评价创建 Request VO")
@Data
public class AppProductCommentCreateReqVO {

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单编号不能为空")
    private String orderNo;

    @Schema(description = "skuId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "skuId不能为空")
    private Long skuId;

    @Schema(description = "评价星数 1->5", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer score;

    @Schema(description = "评价内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    @Schema(description = "上传图片地址，以逗号隔开")
    private String pics;

    @Schema(description = "是否匿名 1-是 0 否", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer anonymousFlag;
}
