package cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo;

import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderPageItemRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 采购分页 Request VO")
@Data
public class PurchasePageItemRespVO {

    @Schema(description = "采购单编号")
    private Long id;

    @Schema(description = "审批流单据号")
    private String bpmNo;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "采购人用户ID")
    private Long userId;

    @Schema(description = "采购人用户昵称")
    private String userName;

    @Schema(description = "订单数")
    private Integer orderCount;

    @Schema(description = "采购总金额")
    private BigDecimal totalAmount;

    @Schema(description = "项目所属部门名称")
    private String projectDepartmentName;

    @Schema(description = "项目所属部门编号")
    private String projectDepartmentNo;

    @Schema(description = "项目编号")
    private String projectNo;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "项目类型名称")
    private String projectTypeName;

    @Schema(description = "采购状态")
    private Integer auditStatus;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 订单信息
     */
    private List<TradeOrderPageItemRespVO> orders;

}
