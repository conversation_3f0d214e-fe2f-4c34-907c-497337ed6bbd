package cn.iocoder.yudao.module.mall.member.controller.app.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Schema(description = "用户 APP - 统一登录 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppSsoLoginReqVO {

    @Schema(description = "加密租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15601691300")
    @NotEmpty(message = "加密租户编号不能为空")
    private String tcode;

    @Schema(description = "登录令牌")
    private String etoken;

    private String uid;

    /**
     * 10：业财SSO
     */
    @Schema(description = "登录类型")
    private Integer etype;


}
