package cn.iocoder.yudao.module.mall.product.controller.admin.skustock;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skustock.ProductSkuStockDO;
import cn.iocoder.yudao.module.mall.product.convert.skustock.ProductSkuStockConvert;
import cn.iocoder.yudao.module.mall.product.service.skustock.ProductSkuStockService;

@Tag(name = "管理后台 - sku库存")
@RestController
@RequestMapping("/product/sku-stock")
@Validated
public class ProductSkuStockController {

    @Resource
    private ProductSkuStockService skuStockService;

    @PostMapping("/create")
    @Operation(summary = "创建sku库存")
    @PreAuthorize("@ss.hasPermission('product:sku-stock:create')")
    public CommonResult<Long> createSkuStock(@Valid @RequestBody ProductSkuStockCreateReqVO createReqVO) {
        return success(skuStockService.createSkuStock(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新sku库存")
    @PreAuthorize("@ss.hasPermission('product:sku-stock:update')")
    public CommonResult<Boolean> updateSkuStock(@Valid @RequestBody ProductSkuStockUpdateReqVO updateReqVO) {
        skuStockService.updateSkuStock(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除sku库存")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:sku-stock:delete')")
    public CommonResult<Boolean> deleteSkuStock(@RequestParam("id") Long id) {
        skuStockService.deleteSkuStock(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得sku库存")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:sku-stock:query')")
    public CommonResult<ProductSkuStockRespVO> getSkuStock(@RequestParam("id") Long id) {
        ProductSkuStockDO skuStock = skuStockService.getSkuStock(id);
        return success(ProductSkuStockConvert.INSTANCE.convert(skuStock));
    }

    @GetMapping("/list")
    @Operation(summary = "获得sku库存列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('product:sku-stock:query')")
    public CommonResult<List<ProductSkuStockRespVO>> getSkuStockList(@RequestParam("ids") Collection<Long> ids) {
        List<ProductSkuStockDO> list = skuStockService.getSkuStockList(ids);
        return success(ProductSkuStockConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得sku库存分页")
    @PreAuthorize("@ss.hasPermission('product:sku-stock:query')")
    public CommonResult<PageResult<ProductSkuStockRespVO>> getSkuStockPage(@Valid ProductSkuStockPageReqVO pageVO) {
        PageResult<ProductSkuStockDO> pageResult = skuStockService.getSkuStockPage(pageVO);
        return success(ProductSkuStockConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出sku库存 Excel")
    @PreAuthorize("@ss.hasPermission('product:sku-stock:export')")
    @OperateLog(type = EXPORT)
    public void exportSkuStockExcel(@Valid ProductSkuStockExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ProductSkuStockDO> list = skuStockService.getSkuStockList(exportReqVO);
        // 导出 Excel
        List<ProductSkuStockExcelVO> datas = ProductSkuStockConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "sku库存.xls", "数据", ProductSkuStockExcelVO.class, datas);
    }

}
