package cn.iocoder.yudao.module.mall.product.dal.mysql.productcomment;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.ProductCommentPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo.AppProductCommentExportReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo.AppProductCommentOrderPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo.AppProductCommentPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentDO;
import cn.iocoder.yudao.module.mall.product.enums.comment.ProductCommentAuditStatusEnum;
import cn.iocoder.yudao.module.mall.product.enums.comment.ProductCommentSortTypeEnum;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 商品评价 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductCommentMapper extends BaseMapperX<ProductCommentDO> {

    default PageResult<ProductCommentDO> selectPage(AppProductCommentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductCommentDO>()
                .eqIfPresent(ProductCommentDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(ProductCommentDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(ProductCommentDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(ProductCommentDO::getSpuId, reqVO.getSpuId())
                .eqIfPresent(ProductCommentDO::getSupplierId, reqVO.getSupplierId())
                .likeIfPresent(ProductCommentDO::getSupplierName, reqVO.getSupplierName())
                .eqIfPresent(ProductCommentDO::getMemberId, reqVO.getMemberId())
                .likeIfPresent(ProductCommentDO::getNickName, reqVO.getNickName())
                .eqIfPresent(ProductCommentDO::getAvatar, reqVO.getAvatar())
                .eqIfPresent(ProductCommentDO::getScore, reqVO.getScore())
                .eqIfPresent(ProductCommentDO::getContent, reqVO.getContent())
                .eqIfPresent(ProductCommentDO::getPics, reqVO.getPics())
                .eqIfPresent(ProductCommentDO::getAnonymousFlag, reqVO.getAnonymousFlag())
                .eqIfPresent(ProductCommentDO::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(ProductCommentDO::getClientIp, reqVO.getClientIp())
                .eqIfPresent(ProductCommentDO::getClientArea, reqVO.getClientArea())
                .eqIfPresent(ProductCommentDO::getLikeCount, reqVO.getLikeCount())
                .eqIfPresent(ProductCommentDO::getReplyCount, reqVO.getReplyCount())
                .eqIfPresent(ProductCommentDO::getReportCount, reqVO.getReportCount())
                .betweenIfPresent(ProductCommentDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductCommentDO::getId));
    }

    default List<ProductCommentDO> selectList(AppProductCommentExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ProductCommentDO>()
                .eqIfPresent(ProductCommentDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(ProductCommentDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(ProductCommentDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(ProductCommentDO::getSpuId, reqVO.getSpuId())
                .eqIfPresent(ProductCommentDO::getSupplierId, reqVO.getSupplierId())
                .likeIfPresent(ProductCommentDO::getSupplierName, reqVO.getSupplierName())
                .eqIfPresent(ProductCommentDO::getMemberId, reqVO.getMemberId())
                .likeIfPresent(ProductCommentDO::getNickName, reqVO.getNickName())
                .eqIfPresent(ProductCommentDO::getAvatar, reqVO.getAvatar())
                .eqIfPresent(ProductCommentDO::getScore, reqVO.getScore())
                .eqIfPresent(ProductCommentDO::getContent, reqVO.getContent())
                .eqIfPresent(ProductCommentDO::getPics, reqVO.getPics())
                .eqIfPresent(ProductCommentDO::getAnonymousFlag, reqVO.getAnonymousFlag())
                .eqIfPresent(ProductCommentDO::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(ProductCommentDO::getClientIp, reqVO.getClientIp())
                .eqIfPresent(ProductCommentDO::getClientArea, reqVO.getClientArea())
                .eqIfPresent(ProductCommentDO::getLikeCount, reqVO.getLikeCount())
                .eqIfPresent(ProductCommentDO::getReplyCount, reqVO.getReplyCount())
                .eqIfPresent(ProductCommentDO::getReportCount, reqVO.getReportCount())
                .betweenIfPresent(ProductCommentDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductCommentDO::getId));
    }

    default PageResult<ProductCommentDO> selectPage(ProductCommentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductCommentDO>()
                .eqIfPresent(ProductCommentDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(ProductCommentDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(ProductCommentDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(ProductCommentDO::getSpuId, reqVO.getSpuId())
                .eqIfPresent(ProductCommentDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(ProductCommentDO::getMemberId, reqVO.getMemberId())
                .likeIfPresent(ProductCommentDO::getNickName, reqVO.getNickName())
                .eqIfPresent(ProductCommentDO::getScore, reqVO.getScore())
                .eqIfPresent(ProductCommentDO::getAnonymousFlag, reqVO.getAnonymousFlag())
                .eqIfPresent(ProductCommentDO::getAuditStatus, reqVO.getAuditStatus())
                .betweenIfPresent(ProductCommentDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductCommentDO::getId));
    }


    default PageResult<ProductCommentDO> getProductCommentPage(AppProductCommentOrderPageReqVO reqVO) {
        ProductCommentSortTypeEnum sortTypeEnum = ProductCommentSortTypeEnum.fromCode(reqVO.getSortType());

        return selectPage(reqVO, new LambdaQueryWrapperX<ProductCommentDO>()
                .eqIfPresent(ProductCommentDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(ProductCommentDO::getOrderId, reqVO.getOrderId())
                .eq(ProductCommentDO::getAuditStatus, ProductCommentAuditStatusEnum.APPROVE.getStatus())
                .orderBy(sortTypeEnum!=null,sortTypeEnum == ProductCommentSortTypeEnum.DATE_ASC,ProductCommentDO::getCreateTime));
    }

    default int updateAuditStatus(Long id, Integer auditStatus, ProductCommentDO update) {
        return update(update, new LambdaUpdateWrapper<ProductCommentDO>()
                .eq(ProductCommentDO::getId, id).eq(ProductCommentDO::getAuditStatus, auditStatus));
    }
}
