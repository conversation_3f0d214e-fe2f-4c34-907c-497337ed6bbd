package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
* 商品 SKU Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ProductSkuDetailRespVO {

    @Schema(description = "商品SPU编号", example = "小米")
    private Long spuId;

    @Schema(description = "供应商skuId")
    private String skuInnerId;

    @Schema(description = "商品SPU名称", example = "小米")
    private String spuName;

    @Schema(description = "供应商编号", example = "1")
    private Long supplierId;

    @Schema(description = "供应商类型", example = "1")
    private Integer supplierType;

    @Schema(description = "供应商名称", example = "小米")
    private String supplierName;

    @Schema(description = "商品品牌编号", example = "1")
    private Long brandId;

    @Schema(description = "商品品牌名称", example = "小米")
    private String brandName;

    @Schema(description = "商品 SKU 名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String skuName;

    @Schema(description = "市场价", example = "1024")
    private BigDecimal marketPrice;

    @Schema(description = "销售价", example = "1024")
    private BigDecimal salePrice;

    @Schema(description = "图片地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/xx.png")
    private String picUrl;

    @Schema(description = "SKU 状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(ProductSpuStatusEnum.class)
    private Integer status;

    @Schema(description = "商品显示状态 1-显示 0-不显示", example = "1")
    private Integer showStatus;

    @Schema(description = "商品实际销量", example = "1")
    private Integer salesCount;

    @Schema(description = "商品初始销量", example = "1")
    private Integer initSalesCount;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "一级商品分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long category1Id;

    @Schema(description = "二级商品分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long category2Id;

    @Schema(description = "三级商品分类编号", example = "1")
    private Long category3Id;

    @Schema(description = "一级商品分类名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "通讯")
    private String category1Name;

    @Schema(description = "二级商品分类名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "手机")
    private String category2Name;

    @Schema(description = "三级商品分类名称", example = "手机配件")
    private String category3Name;

    @Schema(description = "商品分类编号, 以-分隔", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String fullCategoryId;

    @Schema(description = "商品分类名称, 以/分隔", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String fullCategoryName;

    @Schema(description = "描述", example = "")
    private String description;

    @Schema(description = "H5端描述", example = "")
    private String descriptionH5;

    @Schema(description = "图片地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/xx.png")
    private String spuPicUrl;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> sliderPicUrls;

}
