package cn.iocoder.yudao.module.mall.product.controller.app.sku.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/7
 */
@Data
public class GoodsSearchReq {

    private String brandId;

    private String keyword;

    private int pageSize = 10;

    private int pageIndex = 1;

    private Long categoryId3;

    private Long categoryId1;

    private Long categoryId2;

    /**
     * 1,销量降序 2,价格升序 3,价格降序,5上架时间降序
     * 京东： 1,销量降序 2,价格升序 3,价格降序 4,评论数降序 5,上架时间降序 6,15天销售额 7,按15日销量降序 8,按30日销量降序 9,按15日销售额降序 10,按30日销售额排序
     */
    private Integer sortType;

    private String areaIds;

    private Long supplierId;

    /**
     * 最大价格
     */
    private BigDecimal maxPrice;

    /**
     * 最小价格
     */
    private BigDecimal minPrice;

    private List<Long> tagIds;

    private List<Long> skuIds;
    private List<Long> categoryId1s;
    private List<Long> categoryId2s;
    private List<Long> categoryId3s;

    private List<Long> orderedSkus;


    public boolean categoryIsAllNull() {
        return categoryId1 == null && categoryId2 == null && categoryId3 == null;
    }

}
