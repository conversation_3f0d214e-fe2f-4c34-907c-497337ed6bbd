package cn.iocoder.yudao.module.mall.external.open.controller.app;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.annotation.OrderOperateLog;
import cn.iocoder.yudao.module.mall.external.open.annotation.OpenAuth;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.delivery.AppOpenDeliveryBindReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.delivery.AppOpenDeliveryReqVO;
import cn.iocoder.yudao.module.mall.trade.enums.order.OrderOperateTypeEnum;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.*;


@Tag(name = "开放API - 物流")
@RestController
@RequestMapping("/mall/open/delivery")
@Validated
@Slf4j
@OpenAuth
public class AppOpenDeliveryController {
    @Resource
    private DeliveryService deliveryService;

    /**
     * 第三方绑定订单对应的物流信息
     * @param appOpenDeliveryBindReqVO
     * @return
     */
    @PostMapping("/bindOrder")
    @Operation(summary = "第三方绑定订单对应的物流信息")
    @OrderOperateLog(content = "已发货", userName = "开放API", type = OrderOperateTypeEnum.DO_DELIVERING,orderId = "#appOpenDeliveryBindReqVO.orderId")
    public CommonResult<Boolean> bindOrder(@Valid @RequestBody AppOpenDeliveryBindReqVO appOpenDeliveryBindReqVO) {
        Boolean ret = deliveryService.thirdBindOrder(appOpenDeliveryBindReqVO);
        return success(ret);
    }

    /**
     * 订阅物流回调
     * @param appOpenDeliveryReqVO
     * @return
     */
    @PostMapping("/update")
    @Operation(summary = "更新物流信息")
    public CommonResult<Boolean> update(@Valid @RequestBody AppOpenDeliveryReqVO appOpenDeliveryReqVO) {
        appOpenDeliveryReqVO.setCom(appOpenDeliveryReqVO.getCompanyCode());
        appOpenDeliveryReqVO.setNu(appOpenDeliveryReqVO.getNum());
        Boolean ret = deliveryService.thirdUpdate(appOpenDeliveryReqVO);
        if(!ret){
            throw exception(OPEN_APP_DELIVERY_DETAIL_UPDATE_FAILED);
        }

        return CommonResult.success(true);
    }

}
