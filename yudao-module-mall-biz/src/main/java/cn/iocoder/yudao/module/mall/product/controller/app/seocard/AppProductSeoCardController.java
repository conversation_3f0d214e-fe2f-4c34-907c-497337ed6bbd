package cn.iocoder.yudao.module.mall.product.controller.app.seocard;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import cn.iocoder.yudao.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import cn.iocoder.yudao.module.mall.product.controller.app.seocard.vo.AppProductSeoCardRespVO;
import cn.iocoder.yudao.module.mall.product.convert.seocard.ProductSeoCardConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.seocard.ProductSeoCardDO;
import cn.iocoder.yudao.module.mall.product.service.seocard.ProductSeoCardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "APP前台 - 运营区域")
@RestController
@RequestMapping("/product/seo-card")
@Validated
public class AppProductSeoCardController {

    @Resource
    private ProductSeoCardService seoCardService;

    @GetMapping("/get")
    @Operation(summary = "获得运营区域")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @RateLimiter(count = 300, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    public CommonResult<AppProductSeoCardRespVO> getSeoCard(@RequestParam("id") Long id) {
        ProductSeoCardDO seoCard = seoCardService.getSeoCard(id);
        return success(ProductSeoCardConvert.INSTANCE.convert02(seoCard));
    }

    @GetMapping("/list")
    @Operation(summary = "获得运营区域列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @RateLimiter(count = 300, timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    public CommonResult<List<AppProductSeoCardRespVO>> getSeoCardList(@RequestParam("ids") Collection<Long> ids) {
        if(CollUtil.isEmpty(ids)) {
            return success(null);
        }
        if(ids.size() > 50) {
            return success(null);
        }
        List<ProductSeoCardDO> list = seoCardService.getSeoCardList(ids, CommonStatusEnum.ENABLE.getStatus());
        return success(ProductSeoCardConvert.INSTANCE.convertList02(list));
    }

}
