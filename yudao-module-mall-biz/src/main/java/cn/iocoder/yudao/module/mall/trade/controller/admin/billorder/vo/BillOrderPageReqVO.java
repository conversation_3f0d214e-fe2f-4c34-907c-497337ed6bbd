package cn.iocoder.yudao.module.mall.trade.controller.admin.billorder.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 账单订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BillOrderPageReqVO extends PageParam {

    /**
     * 账单id
     */
    @Schema(description = "账单id")
    @NotNull(message = "账单id不能为空")
    private Long billId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "业财融合预约单号")
    private String voucherNo;

    @Schema(description = "是否线下结算")
    private Boolean offlineSettlement;

}
