package cn.iocoder.yudao.module.mall.external.sso.cas.dynamic;

import org.jasig.cas.client.util.AssertionThreadLocalFilter;

import javax.servlet.Filter;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import java.util.Map;

/**
 * @author: HF
 * @description: TODO
 * @date: 2024/4/13 20:24
 */
public class AssertionThreadLocalDynamicFilter extends AbstractDynamicCasFilter {

    public AssertionThreadLocalDynamicFilter(CasConfigService casConfigService) {
        super(casConfigService);
    }

    @Override
    protected Filter genTargetFilter(FilterConfig config, Map<String, String> casConfigMap) throws ServletException {
        DummyFilterConfig newConfig = new DummyFilterConfig("",config.getServletContext(),casConfigMap);
        Filter targetFilter = new AssertionThreadLocalFilter();
        targetFilter.init(newConfig);
        return targetFilter;
    }
}
