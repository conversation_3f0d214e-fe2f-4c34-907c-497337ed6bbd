package cn.iocoder.yudao.module.mall.product.dal.dataobject.spec;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * sku规格关联 DO
 *
 * <AUTHOR>
 */
@TableName("product_sku_spec")
@KeySequence("product_sku_spec_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSkuSpecDO extends TenantBaseDO {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * SKU ID
     */
    private Long skuId;
    /**
     * 规格ID, 0代表为自定义规格
     */
    private Long specId;
    /**
     * 规格属性名称
     */
    private String specName;
    /**
     * 规格属性值
     */
    private String specValue;

}
