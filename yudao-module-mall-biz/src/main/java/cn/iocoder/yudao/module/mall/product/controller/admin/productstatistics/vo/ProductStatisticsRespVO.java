package cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 商品统计返回
 * <AUTHOR>
 * @date 2024/4/19
 */
@Schema(description = "管理后台 - 商品统计查询返回 VO")
@Data
public class ProductStatisticsRespVO {

    /**
     * 商品总数
     */
    @Schema(description = "商品总数")
    private Integer totalCount;

    /**
     * 上架商品数量
     */
    @Schema(description = "上架商品数量")
    private Integer onSaleCount;

    /**
     * 下架商品数量
     */
    @Schema(description = "下架商品数量")
    private Integer offSaleCount;

}
