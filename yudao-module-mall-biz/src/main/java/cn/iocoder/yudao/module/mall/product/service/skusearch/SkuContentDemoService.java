package cn.iocoder.yudao.module.mall.product.service.skusearch;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.app.skusearch.vo.SkuContentDemoPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.SkuContentDemoDO;

/**
 * 商品SKU搜索 Demo
 *
 * <AUTHOR>
 */
public interface SkuContentDemoService {


    void save(SkuContentDemoDO entity);

    void delete(Long id);

    SkuContentDemoDO getById(Long id);

    PageResult<SkuContentDemoDO> seachByPage(SkuContentDemoPageReqVO pageReqVO);

}
