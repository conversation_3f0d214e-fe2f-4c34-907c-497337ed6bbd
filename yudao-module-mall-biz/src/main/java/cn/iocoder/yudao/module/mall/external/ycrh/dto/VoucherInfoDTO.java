package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class VoucherInfoDTO {

    /**
     * 凭证文件名称
     */
    private String voucherName;

    /**
     * 附件类型 "00":凭证信息 "01":业务报销单 "02":收入申报税表 "03":电子发票 "04":纸质发票 "05":合同信息 "06":银行回单 "07":报销申请单 "08":项目预算申请书 "09":审批证明表 "98":审批过程影像 "99":其他附件
     */
    @JsonProperty("fjlx")
    private String voucherType;

}
