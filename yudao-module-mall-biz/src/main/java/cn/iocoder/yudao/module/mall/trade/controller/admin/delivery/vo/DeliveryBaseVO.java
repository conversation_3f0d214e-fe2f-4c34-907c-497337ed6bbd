package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 物流信息 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class DeliveryBaseVO {

    @Schema(description = "是否签收标记，0未签收，1已签收，请忽略，明细状态请参考state字段", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否签收标记，0未签收，1已签收，请忽略，明细状态请参考state字段不能为空")
    private Integer isCheck;

    @Schema(description = "出发行政区域编码")
    private String fromNumber;

    @Schema(description = "出发行政区域名字")
    private String fromName;

    @Schema(description = "目的地行政区域编码")
    private String toNumber;

    @Schema(description = "目的地行政区域名字")
    private String toName;

    @Schema(description = "当前行政区域编码")
    private String curNumber;

    @Schema(description = "当前行政区域名字")
    private String curName;

    @Schema(description = "订阅状态，默认为0未订阅，1订阅", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订阅状态，默认为0未订阅，1订阅不能为空")
    private Integer subscribe;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单编号不能为空")
    private String orderNo;

    @Schema(description = "供应商编号")
    @NotNull(message = "供应商编号不能为空")
    private Long supplierId;

    @Schema(description = "供应商名称")
    @NotNull(message = "供应商名称不能为空")
    private String supplierName;

    @Schema(description = "物流来源")
    private String source;

    @Schema(description = "物流公司编码,一律用小写字母", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物流公司编码,一律用小写字母不能为空")
    private String com;

    @Schema(description = "物流单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物流单号不能为空")
    private String num;

    @Schema(description = "物流单当前状态，默认为0在途，1揽收，2疑难，3签收，4退签，5派件，8清关，14拒签等10个基础物流状态，如需要返回高级物流状态，请参考 resultv2 传值")
    private Integer state;

    @Schema(description = "物流公司名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物流公司名称不能为空")
    private String name;

}
