package cn.iocoder.yudao.module.mall.vop.service;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.vop.controller.admin.vopconfig.vo.VopConfigCreateReqVO;
import cn.iocoder.yudao.module.mall.vop.controller.admin.vopconfig.vo.VopConfigUpdateReqVO;
import cn.iocoder.yudao.module.mall.vop.convert.vopconfig.VopConfigConvert;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopConfigDO;
import cn.iocoder.yudao.module.mall.vop.dal.mysql.VopConfigMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.VOP_CONFIG_NOT_EXISTS;
import static cn.iocoder.yudao.module.mall.vop.dal.redis.RedisKeyConstants.MALL_VOP_CONFIG;

/**
 * VOP配置信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VopConfigServiceImpl implements VopConfigService {

    @Resource
    private VopConfigMapper vopConfigMapper;

    @Override
    public VopConfigDO getVopConfigRequired() {
        VopConfigDO vopConfigDO = getCurrentVopConfig();
        if (vopConfigDO == null) {
            throw new ServiceException(ErrorCodeConstants.VOP_ACCOUNT_NOT_INIT_EXCEPTION);
        }
        return vopConfigDO;
    }

    @Override
    @Cacheable(value = MALL_VOP_CONFIG, unless = "#result == null", key = "'1'")
    public VopConfigDO getVopConfig() {
        return getCurrentVopConfig();
    }

    @Override
    @CacheEvict(value = MALL_VOP_CONFIG, key = "'1'")
    public Long createVopConfig(VopConfigCreateReqVO createReqVO) {
        // 插入
        VopConfigDO vopConfig = VopConfigConvert.INSTANCE.convert(createReqVO);
        vopConfigMapper.insert(vopConfig);
        // 返回
        return vopConfig.getId();
    }

    @Override
    public boolean isVopEnabled() {
        VopConfigDO vopConfigDO = getCurrentVopConfig();
        return vopConfigDO != null && vopConfigDO.validate();
    }

    private VopConfigDO getCurrentVopConfig() {
        return vopConfigMapper.selectOne(Wrappers.<VopConfigDO>lambdaQuery().last("order by create_time desc limit 1"));
    }

    @Override
    @CacheEvict(value = MALL_VOP_CONFIG, key = "'1'")
    public void updateVopConfig(VopConfigUpdateReqVO updateReqVO) {
        // 校验存在
        validateVopConfigExists(updateReqVO.getId());
        // 更新
        VopConfigDO updateObj = VopConfigConvert.INSTANCE.convert(updateReqVO);
        vopConfigMapper.updateById(updateObj);
    }

    @Override
    @CacheEvict(value = MALL_VOP_CONFIG, key = "'1'")
    public void deleteVopConfig(Long id) {
        // 校验存在
        validateVopConfigExists(id);
        // 删除
        vopConfigMapper.deleteById(id);
    }

    private void validateVopConfigExists(Long id) {
        if (vopConfigMapper.selectById(id) == null) {
            throw exception(VOP_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public VopConfigDO getVopConfigRequired(Long id) {
        return vopConfigMapper.selectById(id);
    }

    @Override
    public List<VopConfigDO> getVopConfigList(Collection<Long> ids) {
        return vopConfigMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public List<VopConfigDO> getAll() {
        return vopConfigMapper.selectList();
    }
}
