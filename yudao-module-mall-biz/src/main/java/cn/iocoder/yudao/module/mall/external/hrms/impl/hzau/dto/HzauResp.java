package cn.iocoder.yudao.module.mall.external.hrms.impl.hzau.dto;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 华农信息中心开放平台统一返回类
 *
 * <AUTHOR>
 */
@Data
public class HzauResp<T> {

    private Integer code;

    private String message;

    private String description;

    private String uuid;

    private T result;

    @JsonIgnore
    public boolean isSuccess() {
        return ObjectUtil.equal(code, 10000);
    }

    /**
     * 如果失败 抛出异常
     *
     * @param checkFail
     * @return
     */
    public T getResult(boolean checkFail) {
        if (!isSuccess()) {
            throw exception(ErrorCodeConstants.HRMS_HZAU_FAIL, this.message);
        }
        return result;
    }
}
