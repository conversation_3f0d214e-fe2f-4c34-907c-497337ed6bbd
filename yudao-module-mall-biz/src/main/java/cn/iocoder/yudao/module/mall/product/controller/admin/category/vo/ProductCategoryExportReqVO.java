package cn.iocoder.yudao.module.mall.product.controller.admin.category.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品分类 Excel 导出 Request VO，参数和 CategoryPageReqVO 是一致的")
@Data
public class ProductCategoryExportReqVO {

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "级别")
    private Long categoryLevel;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "父类ID")
    private Long parentId;

    @Schema(description = "完整分类id -分隔")
    private String fullCategoryId;

    @Schema(description = "完整分类名称")
    private String fullCategoryName;

    @Schema(description = "顺序")
    private Integer orderSort;

    @Schema(description = "显示状态,1-启用，0：停用")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "分类类别，1-京东，2-平台")
    private Integer type;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "移动端图标")
    private String iconH5;

    @Schema(description = "经济分类编码")
    private String economyClass;

    @Schema(description = "固资经济分类编码")
    private String economyClass2;

}
