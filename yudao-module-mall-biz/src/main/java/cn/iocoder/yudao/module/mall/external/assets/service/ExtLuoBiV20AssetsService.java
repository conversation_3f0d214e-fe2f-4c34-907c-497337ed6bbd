package cn.iocoder.yudao.module.mall.external.assets.service;

import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.AssetsConfigDO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsCompleteDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsCreateDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsResp;
import cn.iocoder.yudao.module.mall.external.assets.enums.AssetClientTypeEnum;
import cn.iocoder.yudao.module.mall.external.assets.enums.AssetsMethodEnum;
import com.alibaba.excel.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ExtLuoBiV20AssetsService extends AbstractExtAssetsService {

    @Override
    public String getClientCode() {
        return AssetClientTypeEnum.LUOBI_V20.getType();
    }

    @Override
    public ExtAssetsResp<String> pushAssets(AssetsConfigDO configDO, ExtAssetsCreateDTO data) {
        if(StringUtils.isNotBlank(data.getInvoice())) {
            if(StringUtils.isNotBlank(data.getInvoiceUrl())) {
                data.setInvoiceImg(new String[]{data.getInvoiceUrl()});
            }
        }
        return super.doPushAssets(data, AssetsMethodEnum.LBV20_CREATE_ASSETS, configDO);
    }

    @Override
    public boolean completeAssets(AssetsConfigDO configDO, ExtAssetsCompleteDTO data) {
        return super.doCompleteAssets(data, AssetsMethodEnum.LBV20_COMPLETE_ASSETS, configDO);
    }

}
