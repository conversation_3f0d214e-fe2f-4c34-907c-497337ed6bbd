package cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品spu Excel 导出 Request VO，参数和 SpuPageReqVO 是一致的")
@Data
public class ProductSpuExportReqVO {

    @Schema(description = "商品品牌编号")
    private Long brandId;

    @Schema(description = "商品品牌名称")
    private String brandName;

    @Schema(description = "完整分类id -分隔")
    private String fullCategoryId;

    @Schema(description = "规格类型：0 单规格 1 多规格")
    private Integer specType;

    @Schema(description = "商品名称")
    private String spuName;

    @Schema(description = "卖点")
    private String sellPoint;

    @Schema(description = "spu单位")
    private String unit;

    @Schema(description = "商品封面图片")
    private String picUrl;

    @Schema(description = "商品轮播图片")
    private List<String> sliderPicUrls;

    @Schema(description = "商品销量")
    private Integer salesCount;

    @Schema(description = "排序字段")
    private Integer sort;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "一级分类id")
    private Long category1Id;

    @Schema(description = "二级分类id")
    private Long category2Id;

    @Schema(description = "三级分类id")
    private Long category3Id;

    @Schema(description = "一级分类名称")
    private String category1Name;

    @Schema(description = "二级分类名称")
    private String category2Name;

    @Schema(description = "三级分类名称")
    private String category3Name;

}
