package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单明细
 *
 * <AUTHOR>
 * @Date 2024/5/7
 */
@Schema(description = "管理后台 - 订单导出excel VO")
@Data
public class OrderItemExportVO {

    @Schema(description = "订单明细ID")
    @Excel(name = "订单明细ID", orderNum = "0", width = 25)
    private Long itemId;

    @Schema(description = "商品SKU")
    @Excel(name = "商品SKU", orderNum = "1", width = 25)
    private Long skuId;

    @Schema(description = "三方SKU")
    @Excel(name = "三方SKU", orderNum = "2", width = 25)
    private String skuInnerId;

    @Schema(description = "商品标签")
//    @Excel(name = "商品标签", orderNum = "3", width = 20)
    private String tagNameStr;

    @Schema(description = "商品名称")
    @Excel(name = "商品名称", orderNum = "4", width = 55)
    private String skuName;

    @Schema(description = "商品分类编码")
    private String categoryCode;

    @Schema(description = "一级分类")
    @Excel(needMerge = true, name = "一级分类", orderNum = "5", width = 20)
    private String category1Name;

    @Schema(description = "二级分类")
    @Excel(needMerge = true, name = "二级分类", orderNum = "6", width = 20)
    private String category2Name;

    @Schema(description = "三级分类")
    @Excel(needMerge = true, name = "三级分类", orderNum = "7", width = 20)
    private String category3Name;

    @Schema(description = "商品数量")
    @Excel(name = "商品数量", orderNum = "8", width = 15)
    private Integer count;

    @Schema(description = "售后商品数量")
    @Excel(name = "售后商品数量", orderNum = "9", width = 16)
    private Integer afterSaleCount;

    @Schema(description = "商品销量", example = "1")
    @Excel(needMerge = true, name = "商品销量", orderNum = "10", width = 20)
    private Integer salesCount;

    @Schema(description = "商品单价")
    @Excel(name = "商品单价", orderNum = "11", width = 20)
    private BigDecimal skuPrice;

    @Schema(description = "销售额", example = "1")
    @Excel(needMerge = true, name = "销售额", orderNum = "12", width = 20)
    private BigDecimal salesAmount;

    @Schema(description = "固资状态")
    @Excel(name = "固资状态", orderNum = "15", replace = {"建档待处理_0", "建档已提交_1", "无须建档_2", "建档中_3", "建档成功_4", "建档失败_5"}, width = 20)
    private Integer assetStatus;

    @Schema(description = "是否为固资")
    @Excel(name = "是否为固资", orderNum = "16", replace = {"是_true", "否_false"}, width = 15)
    private Boolean isAsset ;

    @Schema(description = "售后状态")
    @Excel(name = "售后状态", orderNum = "17", replace = {"未售后_0", "售后中_1", "已退款_2"}, width = 20)
    private Integer afterSaleStatus;
}
