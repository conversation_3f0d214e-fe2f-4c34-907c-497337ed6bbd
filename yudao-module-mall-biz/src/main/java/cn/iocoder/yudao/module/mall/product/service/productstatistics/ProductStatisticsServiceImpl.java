package cn.iocoder.yudao.module.mall.product.service.productstatistics;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuStatusResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.ProductStatisticsReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.ProductStatisticsRespVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.sku.ProductSkuMapper;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

@Service
@Validated
@Slf4j
public class ProductStatisticsServiceImpl extends ServiceImpl<ProductSkuMapper, ProductSkuDO> implements ProductStatisticsService {

    @Resource
    private ProductSkuService productSkuService;

    @Override
    public ProductStatisticsRespVO overview(ProductStatisticsReqVO reqVO) {
        Long tenantId = TenantContextHolder.getTenantId();
        List<ProductSkuStatusResult> productSkuStatusResults = productSkuService.getProductSkuStatusResult(tenantId, reqVO.getSupplierId());
        //上架商品数量
        ProductSkuStatusResult onSaleResult = productSkuStatusResults.stream().filter(productSkuStatusResult -> ProductSpuStatusEnum.ENABLE.getStatus().equals(productSkuStatusResult.getStatus())).findFirst().orElse(null);
        int onSaleCount = onSaleResult != null ? onSaleResult.getCount() : 0;
        //下架商品数量
        ProductSkuStatusResult offSaleResult = productSkuStatusResults.stream().filter(productSkuStatusResult -> ProductSpuStatusEnum.DISABLE.getStatus().equals(productSkuStatusResult.getStatus())).findFirst().orElse(null);
        int offSaleCount = offSaleResult != null ? offSaleResult.getCount() : 0;
        Integer totalCount = onSaleCount + offSaleCount;
        ProductStatisticsRespVO productStatisticsRespVO = new ProductStatisticsRespVO()
                .setOnSaleCount(onSaleCount)
                .setOffSaleCount(offSaleCount)
                .setTotalCount(totalCount);
        return productStatisticsRespVO;
    }
}
