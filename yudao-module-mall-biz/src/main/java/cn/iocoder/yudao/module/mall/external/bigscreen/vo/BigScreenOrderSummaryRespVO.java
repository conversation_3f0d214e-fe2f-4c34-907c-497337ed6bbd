package cn.iocoder.yudao.module.mall.external.bigscreen.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BigScreenOrderSummaryRespVO {
    @Schema(description = "今日订单数量")
    private Long todayOrderCount;

    @Schema(description = "今日订单金额")
    private BigDecimal todayOrderAmount;

    @Schema(description = "总订单数量")
    private Long totalOrderCount;

    @Schema(description = "总订单金额")
    private BigDecimal totalOrderAmount;
}
