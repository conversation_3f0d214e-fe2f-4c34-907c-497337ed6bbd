package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductSellAmountSummaryRespVO {

    @Schema(description = "商品sku")
    private Long skuId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品销售额")
    private BigDecimal sellAmount;
}
