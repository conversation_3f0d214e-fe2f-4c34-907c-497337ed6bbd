package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/4/18
 */
@Data
public class BalanceWbSkuInfoDTO {

    /**
     * 数量
     */
    @JsonProperty("skuTypeID")
    private String categoryCode;

    /**
     * 商品总价
     */
    @JsonProperty("skuPrice")
    private BigDecimal totalPrice;

    /**
     * 经济分类
     */
    @JsonProperty("jjfl")
    private String economyClass;

}
