package cn.iocoder.yudao.module.mall.product.dal.mysql.skusummary;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusummary.SkuSummaryDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.mall.product.controller.admin.skusummary.vo.*;

/**
 * 供应商上架商品数统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SkuSummaryMapper extends BaseMapperX<SkuSummaryDO> {

    default PageResult<SkuSummaryDO> selectPage(SkuSummaryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SkuSummaryDO>()
                .eqIfPresent(SkuSummaryDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(SkuSummaryDO::getSupplierType, reqVO.getSupplierType())
                .likeIfPresent(SkuSummaryDO::getSupplierName, reqVO.getSupplierName())
                .eqIfPresent(SkuSummaryDO::getListingTotal, reqVO.getListingTotal())
                .eqIfPresent(SkuSummaryDO::getDelistingTotal, reqVO.getDelistingTotal())
                .eqIfPresent(SkuSummaryDO::getDeadline, reqVO.getDeadline())
                .betweenIfPresent(SkuSummaryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SkuSummaryDO::getId));
    }

    default List<SkuSummaryDO> selectList(SkuSummaryExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SkuSummaryDO>()
                .eqIfPresent(SkuSummaryDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(SkuSummaryDO::getSupplierType, reqVO.getSupplierType())
                .likeIfPresent(SkuSummaryDO::getSupplierName, reqVO.getSupplierName())
                .eqIfPresent(SkuSummaryDO::getListingTotal, reqVO.getListingTotal())
                .eqIfPresent(SkuSummaryDO::getDelistingTotal, reqVO.getDelistingTotal())
                .eqIfPresent(SkuSummaryDO::getDeadline, reqVO.getDeadline())
                .betweenIfPresent(SkuSummaryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SkuSummaryDO::getId));
    }

}
