package cn.iocoder.yudao.module.mall.trade.controller.admin.statistics;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.OrderStatsReqVO;
import cn.iocoder.yudao.module.mall.basis.convert.stats.StatsOrderConvert;
import cn.iocoder.yudao.module.mall.basis.service.stats.StatsOrderService;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 订单概览统计")
@RestController
@RequestMapping("/trade/statistics/overview/v2")
@Validated
public class TradeStatisticsOverviewV2Controller {

    @Resource
    private MemberUserService memberUserService;
    @Resource
    private StatsOrderService statsOrderService;

    /**
     * 登录用户数
     * @return
     */
    @GetMapping("/getUserCount")
    @Operation(summary = "登录用户数")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<Long> getUserCount(){
        return success(memberUserService.getUserCount());
    }

    @GetMapping("/getSupplierSale")
    @Operation(summary = "供应商销售额")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<List<StatsSupplierSaleRespVO>> getSupplierSale() {
        OrderStatsReqVO reqVO = new OrderStatsReqVO();
        return success(StatsOrderConvert.INSTANCE.convertSupplierSaleList(statsOrderService.getSupplierSaleStats(reqVO)));
    }

    @GetMapping("/getTotalSale")
    @Operation(summary = "总销量")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<StatsSaleTotalRespVO> getTotalSale() {
        OrderStatsReqVO reqVO = new OrderStatsReqVO();
        return success(StatsOrderConvert.INSTANCE.convertTotalSale(statsOrderService.getTotalOrderSaleStats(reqVO)));
    }

    @PostMapping("/dailySaleSummary")
    @Operation(summary = "按天统计销售额")
    @PreAuthorize("@ss.hasPermission('statistics:overview')")
    public CommonResult<List<OrderSaleDaySummaryRespVO>> dailySaleSummary(@Valid @RequestBody OrderSaleSummaryReqVO orderSaleSummaryReqVO) {
        OrderStatsReqVO reqVO = new OrderStatsReqVO();
        reqVO.setStartDate(DateUtil.format(orderSaleSummaryReqVO.getStartTime(), "yyyy-MM-dd"));
        reqVO.setEndDate(DateUtil.format(orderSaleSummaryReqVO.getEndTime(), "yyyy-MM-dd"));
        return success(StatsOrderConvert.INSTANCE.convertDailySummaryList(statsOrderService.getOrderSaleStatsList(reqVO)));
    }

    @PostMapping("/monthlySalesSummary")
    @Operation(summary = "按月统计销售额")
    @PreAuthorize("@ss.hasPermission('trade:order-statistics:query')")
    public CommonResult<List<OrderSaleMonthSummaryRespVO>> monthlySalesSummary(@Valid @RequestBody OrderSaleMonthSummaryReqVO orderSaleMonthSummaryReqVO) {
        OrderStatsReqVO reqVO = new OrderStatsReqVO();
        reqVO.setStartDate(orderSaleMonthSummaryReqVO.getStartMonth().atDay(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        reqVO.setEndDate(orderSaleMonthSummaryReqVO.getEndMonth().atEndOfMonth().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        reqVO.setTimeType(2);
        return success(StatsOrderConvert.INSTANCE.convertMonthlySummaryList(statsOrderService.getOrderSaleStatsList(reqVO)));
    }

}
