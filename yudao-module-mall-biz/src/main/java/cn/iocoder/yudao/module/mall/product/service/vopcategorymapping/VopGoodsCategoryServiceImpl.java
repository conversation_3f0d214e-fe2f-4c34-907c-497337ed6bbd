package cn.iocoder.yudao.module.mall.product.service.vopcategorymapping;

import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsCategoryItem;
import cn.iocoder.yudao.module.mall.product.convert.vopgoods.VopGoodsConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopGoodsCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.category.ConfigProductCategoryMapper;
import cn.iocoder.yudao.module.mall.product.dal.mysql.category.ProductCategoryMapper;
import cn.iocoder.yudao.module.mall.product.dal.mysql.vopcategorymapping.VopGoodsCategoryMapper;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetCategoryInfoListResponse;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetChildCategoryListResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Validated
public class VopGoodsCategoryServiceImpl implements VopGoodsCategoryService {

    @Resource
    private VopGoodsCategoryMapper vopCategoryMapper;
    @Resource
    private VopGoodsService vopGoodsService;
//    @Resource
//    private ProductCategoryMapper productCategoryMapper;
//    @Resource
//    private ConfigProductCategoryMapper configProductCategoryMapper;


    @Override
    public List<VopGoodsCategoryDO> getVopGoodsPrimaryCategoryList() {
        List<VopGoodsCategoryDO> vopGoodsCategoryDOS = vopCategoryMapper.selectPrimaryJdList();
        if (vopGoodsCategoryDOS == null) {
            return Collections.emptyList();
        }
        return vopGoodsCategoryDOS;
    }

    @Override
    public List<VopGoodsCategoryItem> getChildCategoryList(Long parentCategoryId) {
        VopGoodsGetCategoryInfoListResponse response = vopGoodsService.getCategoryInfoList(String.valueOf(parentCategoryId));
        List<com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getCategoryInfoList.GetCategoryInfoGoodsResp> list = response.getOpenRpcResult().getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        VopGoodsGetChildCategoryListResponse listResponse = vopGoodsService.getChildCategoryList(list.get(0).getCategoryId());
        List<com.jd.open.api.sdk.domain.vopsp.CategoryInfoGoodsProvider.response.getChildCategoryList.GetCategoryInfoGoodsResp> childList = listResponse.getOpenRpcResult().getResult();
        if (CollectionUtils.isEmpty(childList)) {
            return Collections.emptyList();
        }
        List<VopGoodsCategoryItem> itemList = new ArrayList<>();
        childList.forEach(item -> {
            if (Objects.equals(item.getNeedShow(), ProductCategoryStatusEnum.DISABLE.getStatus())) {
                return;
            }
            VopGoodsCategoryItem categoryItem = VopGoodsConvert.INSTANCE.convertCategory(item);
            //不可递归 数据量太大了 vop查询很慢
            itemList.add(categoryItem);
        });
        return itemList;
    }
}
