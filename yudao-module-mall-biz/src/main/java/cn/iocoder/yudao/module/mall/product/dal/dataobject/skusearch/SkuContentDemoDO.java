package cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.util.List;


/**
 * SKU搜索演示DO
 */
@Data
@EqualsAndHashCode()
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "es_mall_sku")
public class SkuContentDemoDO implements Serializable {

    @Id
    private Long skuId;

    @Field(type = FieldType.Long)
    private Long tenantId;

    @Field(type = FieldType.Long)
    private Long supplierId;

    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String spuName;

    @Field(type = FieldType.Long)
    private Long categoryId1;

    /**
     * 商品分类ID路径, 以中逗号分隔, 如 101,1006,10007
     */
    @Field(type = FieldType.Keyword)
    private String categoryIdPath;

    @Field(type = FieldType.Keyword)
    private String categoryName1;

    @Field(type = FieldType.Long)
    private Long brandId;

    @Field(type = FieldType.Integer)
    private Integer status;

    @Field(type = FieldType.Nested)
    private List<SkuSpecValueDO> specValueList;


}
