package cn.iocoder.yudao.module.mall.product.dal.mysql.spec;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSkuSpecDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * sku规格关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSkuSpecMapper extends BaseMapperX<ProductSkuSpecDO> {

    void deleteById2(Long id);

    void deleteBySkuId(Long skuId);

}
