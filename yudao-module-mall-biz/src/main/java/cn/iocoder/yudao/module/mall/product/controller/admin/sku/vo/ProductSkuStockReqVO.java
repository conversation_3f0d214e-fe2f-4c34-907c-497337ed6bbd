package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商品 SKU 更新价格 VO")
@Data
@ToString(callSuper = true)
public class ProductSkuStockReqVO {

    @Schema(description = "主键", example = "1024")
    @NotNull
    private Long id;

    @Schema(description = "spuId", example = "1024")
    @NotNull
    private Long spuId;

    @Schema(description = "调整库存", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "调整库存不能为空")
    @Min(-99999)
    @Max(99999)
    private Integer adjustStock;

}
