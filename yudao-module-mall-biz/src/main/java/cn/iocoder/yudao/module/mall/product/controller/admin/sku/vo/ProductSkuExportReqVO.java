package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@ToString(callSuper = true)
public class ProductSkuExportReqVO {
    @Schema(description = "商品SKU编号")
    private Long id;

    @Schema(description = "商品三方SKU编号")
    private String skuInnerId;

    @Schema(description = "商品品牌编号")
    private Long brandId;

    @Schema(description = "商品品牌名称")
    private String brandName;

    @Schema(description = "完整分类id -分隔")
    private String fullCategoryId;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "排序字段")
    private Integer sort;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "列出状态")
    private Integer showStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "一级分类id")
    private Long category1Id;

    @Schema(description = "二级分类id")
    private Long category2Id;

    @Schema(description = "三级分类id")
    private Long category3Id;

    @Schema(description = "一级分类名称")
    private String category1Name;

    @Schema(description = "二级分类名称")
    private String category2Name;

    @Schema(description = "三级分类名称")
    private String category3Name;

    @Schema(description = "最低销量")
    private Integer salesCountMin;

    @Schema(description = "最高销量")
    private Integer salesCountMax;

    @Schema(description = "最低价格")
    private Integer salePriceMin;

    @Schema(description = "最高价格")
    private Integer salePriceMax;

    @Schema(description = "排序类型")
    private Integer sortType;

    @Schema(description = "seo运营状态")
    private Integer seoStatus;

    @Schema(description = "标签")
    private List<Long> tags;

    @Schema(description = "标签状态，0-未打标签 1-已打标签")
    private Integer tagStatus;
}