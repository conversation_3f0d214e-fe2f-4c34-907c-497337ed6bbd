package cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SupplierSkuSummaryRespVO {

    @Schema(description = "供应商Id")
    private Long supplierId;

    @Schema(description = "供应商名字")
    private String supplierName;

    @Schema(description = "统计商品总数、上架总数、下架总数")
    private SkuSummaryRespVO skuSummaryRespVO;

    @Schema(description = "接近上限90%告警")
    private boolean warn;
}
