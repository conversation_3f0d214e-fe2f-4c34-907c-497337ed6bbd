package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 交易订单的分页项 Response VO")
@Data
public class TradeOrderPageItemRespVO extends TradeOrderBaseVO {

    @Schema(description = "账单结算状态")
    private Integer billStatus;

    @Schema(description = "是否需要推送到业财融合")
    private Boolean ycrhNeed;

    @Schema(description = "订单业财融合状态")
    private String ycrhStatus;

    @Schema(description = "订单业财融合预约单号")
    private String voucherNo;

    /**
     * 订单项列表
     */
    @Schema(description = "管理后台 - 订单项列表")
    private List<TradeOrderItemBaseVO> items;

}
