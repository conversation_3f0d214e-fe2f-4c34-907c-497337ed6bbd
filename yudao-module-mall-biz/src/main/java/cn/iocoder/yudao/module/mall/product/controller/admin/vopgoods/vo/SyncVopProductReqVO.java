package cn.iocoder.yudao.module.mall.product.controller.admin.vopgoods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 按分类同步商品 Request VO")
@Data
public class SyncVopProductReqVO {

    @Schema(description = "categoryIds")
    private List<Long> categoryIds;

    @Schema(description = "maxPageSize", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "maxPageSize不能为空")
    private Long maxPageSize;
}
