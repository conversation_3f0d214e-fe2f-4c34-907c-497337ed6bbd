package cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 标签分组 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ProductTagGroupBaseVO {

    @Schema(description = "分组名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分组名称不能为空")
    private String name;

    @Schema(description = "分组描述", requiredMode = Schema.RequiredMode.REQUIRED)
    private String memo;

    @Schema(description = "状态, 0-启用，1-禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态, 0-启用，1-禁用不能为空")
    private Integer status;

}
