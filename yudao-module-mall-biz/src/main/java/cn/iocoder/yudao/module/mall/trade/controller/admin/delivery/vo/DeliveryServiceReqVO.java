package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "虚拟商品、服务类发货请求 - Request VO")
@Data
public class DeliveryServiceReqVO {
    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @NotNull(message = "订单编号不能为空")
    private String orderNo;
}
