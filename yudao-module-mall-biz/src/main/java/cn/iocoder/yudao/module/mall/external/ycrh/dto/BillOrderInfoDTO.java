package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class BillOrderInfoDTO {

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 订单状态 0: 待提交财务 1:已提交财务 2:已接单 3:已分单 4: 已制单 9：已退单
     */
    private String status;

    /**
     * 预约单号
     */
    private String voucherNo;

    /**
     * 凭证附件编号
     */
    @JsonProperty("pzbh")
    private String pzNo;

    /**
     * 原因
     */
    private String reason;

    /**
     * 更新时间
     */
    private String updateTime;
}
