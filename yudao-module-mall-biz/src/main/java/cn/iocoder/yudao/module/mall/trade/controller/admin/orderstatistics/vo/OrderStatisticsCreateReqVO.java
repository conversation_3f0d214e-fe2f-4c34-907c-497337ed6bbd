package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 订单统计创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderStatisticsCreateReqVO extends OrderStatisticsBaseVO {

}
