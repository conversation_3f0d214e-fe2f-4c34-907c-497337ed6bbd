package cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.jd.open.api.sdk.domain.vopsp.SearchGoodsProvider.response.searchSku.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class VopGoodsSearchPageResultResp {

    /**
     * 商品分页数据
     */
    @Schema(description = "商品分页数据")
    private PageResult<VopSkuGoodsPageItem> pageResult;

    /**
     * 相关分类汇总信息
     */
    @Schema(description = "相关分类汇总信息")
    private List<CategorySearchAggGoodsResp> categoryAggList;

    /**
     * 品牌汇总信息
     */
    @Schema(description = "品牌汇总信息")
    private List<BrandSearchAggGoodsResp> brandAggList;

    /**
     * 价格汇总信息
     */
    @Schema(description = "价格汇总信息")
    private List<PriceSearchAggGoodsResp> priceAggList;

    /**
     * 扩展属性具体类目集合
     */
    @Schema(description = "扩展属性具体类目集合")
    private List<ExtAttrSearchAggGoodsResp> extAttrAggList;
}
