package cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

/**
 * 商品评价回复 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ProductCommentReplyBaseVO {

    @Schema(description = "评价id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "评价id不能为空")
    private Long commentId;

    @Schema(description = "供应商id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "供应商id不能为空")
    private Long supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "会员id不能为空")
    private Long memberId;

    @Schema(description = "会员昵称")
    private String nickName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "评价内容")
    private String content;

    @Schema(description = "审核状态 0-待审核 1-审核通过 2-审核驳回", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "审核状态 0-待审核 1-审核通过 2-审核驳回不能为空")
    private Integer auditStatus;

    @Schema(description = "回复者ip")
    private String clientIp;

    @Schema(description = "回复者地区")
    private String clientArea;

    @Schema(description = "举报次数")
    private Integer reportCount;

}
