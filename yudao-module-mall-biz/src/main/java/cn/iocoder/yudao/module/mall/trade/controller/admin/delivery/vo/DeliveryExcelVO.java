package cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 物流信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class DeliveryExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("物流公司编码,一律用小写字母")
    private String com;

    @ExcelProperty("物流单号")
    private String num;

    @ExcelProperty("是否签收标记，0未签收，1已签收，请忽略，明细状态请参考state字段")
    private Integer isCheck;

    @ExcelProperty("出发行政区域编码")
    private String fromNumber;

    @ExcelProperty("出发行政区域名字")
    private String fromName;

    @ExcelProperty("目的地行政区域编码")
    private String toNumber;

    @ExcelProperty("目的地行政区域名字")
    private String toName;

    @ExcelProperty("当前行政区域编码")
    private String curNumber;

    @ExcelProperty("当前行政区域名字")
    private String curName;

    @ExcelProperty("订阅状态，默认为0未订阅，1订阅")
    private Integer subscribe;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("物流单当前状态，默认为0在途，1揽收，2疑难，3签收，4退签，5派件，8清关，14拒签等10个基础物流状态，如需要返回高级物流状态，请参考 resultv2 传值")
    private Integer state;

    @ExcelProperty("订单编号")
    private String orderNo;

    @ExcelProperty("物流来源")
    private String source;

}
