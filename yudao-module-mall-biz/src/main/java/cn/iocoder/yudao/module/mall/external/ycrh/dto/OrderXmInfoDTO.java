package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class OrderXmInfoDTO {

   /**
    * 部门编号
    */
   @JsonProperty("bmbh")
   private String  departmentNo;

   /**
    * 项目编号
    */
   @JsonProperty("xmbh")
   private String  projectNo;

   /**
    * 项目负责人编号
    */
   @JsonProperty("xmfzrbh")
   private String  chargeNo;

   /**
    * 使用金额
    */
   @JsonProperty("JE")
   private BigDecimal amount;

   /**
    * 商品编号
    */
   private String  skuId;

   /**
    * 是否对公结算（privatePaymentList不为空时必填）  1：对公（默认）2：对私
    */
   @JsonProperty("isDg")
   private String  isCorporate;
}
