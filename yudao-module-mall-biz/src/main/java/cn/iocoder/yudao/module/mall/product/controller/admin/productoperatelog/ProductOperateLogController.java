package cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo.ProductOperateLogPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo.ProductOperateLogRespVO;
import cn.iocoder.yudao.module.mall.product.convert.productoperatelog.ProductOperateLogConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productoperatelog.ProductOperateLogDO;
import cn.iocoder.yudao.module.mall.product.service.productoperatelog.ProductOperateLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 商品操作日志记录
 */
@Tag(name = "管理后台 - 商品操作日志记录")
@RestController
@RequestMapping("/product/operate-log")
@Validated
public class ProductOperateLogController {

    @Resource
    private ProductOperateLogService operateLogService;


    /**
     * 获得商品操作日志记录
     * @param id
     * @return
     */
    @GetMapping("/get")
    @Operation(summary = "获得商品操作日志记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:operate-log:query')")
    public CommonResult<ProductOperateLogRespVO> getOperateLog(@RequestParam("id") Long id) {
        ProductOperateLogDO operateLog = operateLogService.getOperateLog(id);
        return success(ProductOperateLogConvert.INSTANCE.convert(operateLog));
    }

    /**
     * 获得商品操作日志记录列表
     * @param ids
     * @return
     */
    @GetMapping("/list")
    @Operation(summary = "获得商品操作日志记录列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('product:operate-log:query')")
    public CommonResult<List<ProductOperateLogRespVO>> getOperateLogList(@RequestParam("ids") Collection<Long> ids) {
        List<ProductOperateLogDO> list = operateLogService.getOperateLogList(ids);
        return success(ProductOperateLogConvert.INSTANCE.convertList(list));
    }

    /**
     * 获得商品操作日志记录分页
     * @param pageVO
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "获得商品操作日志记录分页")
    @PreAuthorize("@ss.hasPermission('product:operate-log:query')")
    public CommonResult<PageResult<ProductOperateLogRespVO>> getOperateLogPage(@Valid ProductOperateLogPageReqVO pageVO) {
        PageResult<ProductOperateLogDO> pageResult = operateLogService.getOperateLogPage(pageVO);
        return success(ProductOperateLogConvert.INSTANCE.convertPage(pageResult));
    }


}
