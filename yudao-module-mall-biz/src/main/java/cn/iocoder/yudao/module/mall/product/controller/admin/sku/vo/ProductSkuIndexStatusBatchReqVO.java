package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 商品SKU索引状态批量查询请求VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "商品SKU索引状态批量查询请求VO")
public class ProductSkuIndexStatusBatchReqVO {

    @Schema(description = "SKU ID列表", requiredMode = Schema.RequiredMode.REQUIRED, example = "[1024, 1025, 1026]")
    @NotEmpty(message = "SKU ID列表不能为空")
    @Size(max = 100, message = "单次查询SKU数量不能超过100个")
    private List<Long> skuIds;
}
