package cn.iocoder.yudao.module.mall.product.dal.dataobject.wish;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.mall.product.enums.wish.ProductWishStatusEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 心愿单 DO
 *
 * <AUTHOR>
 */
@TableName("product_wish")
@KeySequence("product_wish_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductWishDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商品分类ID,逗号分隔
     */
    private String categoryIds;
    /**
     * 商品分类名称,/分隔
     */
    private String categoryNames;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 型号
     */
    private String model;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 预计下单时间
     */
    private LocalDateTime orderTime;
    /**
     * 产品链接
     */
    private String productLink;
    /**
     * 参数描述
     */
    private String productMemo;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 状态: {@link ProductWishStatusEnum}
     */
    private Integer status;
    /**
     * 回复内容
     */
    private String replyContent;
    /**
     * 回复扩展参数
     */
    private String extParams;
    /**
     * 会员用户姓名
     */
    private String userName;
    /**
     * 会员用户id
     */
    private Long userId;

}
