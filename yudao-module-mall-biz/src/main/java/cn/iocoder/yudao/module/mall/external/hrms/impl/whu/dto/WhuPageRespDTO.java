package cn.iocoder.yudao.module.mall.external.hrms.impl.whu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 分页查询接口响应结果
 * <AUTHOR>
 */
@Data
public class WhuPageRespDTO<T> extends WhuPageDTO {

    @JsonProperty("data_struct")
    private Map<String, String> dataStruct;

    @JsonProperty("encrypted_field")
    private String encryptedField;

    @JsonProperty("data")
    private List<T> list;

}
