package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/11
 */
@Data
public class OrderQueryRespDTO {

    /**
     * 用户识别号
     */
    private String appid;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单状态
     * “1”:已推送
     * “2”:审批中
     * “3”:已审批
     * “4”:已下单
     * “5”:待结算
     * “6”:结算中
     * “7”:已结算
     * “0”:已取消
     * “11”:已拆分
     * “21”:已驳回
     */
    private String orderStatus;
    /**
     * 制单状态
     * “0”:未制单
     * “1”:已制单
     * “2”:已退单
     */
    private String repayStatus;
    /**
     * 账单编号
     */
    private String billNo;
    /**
     * 预约单号
     */
    private String voucherNo;
    /**
     * 订单金额
     */
    private BigDecimal orderAmount;
    /**
     * 退款金额
     */
    private BigDecimal returnAmount;
    /**
     * 冻结流水记录
     */
    @JsonProperty("djRecordList")
    private List<OrderFrozenRecordDTO> frozenRecordList;

}
