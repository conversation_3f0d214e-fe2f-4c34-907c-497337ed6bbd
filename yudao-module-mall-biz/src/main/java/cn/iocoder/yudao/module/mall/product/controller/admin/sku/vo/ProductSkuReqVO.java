package cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商品 SPU 分页 Request VO")
@Data
@ToString(callSuper = true)
public class ProductSkuReqVO {

    @Schema(description = "商品品牌编号")
    @NotNull(message = "skuId不能为空")
    private Long skuId;

    @Schema(description = "供应商编号")
    @NotNull(message = "supplierId不能为空")
    private Long supplierId;

}
