package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
@EqualsAndHashCode(exclude = { "status", "statusMemo" })
public class UserProjectRespDTO {

    /**
     * 部门编号
     */
    @JsonProperty("bmbh")
    private String departmentNo;

    /**
     * 部门名称
     */
    @JsonProperty("bmmc")
    private String departmentName;

    /**
     * 项目编号
     */
    @JsonProperty("xmbh")
    private String projectNo;

    /**
     * 项目名称
     */
    @JsonProperty("xmmc")
    private String projectName;

    /**
     * 项目类型
     */
    @JsonProperty("xmlx")
    private String projectType;

    /**
     * 项目类型名称
     */
    @JsonProperty("xmlxmc")
    private String projectTypeName;

    /**
     * 是否国库
     * 1:是 0:否
     */
    @JsonProperty("isgk")
    private String isTreasury ;

    /**
     * 国库信息码
     */
    @JsonProperty("gkxxm")
    private String treasuryNo;

    /**
     * 项目大类 01-预算 02-科研 03-专项 04-基建 05-收入 06-基金 07-代管 99-其他
     */
    @JsonProperty("cclass")
    private String projectClass;

    /**
     * 主负责人编号
     */
    @JsonProperty("zfzrbh")
    private String chargeNo;

    /**
     * 主负责人姓名
     */
    @JsonProperty("zfzrxm")
    private String chargeName;

    /**
     * 允许支出经济分类 逗号分割
     */
    @JsonProperty("jjflzckm")
    private String economyClass;

    /**
     * 不允许支出经济分类 逗号分割
     */
    @JsonProperty("nojjflzckm")
    private String noEconomyClass;

    /**
     *项目角色 1:第一项目主负责人  2:主负责人 3:报销员(查询经费,无使用权限)
     */
    @JsonProperty("xmjs")
    private String projectRole;

    /**
     * 授权金额 （项目角色为3时返回）
     */
    @JsonProperty("sqje")
    private String authAmount;

    /**
     * 授权截止日期
     */
    @JsonProperty("sqjzrq")
    private String authEndTime;

    /**
     * 昨日余额
     */
    @JsonProperty("zrye")
    private String yesterdayAmount;

    /**
     * 财政支出
     */
    @JsonProperty("isczzc")
    private String fiscalExpenditure;

    /**
     * 数据来源（ZW账务，WB网报）
     */
    @JsonProperty("sjly")
    private String source;

    /**
     * 是否可用(skuTypeID不为空时返回)
     * 1:可用 0:不可用 2:可用 须补充材料
     */
    @JsonProperty("isks")
    private String status;

    /**
     * 状态备注
     */
    @JsonIgnore
    private String statusMemo;

}
