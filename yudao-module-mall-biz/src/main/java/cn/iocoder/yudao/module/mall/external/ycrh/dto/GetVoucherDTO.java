package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 获取单据状态
 * <AUTHOR>
 * @date 2023/7/29
 */
@Data
public class GetVoucherDTO {

    /**
     * 业务流水号
     */
    @JsonProperty("ywlsh")
    private String businessNo;

    /**
     * 模块代码（由审批系统分配）
     */
    @JsonProperty("mkdm")
    private String moduleName;

    /**
     * 业务编号 业务系统根据单据业务自定义
     */
    @JsonProperty("ywbh")
    private String businessCode;



}
