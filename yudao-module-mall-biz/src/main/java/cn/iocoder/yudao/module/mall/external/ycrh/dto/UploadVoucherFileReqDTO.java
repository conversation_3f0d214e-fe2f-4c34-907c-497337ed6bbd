package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 凭证附件上传
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class UploadVoucherFileReqDTO {

    /**
     * 查询类型("0":以识别结果为准 "1":以传入参数为准)
     */
    private String fileName;

    /**
     * 附件类型编号:
     * "00":凭证信息
     * "01":业务报销单
     * "02":收入申报税表
     * "03":电子发票
     * "04":纸质发票
     * "05":合同信息
     * "06":银行回单
     * "07":报销申请单
     * "08":项目预算申请书
     * "09":审批证明类
     * "10":科研项目入账单
     * "11":科研结题结转单
     * "98":审批过程影像
     * "99":其他附件
     */
    @JsonProperty("fjlx")
    private String fileType;

    /**
     * 文件内容的Base64字符串
     */
    private String file;

    /**
     * 是否覆盖
     * 0:否(默认)
     * 1:是
     */
    private String isOverwrite;



}
