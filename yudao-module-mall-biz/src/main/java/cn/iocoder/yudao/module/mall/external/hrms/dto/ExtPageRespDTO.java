package cn.iocoder.yudao.module.mall.external.hrms.dto;

import lombok.Data;

import java.util.List;

@Data
public class ExtPageRespDTO<T> {

    private Integer pageNo;
    private Integer pageSize;
    private Long total;
    private List<T> list;

    public long getTotalPages() {
        if(total == null || pageSize == null) {
            return 0;
        }

        return (total + pageSize - 1) / pageSize;
    }

}
