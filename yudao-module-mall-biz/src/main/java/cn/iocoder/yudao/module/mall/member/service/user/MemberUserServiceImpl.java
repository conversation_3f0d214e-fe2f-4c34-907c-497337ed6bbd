package cn.iocoder.yudao.module.mall.member.service.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.common.util.string.Sm2Utils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.mall.external.hrms.HrmsClient;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtDeptDTO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtPageReqDTO;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtPageRespDTO;
import cn.iocoder.yudao.module.mall.external.ycrh.YcrhClient;
import cn.iocoder.yudao.module.mall.external.ycrh.dto.YgInfoRespDTO;
import cn.iocoder.yudao.module.mall.member.controller.admin.user.vo.MemberUserCreateReqVO;
import cn.iocoder.yudao.module.mall.member.controller.admin.user.vo.MemberUserUpdateReqVO;
import cn.iocoder.yudao.module.mall.member.controller.admin.user.vo.UserQueryReqVO;
import cn.iocoder.yudao.module.mall.member.controller.app.user.vo.AppUserUpdateMobileReqVO;
import cn.iocoder.yudao.module.mall.member.controller.app.user.vo.AppUserUpdatePasswordReqVO;
import cn.iocoder.yudao.module.mall.member.convert.user.UserConvert;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.member.dal.mysql.user.MemberUserMapper;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptCreateReqDTO;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.sms.SmsCodeApi;
import cn.iocoder.yudao.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import cn.iocoder.yudao.module.system.enums.sms.SmsSceneEnum;
import com.alibaba.ttl.TtlRunnable;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.module.mall.member.enums.ErrorCodeConstants.*;

/**
 * 会员 User Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Valid
@Slf4j
public class MemberUserServiceImpl extends ServiceImpl<MemberUserMapper, MemberUserDO>  implements MemberUserService {

    @Resource
    private MemberUserMapper memberUserMapper;
    @Resource
    private FileApi fileApi;
    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private YcrhClient ycrhClient;
    @Resource
    private ThreadPoolExecutor threadPoolExecutor;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private HrmsClient hrmsClient;

    @Override
    public MemberUserDO getUserByMobile(String mobile) {
        try {
            return memberUserMapper.selectByMobile(mobile);
        } catch(TooManyResultsException ee) {
            log.error("手机号{}存在多条会员数据，需尽快处理", mobile);
            throw exception(USER_MOBILE_REPEAT);
        }
    }

    @Override
    public MemberUserDO getUserByUserNo(String userNo) {
        try {
            return memberUserMapper.selectByUserNo(userNo);
        } catch(TooManyResultsException ee) {
            log.error("用户编号{}存在多条会员数据，需尽快处理", userNo);
            throw exception(USER_NO_REPEAT);
        }
    }

    @Override
    public boolean fillUserMobile(Long userId, String mobile) {
        MemberUserDO user = getUser(userId);
        if(user != null && StrUtil.isBlank(user.getMobile())) {
            try {
                memberUserMapper.updateById(new MemberUserDO().setId(userId).setMobile(mobile));
                return true;
            } catch (Exception e) {
                log.error("fillUserMobile error: {}, {}", userId, mobile);
            }
        }

        return false;
    }

    @Override
    public List<MemberUserDO> getUserListByNickname(String nickname) {
        return memberUserMapper.selectListByNicknameLike(nickname);
    }

    @Override
    public MemberUserDO createUserIfAbsent(String mobile, String registerIp) {
        // 用户已经存在
        MemberUserDO user = memberUserMapper.selectByMobile(mobile);
        if (user != null) {
            return user;
        }
        // 用户不存在，则进行创建
        return this.createUser(mobile, registerIp);
    }

    private MemberUserDO createUser(String mobile, String registerIp) {
        // 生成密码
        String password = IdUtil.fastSimpleUUID();
        // 插入用户
        MemberUserDO user = new MemberUserDO();
        user.setMobile(mobile);
        // 默认开启
        user.setStatus(CommonStatusEnum.ENABLE.getStatus());
        // 加密密码
        user.setPassword(encodePassword(password));
        user.setRegisterIp(registerIp);
        memberUserMapper.insert(user);
        return user;
    }

    @Override
    public void updateUserLogin(Long id, String loginIp) {
        memberUserMapper.updateById(new MemberUserDO().setId(id)
                .setLoginIp(loginIp).setLoginDate(LocalDateTime.now()));
    }

    @Override
    public MemberUserDO getUser(Long id) {
        return memberUserMapper.selectById(id);
    }

    @Override
    public List<MemberUserDO> getUserList(Collection<Long> ids) {
        return memberUserMapper.selectBatchIds(ids);
    }

    @Override
    public void updateUserNickname(Long userId, String nickname) {
        MemberUserDO user = this.checkUserExists(userId);
        // 仅当新昵称不等于旧昵称时进行修改
        if (nickname.equals(user.getNickname())) {
            return;
        }
        MemberUserDO userDO = new MemberUserDO();
        userDO.setId(user.getId());
        userDO.setNickname(nickname);
        memberUserMapper.updateById(userDO);
    }

    @Override
    public String updateUserAvatar(Long userId, InputStream avatarFile) throws Exception {
        this.checkUserExists(userId);
        // 创建文件
        String avatar = fileApi.createFile(IoUtil.readBytes(avatarFile));
        // 更新头像路径
        memberUserMapper.updateById(MemberUserDO.builder().id(userId).avatar(avatar).build());
        return avatar;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserMobile(Long userId, AppUserUpdateMobileReqVO reqVO) {
        // 检测用户是否存在
        checkUserExists(userId);
        // TODO 芋艿：oldMobile 应该不用传递

        // 校验旧手机和旧验证码
        smsCodeApi.useSmsCode(new SmsCodeUseReqDTO().setMobile(reqVO.getOldMobile()).setCode(reqVO.getOldCode())
                .setScene(SmsSceneEnum.MEMBER_UPDATE_MOBILE.getScene()).setUsedIp(getClientIP()));
        // 使用新验证码
        smsCodeApi.useSmsCode(new SmsCodeUseReqDTO().setMobile(reqVO.getMobile()).setCode(reqVO.getCode())
                .setScene(SmsSceneEnum.MEMBER_UPDATE_MOBILE.getScene()).setUsedIp(getClientIP()));

        // 更新用户手机
        memberUserMapper.updateById(MemberUserDO.builder().id(userId).mobile(reqVO.getMobile()).build());
    }

    @Override
    public void updateUserPassword(Long userId, AppUserUpdatePasswordReqVO reqVO) {
        // 检测用户是否存在
        checkUserExists(userId);

        MemberUserDO user = memberUserMapper.selectById(userId);
        if(!isPasswordMatch(Sm2Utils.decrypt(reqVO.getOldPassword()), user.getPassword())) {
            throw exception(USER_OLD_PASSWORD_ERROR);
        }

        user.setPassword(encodePassword(Sm2Utils.decrypt(reqVO.getNewPassword())));
        memberUserMapper.updateById(user);
    }

    @Override
    public boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        return StringUtils.isNotBlank(encodedPassword) && passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Override
    public PageResult<MemberUserDO> getUserPage(UserQueryReqVO reqVO) {
        return memberUserMapper.selectPage(reqVO, new LambdaQueryWrapperX<MemberUserDO>()
                .eq(StringUtils.isNotBlank(reqVO.getUserNo()), MemberUserDO::getUserNo, reqVO.getUserNo())
                .eq(StringUtils.isNotBlank(reqVO.getMobile()), MemberUserDO::getMobile, reqVO.getMobile())
                .betweenIfPresent(MemberUserDO::getLoginDate, reqVO.getLoginDate())
                .eqIfPresent(MemberUserDO::getStatus, reqVO.getStatus())
                .eqIfPresent(MemberUserDO::getDeptId, reqVO.getDeptId())
                .in(CollUtil.isNotEmpty(reqVO.getDeptIds()), MemberUserDO::getDeptId, reqVO.getDeptIds())
                .likeRight(StringUtils.isNotBlank(reqVO.getNickname()), MemberUserDO::getNickname, reqVO.getNickname())
                .likeRight(StringUtils.isNotBlank(reqVO.getName()), MemberUserDO::getName, reqVO.getName())
                .orderByDesc(MemberUserDO::getId)
        );
    }

    @Override
    public void deleteById(Long id) {
        memberUserMapper.deleteById(id);
    }

    @Override
    public Long createMemberUser(@Valid MemberUserCreateReqVO createReqVO) {
        // 校验员工编号
        validateUserNo(null, createReqVO.getUserNo());
        // 校验手机号
        validateUserMobile(null, createReqVO.getMobile());

        createReqVO.setRegisterIp(ServletUtils.getClientIP());
        if(createReqVO.getAvatar() == null) {
            createReqVO.setAvatar("");
        }
        createReqVO.setPassword(encodePassword(createReqVO.getPassword()));
        MemberUserDO memberUserDO = UserConvert.INSTANCE.convert(createReqVO);
        memberUserMapper.insert(memberUserDO);

        return memberUserDO.getId();
    }

    @Override
    public void updateMemberUser(@Valid MemberUserUpdateReqVO updateReqVO) {
        // 校验员工编号
        validateUserNo(updateReqVO.getId(), updateReqVO.getUserNo());
        // 校验手机号
        validateUserMobile(updateReqVO.getId(), updateReqVO.getMobile());
        if(StringUtils.isNotBlank(updateReqVO.getPassword())) {
            updateReqVO.setPassword(encodePassword(updateReqVO.getPassword()));
        }
        MemberUserDO memberUserDO = UserConvert.INSTANCE.convert(updateReqVO);
        memberUserMapper.updateById(memberUserDO);
    }

    @Override
    public Long getUserCount() {
        return memberUserMapper.selectCount();
    }

    private void validateUserNo(Long id, String userNo) {
        MemberUserDO memberUser = memberUserMapper.selectByUserNo(userNo);
        if (memberUser != null && !memberUser.getId().equals(id)) {
            throw exception(USER_NO_EXISTS);
        }
    }

    private void validateUserMobile(Long id, String mobile) {
        if(StringUtils.isBlank(mobile)) {
            return;
        }
        long count = memberUserMapper.countByMobile(mobile);
        if(count > 1) {
            throw exception(USER_MOBILE_EXISTS);
        }
        MemberUserDO memberUser = memberUserMapper.selectByMobile(mobile);
        if(memberUser == null) {
            return;
        }
        if(id == null || !memberUser.getId().equals(id)) {
            throw exception(USER_MOBILE_EXISTS);
        }
    }

    @Override
    public void fillDetailByUserNo(String userNo) {
        // 通过业财异步收集用户信息
        threadPoolExecutor.execute(TtlRunnable.get(() -> {
            try {
                MemberUserDO memberUserDO = memberUserMapper.selectByUserNo(userNo);
                if(memberUserDO == null) {
                    log.info("userNo未入库，忽略填充信息");
                    return;
                }

                if(hrmsClient.isExtHrmsServiceActive()) {
                    hrmsClient.fillMemberInfo(memberUserDO.getUserNo(), memberUserDO);
                } else if(ycrhClient.isYcrhConfigValid()) {
                    YgInfoRespDTO ygInfo = ycrhClient.getYgInfo(userNo);
                    if(ygInfo == null) {
                        log.info("userNo不存在，忽略填充其它信息");
                        return;
                    }
                    fillMemberInfoByYcrh(memberUserDO, ygInfo);
                } else {
                    log.info("无会员信息客户端，填充处理忽略...");
                    return;
                }

                fillDeptId((memberUserDO));
                memberUserMapper.updateById(memberUserDO);
            } catch(Exception e) {
                log.error("fillDetailByUserNo error: ", e);
            }
        }));
    }

    private void fillMemberInfoByYcrh(MemberUserDO memberUserDO, YgInfoRespDTO ygInfo) {
        if(StringUtils.isNotBlank(ygInfo.getYgName())){
            memberUserDO.setName(ygInfo.getYgName());
        }
        if(StringUtils.isNotBlank(ygInfo.getYgName())) {
            memberUserDO.setNickname(ygInfo.getYgName());
        }
        if(StringUtils.isNotBlank(ygInfo.getDepartmentNo())){
            memberUserDO.setDeptCode(ygInfo.getDepartmentNo());
        }
        if(StringUtils.isNotBlank(ygInfo.getDepartmentName())){
            memberUserDO.setDeptName(ygInfo.getDepartmentName());
        }
        if(StringUtils.isNotBlank(ygInfo.getYgType())) {
            memberUserDO.setUserType(ygInfo.getYgType());
        }
        if(StringUtils.isNotBlank(ygInfo.getMobile())){
            memberUserDO.setMobile(ygInfo.getMobile());
        }
        if(StringUtils.isNotBlank(ygInfo.getJobTitle())){
            memberUserDO.setJobTitle(ygInfo.getJobTitle());
        }
        if(memberUserDO.getDeptId() == null && StringUtils.isNotBlank(memberUserDO.getDeptName())) {
            DeptRespDTO deptDTO = queryDept(memberUserDO.getDeptName());
            if(deptDTO != null) {
                memberUserDO.setDeptId(deptDTO.getId());
            }
        }
    }

    /**
     * 对密码进行加密
     *
     * @param password 密码
     * @return 加密后的密码
     */
    private String encodePassword(String password) {
        return passwordEncoder.encode(password);
    }

    @VisibleForTesting
    public MemberUserDO checkUserExists(Long id) {
        if (id == null) {
            return null;
        }
        MemberUserDO user = memberUserMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return user;
    }

    @Override
    public void buildDeptArchitectureV2(Long parentId) {
        Assert.notNull(parentId, "上级部门ID不能为空");
        String key = "member:build-org:" + TenantContextHolder.getTenantId();
        RLock lock = redissonClient.getLock(key);
        try {
            boolean isRunning = lock.isLocked();
            Assert.isTrue(!isRunning, "任务还在处理中，请稍后重试");
            threadPoolExecutor.execute(TtlRunnable.get(() -> {
                try {
                    lock.tryLock(5, 120, TimeUnit.SECONDS);
                    doBuildDeptArchitectureV2(parentId);
                } catch (Exception e) {
                    log.error("buildDeptArchitectureV2 task error", e);
                } finally {
                    if(lock.isLocked() && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }));
        } catch (Exception e) {
            log.error("buildDeptArchitectureV2 error: ", e);
        }
    }

    private void doBuildDeptArchitectureV2(Long parentId) {
        List<MemberUserDO> userList = memberUserMapper.selectList(Wrappers.lambdaQuery(MemberUserDO.class).isNull(MemberUserDO::getDeptId));
        log.info("构建会员部门结构开始, 总会员数:{}", userList.size());
        if(CollUtil.isEmpty(userList)) {
            return;
        }

        Map<String, DeptRespDTO> deptMap = new HashMap<>();
        List<MemberUserDO> updates = new ArrayList<>();
        for(MemberUserDO user : userList) {
            try {
                String deptName = user.getDeptName();
                if(StringUtils.isBlank(deptName)) {
                    log.info("部门名称为空: {}, 忽略处理", user.getNickname());
                    continue;
                }

                DeptRespDTO dept = deptMap.get(deptName);
                if(!deptMap.containsKey(deptName)) {
                    dept = queryDept(deptName);
                    deptMap.put(deptName, dept);
                    if(dept == null) {
                        log.info("会员部门不存在: {}, 尝试进行创建", deptName);
                        dept = createDept(parentId, deptName, user.getDeptCode());
                        deptMap.put(deptName, dept);
                        if(dept == null) {
                            log.info("会员部门创建失败: {}", deptName);
                            continue;
                        }
                    }
                }
                if(dept == null) {
                    continue;
                }

                updates.add(new MemberUserDO().setId(user.getId()).setDeptId(dept.getId()));
            } catch (Exception e) {
                log.error("会员更新异常: {}, {}", user.getNickname(), user.getUserNo(), e);
            }
        }

        log.info("会员部门更新数量：{}", updates.size());
        if(CollUtil.isNotEmpty(updates)) {
            memberUserMapper.updateBatch(updates, 1000);
        }
    }

    @Override
    public void buildDeptArchitectureV3() {
        String rootDeptCode = hrmsClient.getRootDeptCode();
        log.info("上级部门编码：{}", rootDeptCode);
        if(StringUtils.isBlank(rootDeptCode)) {
            return;
        }
        DeptRespDTO rootDept = queryDeptByCode(rootDeptCode);
        if(rootDept == null) {
            log.info("上级部门编码无效: {}", rootDeptCode);
            return;
        }

        String key = String.format("member:build-org:%d", TenantContextHolder.getTenantId());
        RLock lock = redissonClient.getLock(key);
        try {
            boolean isRunning = lock.isLocked();
            Assert.isTrue(!isRunning, "任务还在处理中，请稍后重试");
            threadPoolExecutor.execute(TtlRunnable.get(() -> {
                try {
                    lock.tryLock(5, 120, TimeUnit.SECONDS);
                    doBuildDeptArchitectureV3(rootDept.getId());
                } catch (Exception e) {
                    log.error("buildDeptArchitectureV3 task error", e);
                } finally {
                    if(lock.isLocked() && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }));
        } catch (Exception e) {
            log.error("buildDeptArchitectureV3 error: ", e);
        }
    }

    @Override
    public void buildDeptArchitectureV4() {
        String rootDeptCode = hrmsClient.getRootDeptCode();
        log.info("上级部门编码：{}", rootDeptCode);
        if(StringUtils.isBlank(rootDeptCode)) {
            return;
        }
        DeptRespDTO rootDept = queryDeptByCode(rootDeptCode);
        if(rootDept == null) {
            log.info("上级部门编码无效: {}", rootDeptCode);
            return;
        }

        String key = String.format("member:build-org:%d", TenantContextHolder.getTenantId());
        RLock lock = redissonClient.getLock(key);
        try {
            boolean isRunning = lock.isLocked();
            Assert.isTrue(!isRunning, "任务还在处理中，请稍后重试");
            threadPoolExecutor.execute(TtlRunnable.get(() -> {
                try {
                    lock.tryLock(5, 120, TimeUnit.SECONDS);
                    doBuildDeptArchitectureV4();
                } catch (Exception e) {
                    log.error("buildDeptArchitectureV4 task error", e);
                } finally {
                    if(lock.isLocked() && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }));
        } catch (Exception e) {
            log.error("buildDeptArchitectureV4 error: ", e);
        }
    }

    private void doBuildDeptArchitectureV3(Long parentId) {
        ExtPageReqDTO reqDTO = new ExtPageReqDTO();
        reqDTO.setPageNo(1);
        reqDTO.setPageSize(100);
        ExtPageRespDTO<ExtDeptDTO> respDTO = hrmsClient.pageQueryDept(reqDTO);

        if(respDTO == null) {
            return;
        }
        if(respDTO.getTotal()== null || respDTO.getTotal() <= 0) {
            log.info("外部人事部门查询数量为空");
        }
        long total = respDTO.getTotal();
        long createCount = 0L;

        while(respDTO != null && CollUtil.isNotEmpty(respDTO.getList()) && reqDTO.getPageNo() <= respDTO.getTotalPages()) {
            for(ExtDeptDTO deptDTO : respDTO.getList()) {
                try {
                    if(StringUtils.isBlank(deptDTO.getCode())) {
                        continue;
                    }
                    DeptRespDTO dept = queryDeptByCode(deptDTO.getCode());
                    if(dept == null) {
                        createDept(parentId, deptDTO.getName(), deptDTO.getCode());
                        createCount++;
                    }
                } catch (Exception e) {
                    log.error("外部人事部门处理异常:", e);
                }
            }
            if(respDTO.getList().size() < reqDTO.getPageSize()) {
                break;
            }

            reqDTO.setPageNo(reqDTO.getPageNo() + 1);
            respDTO = hrmsClient.pageQueryDept(reqDTO);
        }

        log.info("外部人事部门总数：{}， 新增数量：{}", total, createCount);
    }

    private void doBuildDeptArchitectureV4() {
        List<MemberUserDO> userList = memberUserMapper.selectList(Wrappers.lambdaQuery(MemberUserDO.class).isNotNull(MemberUserDO::getUserNo));
        log.info("构建会员部门结构开始, 总会员数:{}", userList.size());
        if(CollUtil.isEmpty(userList)) {
            return;
        }

        List<MemberUserDO> updates = new ArrayList<>();
        for(MemberUserDO user : userList) {
            try {
                hrmsClient.fillMemberInfo(user.getUserNo(), user);
                fillDeptId(user);
                updates.add(user);
            } catch (Exception e) {
                log.error("会员更新异常: {}, {}", user.getNickname(), user.getUserNo(), e);
            }
        }

        log.info("会员部门更新数量：{}", updates.size());
        if(CollUtil.isNotEmpty(updates)) {
            memberUserMapper.updateBatch(updates, 1000);
        }
    }

    private void fillDeptId(MemberUserDO userDO) {
        if(userDO != null && StringUtils.isNotBlank(userDO.getDeptCode())) {
            String key = String.format("member:fill-dept:%d:%s", TenantContextHolder.getTenantId(), userDO.getDeptCode());
            RLock lock = redissonClient.getLock(key);
            try {
                if(lock.tryLock(10, 20, TimeUnit.SECONDS)) {
                    DeptRespDTO deptDTO = queryDeptByCode(userDO.getDeptCode());
                    if(deptDTO != null) {
                        userDO.setDeptId(deptDTO.getId());
                    } else {
                        // 查询父级部门ID，再创建部门;
                        String rootDeptCode = hrmsClient.getRootDeptCode();
                        if(StringUtils.isBlank(rootDeptCode)) {
                            return;
                        }
                        DeptRespDTO rootDept = queryDeptByCode(rootDeptCode);
                        if(rootDept == null) {
                            return;
                        }
                        DeptRespDTO dept = createDept(rootDept.getId(), userDO.getDeptName(), userDO.getDeptCode());
                        if(dept != null) {
                            userDO.setDeptId(dept.getId());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("fillDeptId error: ", e);
            } finally {
                if(lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    private DeptRespDTO queryDeptByCode(String deptCode) {
        CommonResult<DeptRespDTO> orgDeptResp = deptApi.getDeptByCode(deptCode);
        DeptRespDTO dept = orgDeptResp.getCheckedData();
        if(dept == null || dept.getName() == null) {
            return null;
        }

        return dept;
    }

    private DeptRespDTO queryDept(String deptName) {
        CommonResult<DeptRespDTO> orgDeptResp = deptApi.getDeptByName(deptName);
        DeptRespDTO dept = orgDeptResp.getCheckedData();
        if(dept == null || dept.getName() == null) {
            return null;
        }

        return dept;
    }

    private DeptRespDTO createDept(Long parentId, String deptName, String deptCode) {
        DeptCreateReqDTO reqDTO = new DeptCreateReqDTO();
        reqDTO.setParentId(parentId);
        reqDTO.setName(deptName);
        reqDTO.setCode(deptCode);
        CommonResult<DeptRespDTO> deptCreateResp = deptApi.saveDept(reqDTO);

        DeptRespDTO dept = deptCreateResp.getCheckedData();
        if(dept == null || dept.getName() == null) {
            return null;
        }

        return dept;
    }

    @Override
    public List<MemberUserDO> searchUserList(String keyword, Integer limit) {
        return memberUserMapper.selectList(Wrappers.lambdaQuery(MemberUserDO.class)
                .eq(MemberUserDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .and(wq -> wq
                        .like(StrUtil.isNotBlank(keyword), MemberUserDO::getName, keyword)
                        .or()
                        .like(StrUtil.isNotBlank(keyword), MemberUserDO::getUserNo, keyword)
                )
                .last(" limit " + limit));
    }

    @Resource
    private DeptApi deptApi;

}
