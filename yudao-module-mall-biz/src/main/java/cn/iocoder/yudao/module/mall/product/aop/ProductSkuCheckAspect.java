package cn.iocoder.yudao.module.mall.product.aop;

import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.annotation.ProductSkuCheck;
import cn.iocoder.yudao.module.mall.mq.producer.product.ProductSkuProducer;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.util.SpringParseUtils;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopSkuPoolDO;
import cn.iocoder.yudao.module.mall.vop.service.VopConfigService;
import cn.iocoder.yudao.module.mall.vop.service.VopSkuPoolService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/2/7
 */
@Component
@Aspect
@Order(1001)
@Slf4j
public class ProductSkuCheckAspect {

    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private RedissonClient redissonclient;
    @Resource
    private ProductSkuProducer productSkuProducer;
    @Resource
    private VopSkuPoolService vopSkuPoolService;
    @Resource
    private VopConfigService vopConfigService;
    @Resource
    private RedisTemplate redisTemplate;

    private static final String SKU_CHECK_PREFIX = "product:sku:check:";

    @Around("@annotation(productSkuCheck)")
    public Object around(ProceedingJoinPoint joinPoint, ProductSkuCheck productSkuCheck) throws Throwable {
        String skuId = SpringParseUtils.parseExpression(joinPoint, productSkuCheck.skuId());
        ProductSkuCheck.CheckSortType checkSortType = productSkuCheck.checkSort();
        Object result;
        if (checkSortType == ProductSkuCheck.CheckSortType.BEFORE) {
            checkSku(skuId, productSkuCheck.async());
            result = joinPoint.proceed();
        } else {
            result = joinPoint.proceed();
            checkSku(skuId, productSkuCheck.async());
        }
        return result;
    }

    private void checkSku(String skuId, boolean async) {
        if (StringUtils.isBlank(skuId) || !NumberUtil.isLong(skuId)) {
            return;
        }

        ProductSkuDO skuDO = productSkuService.getSimpleSkuById(Long.valueOf(skuId));
        if(skuDO == null) {
            return;
        }

        // 只处理京东商品
        if(!skuDO.isJd()) {
            return;
        }

        String skuInnerId = skuDO.getSkuInnerId();
        RLock lock = redissonclient.getLock(SKU_CHECK_PREFIX + skuId);
        if (async) {
            Long tenantId = TenantContextHolder.getTenantId();
            CompletableFuture.runAsync(() -> {
                try {
                    if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                        TenantContextHolder.setTenantId(tenantId);
                        syncProductIfNecessary(skuInnerId);
                    }
                } catch (Exception e) {
                    log.error("async goodsUpdate {} fail !", skuId, e);
                } finally {
                    lock.unlock();
                }

            });
        } else {
            try {
                if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                    syncProductIfNecessary(skuInnerId);
                }
            } catch (Exception e) {
                log.error("goodsUpdate {} fail !", skuId, e);
            } finally {
                lock.unlock();
            }
        }
    }

    private boolean syncProductIfNecessary(String skuId) {
        String key = String.format("jd-vop-sync:%s:%s", TenantContextHolder.getTenantId().toString(), skuId);
        if(redisTemplate.opsForValue().setIfAbsent(key, "1", Duration.ofHours(4))) {
            String poolName = null;
            if(!vopConfigService.getVopConfig().getFullPoolSwitch()){
                VopSkuPoolDO vopSkuPoolDO = vopSkuPoolService.getPoolBySkuId(skuId);
                if(vopSkuPoolDO != null){
                    poolName = vopSkuPoolDO.getPoolName();
                }
            }
            productSkuProducer.sendVopProductFetch(poolName, Long.valueOf(skuId));
            return true;
        }

        return false;
    }


}
