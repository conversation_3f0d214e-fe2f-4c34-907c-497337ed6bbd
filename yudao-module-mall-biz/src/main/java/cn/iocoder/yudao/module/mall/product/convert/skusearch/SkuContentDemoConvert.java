package cn.iocoder.yudao.module.mall.product.convert.skusearch;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.SupplierCreateReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.SupplierRespVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.SupplierUpdateReqVO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.product.controller.app.skusearch.vo.SkuContentDemoRespVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.SkuContentDemoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Mapper
public interface SkuContentDemoConvert {

    SkuContentDemoConvert INSTANCE = Mappers.getMapper(SkuContentDemoConvert.class);

    PageResult<SkuContentDemoRespVO> convertPage(PageResult<SkuContentDemoDO> page);

}
