package cn.iocoder.yudao.module.mall.product.service.skublackarea;

import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skublackarea.ProductSkuBlackAreaDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商品sku禁售区域 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductSkuBlackAreaService  extends IService<ProductSkuBlackAreaDO> {

    /**
     * 创建商品sku禁售区域
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSkuBlackArea(@Valid ProductSkuBlackAreaCreateReqVO createReqVO);

    /**
     * 更新商品sku禁售区域
     *
     * @param updateReqVO 更新信息
     */
    void updateSkuBlackArea(@Valid ProductSkuBlackAreaUpdateReqVO updateReqVO);

    /**
     * 删除商品sku禁售区域
     *
     * @param id 编号
     */
    void deleteSkuBlackArea(Long id);

    /**
     * 获得商品sku禁售区域
     *
     * @param id 编号
     * @return 商品sku禁售区域
     */
    ProductSkuBlackAreaDO getSkuBlackArea(Long id);

    /**
     * 获得商品sku禁售区域列表
     *
     * @param ids 编号
     * @return 商品sku禁售区域列表
     */
    List<ProductSkuBlackAreaDO> getSkuBlackAreaList(Collection<Long> ids);

    /**
     * 获得商品sku禁售区域分页
     *
     * @param pageReqVO 分页查询
     * @return 商品sku禁售区域分页
     */
    PageResult<ProductSkuBlackAreaDO> getSkuBlackAreaPage(ProductSkuBlackAreaPageReqVO pageReqVO);

    /**
     * 获得商品sku禁售区域列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 商品sku禁售区域列表
     */
    List<ProductSkuBlackAreaDO> getSkuBlackAreaList(ProductSkuBlackAreaExportReqVO exportReqVO);


    /**
     * 设置商品禁售区域
     * @param reqVOs
     * @param supplierId
     */
    void setProductSkuBlackArea(List<ProductSkuBlackAreaReqVO> reqVOs, Long supplierId);


    /**
     * 查询商品禁售区域
     * @param skuIds
     * @param supplierId
     * @return
     */
    List<ProductSkuBlackAreaDO> getProductSkuBlackArea(List<Long> skuIds, Long supplierId);

}
