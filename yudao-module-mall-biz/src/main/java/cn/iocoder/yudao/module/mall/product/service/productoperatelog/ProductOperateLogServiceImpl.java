package cn.iocoder.yudao.module.mall.product.service.productoperatelog;

import cn.iocoder.yudao.module.mall.framework.service.LogDiffService;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductOperateTypeEnum;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import de.danielbechler.diff.ObjectDifferBuilder;
import de.danielbechler.diff.node.DiffNode;
import org.apache.commons.beanutils.BeanComparator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import cn.iocoder.yudao.module.mall.product.controller.admin.productoperatelog.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productoperatelog.ProductOperateLogDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.module.mall.product.convert.productoperatelog.ProductOperateLogConvert;
import cn.iocoder.yudao.module.mall.product.dal.mysql.productoperatelog.ProductOperateLogMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.*;

/**
 * 商品操作日志记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductOperateLogServiceImpl implements ProductOperateLogService {

    @Resource
    private ProductOperateLogMapper operateLogMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private LogDiffService logDiffService;

    @Override
    public Long createOperateLog(ProductOperateLogCreateReqVO createReqVO) {
        // 插入
        ProductOperateLogDO operateLog = ProductOperateLogConvert.INSTANCE.convert(createReqVO);
        operateLogMapper.insert(operateLog);
        // 返回
        return operateLog.getId();
    }

    @Override
    public void saveCreateOperateSpuLog(ProductSpuDO spu) {
        saveProductOperateLogDO(null, spu.getId(), spu.getSupplierId(), "创建商品");

    }

    @Override
    public void saveCreateOperateSkuLog(ProductSkuDO sku) {
        saveProductOperateLogDO(sku.getId(), sku.getSpuId(), sku.getSupplierId(), "创建商品");

    }

    @Override
    public void saveUpdateOperateSpuLog(ProductSpuDO sourceSpu, ProductSpuDO targetSpu) {
        String diff = logDiffService.diff(sourceSpu, targetSpu);
        saveProductOperateLogDO(null, sourceSpu.getId(), sourceSpu.getSupplierId(), diff);
    }

    @Override
    public void saveUpdateOperateSkuLog(ProductSkuDO sourceSku, ProductSkuDO targetSku) {
        String diff = logDiffService.diff(sourceSku, targetSku);
        saveProductOperateLogDO(sourceSku.getId(), sourceSku.getSpuId(), sourceSku.getSupplierId(), diff);
    }

    @Override
    public void saveProductOperateLogDO(Long skuId, Long spuId, Long supplierId, String content) {
        ProductOperateLogDO operateLog = new ProductOperateLogDO();
        operateLog.setOperateType(ProductOperateTypeEnum.UPDATE.getCode());
        operateLog.setContent(content);
        operateLog.setSkuId(skuId);
        operateLog.setSpuId(spuId);
        operateLog.setSupplierId(supplierId);
        Long userId = getLoginUserId();
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getData();
        if (adminUserRespDTO != null) {
            operateLog.setUserId(adminUserRespDTO.getId());
            operateLog.setUserName(adminUserRespDTO.getNickname());
        }
        this.operateLogMapper.insert(operateLog);
    }


    @Override
    public void updateOperateLog(ProductOperateLogUpdateReqVO updateReqVO) {
        // 校验存在
        validateOperateLogExists(updateReqVO.getId());
        // 更新
        ProductOperateLogDO updateObj = ProductOperateLogConvert.INSTANCE.convert(updateReqVO);
        operateLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteOperateLog(Long id) {
        // 校验存在
        validateOperateLogExists(id);
        // 删除
        operateLogMapper.deleteById(id);
    }

    private void validateOperateLogExists(Long id) {
    }

    @Override
    public ProductOperateLogDO getOperateLog(Long id) {
        return operateLogMapper.selectById(id);
    }

    @Override
    public List<ProductOperateLogDO> getOperateLogList(Collection<Long> ids) {
        return operateLogMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ProductOperateLogDO> getOperateLogPage(ProductOperateLogPageReqVO pageReqVO) {
        return operateLogMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProductOperateLogDO> getOperateLogList(ProductOperateLogExportReqVO exportReqVO) {
        return operateLogMapper.selectList(exportReqVO);
    }

    @Override
    public void saveProductOperateLog(ProductOperateLogDO productOperateLogDO) {
         operateLogMapper.insert(productOperateLogDO);
    }

    public static void main(String[] args) {
        ProductOperateLogDO productOperateLogDO = new ProductOperateLogDO();
        productOperateLogDO.setUserId(1233L);
        productOperateLogDO.setUserName("zhang");

        ProductOperateLogDO productOperateLogDO1 = new ProductOperateLogDO();
        productOperateLogDO1.setUserId(1233L);
        productOperateLogDO.setUserName("san");
        DiffNode diff = ObjectDifferBuilder.buildDefault().compare(productOperateLogDO, productOperateLogDO1);
        System.out.println(diff);
        int compare = new BeanComparator<ProductOperateLogDO>().compare(productOperateLogDO, productOperateLogDO1);

    }

}
