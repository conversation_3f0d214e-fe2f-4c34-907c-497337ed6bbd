package cn.iocoder.yudao.module.mall.product.service.spec;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecValueReqVO;
import cn.iocoder.yudao.module.mall.product.convert.spec.ProductSpecConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSkuSpecDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.category.ConfigProductCategorySpecMapper;
import cn.iocoder.yudao.module.mall.product.dal.mysql.spec.ProductSkuSpecMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * sku规格值 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductSkuSpecServiceImpl extends ServiceImpl<ProductSkuSpecMapper, ProductSkuSpecDO> implements ProductSkuSpecService {

    @Resource
    private ConfigProductCategorySpecMapper configProductCategorySpecMapper;
    @Resource
    private ProductSkuSpecMapper productSkuSpecMapper;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public void handleSkuSpec(@Valid List<ProductSpecValueReqVO> specVaueList, Long productSkuId) {
        // 处理规格
        if(CollUtil.isNotEmpty(specVaueList)) {
            specVaueList.forEach(item -> {
                if(item.getSpecId() == null) {
                    item.setSpecId(0L);
                }
            });
        }
        // 先清空商品SPU的规格值
        productSkuSpecMapper.deleteBySkuId(productSkuId);
        // 保存规格值
        if(CollUtil.isNotEmpty(specVaueList)) {
            productSkuSpecMapper.insertBatch(ProductSpecConvert.INSTANCE.convertDOList12(specVaueList, productSkuId));
        }
    }

    @Override
    public void deleteProductSkuSpec(Long id) {
        // 校验存在
        validateProductSkuSpecExists(id);
        // 删除
        productSkuSpecMapper.deleteById2(id);
    }

    private void validateProductSkuSpecExists(Long id) {
        if (productSkuSpecMapper.selectById(id) == null) {
        }
    }

    @Override
    public ProductSkuSpecDO getProductSkuSpec(Long id) {
        return productSkuSpecMapper.selectById(id);
    }

    @Override
    public List<ProductSkuSpecDO> getProductSkuSpecList(Collection<Long> ids) {
        return productSkuSpecMapper.selectBatchIds(ids);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void saveBatchBySku(List<ProductSkuSpecDO> productSkuSpecDOS, Long skuId) {
        if(skuId == null) {
            return;
        }
        String key = String.format("lock-product-sku-%d", skuId);
        RLock lock = redissonClient.getLock(key);
        try {
            lock.tryLock(10, 10, TimeUnit.SECONDS);
            productSkuSpecMapper.deleteBySkuId(skuId);
            if(CollUtil.isEmpty(productSkuSpecDOS)) {
                return;
            }
            productSkuSpecMapper.insertBatch(productSkuSpecDOS);
        } catch (Exception e) {
            log.error("saveBatchBySku lock error: ", e);
        } finally {
            if(lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();;
            }
        }
    }

    @Override
    public List<ProductSkuSpecDO> getProductSkuSpecBySkuId(Long skuId) {
        return getProductSkuSpecBySkuIds(Arrays.asList(skuId));
    }

    @Override
    public List<ProductSkuSpecDO> getProductSkuSpecBySkuIds(List<Long> skuIds) {
        if(CollectionUtils.isEmpty(skuIds)) {
            return null;
        }
        LambdaQueryWrapper<ProductSkuSpecDO> wrapper = Wrappers.lambdaQuery(ProductSkuSpecDO.class).in(ProductSkuSpecDO::getSkuId, skuIds);
        return productSkuSpecMapper.selectList(wrapper);
    }

}
