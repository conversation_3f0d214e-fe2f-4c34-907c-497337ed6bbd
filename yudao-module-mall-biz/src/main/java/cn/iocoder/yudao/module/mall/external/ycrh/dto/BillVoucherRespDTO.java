package cn.iocoder.yudao.module.mall.external.ycrh.dto;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 账单预约单详情
 * <AUTHOR>
 * @date 2024/4/8
 */
@Data
public class BillVoucherRespDTO {

    /**
     * 部门编号
     */
    @JsonProperty("bmbh")
    private String deptNo;
    /**
     * 项目编号
     */
    @JsonProperty("xmbh")
    private String projectNo;
    /**
     * 报销凭证编号
     */
    @JsonProperty("pzbh")
    private String reimbursementNo;
    /**
     * 预约单号
     */
    @JsonProperty("yydh")
    private String vourcherNo;
    /**
     * 制单人
     */
    @JsonProperty("zdr")
    private String reimbursementBy;
    /**
     * 制单时间
     */
    @JsonProperty("pzrq")
    private String reimbursementTime;
    /**
     * 凭证状态
     * 5-已提交财务
     * 2-财务接单
     * 3-成功
     * 4-失败
     */
    @JsonProperty("zt")
    private String reimbursementStatus;
    /**
     * 说明备注
     */
    @JsonProperty("sm")
    private String memo;

    @JsonIgnore
    public boolean isComplete() {
        return ObjectUtil.equal(reimbursementStatus, "3") ||
                ObjectUtil.equal(reimbursementStatus, "4");
    }

    @JsonIgnore
    public boolean isCompleteSuccess() {
        return ObjectUtil.equal(reimbursementStatus, "3");
    }

}
