package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单统计总数
 */
@Data
public class OrderStatisticsTotalVO {

    /**
     * 订单总数
     */
    private Integer totalOrderNum;

    /**
     * 订单总金额
     */
    private BigDecimal totalOrderAmount;

    /**
     * 已完成订单总数量
     */
    private Integer  totalOrderCompleteNum;

    /**
     * 已完成订单总金额
     */
    private BigDecimal totalOrderCompleteAmount;

    /**
     * 已结算订单总数量
     */
    private Integer totalOrderSettleNum;

    /**
     * 已结算订单总金额
     */
    private Integer totalOrderSettleAmount;


}
