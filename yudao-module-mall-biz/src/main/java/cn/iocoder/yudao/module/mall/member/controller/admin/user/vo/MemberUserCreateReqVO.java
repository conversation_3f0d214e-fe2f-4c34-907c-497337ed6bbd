package cn.iocoder.yudao.module.mall.member.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

@Schema(description = "管理后台 - 会员 创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberUserCreateReqVO extends MemberUserBaseVO {

    @Schema(description = "密码", example = "1")
    @NotEmpty(message = "密码不能为空")
//    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{6,30}$", message = "密码必须包含字母大小写及字符且长度为6-30位")
    @Pattern(regexp = "^.{6,30}$", message = "密码长度必须为6-30位")
    private String password;

}
