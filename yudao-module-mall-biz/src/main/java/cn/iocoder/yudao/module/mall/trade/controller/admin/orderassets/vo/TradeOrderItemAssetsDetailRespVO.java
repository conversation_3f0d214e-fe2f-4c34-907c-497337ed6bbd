package cn.iocoder.yudao.module.mall.trade.controller.admin.orderassets.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 订单明细固资信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TradeOrderItemAssetsDetailRespVO extends TradeOrderItemAssetsRespVO {

    private List<TradeOrderAssetsDetailVO> detailList;

}
