package cn.iocoder.yudao.module.mall.external.assets.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.AssetsConfigDO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsCompleteDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsCreateDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsResp;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsStatusUpdateReq;
import cn.iocoder.yudao.module.mall.external.assets.dto.sibide.ExtSiBiDeAssetsStatusDTO;
import cn.iocoder.yudao.module.mall.external.assets.enums.AssetClientTypeEnum;
import cn.iocoder.yudao.module.mall.external.assets.mapper.ExtSibideAssetMapper;
import cn.iocoder.yudao.module.mall.trade.enums.order.AssetStatusEnum;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 思必得资产中间库实现
 * 只能应用于本地化布署场景
 */
@Slf4j
@Service
@ConditionalOnProperty(value = "yudao.asset-db", havingValue = "true")
@DS("asset-sbd")
public class ExtSibideDBAssetsService extends AbstractExtAssetsService {
    
    @Resource
    private ExtSibideAssetMapper assetMapper;

    @Override
    public String getClientCode() {
        return AssetClientTypeEnum.SIBIDE_DB.getType();
    }

    @Override
    public boolean needPollStatus() {
        return true;
    }

    private void preHandleData(ExtAssetsCreateDTO data) {
        if(data.getInvoice() == null) {
            data.setInvoice("");
        }
        if(data.getInvoiceUrl() == null) {
            data.setInvoiceUrl("");
        }
        if(data.getPhone() == null) {
            data.setPhone("");
        }
        if(data.getUser() == null) {
            data.setUser("");
        }
        if(data.getUserName() == null) {
            data.setUserName("");
        }
        if(data.getDept() == null) {
            data.setDept("");
        }
        if(data.getDeptName() == null) {
            data.setDeptName("");
        }
        if(data.getModel() == null) {
            data.setModel("");
        }
        if(data.getFormat() == null) {
            data.setFormat("");
        }
        if(data.getExtSupUser() == null) {
            data.setExtSupUser("");
        }
        if(data.getExtSupPhone() == null) {
            data.setExtSupPhone("");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @TenantIgnore
    public ExtAssetsResp<String> pushAssets(AssetsConfigDO configDO, ExtAssetsCreateDTO data) {
        if(StringUtils.isNotBlank(data.getInvoice())) {
            if(StringUtils.isNotBlank(data.getInvoiceUrl())) {
                data.setInvoiceImg(null);
            }
        }
        preHandleData(data);
        long count = assetMapper.selectOrderCount(data.getOrderNo());
        if(count > 0) {
            assetMapper.updateOrder(data);
        } else {
            assetMapper.insertOrder(data);
        }
        assetMapper.deleteOrderItems(data.getOrderNo(), data.getOrderDetailNo());
        assetMapper.insertOrderItem(data);

        return new ExtAssetsResp().success();
    }

    @Override
    @TenantIgnore
    public boolean completeAssets(AssetsConfigDO configDO, ExtAssetsCompleteDTO data) {

        return true;
    }

    @Override
    @TenantIgnore
    public ExtAssetsStatusUpdateReq doPollStatus(String orderNo, Long orderItemId) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("orderNo", orderNo);
            param.put("orderDetailNo", orderItemId.toString());
            List<ExtSiBiDeAssetsStatusDTO> statusList = assetMapper.selectAssetStatusV2(param);
            if(CollUtil.isEmpty(statusList)) {
                return null;
            }

            ExtSiBiDeAssetsStatusDTO assetStatus = statusList.get(0);
            log.info("ext-assets sibide-status: {}", assetStatus);
            if(StringUtils.isBlank(assetStatus.getAcceptNo())) {
                return null;
            }

            ExtAssetsStatusUpdateReq updateReq = new ExtAssetsStatusUpdateReq();
            updateReq.setApproveBy(assetStatus.getApproveBy());
            updateReq.setApproveMemo(assetStatus.getApproveMemo());
            if(assetStatus.getApproveTime() != null) {
                updateReq.setApproveTime(assetStatus.getApproveTime().getTime());
            }
            updateReq.setAcceptNo(assetStatus.getAcceptNo());
            updateReq.setOrderDetailNo(assetStatus.getOrderDetailNo());


            // 判断是否已完成
            if(StrUtil.equalsAny(assetStatus.getApproveStatus(), "1")) {
                updateReq.setApproveStatus(AssetStatusEnum.ASSET_SUCCESS.getStatus());

                updateReq.setCategory6(assetStatus.getCategory6());
                updateReq.setCategory16(assetStatus.getCategory16());
                updateReq.setAssetNo(Arrays.asList(assetStatus.getAssetCode()));
            } else if(StrUtil.equalsAny(assetStatus.getApproveStatus(), "-1")) {
                // 驳回
                updateReq.setApproveStatus(AssetStatusEnum.ASSET_FAIL.getStatus());
            } else {
                // 审批中
                updateReq.setApproveStatus(AssetStatusEnum.ASSET_DOING.getStatus());
            }

            return updateReq;
        } catch (Exception e) {
            log.error("doPollStatus error: ", e);
        }

        return null;
    }

}
