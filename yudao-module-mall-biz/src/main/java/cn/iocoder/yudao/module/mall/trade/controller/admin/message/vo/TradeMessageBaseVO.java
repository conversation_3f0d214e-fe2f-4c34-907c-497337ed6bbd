package cn.iocoder.yudao.module.mall.trade.controller.admin.message.vo;

import cn.iocoder.yudao.module.mall.trade.enums.order.TradeMessageStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeMessageTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
* 交易订单消息VO
 * <AUTHOR>
*/
@Data
public class TradeMessageBaseVO {

    /**
     * 消息编号
     */
    private Long id;

    /**
     * 消息类型 参考{@link TradeMessageTypeEnum}
     */
    private Integer messageType;

    /**
     * 消息状态 参考{@link TradeMessageStatusEnum}
     */
    private Integer messageStatus;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 消息创建时间
     */
    private LocalDateTime createTime;

    /**
     * 消息更新时间
     */
    private LocalDateTime updateTime;

}
