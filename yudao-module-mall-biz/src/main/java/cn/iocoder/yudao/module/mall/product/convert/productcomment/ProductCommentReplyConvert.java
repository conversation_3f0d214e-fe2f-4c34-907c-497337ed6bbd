package cn.iocoder.yudao.module.mall.product.convert.productcomment;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentReplyDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商品评价回复 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductCommentReplyConvert {

    ProductCommentReplyConvert INSTANCE = Mappers.getMapper(ProductCommentReplyConvert.class);

    ProductCommentReplyDO convert(ProductCommentReplyCreateReqVO bean);

    ProductCommentReplyDO convert(ProductCommentReplyUpdateReqVO bean);

    ProductCommentReplyRespVO convert(ProductCommentReplyDO bean);

    List<ProductCommentReplyRespVO> convertList(List<ProductCommentReplyDO> list);

    PageResult<ProductCommentReplyRespVO> convertPage(PageResult<ProductCommentReplyDO> page);

}
