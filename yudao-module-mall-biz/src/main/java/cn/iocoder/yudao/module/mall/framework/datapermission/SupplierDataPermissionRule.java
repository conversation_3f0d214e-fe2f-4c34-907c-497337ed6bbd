package cn.iocoder.yudao.module.mall.framework.datapermission;

import cn.iocoder.yudao.framework.datapermission.core.rule.DataPermissionRule;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.common.OpenAppDO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierAccountDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productoperatelog.ProductOperateLogDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skublackarea.ProductSkuBlackAreaDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skustock.ProductSkuStockDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusummary.SkuSummaryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.aftersale.TradeAfterSaleDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.bill.BillDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.cart.TradeCartItemDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.cart.TradeCollectItemDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryProductSkuDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryTrackDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.message.TradeMessageDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.*;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.orderstatistics.OrderStatisticsDO;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopAccessTokenDO;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopConfigDO;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
public class SupplierDataPermissionRule implements DataPermissionRule {

    private static final Set<String> SUPPLIER_TABLE_NAMES = new HashSet<>();
    private static final List<Class<? extends BaseDO>> tableClassList = Arrays.asList(
            TradeOrderDO.class,
            TradeOrderItemDO.class,
            ProductSkuDO.class,
            ProductSpuDO.class,
            SupplierAccountDO.class,
            OpenAppDO.class,
            VopAccessTokenDO.class,
            VopConfigDO.class,
            ProductCommentDO.class,
            ProductOperateLogDO.class,
            ProductSkuBlackAreaDO.class,
            ProductSkuStockDO.class,
            SkuSummaryDO.class,
            TradeAfterSaleDO.class,
            BillDO.class,
            TradeCartItemDO.class,
            TradeCollectItemDO.class,
            TradeMessageDO.class,
            OrderStatisticsDO.class,
            TradeOrderItemAssetsDO.class,
            TradeOrderAssetsDetailDO.class,
            TradeOrderInvoiceDO.class,
            DeliveryDO.class,
            DeliveryTrackDO.class,
            DeliveryProductSkuDO.class
    );

    @Override
    public Set<String> getTableNames() {
        if(SUPPLIER_TABLE_NAMES.isEmpty()){
            tableClassList.forEach(tableClass -> SUPPLIER_TABLE_NAMES.add(tableNameFromClass(tableClass)));
        }
        return SUPPLIER_TABLE_NAMES;
    }

    @Override
    public Expression getExpression(String tableName, Alias tableAlias) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        if(user != null && user.getSupplierId() != null){
            return new EqualsTo(MyBatisUtils.buildColumn(tableName, tableAlias, "supplier_id"), new LongValue(user.getSupplierId()));
        }
        return null;
    }

    String tableNameFromClass(Class<? extends BaseDO> entityClass) {
        return TableInfoHelper.getTableInfo(entityClass).getTableName();
    }
}

