package cn.iocoder.yudao.module.mall.member.convert.auth;

import cn.hutool.core.map.MapUtil;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.SsoConfigDO;
import cn.iocoder.yudao.module.mall.external.sso.cas.controller.app.vo.AppSsoCasLoginReqVO;
import cn.iocoder.yudao.module.mall.member.controller.app.auth.vo.*;
import cn.iocoder.yudao.module.mall.member.controller.app.social.vo.AppSocialUserUnbindReqVO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import cn.iocoder.yudao.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import cn.iocoder.yudao.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserBindReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserUnbindReqDTO;
import cn.iocoder.yudao.module.system.enums.sms.SmsSceneEnum;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Map;


@Mapper
public interface AuthConvert {

    AuthConvert INSTANCE = Mappers.getMapper(AuthConvert.class);

    SocialUserBindReqDTO convert(Long userId, Integer userType, AppAuthSocialLoginReqVO reqVO);

    SocialUserUnbindReqDTO convert(Long userId, Integer userType, AppSocialUserUnbindReqVO reqVO);

    SmsCodeSendReqDTO convert(AppAuthSmsSendReqVO reqVO);

    SmsCodeUseReqDTO convert(AppAuthSmsBaseReqVO reqVO, SmsSceneEnum scene, String usedIp);

    SmsCodeUseReqDTO convert(AppAuthResetPasswordReqVO reqVO, SmsSceneEnum scene, String usedIp);

    SmsCodeUseReqDTO convert(AppAuthSmsLoginReqVO reqVO, SmsSceneEnum scene, String usedIp);

    AppAuthLoginRespVO convert(OAuth2AccessTokenRespDTO bean);

    default AppSsoCasLoginReqVO convert(Map<String, Object> map, SsoConfigDO config) {
        AppSsoCasLoginReqVO vo = new AppSsoCasLoginReqVO();
        if (map != null) {
            Map<String, String> paramMap = config.getParamMap();
            String  keyName = MapUtil.getStr(paramMap, "cn", "cn");
            String  keyUid = MapUtil.getStr(paramMap, "uid", "uid");
            String  keyDeptName = MapUtil.getStr(paramMap, "deptName", "deptName");
            String  keyDeptNo = MapUtil.getStr(paramMap, "deptNo", "deptNo");
            vo.setUserName(MapUtil.getStr(map, keyName, ""));
            vo.setUserNo(MapUtil.getStr(map, keyUid, keyUid));
            vo.setDeptName(MapUtil.getStr(map, keyDeptName, ""));
            vo.setDeptNo(MapUtil.getStr(map, keyDeptNo, ""));
        }

        return vo;
    }

}
