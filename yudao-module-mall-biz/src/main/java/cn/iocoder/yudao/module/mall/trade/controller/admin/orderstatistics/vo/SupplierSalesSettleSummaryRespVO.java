package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupplierSalesSettleSummaryRespVO {

    @Schema(description = "销售订单数")
    private Long orderCount;

    @Schema(description = "销售总金额")
    private BigDecimal orderAmount;

    @Schema(description = "结算订单数")
    private Long settleCount;

    @Schema(description = "结算总金额")
    private BigDecimal settleAmount;
}
