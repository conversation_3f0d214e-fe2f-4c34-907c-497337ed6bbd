package cn.iocoder.yudao.module.mall.trade.controller.admin.settle.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/24 14:07
 */
@Schema(description = "管理后台 - 订单结算导出excel VO")
@Data
public class OrderSettleExportVO {

    @Schema(description = "序号")
    @Excel(needMerge = true, name = "序号", orderNum = "0")
    private Integer index;

    @Schema(description = "订单号")
    @Excel(needMerge = true, name = "订单号", orderNum = "1",width = 24)
    private String orderNo;

    @Schema(description = "外部订单编号")
    @Excel(needMerge = true, name = "外部订单编号", orderNum = "2",width = 24, replace = {"--_null"})
    private String thirdOrderId;

    @Schema(description = "供应商名称")
    @Excel(needMerge = true, name = "供应商名称", orderNum = "3",width = 20)
    private String supplierName;

    @Schema(description = "下单人")
    @Excel(needMerge = true, name = "下单人", orderNum = "4",width = 15)
    private String userName;

    @Schema(description = "收货人")
    @Excel(needMerge = true, name = "收货人", orderNum = "5",width = 15)
    private String receiverName;

    @Schema(description = "订单状态")
    @Excel(needMerge = true, name = "订单状态", orderNum = "6",width = 15)
    private String status;

    @Schema(description = "开票状态")
    @Excel(needMerge = true, name = "开票状态", orderNum = "7" ,width = 15)
    private String invoiceStatus;

    @Schema(description = "下单时间")
    @Excel(needMerge = true, name = "下单时间", orderNum = "8", exportFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", replace = {"--_null"}, width = 24)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime submitTime;

    @Schema(description = "商品金额")
    @Excel(needMerge = true, name = "商品金额", orderNum = "9",width = 15)
    private BigDecimal productPrice;

    @Schema(description = "运费金额")
    @Excel(needMerge = true, name = "运费金额", orderNum = "10",width = 15, replace = {"0_null"})
    private BigDecimal deliveryPrice;

    @Schema(description = "订单总金额")
    @Excel(needMerge = true, name = "订单总金额", orderNum = "11",width = 15)
    private BigDecimal orderTotalPrice;

    @Schema(description = "退款金额")
    @Excel(needMerge = true, name = "退款金额", orderNum = "12",width = 15, replace = {"0_null"})
    private BigDecimal refundAmount;

    @Schema(description = "订单明细")
    @ExcelCollection(name = "订单明细", orderNum = "13")
    private List<OrderItemSettleExportVO> orderItems;

}
