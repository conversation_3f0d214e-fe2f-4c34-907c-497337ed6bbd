package cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * <AUTHOR>
 * @date 2023-09-19
 */
@Schema(description = "app - 商品sku禁售区域创建 Request VO")
@Data
@ToString(callSuper = true)
public class ProductSkuBlackAreaReqVO {

    @Schema(description = "spu编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "spu编号不能为空")
    private Long spuId;

    @Schema(description = "商品 SPU 名字")
    private String spuName;

    @Schema(description = "sku编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "sku编号不能为空")
    private Long skuId;

    @Schema(description = "商品 SKU 名字")
    private String skuName;


    @Schema(description = "区域")
    private List<Area> areas;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private Date beginTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结束时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date endTime;

    @Data
    public static class Area {
        @Schema(description = "所在省份")
        private Long provinceId;

        @Schema(description = "所在城市")
        private Long cityId;

        @Schema(description = "所在区县")
        private Long countyId;
    }


}
