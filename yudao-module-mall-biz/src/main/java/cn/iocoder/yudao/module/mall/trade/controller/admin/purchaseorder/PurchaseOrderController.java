package cn.iocoder.yudao.module.mall.trade.controller.admin.purchaseorder;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.mall.trade.controller.admin.purchaseorder.vo.*;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchaseorder.PurchaseOrderDO;
import cn.iocoder.yudao.module.mall.trade.convert.purchaseorder.PurchaseOrderConvert;
import cn.iocoder.yudao.module.mall.trade.service.purchaseorder.PurchaseOrderService;

@Tag(name = "管理后台 - 采购订单")
@RestController
@RequestMapping("/trade/purchase-order")
@Validated
public class PurchaseOrderController {

    @Resource
    private PurchaseOrderService purchaseOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建采购订单")
    @PreAuthorize("@ss.hasPermission('trade:purchase-order:create')")
    public CommonResult<Long> createPurchaseOrder(@Valid @RequestBody PurchaseOrderCreateReqVO createReqVO) {
        return success(purchaseOrderService.createPurchaseOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新采购订单")
    @PreAuthorize("@ss.hasPermission('trade:purchase-order:update')")
    public CommonResult<Boolean> updatePurchaseOrder(@Valid @RequestBody PurchaseOrderUpdateReqVO updateReqVO) {
        purchaseOrderService.updatePurchaseOrder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除采购订单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('trade:purchase-order:delete')")
    public CommonResult<Boolean> deletePurchaseOrder(@RequestParam("id") Long id) {
        purchaseOrderService.deletePurchaseOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得采购订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:purchase-order:query')")
    public CommonResult<PurchaseOrderRespVO> getPurchaseOrder(@RequestParam("id") Long id) {
        PurchaseOrderDO purchaseOrder = purchaseOrderService.getPurchaseOrder(id);
        return success(PurchaseOrderConvert.INSTANCE.convert(purchaseOrder));
    }

    @GetMapping("/list")
    @Operation(summary = "获得采购订单列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('trade:purchase-order:query')")
    public CommonResult<List<PurchaseOrderRespVO>> getPurchaseOrderList(@RequestParam("ids") Collection<Long> ids) {
        List<PurchaseOrderDO> list = purchaseOrderService.getPurchaseOrderList(ids);
        return success(PurchaseOrderConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得采购订单分页")
    @PreAuthorize("@ss.hasPermission('trade:purchase-order:query')")
    public CommonResult<PageResult<PurchaseOrderRespVO>> getPurchaseOrderPage(@Valid PurchaseOrderPageReqVO pageVO) {
        PageResult<PurchaseOrderDO> pageResult = purchaseOrderService.getPurchaseOrderPage(pageVO);
        return success(PurchaseOrderConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出采购订单 Excel")
    @PreAuthorize("@ss.hasPermission('trade:purchase-order:export')")
    @OperateLog(type = EXPORT)
    public void exportPurchaseOrderExcel(@Valid PurchaseOrderExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<PurchaseOrderDO> list = purchaseOrderService.getPurchaseOrderList(exportReqVO);
        // 导出 Excel
        List<PurchaseOrderExcelVO> datas = PurchaseOrderConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "采购订单.xls", "数据", PurchaseOrderExcelVO.class, datas);
    }
}
