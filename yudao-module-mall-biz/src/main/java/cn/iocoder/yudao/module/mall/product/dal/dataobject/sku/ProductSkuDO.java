package cn.iocoder.yudao.module.mall.product.dal.dataobject.sku;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.mall.annotation.DiffField;
import cn.iocoder.yudao.module.mall.enums.basis.SupplierTypeEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSkuSeoStatusEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSkuShowStatusEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 商品 SKU DO
 *
 * <AUTHOR>
 */
@TableName(value = "product_sku",autoResultMap = true)
@KeySequence("product_sku_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSkuDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 供应商skuId
     */
    @DiffField(name = "供应商skuId")
    private String skuInnerId;
    /**
     * sku名称
     */
    @DiffField(name = "sku名称")
    private String skuName;

    /**
     * spu编号
     */
    @DiffField(name = "spu编号")
    private Long spuId;
    /**
     * 商品 SPU 名字
     */
    @DiffField(name = "SPU名字")
    private String spuName;

    /**
     * 供应商id
     */
    @DiffField(name = "供应商id")
    private Long supplierId;

    /**
     * 供应商类型
     */
    @DiffField(name = "供应商类型")
    private Integer supplierType;

    /**
     * 供应商名称
     */
    @DiffField(name = "供应商名称")
    private String supplierName;

    /**
     * 市场价
     */
    @DiffField(name = "市场价")
    private BigDecimal marketPrice;
    /**
     * 销售价
     */
    @DiffField(name = "销售价")
    private BigDecimal salePrice;
    /**
     * 图片地址
     */
    @DiffField(name = "图片地址")
    private String picUrl;
    /**
     * 商品实际销量
     */
    private Integer salesCount;
    /**
     * 商品初始销量，控制ES排序用，和实际销量无关
     */
    private Integer initSalesCount;

    /**
     * 最低起购量
     */
    private Integer lowestBuy;

    /**
     * 上下架状态：  {@link cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum}
     */
    @DiffField(name = "状态")
    private Integer status;

    /**
     * 商品显示状态  {@link ProductSkuShowStatusEnum}
     */
    private Integer showStatus;

    /**
     * 商品seo运营状态  {@link ProductSkuSeoStatusEnum}
     */
    private Integer seoStatus;

    /**
     * 平台上下架状态：  {@link cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum}
     */
    private Integer platformStatus;

    /**
     * 销售额
     */
    private BigDecimal salesAmount;

    public Boolean isJd() {
        return ObjectUtil.equal(supplierType, SupplierTypeEnum.JD.getType());
    }

    private static final String URL_TPL = "https://{}/#/detail/{}/{}";

    public static String buildUrl(String domain, Long supplierId, Long skuId) {
        return StrUtil.format(URL_TPL, domain, supplierId, skuId);
    }

}

