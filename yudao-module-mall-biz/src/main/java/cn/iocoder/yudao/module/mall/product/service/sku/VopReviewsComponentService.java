package cn.iocoder.yudao.module.mall.product.service.sku;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopAfsComponentService;
import com.alibaba.fastjson.JSON;
import com.jd.open.api.sdk.request.vopzj.VopAfsGetComponentUrlRequest;
import com.jd.open.api.sdk.response.vopzj.VopAfsGetComponentUrlResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.VOP_REQUEST_EXCEPTION;


/**
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 21:44
 */

@Service
@Slf4j
public class VopReviewsComponentService {

    @Autowired
    VopAfsComponentService vopAfsComponentService;
    @Autowired
    ProductSkuService productSkuService;

    @Autowired
    private SupplierService supplierService;

    public String getReviewsComponentUrl(Long skuId) {
        VopAfsGetComponentUrlRequest request = new VopAfsGetComponentUrlRequest();
        Long skuInnerId = productSkuService.convert2JdSkuId(skuId);
        //1、评价组件M端
        request.setComponentType(30);
        Map<String, Object> mapReq = new HashMap<>();
        //京东商品编号
        mapReq.put("skuId", skuInnerId);
        mapReq.put("unselectedColor", "#F5FFFA");
        mapReq.put("selectedColor", "#FF4500");
        mapReq.put("hiddenH5Nav", true);
        request.setExt(JSON.toJSONString(mapReq));

        VopAfsGetComponentUrlResponse componentUrlResponse = vopAfsComponentService.getComponentUrl(request);
        if (componentUrlResponse.getOpenRpcResult().getSuccess()) {
            return componentUrlResponse.getOpenRpcResult().getResult();
        } else {
            throw new ServiceException(VOP_REQUEST_EXCEPTION.getCode(), componentUrlResponse.getOpenRpcResult().getResultMessage());
        }
    }
}
