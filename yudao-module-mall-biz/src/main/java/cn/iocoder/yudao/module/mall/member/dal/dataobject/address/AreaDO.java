package cn.iocoder.yudao.module.mall.member.dal.dataobject.address;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/9/18 13:44
 */

@TableName("mall_area")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AreaDO {

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * 用户编号
     */
    private Long parentId;

    /**
     * 收件人名称
     */
    private String name;

    /**
     * 级别
     */
    private Integer level;

}
