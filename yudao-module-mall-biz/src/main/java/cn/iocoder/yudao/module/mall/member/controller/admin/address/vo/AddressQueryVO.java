package cn.iocoder.yudao.module.mall.member.controller.admin.address.vo;

import cn.iocoder.yudao.module.mall.member.enums.AddressLevelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 查询省市区id
 */
@Data
public class AddressQueryVO {
    /**
     * 1-省 2-市 3-区县 4-乡镇
     */
    @Schema(description = "1-省 2-市 3-区县 4-乡镇")
    @NotNull
    private AddressLevelEnum addressLevel;

    /**
     * 区域编号 addressLevel为1时不传
     */
    @Schema(description = "区域编号 addressLevel为1时不传")
    private Long areaId;
}
