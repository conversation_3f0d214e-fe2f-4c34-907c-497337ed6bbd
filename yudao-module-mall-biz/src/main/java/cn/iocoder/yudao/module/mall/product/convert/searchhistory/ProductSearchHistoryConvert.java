package cn.iocoder.yudao.module.mall.product.convert.searchhistory;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.usersearchhistory.vo.UserSearchHistoryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.searchhistory.vo.ProductSearchHistoryRespVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.searchhistory.ProductSearchHistoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProductSearchHistoryConvert {

    ProductSearchHistoryConvert INSTANCE = Mappers.getMapper(ProductSearchHistoryConvert.class);


    List<ProductSearchHistoryRespVO> convertList(List<ProductSearchHistoryDO> list);

    PageResult<UserSearchHistoryRespVO> convert(PageResult<ProductSearchHistoryDO> bean);





}
