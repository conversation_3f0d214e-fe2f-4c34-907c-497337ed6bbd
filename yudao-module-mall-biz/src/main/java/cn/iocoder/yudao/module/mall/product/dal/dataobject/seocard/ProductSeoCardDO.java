package cn.iocoder.yudao.module.mall.product.dal.dataobject.seocard;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 运营区域 DO
 *
 * <AUTHOR>
 */
@TableName("product_seo_card")
@KeySequence("product_seo_card_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSeoCardDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 区块名称
     */
    private String name;
    /**
     * 显示标题
     */
    private String title;
    /**
     * 图标
     */
    private String icon;
    /**
     * 背景图
     */
    private String imageUrl;
    /**
     * 区块类型 {@link  cn.iocoder.yudao.module.mall.product.enums.seo.SeoCardTypeEnum}
     */
    private Integer type;
    /**
     * 区块布局 {@link  cn.iocoder.yudao.module.mall.product.enums.seo.SeoCardLayoutEnum}
     */
    private Integer layout;
    /**
     * 区块内容,json格式
     */
    private String content;
    /**
     * 置顶商品,逗号分隔
     */
    private String topSku;
    /**
     * 区块备注
     */
    private String memo;
    /**
     * 区块排序
     */
    private Integer sort;
    /**
     * 状态, 0-启用，1-禁用 {@link  cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;

}
