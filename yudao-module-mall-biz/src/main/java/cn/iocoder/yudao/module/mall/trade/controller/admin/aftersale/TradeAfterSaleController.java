package cn.iocoder.yudao.module.mall.trade.controller.admin.aftersale;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import cn.iocoder.yudao.framework.ratelimiter.core.keyresolver.impl.UserRateLimiterKeyResolver;
import cn.iocoder.yudao.module.mall.trade.controller.admin.aftersale.vo.*;
import cn.iocoder.yudao.module.mall.trade.controller.admin.aftersale.vo.log.TradeAfterSaleLogRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.aftersale.vo.AppTradeAfterSaleCreateReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.aftersale.vo.AppTradeAfterSaleDeliveryReqVO;
import cn.iocoder.yudao.module.mall.trade.convert.aftersale.TradeAfterSaleConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.aftersale.TradeAfterSaleDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.aftersale.TradeAfterSaleLogDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.enums.aftersale.TradeAfterSaleStatusEnum;
import cn.iocoder.yudao.module.mall.trade.service.aftersale.TradeAfterSaleService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.trade.service.order.VopComponentService;
import cn.iocoder.yudao.module.mall.trade.service.purchase.PurchaseService;
import cn.iocoder.yudao.module.pay.api.notify.dto.PayRefundNotifyReqDTO;
import de.danielbechler.util.Assert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 交易售后")
@RestController
@RequestMapping("/trade/after-sale")
@Validated
@Slf4j
public class TradeAfterSaleController {

    @Resource
    private TradeAfterSaleService afterSaleService;
    @Resource
    private TradeOrderService orderService;
    @Resource
    private VopComponentService vopComponentService;
    @Resource
    private PurchaseService purchaseService;

    @PostMapping(value = "/create")
    @Operation(summary = "申请售后")
    @Idempotent(timeout = 10, message = "正在处理中，请勿重复提交")
    @RateLimiter(count = 6, timeUnit = TimeUnit.MINUTES, keyResolver = UserRateLimiterKeyResolver.class)
    @PreAuthorize("@ss.hasPermission('trade:after-sale:create')")
    public CommonResult<Long> createAfterSale(@RequestBody AppTradeAfterSaleCreateReqVO createReqVO) {
        return success(afterSaleService.createAfterSale(null, createReqVO));
    }

    @PostMapping(value = "/delivery")
    @Operation(summary = "退回货物")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:delivery')")
    @RateLimiter(count = 6, timeUnit = TimeUnit.MINUTES, keyResolver = UserRateLimiterKeyResolver.class)
    public CommonResult<Boolean> deliveryAfterSale(@RequestBody AppTradeAfterSaleDeliveryReqVO deliveryReqVO) {
        afterSaleService.deliveryAfterSale(getLoginUserId(), deliveryReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得交易售后分页")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:query')")
    public CommonResult<PageResult<TradeAfterSaleRespPageItemVO>> getAfterSalePage(@Valid TradeAfterSalePageReqVO pageVO) {
        // 查询售后
        PageResult<TradeAfterSaleDO> pageResult = afterSaleService.getAfterSalePage(pageVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }

        return success(TradeAfterSaleConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/get")
    @Operation(summary = "获得交易售后详情")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:query')")
    public CommonResult<TradeAfterSaleRespVO> getAfterSaleDetail(Long id, Long orderItemId) {
        TradeAfterSaleDO afterSaleDO = null;
        if(id != null) {
            afterSaleDO = afterSaleService.getById(id);
        } else if(orderItemId != null) {
            afterSaleDO = afterSaleService.getByOrderItem(orderItemId);
        }
        Assert.notNull(afterSaleDO, "售后单不存在");

        TradeAfterSaleRespVO respVO = TradeAfterSaleConvert.INSTANCE.convert02(afterSaleDO);
        return success(respVO);
    }

    @GetMapping("/log-list")
    @Operation(summary = "获得交易售后日志")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:query')")
    public CommonResult<List<TradeAfterSaleLogRespVO>> getAfterSalePage(@RequestParam Long afterSaleId) {
        List<TradeAfterSaleLogDO> logDOList = afterSaleService.getLogList(afterSaleId);

        return success(TradeAfterSaleConvert.INSTANCE.convertList05(logDOList));
    }

    @PutMapping("/agree")
    @Operation(summary = "同意售后")
    @Parameter(name = "id", description = "售后编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:approve')")
    public CommonResult<Boolean> agreeAfterSale(@RequestParam("id") Long id) {
        afterSaleService.agreeAfterSale(getLoginUserId(), id);
        return success(true);
    }

    @PutMapping("/disagree")
    @Operation(summary = "拒绝售后")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:approve')")
    public CommonResult<Boolean> disagreeAfterSale(@RequestBody TradeAfterSaleDisagreeReqVO confirmReqVO) {
        afterSaleService.disagreeAfterSale(getLoginUserId(), confirmReqVO);
        return success(true);
    }

    @PutMapping("/receive")
    @Operation(summary = "确认收货")
    @Parameter(name = "id", description = "售后编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:receive')")
    public CommonResult<Boolean> receiveAfterSale(@RequestParam("id") Long id) {
        afterSaleService.receiveAfterSale(getLoginUserId(), id);
        return success(true);
    }

    @PutMapping("/refuse")
    @Operation(summary = "拒绝收货")
    @Parameter(name = "id", description = "售后编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:receive')")
    public CommonResult<Boolean> refuseAfterSale(@RequestBody TradeAfterSaleRefuseReqVO refuseReqVO) {
        afterSaleService.refuseAfterSale(getLoginUserId(), refuseReqVO);
        return success(true);
    }

    @PostMapping("/refund")
    @Operation(summary = "确认退款")
    @Parameter(name = "id", description = "售后编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:refund')")
    public CommonResult<Boolean> refundAfterSale(@RequestParam("id") Long id) {
        afterSaleService.refundAfterSale(getLoginUserId(), getClientIP(), id);
        return success(true);
    }

    @PostMapping("/refund-v2")
    @Operation(summary = "只执行退款")
    @Parameter(name = "id", description = "售后编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:refund')")
    public CommonResult<Boolean> refundAfterSaleV2(@RequestParam("id") Long id) {
        afterSaleService.refundAfterSaleV2(getLoginUserId(), getClientIP(), id);
        return success(true);
    }

    @PostMapping("/push-ycrh")
    @Operation(summary = "推送业财订单售后")
    @Parameter(name = "id", description = "售后编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:refund')")
    public CommonResult<Boolean> pushAfterSale2Ycrh(@RequestParam("id") Long id) {
        TradeAfterSaleDO afterSaleDO = afterSaleService.getById(id);
        if(ObjectUtil.equal(TradeAfterSaleStatusEnum.COMPLETE.getStatus(), afterSaleDO.getStatus())) {
            purchaseService.pushOrderAfterSale2Ycrh(afterSaleDO);
        }
        return success(true);
    }

    /**
     * 查询单个订单的售后服务组件URL
     *
     * @param orderItemId
     * @return
     */
    @GetMapping(value = "/getAfterSaleComponentUrl")
    @Operation(summary = "查询单个订单的售后服务组件URL")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:vop')")
    public CommonResult<String> getAfterSaleComponentUrl(@RequestParam(value = "orderItemId", required = true) Long orderItemId) {
        TradeOrderItemDO tradeOrderItemDO = orderService.getOrderItemById(orderItemId);
        return CommonResult.success(vopComponentService.getOrderAfterSaleComponentUrl(tradeOrderItemDO));
    }

    /**
     * 查询所有订单的售后服务组件URL
     * @return
     */
    @GetMapping(value = "/getAllAfterSaleComponentUrl")
    @Operation(summary = "查询所有订单的售后服务组件URL")
    @PreAuthorize("@ss.hasPermission('trade:after-sale:vop')")
    public CommonResult<String> getAllAfterSaleComponentUrl() {
        return CommonResult.success(vopComponentService.getAllAfterSaleAdminComponentUrl());
    }

    @PostMapping("/update-refunded")
    @Operation(summary = "更新售后订单为已退款") // 由 pay-module 支付服务，进行回调，可见 PayNotifyJob
    @PermitAll // 无需登录，安全由 PayDemoOrderService 内部校验实现
    public CommonResult<Boolean> updateAfterRefund(@RequestBody PayRefundNotifyReqDTO notifyReqDTO) {
        // 目前业务逻辑，不需要做任何事情
        // 当然，退款会有小概率会失败的情况，可以监控失败状态，进行告警
        log.info("[updateAfterRefund][notifyReqDTO({})]", notifyReqDTO);
        return success(true);
    }



}
