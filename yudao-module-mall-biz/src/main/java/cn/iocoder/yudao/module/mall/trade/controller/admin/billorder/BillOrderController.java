package cn.iocoder.yudao.module.mall.trade.controller.admin.billorder;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.mall.trade.controller.admin.billorder.vo.*;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.billorder.BillOrderDO;
import cn.iocoder.yudao.module.mall.trade.convert.billorder.BillOrderConvert;
import cn.iocoder.yudao.module.mall.trade.service.billorder.BillOrderService;

@Tag(name = "管理后台 - 账单订单")
@RestController
@RequestMapping("/trade/bill-order")
@Validated
public class BillOrderController {

    @Resource
    private BillOrderService billOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建账单订单")
    @PreAuthorize("@ss.hasPermission('trade:bill-order:create')")
    public CommonResult<Long> createBillOrder(@Valid @RequestBody BillOrderCreateReqVO createReqVO) {
        return success(billOrderService.createBillOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新账单订单")
    @PreAuthorize("@ss.hasPermission('trade:bill-order:update')")
    public CommonResult<Boolean> updateBillOrder(@Valid @RequestBody BillOrderUpdateReqVO updateReqVO) {
        billOrderService.updateBillOrder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除账单订单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('trade:bill-order:delete')")
    public CommonResult<Boolean> deleteBillOrder(@RequestParam("id") Long id) {
        billOrderService.deleteBillOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得账单订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:bill-order:query')")
    public CommonResult<BillOrderRespVO> getBillOrder(@RequestParam("id") Long id) {
        BillOrderDO billOrder = billOrderService.getBillOrder(id);
        return success(BillOrderConvert.INSTANCE.convert(billOrder));
    }

    @GetMapping("/list")
    @Operation(summary = "获得账单订单列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('trade:bill-order:query')")
    public CommonResult<List<BillOrderRespVO>> getBillOrderList(@RequestParam("ids") Collection<Long> ids) {
        List<BillOrderDO> list = billOrderService.getBillOrderList(ids);
        return success(BillOrderConvert.INSTANCE.convertList(list));
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出账单订单 Excel")
    @PreAuthorize("@ss.hasPermission('trade:bill-order:export')")
    @OperateLog(type = EXPORT)
    public void exportBillOrderExcel(@Valid BillOrderExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<BillOrderDO> list = billOrderService.getBillOrderList(exportReqVO);
        // 导出 Excel
        List<BillOrderExcelVO> datas = BillOrderConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "账单订单.xls", "数据", BillOrderExcelVO.class, datas);
    }

}
