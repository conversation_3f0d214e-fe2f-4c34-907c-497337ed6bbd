package cn.iocoder.yudao.module.mall.trade.controller.admin.message.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 交易订单的分页 Request VO")
@Data
public class TradeMessagePageReqVO extends PageParam {

    @Schema(description = "消息编号", example = "1123")
    private Long id;

    @Schema(description = "供应商ID", example = "12")
    private Long supplierId;

    @Schema(description = "消息类型", example = "1")
    private Integer messageType;

    @Schema(description = "消息状态", example = "12")
    private Integer messageStatus;

    @Schema(description = "创建时间", example = "12")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
