package cn.iocoder.yudao.module.mall.product.controller.app.sku.vo;

import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.BookSkuExtInfoGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.SoundSkuExtInfoGoodsResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23
 */
@Data
public class AppSkuDetailInfo {

    private Long supplierId;
    private String supplierName;
    private String logoUrl;
    private Long skuId;
    private String skuInnerId;
    private String saleUnit;
    private String weight;
    private String productArea;
    private String wareInfo;
    private String imagePath;
    private String specificParam;

    /**
     * 商品状态 1上架 0下架
     */
    private int skuState;
    private String brandName;
    private String category;
    private String skuName;
    private String introduce;
    private String introducePc;
    private String introduceApp;
    private String introduceWechat;
    private int skuType;
    private int selfSellType;
    private String seoModel;
    private String taxCode;
    private String capacity;
    private Integer lowestBuy;
    private String warrantDesc;
    private String spuName;
    private Long spuId;
    private Integer specType;
    private int logisticsType;
    private int saveEnergyType;
    private BigDecimal taxRatePercentage;
    private String color;
    private String size;
    private String width;
    private String length;
    private String height;
    private Integer deliveryTime;
    private Boolean isJD;

    //京东图书
    private BookSkuExtInfoGoodsResp bookExtInfo;
    //京东音像制品
    private SoundSkuExtInfoGoodsResp soundExtInfo;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 售卖状态
     */
    @Schema(description = "售卖状态")
    private Integer saleStatus;

    /**
     * 轮播图
     */
    private List<String> sliderPicUrls;

    private List<ParamGroupAttributeGoodsRespVO> paramGroupAttrList;

}
