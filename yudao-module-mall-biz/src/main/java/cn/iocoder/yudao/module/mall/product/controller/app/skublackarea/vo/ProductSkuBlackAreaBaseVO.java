package cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import javax.validation.constraints.*;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * 商品sku禁售区域 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ProductSkuBlackAreaBaseVO implements Serializable {

    /**
     * spu编号
     */
    @Schema(description = "spu编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "spu编号不能为空")
    private Long spuId;

    /**
     * 商品 SPU 名字
     */
    @Schema(description = "商品 SPU 名字")
    private String spuName;

    /**
     * sku编号
     */
    @Schema(description = "sku编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "sku编号不能为空")
    private Long skuId;

    /**
     * 商品 SKU 名字
     */
    @Schema(description = "商品 SKU 名字")
    private String skuName;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "供应商id不能为空")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "供应商名称不能为空")
    private String supplierName;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结束时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime endTime;

    /**
     * 所在省份
     */
    @Schema(description = "所在省份")
    private Long provinceId;

    /**
     * 所在城市
     */
    @Schema(description = "所在城市")
    private Long cityId;

    /**
     * 所在区县
     */
    @Schema(description = "所在区县")
    private Long countyId;

    /**
     * 状态 1-启用 0-禁用
     */
    @Schema(description = "状态 1-启用 0-禁用")
    private Boolean status;

}
