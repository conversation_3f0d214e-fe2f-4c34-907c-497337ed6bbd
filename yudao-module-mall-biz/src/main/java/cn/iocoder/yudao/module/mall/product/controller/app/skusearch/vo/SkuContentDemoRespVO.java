package cn.iocoder.yudao.module.mall.product.controller.app.skusearch.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Schema(description = "APP前台 - 商品搜索 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SkuContentDemoRespVO extends SkuContentDemoBaseVO {

    @Schema(description = "id", required = true)
    private Long id;

}
