package cn.iocoder.yudao.module.mall.trade.controller.admin.settle.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 订单结算请求参数")
@Data
public class OrderSettleReq extends PageParam {

    /**
     * 结算状态： 0 待结算 1已结算
     */
    @Schema(description = "结算状态： 0 待结算 1已结算", example = "1")
    private Integer settleStatus;

    /**
     * 发票状态： 0 未开票 1 已申请开票 2 开票完成
     * 枚举 {@link cn.iocoder.yudao.module.mall.trade.enums.order.InvoiceStatusEnum}
     */
    @Schema(description = "发票状态", example = "1")
    private Integer invoiceStatus;

    @Schema(description = "固资状态", example = "1")
    private Integer assetStatus;

    @Schema(description = "订单编号", example = "1024")
    private String orderNo;

    @Schema(description = "京东订单编号", example = "1024")
    private Long jdOrderId;

    @Schema(description = "姓名")
    private String receiverName;

    @Schema(description = "手机号")
    private String receiverMobile;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "供应商ID", example = "1024")
    private Long supplierId;

    @Schema(description = "支付方式", example = "1")
    private Integer paymentMethod;

    @Schema(description = "是否线下结算", example = "false")
    private Boolean offlineSettlement;

    /**
     * 订单创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * 订单完成时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] finishTime;

    /**
     * 订单是否为父子订单
     */
    @Schema(description = "订单是否为父子订单")
    private Integer parentType;

}
