package cn.iocoder.yudao.module.mall.product.service.tag;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupSimpleVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.tag.vo.ProductTagGroupUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.tag.ProductTagGroupDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 标签分组 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductTagGroupService extends IService<ProductTagGroupDO> {

    /**
     * 创建标签分组
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTagGroup(@Valid ProductTagGroupCreateReqVO createReqVO);

    /**
     * 更新标签分组
     *
     * @param updateReqVO 更新信息
     */
    void updateTagGroup(@Valid ProductTagGroupUpdateReqVO updateReqVO);

    /**
     * 删除标签分组
     *
     * @param id 编号
     */
    void deleteTagGroup(Long id);

    /**
     * 获得标签分组
     *
     * @param id 编号
     * @return 标签分组
     */
    ProductTagGroupDO getTagGroup(Long id);

    /**
     * 获得标签分组列表
     *
     * @param ids 编号
     * @return 标签分组列表
     */
    List<ProductTagGroupDO> getTagGroupList(Collection<Long> ids);

    /**
     * 获取全量启用的标签分组，包含子标签
     * @return
     */
    List<ProductTagGroupSimpleVO> getAllSimpleList();

    /**
     * 获得标签分组分页
     *
     * @param pageReqVO 分页查询
     * @return 标签分组分页
     */
    PageResult<ProductTagGroupDO> getTagGroupPage(ProductTagGroupPageReqVO pageReqVO);

}
