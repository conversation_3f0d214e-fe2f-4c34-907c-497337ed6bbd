package cn.iocoder.yudao.module.mall.member.service.address;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.redis.util.RedisUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.AppAddressCreateReqVO;
import cn.iocoder.yudao.module.mall.member.controller.app.address.vo.AppAddressUpdateReqVO;
import cn.iocoder.yudao.module.mall.member.convert.address.AddressConvert;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AddressDO;
import cn.iocoder.yudao.module.mall.member.dal.mysql.address.AddressMapper;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopAddressService;
import cn.iocoder.yudao.module.mall.vop.req.VopAddressVerifyAreaFourIdOpenReq;
import cn.iocoder.yudao.module.mall.vop.service.VopConfigService;
import com.jd.open.api.sdk.response.vopdz.VopAddressVerifyAreaFourIdOpenReqResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.basis.dal.redis.RedisKeyConstants.MALL_USER_DEFAULT_ADDRESS;
import static cn.iocoder.yudao.module.mall.member.enums.ErrorCodeConstants.ADDRESS_NOT_EXISTS;
import static cn.iocoder.yudao.module.mall.member.enums.ErrorCodeConstants.ADDRESS_NOT_VALID;

/**
 * 用户收件地址 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AddressServiceImpl implements AddressService {

    @Resource
    private AddressMapper addressMapper;
    @Resource
    private VopAddressService vopAddressService;
    @Resource
    private VopConfigService vopConfigService;
    @Resource
    private AreaServiceImpl areaService;
    @Resource
    private RedisUtils redisUtils;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = MALL_USER_DEFAULT_ADDRESS, key = "#userId")
    public Long createAddress(Long userId, AppAddressCreateReqVO createReqVO) {
        //校验地址
        String errorMsg = validateAddress(createReqVO.getProvinceId(), createReqVO.getCityId(), createReqVO.getCountyId(), createReqVO.getTownId());
        if(StringUtils.isNotBlank(errorMsg)) {
            log.warn("用户地址不合法: {}",errorMsg);
            throw exception(ADDRESS_NOT_VALID);

        }
        // 如果添加的是默认收件地址，则将原默认地址修改为非默认
        if (Boolean.TRUE.equals(createReqVO.getDefaulted())) {
            List<AddressDO> addresses = addressMapper.selectListByUserIdAndDefaulted(userId, true);
            addresses.forEach(address -> addressMapper.updateById(new AddressDO().setId(address.getId()).setDefaulted(false)));
        }
        // 插入
        AddressDO address = AddressConvert.INSTANCE.convert(createReqVO);
        List<AddressDO> addressList = getAddressList(userId);
        if(CollectionUtil.isEmpty(addressList)) {
            address.setDefaulted(true);
        }

        address.setUserId(userId);
        addressMapper.insert(address);
        // 返回
        return address.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = MALL_USER_DEFAULT_ADDRESS, key = "#userId")
    public void updateAddress(Long userId, AppAddressUpdateReqVO updateReqVO) {
        // 校验存在,校验是否能够操作
        validAddressExists(userId, updateReqVO.getId());
        //校验地址
        String errorMsg = validateAddress(updateReqVO.getProvinceId(), updateReqVO.getCityId(), updateReqVO.getCountyId(), updateReqVO.getTownId());
        if(StringUtils.isNotBlank(errorMsg)) {
            log.warn("用户地址不合法: {}",errorMsg);
            throw exception(ADDRESS_NOT_VALID);

        }
        // 如果修改的是默认收件地址，则将原默认地址修改为非默认
        if (Boolean.TRUE.equals(updateReqVO.getDefaulted())) {
            List<AddressDO> addresses = addressMapper.selectListByUserIdAndDefaulted(userId, true);
            // 排除自己
            addresses.stream().filter(u -> !u.getId().equals(updateReqVO.getId()))
                    .forEach(address -> addressMapper.updateById(new AddressDO().setId(address.getId()).setDefaulted(false)));
        }

        // 更新
        AddressDO updateObj = AddressConvert.INSTANCE.convert(updateReqVO);
        addressMapper.updateById(updateObj);
    }

    @Override
    @CacheEvict(value = MALL_USER_DEFAULT_ADDRESS, key = "#userId")
    public void deleteAddress(Long userId, Long id) {
        // 校验存在,校验是否能够操作
        validAddressExists(userId, id);
        // 删除
        addressMapper.deleteById(id);
    }

    private void validAddressExists(Long userId, Long id) {
        AddressDO addressDO = getAddress(userId, id);
        if (addressDO == null) {
            throw exception(ADDRESS_NOT_EXISTS);
        }
    }

    @Override
    public AddressDO getAddress(Long userId, Long id) {
        return addressMapper.selectByIdAndUserId(id, userId);
    }

    @Override
    public AddressDO getAddressIncludeDelete(Long userId, Long id) {
        return addressMapper.getAddressIncludeDelete(id, userId);
    }

    @Override
    public List<AddressDO> getAddressList(Long userId) {
        return addressMapper.selectListByUserIdAndDefaulted(userId, null);
    }

    @Override
    @Cacheable(value = MALL_USER_DEFAULT_ADDRESS, key = "#userId")
    public AddressDO getDefaultUserAddress(Long userId) {
        List<AddressDO> addresses = addressMapper.selectListByUserIdAndDefaulted(userId, true);
        return CollUtil.getFirst(addresses);
    }

    @Override
    public AddressDO checkExists(AddressDO addressDO) {
        List<AddressDO> addressList = getAddressList(addressDO.getUserId());
        boolean haveDefaultAdd = false;
        if(CollUtil.isNotEmpty(addressList)) {
            haveDefaultAdd = addressList.stream().anyMatch(address -> address.getDefaulted());
            AddressDO existAddress = addressList.stream().filter(item -> {
                return item.getName().equals(addressDO.getName()) &&
                        item.getMobile().equals(addressDO.getMobile()) &&
                        item.getProvinceId().equals(addressDO.getProvinceId()) &&
                        ObjectUtil.equal(item.getCityId(), addressDO.getCityId()) &&
                        ObjectUtil.equal(item.getCountyId(), addressDO.getCountyId()) &&
                        ObjectUtil.equal(item.getTownId(), addressDO.getTownId()) &&
                        item.getConsigneeAddress().equals(addressDO.getConsigneeAddress());
            }).findFirst().orElse(null);
            if(existAddress != null) {
                if(!haveDefaultAdd) {
                    existAddress.setDefaulted(true);
                    addressMapper.updateById(new AddressDO().setId(existAddress.getId()).setDefaulted(true));
                }
                return existAddress;
            }
        }

        addressDO.setDefaulted(!haveDefaultAdd);
        addressMapper.insert(addressDO);
        String key = String.format("%s:%d:%d", MALL_USER_DEFAULT_ADDRESS, TenantContextHolder.getTenantId(), addressDO.getUserId());
        redisUtils.deleteKeys(key);

        return addressDO;
    }

    private String validateAddress(Long provinceId, Long cityId, Long countyId, Long townId) {
        if(vopConfigService.isVopEnabled()) {
            //校验地址是否合法
            VopAddressVerifyAreaFourIdOpenReq req = new VopAddressVerifyAreaFourIdOpenReq();
            req.setProvinceId(provinceId);
            req.setCityId(cityId);
            req.setCountyId(countyId);
            req.setTownId(townId);
            VopAddressVerifyAreaFourIdOpenReqResponse response = vopAddressService.verifyAreaFourIdOpen(req);
            if(response.getOpenRpcResult() != null) {
                if(!response.getOpenRpcResult().getSuccess()) {
                    return response.getOpenRpcResult().getResultMessage();
                }
            }
            return null;
        }

        return areaService.validateAreaFullLevel(provinceId, cityId, countyId, townId);
    }
}
