package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/9/19 16:02
 */
@Data
public class AppOpenAreaReqVO {

    /**
     * 1-省 2-市 3-区县 4-乡镇
     */
    @Schema(description = "1-省 2-市 3-区县 4-乡镇")
    @NotNull(message = "区域级别不能为空")
    private Integer level;

    /**
     * 区域编号 addressLevel为1时不传
     */
    @Schema(description = "区域编号 addressLevel为1时不传")
    private Long areaId;

}
