package cn.iocoder.yudao.module.mall.product.dal.dataobject.skublackarea;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 商品sku禁售区域 DO
 *
 * <AUTHOR>
 */
@TableName("product_sku_black_area")
@KeySequence("product_sku_black_area_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSkuBlackAreaDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * spu编号
     */
    private Long spuId;

    /**
     * 商品 SPU 名字
     */
    private String spuName;

    /**
     * sku编号
     */
    private Long skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 所在省份
     */
    private Long provinceId;
    /**
     * 所在城市
     */
    private Long cityId;
    /**
     * 所在区县
     */
    private Long countyId;
    /**
     * 状态 1-启用 0-禁用
     */
    private Boolean status;

}
