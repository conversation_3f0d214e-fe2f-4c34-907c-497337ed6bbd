package cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 订单主信息导出
 *
 * <AUTHOR>
 * @Date 2024/6/13
 */
@Schema(description = "管理后台 - 订单导出excel VO")
@Data
public class CommonOrderExportVO {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "订单号")
    @Excel(name = "订单号", orderNum = "1",width = 24)
    private String no;

    @Schema(description = "外部订单号")
    @Excel(name = "外部订单号", orderNum = "2",width = 24, replace = {"--_null"})
    private String thirdOrderId;

    @Schema(description = "父订单号", example = "88888888")
    @Excel(name = "父订单号", orderNum = "3",width = 24, replace = {"--_null"})
    private String parentNo;

    @Schema(description = "审批单号")
    @Excel(name = "审批单号", orderNum = "4",replace = {"--_null"}, width = 24)
    private String bpmNo;

    @Schema(description = "供应商名称")
    @Excel(name = "供应商名称", orderNum = "5",width = 20)
    private String supplierName;

    @Schema(description = "下单人")
    @Excel(name = "下单人", orderNum = "6",width = 15)
    private String userName;

    @Schema(description = "下单人工号")
    @Excel(name = "下单人工号", orderNum = "7", replace = {"--_null"}, width = 15)
    private String userNo;

    @Schema(description = "所在部门")
    @Excel(name = "所在部门", orderNum = "8", replace = {"--_null"}, width = 15)
    private String deptName;

    @Schema(description = "收货人")
    @Excel(name = "收货人", orderNum = "9",width = 15)
    private String receiverName;

    @Schema(description = "收货地址")
    @Excel(name = "收货地址", orderNum = "10",width = 45)
    private String receiverAddress;

    @Schema(description = "收货人手机")
    @Excel(name = "收货人手机", orderNum = "10",width = 15)
    private String receiverMobile;

    @Schema(description = "订单状态")
    @Excel(name = "订单状态", orderNum = "11", replace = {"未确认_1", "已确认_2", "已发货_3", "已送达_4", "已签收_5", "已完成_8", "已取消_9"}, width = 15)
    private Integer status;

    @Schema(description = "开票状态")
    @Excel(name = "开票状态", orderNum = "12", replace = {"未开票_0", "已申请开票_1", "开票完成_2", "开票失败_3", "验真处理中_4", "验真完成_5", "验真失败_6"}, width = 15)
    private Integer invoiceStatus;

    @Schema(description = "账单编号")
    @Excel(name = "账单编号", orderNum = "13", replace = {"--_null"}, width = 15)
    private Long billNo;

    @Schema(description = "下单时间")
    @Excel(name = "下单时间", orderNum = "14", exportFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", replace = {"--_null"}, width = 24)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime submitTime;

    @Schema(description = "完成时间")
    @Excel(name = "完成时间", orderNum = "15", exportFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", replace = {"--_null"}, width = 24)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime finishTime;

    @Schema(description = "商品金额")
    @Excel(name = "商品金额", orderNum = "16",width = 15, replace = {"0_null"})
    private BigDecimal productPrice;

    @Schema(description = "运费金额")
    @Excel(name = "运费金额", orderNum = "17",width = 15, replace = {"0_null"})
    private BigDecimal deliveryPrice;

    @Schema(description = "订单总金额")
    @Excel(name = "订单总金额", orderNum = "18",width = 15, replace = {"0_null"})
    private BigDecimal orderPrice;

    @Schema(description = "退款金额")
    @Excel(name = "退款金额", orderNum = "19",width = 15, replace = {"0_null"})
    private BigDecimal refundPrice;

    @Schema(description = "账期支付金额")
    @Excel(name = "账期支付金额", orderNum = "20", width = 20, replace = {"0_null"})
    private BigDecimal periodPayPrice;

    @Schema(description = "个人支付金额")
    @Excel(name = "个人支付金额", orderNum = "21", width = 20, replace = {"0_null"})
    private BigDecimal personalPayPrice;

    @Schema(description = "下单终端")
    @Excel(name = "下单终端", orderNum = "22", replace = {"PC端_1", "移动端H5_20"}, width = 15)
    private Integer platform;

    @Schema(description = "订单明细")
    private List<OrderItemExportVO> itemList;

    @Schema(description = "订单明细ID")
    @Excel(name = "订单明细ID", orderNum = "23", width = 25)
    private Long itemId;

    @Schema(description = "商品SKU")
    @Excel(name = "商品SKU", orderNum = "24", width = 25)
    private Long skuId;

    @Schema(description = "三方SKU")
    @Excel(name = "三方SKU", orderNum = "25", width = 25)
    private String skuInnerId;

    @Schema(description = "商品标签")
//    @Excel(name = "商品标签", orderNum = "3", width = 20)
    private String tagNameStr;

    @Schema(description = "商品名称")
    @Excel(name = "商品名称", orderNum = "26", width = 55)
    private String skuName;

    @Schema(description = "商品分类编码")
    private String categoryCode;

    @Schema(description = "一级分类")
    @Excel(needMerge = true, name = "一级分类", orderNum = "27", width = 20)
    private String category1Name;

    @Schema(description = "二级分类")
    @Excel(needMerge = true, name = "二级分类", orderNum = "28", width = 20)
    private String category2Name;

    @Schema(description = "三级分类")
    @Excel(needMerge = true, name = "三级分类", orderNum = "29", width = 20)
    private String category3Name;

    @Schema(description = "商品数量")
    @Excel(name = "商品数量", orderNum = "30", width = 15)
    private Integer count;

    @Schema(description = "售后商品数量")
    @Excel(name = "售后商品数量", orderNum = "31", width = 16)
    private Integer afterSaleCount;

    @Schema(description = "商品销量", example = "1")
    @Excel(needMerge = true, name = "商品销量", orderNum = "32", width = 20)
    private Integer salesCount;

    @Schema(description = "商品单价")
    @Excel(name = "商品单价", orderNum = "33", width = 20)
    private BigDecimal skuPrice;

    @Schema(description = "销售额", example = "1")
    @Excel(needMerge = true, name = "销售额", orderNum = "34", width = 20)
    private BigDecimal salesAmount;

    @Schema(description = "固资状态")
    @Excel(name = "固资状态", orderNum = "35", replace = {"建档待处理_0", "建档已提交_1", "无须建档_2", "建档中_3", "建档成功_4", "建档失败_5"}, width = 20)
    private Integer assetStatus;

    @Schema(description = "是否为固资")
    @Excel(name = "是否为固资", orderNum = "36", replace = {"是_true", "否_false"}, width = 15)
    private Boolean isAsset ;

    @Schema(description = "售后状态")
    @Excel(name = "售后状态", orderNum = "37", replace = {"未售后_0", "售后中_1", "已退款_2"}, width = 20)
    private Integer afterSaleStatus;

}
