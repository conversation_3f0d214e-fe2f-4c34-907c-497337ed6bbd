package cn.iocoder.yudao.module.mall.product.service.productcomment;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.productcomment.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productcomment.ProductCommentReplyDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 商品评价回复 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductCommentReplyService {

    /**
     * 创建商品评价回复
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProductCommentReply(@Valid ProductCommentReplyCreateReqVO createReqVO);

    /**
     * 更新商品评价回复
     *
     * @param updateReqVO 更新信息
     */
    void updateProductCommentReply(@Valid ProductCommentReplyUpdateReqVO updateReqVO);

    /**
     * 删除商品评价回复
     *
     * @param id 编号
     */
    void deleteProductCommentReply(Long id);

    /**
     * 获得商品评价回复
     *
     * @param id 编号
     * @return 商品评价回复
     */
    ProductCommentReplyDO getProductCommentReply(Long id);

    /**
     * 获得商品评价回复列表
     *
     * @param ids 编号
     * @return 商品评价回复列表
     */
    List<ProductCommentReplyDO> getProductCommentReplyList(Collection<Long> ids);

    /**
     * 获得商品评价回复分页
     *
     * @param pageReqVO 分页查询
     * @return 商品评价回复分页
     */
    PageResult<ProductCommentReplyDO> getProductCommentReplyPage(ProductCommentReplyPageReqVO pageReqVO);

}
