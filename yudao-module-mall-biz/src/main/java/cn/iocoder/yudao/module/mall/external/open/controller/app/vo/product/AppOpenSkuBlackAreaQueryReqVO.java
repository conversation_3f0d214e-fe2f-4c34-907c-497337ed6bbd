package cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 查询商品禁售区域
 * <AUTHOR>
 */
@Data
public class AppOpenSkuBlackAreaQueryReqVO {
    /**
     * 商品skuId
     */
    @Schema(description = "商品skuId")
    @NotNull(message = "skuIds不能为null")
    @NotEmpty(message = "skuIds不能为空")
    private List<Long> skuIds;

}
