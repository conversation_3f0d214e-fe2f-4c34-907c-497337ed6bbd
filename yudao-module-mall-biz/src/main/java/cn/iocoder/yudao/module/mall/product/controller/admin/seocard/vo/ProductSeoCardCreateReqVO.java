package cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 运营区域创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSeoCardCreateReqVO extends ProductSeoCardBaseVO {

    @Schema(description = "区域内容,json格式")
    private String content;

    @Schema(description = "置顶商品,逗号分隔")
    private String topSku;

}
