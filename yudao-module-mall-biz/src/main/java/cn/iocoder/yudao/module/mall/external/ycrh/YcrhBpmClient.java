package cn.iocoder.yudao.module.mall.external.ycrh;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.YcrhConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.integration.YcrhConfigService;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.external.ycrh.dto.*;
import cn.iocoder.yudao.module.mall.external.ycrh.enums.YcrhMethodEnum;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/27
 */
@Service
@Slf4j
public class YcrhBpmClient {

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private HttpCommonClient httpCommonClient;

    @Resource
    private YcrhConfigService ycrhConfigService;

    /**
     * 获取租户的业财融合对接配置信息
     * @return
     */
    private YcrhConfigDO getConfig() {
        YcrhConfigDO ycrhConfig = ycrhConfigService.getYcrhConfig();
        if(ycrhConfig != null) {
            if(StringUtils.isBlank(ycrhConfig.getApproveHost())) {
                throw new ServiceException(ErrorCodeConstants.YCRH_CONFIG_NULL);
            }
            return ycrhConfig;
        } else {
            log.error("租户业财整合配置为空");
            throw new ServiceException(ErrorCodeConstants.YCRH_CONFIG_FAIL);
        }
    }

    @SneakyThrows
    public List<ApprovalUserRespDTO> getApprovalUsers(ApprovalUserQueryReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        reqDTO.setBusinessType(ycrhConfig.getParamBillBizCode2());

        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.GET_APPROVER;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        TypeReference<YcrhResp<List<ApprovalUserRespDTO>>> typeReference = new TypeReference<YcrhResp<List<ApprovalUserRespDTO>>>() {};
        YcrhResp<List<ApprovalUserRespDTO>> resp = httpCommonClient.sendRequest(ycrhConfig.getApproveHost(), ycrhMethodEnum, param, typeReference, false);
        return resp.getData();
    }

    /**
     * 上传单据
     *
     * @param reqDTO
     */
    public boolean uploadVoucher(UploadVoucherDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.UPLOAD_VOUCHER;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<Void> resp = httpCommonClient.sendRequest(ycrhConfig.getApproveHost(), ycrhMethodEnum, param, Void.class, true);
        return resp.isSuccess();
    }

    /**
     * 删除单据
     *
     * @param reqDTO
     */
    public boolean deleteVoucher(DeleteVoucherDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        reqDTO.setBusinessCode(ycrhConfig.getParamBillBizCode2());

        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.DELETE_VOUCHER;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<Void> resp = httpCommonClient.sendRequest(ycrhConfig.getApproveHost(), ycrhMethodEnum, param, Void.class, false);
        return resp.isSuccess();
    }

    /**
     * 查询单据
     *
     * @param reqDTO
     */
    @SneakyThrows
    public List<ApprovalResultDTO> getVoucher(GetVoucherDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        if(StringUtils.isBlank(reqDTO.getModuleName())) {
            reqDTO.setModuleName("YC");
        }
        reqDTO.setBusinessCode(ycrhConfig.getParamBillBizCode2());

        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.GET_VOUCHER;
        TypeReference<YcrhResp<List<ApprovalResultDTO>>> typeReference = new TypeReference<YcrhResp<List<ApprovalResultDTO>>>() {};
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<List<ApprovalResultDTO>> ycrhResp = httpCommonClient.sendRequest(ycrhConfig.getApproveHost(), ycrhMethodEnum, param, typeReference, false);
        return ycrhResp.getData();
    }

    /**
     * 查询单据最终状态, resultCode枚举：
     * 0: 未审批或审批中
     * 1: 审批通过
     * 2: 审批驳回
     * 9: 审批单不存在
     *
     * @param reqDTO
     */
    @SneakyThrows
    public YcrhResp getFinalVoucher(GetVoucherDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        if(StringUtils.isBlank(reqDTO.getModuleName())) {
            reqDTO.setModuleName("YC");
        }
        reqDTO.setBusinessCode(ycrhConfig.getParamBillBizCode2());

        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.GET_FINAL_VOUCHER;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<Void> ycrhResp = httpCommonClient.sendRequest(ycrhConfig.getApproveHost(), ycrhMethodEnum, param, Void.class, false);
        return ycrhResp;
    }

    /**
     * 归档审批单，归档后无法执行撤消等操作
     *
     * @param reqDTO
     */
    @SneakyThrows
    public YcrhResp completeVoucher(CompleteVoucherReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        if(StringUtils.isBlank(reqDTO.getModuleName())) {
            reqDTO.setModuleName("YC");
        }
        reqDTO.setBusinessCode(ycrhConfig.getParamBillBizCode2());

        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.COMPLETE_VOUCHER;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<Void> ycrhResp = httpCommonClient.sendRequest(ycrhConfig.getApproveHost(), ycrhMethodEnum, param, Void.class, false);
        return ycrhResp;
    }

    /**
     * 上传附件
     * @param reqDTO
     */
    public void uploadAttachments(UploadAttachmentsDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.UPLOAD_BPM_ATTACHMENTS;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        httpCommonClient.sendRequest(ycrhConfig.getApproveHost(), ycrhMethodEnum, param, Void.class, true);
    }

    /**
     * 上传附件
     * @param reqDTO
     */
    public YcrhResp<?> uploadAttachmentsV2(UploadAttachmentsDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.UPLOAD_BPM_ATTACHMENTS;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        return httpCommonClient.sendRequest(ycrhConfig.getApproveHost(), ycrhMethodEnum, param, Void.class, false);
    }

    public YcrhResp<Object> commonRequest(YcrhMethodEnum ycrhMethodEnum, Map<String, Object> param) {
        YcrhConfigDO ycrhConfig = getConfig();
        JSONObject paramJson = assembleParam(ycrhMethodEnum, param, ycrhConfig);
        return httpCommonClient.sendRequest(ycrhConfig.getApproveHost(), ycrhMethodEnum, paramJson, Object.class, false);
    }

    private JSONObject assembleParam(YcrhMethodEnum ycrhMethodEnum, Object param, YcrhConfigDO ycrhConfig) {
        Map<String, Object> data = objectMapper.convertValue(param, new TypeReference<Map<String, Object>>() {});
        data = MapUtil.map(data, (k, v) -> v == null && ObjectUtil.isBasicType(v) ? "" : v);
        YcrhReqDTO dto = new YcrhReqDTO();
        dto.getData().putAll(data);
        dto.setMethod(ycrhMethodEnum.getMethodName());
        dto.setAppid(ycrhConfig.getAppKey());
        Map<String, Object> paramMap = objectMapper.convertValue(dto, Map.class);
        String sign = SignUtil.createSign(paramMap, ycrhConfig.getAppSecret());
        log.info("业财融合审批类接口调用签名sign: {}", sign);
        JSONObject obj = dto.toJsonObj();
        obj.put("sign", sign);
        return obj;
    }

    public static void main(String[] args) {
        GetVoucherDTO bean = new GetVoucherDTO();
        Map<String, Object> map = YcrhUtils.bean2Map(bean);
        System.out.println("map====" + map);
    }

}
