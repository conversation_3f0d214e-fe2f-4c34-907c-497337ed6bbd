package cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderSummaryRespVO {

    @Schema(description = "待确认订单个数")
    private Long notConfirmOrderCount;

    @Schema(description = "待确认订单金额")
    private BigDecimal notConfirmOrderAmount;

    @Schema(description = "待发货订单个数")
    private Long notDeliveryCount;

    @Schema(description = "待发货订单金额")
    private BigDecimal notDeliveryAmount;

    @Schema(description = "售后订单个数")
    private Long afterSellCount;

    @Schema(description = "售后订单金额")
    private BigDecimal afterSellAmount;

    @Schema(description = "已完成订单个数")
    private Long completeCount;

    @Schema(description = "已完成订单金额")
    private BigDecimal completeAmount;

    @Schema(description = "审批中订单个数")
    private Long auditingCount;

    @Schema(description = "审批中订单金额")
    private BigDecimal auditingAmount;

    @Schema(description = "待审批订单个数")
    private Long auditedCount;

    @Schema(description = "待审批订单金额")
    private BigDecimal auditedAmount;

    @Schema(description = "待结算订单个数")
    private Long notSettlementCount;

    @Schema(description = "待结算订单金额")
    private BigDecimal notSettlementAmount;

    @Schema(description = "已结算订单个数")
    private Long hasSettlementCount;

    @Schema(description = "已结算订单金额")
    private BigDecimal hasSettlementAmount;

    @Schema(description = "已取消订单个数")
    private Long cancelCount;

    @Schema(description = "已取消订单金额")
    private BigDecimal cancelAmount;
}
