package cn.iocoder.yudao.module.mall.product.service.category;

import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.enums.category.ProductCategoryStatusEnum;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品分类 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductCategoryService extends IService<ProductCategoryDO> {

    /**
     * 创建商品分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCategory(@Valid ProductCategoryCreateReqVO createReqVO);

    /**
     * 克隆平台配置商品分类
     *
     * @param cloneReqVO 创建信息
     */
    void cloneCategory(@Valid ProductCategoryCloneReqVO cloneReqVO);

    /**
     * 更新商品分类
     *
     * @param updateReqVO 更新信息
     */
    void updateCategory(@Valid ProductCategoryUpdateReqVO updateReqVO);

    /**
     * 导入商品分类
     * @param list
     */
    void importList(List<ProductCategoryImportExcelVO> list);

    /**
     * 删除商品分类
     *
     * @param id 编号
     */
    void deleteCategory(Long id);

    void deleteCategoryWithChildren(Long categoryId);

    /**
     * 获得商品分类
     *
     * @param id 编号
     * @return 商品分类
     */
    ProductCategoryDO getCategory(Long id);


    /**
     * 批量获取商品分类
     * @param categoryIds
     * @return
     */
    List<ProductCategoryDO> getByCategoryIds(Collection<Long> categoryIds);

    /**
     * 获取商品分类
     * @param type 分类类型
     * @param categoryName 分类名称
     * @return
     */
    ProductCategoryDO getByCategoryName(Integer type, String categoryName);

    /**
     * 获取商品分类
     * @param categoryId
     * @return
     */
    ProductCategoryDO getByCategoryId(Long categoryId);

    List<ProductCategoryDO> getListByEconomyClass(List<String> economyClassList);

    List<ProductCategoryDO> getListByNotEconomyClass(List<String> notEconomyClassList);

    String getEconomyClassByCategoryId(Long categoryId);

    Map<Long, String> getEconomyClassMapByCategoryId(Collection<Long> categoryIds);

    /**
     * 根据商品分类编号及固资类型查询经济分类
     * @param categoryVOS
     * @return
     */
    Map<Long, String> getEconomyClassMapByCategoryIdV2(Collection<SimpleCategoryVO> categoryVOS);

    /**
     * 获取商品分类名称
     * @param categoryId
     * @return
     */
    String getNameByCategoryId(Long categoryId);

    /**
     * 批量获取商品分类及其父类
     * @param categoryIds
     * @return
     */
    List<ProductCategoryDO> getParentsByCategoryIds(List<Long> categoryIds);

    /**
     * 查询分类列表，仅包含分类至根分类
     * @param categoryId
     * @return
     */
    List<ProductCategoryDO> getListFromCurrent2Root(Long categoryId);

    /**
     * 校验商品分类
     *
     * @param id 分类编号
     */
    void validateCategory(Long id);

    /**
     * 获得商品分类的层级
     *
     * @param id 编号
     * @return 商品分类的层级
     */
    Integer getCategoryLevel(Long id);

    /**
     * 获得商品分类列表
     *
     * @param listReqVO 查询条件
     * @return 商品分类列表
     */
    List<ProductCategoryDO> getAllCategoryList(ProductCategoryListReqVO listReqVO);

    /**
     * 获取一级分类列表
     * @param status 状态
     * @return
     */
    List<ProductCategoryDO> getRootCategoryList(ProductCategoryStatusEnum status);

    /**
     * 查询分类总数量
     * @return
     */
    Integer getCategoryTotalCount();

    /**
     * 更新分类状态
     * @param id
     * @param status
     */
    void updateCategoryStatus(Long id, ProductCategoryStatusEnum status);

    /**
     * 获取分类名称路径
     * @param idPath, 逗号分隔
     * @param seperator, 分隔符
     * @return
     */
    String getNamePath(String idPath, String seperator);

    /**
     * 获取分类名称路径
     * @param ids, 分类id
     * @param seperator, 分隔符
     * @return
     */
    String getNamePath(List<Long> ids, String seperator);

    /**
     * 校验商品分类规则
     * @param ids
     * @return
     */
    boolean validateCategoryIds(List<Long> ids);

    /**
     * 获取下级分类列表
     * @param parentCategoryId
     * @param status 状态
     * @return
     */
    List<ProductCategoryDO> getChildCategoryList(Long parentCategoryId, ProductCategoryStatusEnum status);

    /**
     * 获取下级分类树形列表，会填充所有子节点
     * @param parentCategoryId
     * @param status 状态
     * @return
     */
    List<ProductCategoryDO> getChildCategoryTreeList(Long parentCategoryId, ProductCategoryStatusEnum status);

    /**
     * 初始化商品分类配置
     * @param tenantId
     */
    void initCategory4Tenant(Long tenantId);

    /**
     * 从商品池名称解析出三级分类名称
     * @param poolName
     * @return
     */
    String getCategoryNameByPoolName(String poolName);

    /**
     * 从商品池名称解析出三级分类ID
     * @param poolName
     * @return
     */
    Long getCategoryIdByPoolName(String poolName);

    /**
     * 一级分类个数
     * @return
     */
    Long getCategoryCount();

    /**
     * 校验是否为叶子分类
     * @return
     */
    Boolean validLeafCategory(Long categoryId);

    /**
     * 判断是否所有的分类状态为启用
     * @param categoryIds
     * @return
     */
    boolean isAllEnable(Set<Long> categoryIds);

    /**
     * 查询所有禁用的分类ID集合
     * @return
     */
    Set<Long> getDisabledIdList();

    /**
     * 将分类ID列表转换为末级分类ID列表
     * 如果传入的分类ID是非末级分类，则递归获取其所有末级子分类
     * 如果传入的分类ID已经是末级分类，则直接返回
     * @param categoryIds 分类ID列表
     * @return 末级分类ID列表
     */
    List<Long> convertToLeafCategoryIds(List<Long> categoryIds);

    /**
     * 获取分类Excel导出数据
     * 将分类数据转换为三级分类结构的Excel格式
     * @param listReqVO 查询条件
     * @return Excel导出数据列表
     */
    List<ProductCategoryExcelVO> getCategoryExcelData(ProductCategoryListReqVO listReqVO);

}
