package cn.iocoder.yudao.module.mall.product.controller.admin.seocard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 运营区域状态更新 Request VO")
@Data
@ToString(callSuper = true)
public class ProductSeoCardStatusUpdateReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "状态")
    @NotNull(message = "id不能为空")
    private Integer status;

}
