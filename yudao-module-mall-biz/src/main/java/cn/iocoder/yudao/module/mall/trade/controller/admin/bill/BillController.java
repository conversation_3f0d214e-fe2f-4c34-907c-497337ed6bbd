package cn.iocoder.yudao.module.mall.trade.controller.admin.bill;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.basisconfig.BasisConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.basisconfig.BasisConfigService;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.enums.basis.ExportTaskTypeEnum;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.mall.trade.controller.admin.bill.vo.*;
import cn.iocoder.yudao.module.mall.trade.controller.admin.billorder.vo.BillOrderPageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.BillExportVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderPageItemRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderPageReqVO;
import cn.iocoder.yudao.module.mall.trade.convert.bill.BillConvert;
import cn.iocoder.yudao.module.mall.trade.convert.order.TradeOrderConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.bill.BillDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.billorder.BillOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderBillStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderYcrhStatusEnum;
import cn.iocoder.yudao.module.mall.trade.service.bill.BillService;
import cn.iocoder.yudao.module.mall.trade.service.billorder.BillOrderService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.trade.service.settle.OrderSettleService;
import cn.iocoder.yudao.module.mall.util.exportTask.ExportTaskUtil;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTask;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskUtils;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.ttl.TtlRunnable;
import com.alibaba.ttl.TtlWrappers;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 管理后台 - 账单
 */
@Tag(name = "管理后台 - 账单")
@RestController
@RequestMapping("/trade/bill")
@Validated
@Slf4j
public class BillController {

    @Resource
    private BillService billService;

    @Resource
    private TradeOrderService tradeOrderService;

    @Resource
    private BillOrderService billOrderService;

    @Resource
    private OrderSettleService orderSettleService;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private MemberUserService memberUserService;

    @Resource
    private BasisConfigService basicConfigService;

    @Resource
    private AsyncFrontTaskUtils asyncFrontTaskUtils;

    @Resource
    private SupplierService supplierService;

    @Resource
    private ExportTaskUtil exportTaskUtil;

    /**
     * 创建账单
     * @param createReqVO
     * @return
     */
    @PostMapping("/create")
    @Operation(summary = "创建账单")
    @PreAuthorize("@ss.hasPermission('trade:bill:create')")
    public CommonResult<Long> createBill(@Valid @RequestBody BillCreateReqVO createReqVO) {
        supplierService.validateLoginUserSupplier(createReqVO.getSupplierId());

        Long userId = getLoginUserId();
        AdminUserRespDTO user = adminUserApi.getUser(userId).getData();
        if (user == null) {
            user = new AdminUserRespDTO().setId(userId);
        }
        return success(billService.saveBill(user, createReqVO));
    }

    /**
     * 更新账单
     * @param updateReqVO
     * @return
     */
    @PostMapping("/update")
    @Operation(summary = "更新账单")
    @PreAuthorize("@ss.hasPermission('trade:bill:update')")
    public CommonResult<Boolean> updateBill(@RequestBody BillUpdateReqVO updateReqVO) {
        supplierService.validateLoginUserSupplier(updateReqVO.getSupplierId());

        billService.updateBill(updateReqVO);
        return success(true);
    }

    /**
     * 更新订单结算方式
     * @param updateOrderSettlementWayReqVO
     * @return
     */
    @PostMapping("/update-order-settlement-way")
    @Operation(summary = "更新订单结算方式")
    @PreAuthorize("@ss.hasPermission('trade:bill:update')")
    public CommonResult<Boolean> updateOrderSettlementWay(@Valid @RequestBody BillUpdateOrderSettlementWayReqVO updateOrderSettlementWayReqVO) {
        supplierService.validateLoginUserSupplier(updateOrderSettlementWayReqVO.getSupplierId());

        billService.updateOrderSettlementWay(updateOrderSettlementWayReqVO);
        return success(true);
    }

    /**
     * 添加账单与订单关联
     * @param reqVO
     * @return
     */
    @PostMapping("/add-order-relation")
    @Operation(summary = "添加账单与订单关联")
    @PreAuthorize("@ss.hasPermission('trade:bill:order')")
    public CommonResult<Boolean> addOrderRelation(@RequestBody BillOrderRelationReqVO reqVO) {
        billService.addBillOrderRelation(reqVO.getBillId(), reqVO.getOrderNos());
        return success(true);
    }

    /**
     * 批量添加订单到账单
     *
     * @return
     */
    @GetMapping("/batch-add-order-relation")
    @Operation(summary = "批量添加订单到账单")
    @PreAuthorize("@ss.hasPermission('trade:bill:order')")
    public CommonResult batchAddOrderToBill(TradeOrderPageReqVO reqVO) {
        supplierService.validateLoginUserSupplier(reqVO.getSupplierId());

        reqVO.setParentType(0);
        reqVO.setNotZero(true);
        reqVO.setBillStatus(TradeOrderBillStatusEnum.INIT.getStatus());
        reqVO.setStatus(TradeOrderStatusEnum.COMPLETED.getStatus());
        List<String> orderNos = tradeOrderService.getOrderNos(reqVO);
        if(CollectionUtils.isNotEmpty(orderNos)){
            billService.addBillOrderRelation(reqVO.getNotBillId(), orderNos);
        }

        return success(null);
    }

    /**
     * 移除账单与订单关联
     * @param reqVO
     * @return
     */
    @PostMapping("/delete-order-relation")
    @Operation(summary = "移队账单与订单关联")
    @PreAuthorize("@ss.hasPermission('trade:bill:order')")
    public CommonResult<Boolean> deleteOrderRelation(@RequestBody BillOrderRelationReqVO reqVO) {
        billService.deleteBillOrderRelation(reqVO.getBillId(), reqVO.getOrderNos());
        return success(true);
    }

    /**
     * 查询待结算订单
     *
     * @return
     */
    @GetMapping("/get-bill-order")
    @Operation(summary = "查询待结算订单")
    @PreAuthorize("@ss.hasPermission('trade:bill:query')")
    public CommonResult<PageResult<TradeOrderPageItemRespVO>> getBillOrder(TradeOrderPageReqVO orderReqVO) {
        supplierService.validateLoginUserSupplier(orderReqVO.getSupplierId());

        orderReqVO.setBillStatus(TradeOrderBillStatusEnum.INIT.getStatus());
        orderReqVO.setStatus(TradeOrderStatusEnum.COMPLETED.getStatus());
        orderReqVO.setSortType(2);
        orderReqVO.setParentType(0);
        orderReqVO.setNotZero(true);

        PageResult<TradeOrderDO> pageResult = tradeOrderService.getOrderPage2(orderReqVO);
        PageResult<TradeOrderPageItemRespVO> orderPageResult = TradeOrderConvert.INSTANCE.convertPage2(pageResult);
        BasisConfigDO basisConfigDO = basicConfigService.getBasisConfig();
        if(basisConfigDO != null && basisConfigDO.needVerifyInvoice()) {
            List<Long> respOrderIds = orderPageResult.getList().stream().map(item -> item.getId()).collect(Collectors.toList());
            Map<Long, Boolean> orderInvoiceCheckStatusMap = orderSettleService.getInvoiceCheckStatus(respOrderIds);
            orderPageResult.getList().forEach(item -> {
                item.setInvoiceCheckStatus(orderInvoiceCheckStatusMap.get(item.getId()));
            });
        }

        return success(orderPageResult);
    }

    /**
     * 删除账单
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除账单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:bill:delete')")
    public CommonResult<Boolean> deleteBill(@RequestParam("id") Long id) {
        billService.deleteBill(id);
        return success(true);
    }

    /**
     * 获得账单
     * @param id
     * @return
     */
    @GetMapping("/get")
    @Operation(summary = "获得账单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:bill:query')")
    public CommonResult<BillRespVO> getBill(@RequestParam("id") Long id) {
        BillRespVO billRespVO = BillConvert.INSTANCE.convert(billService.getBill(id)).setSettlementCount(0);

        List<BillOrderDO> billOrderDOS = billOrderService.getBillOrdersByBillId(id);
        List<Long> orderIds = billOrderDOS.stream().map(BillOrderDO::getOrderId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderIds)) {
            return success(billRespVO);
        }

        int batchSize = 1000;
        int totalSettledOrderCount = 0;
        for (int i = 0; i < orderIds.size(); i += batchSize) {
            List<Long> batchOrderIds = orderIds.subList(i, Math.min(i + batchSize, orderIds.size()));
            long settledOrderCount = tradeOrderService.count(
                    new LambdaQueryWrapperX<TradeOrderDO>()
                            .eq(TradeOrderDO::getYcrhStatus, TradeOrderYcrhStatusEnum.SETTLE_COMPLETE.getStatus())
                            .in(TradeOrderDO::getId, batchOrderIds)
            );

            totalSettledOrderCount += settledOrderCount;
        }

        billRespVO.setSettlementCount(totalSettledOrderCount);
        return success(billRespVO);
    }

    /**
     * 导出账单
     *
     * @param billIds
     */
    @PostMapping("/export")
    @Operation(summary = "导出账单")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.BILL_ORDER_LIST)
    @PreAuthorize("@ss.hasPermission('trade:bill:export')")
    public CommonResult<String> export(@RequestBody List<Long> billIds) {
        final String taskId = AsyncFrontTaskContext.getTaskId();
        asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), AsyncTaskTypeEnum.BILL_ORDER_LIST,
                TtlRunnable.get(() -> {
                    try {
                        asyncFrontTaskUtils.updateExportProgress(30, null);
                        List<BillExportVO> exportList = new ArrayList<>();

                        billIds.forEach(item -> {
                            BillDO bill = billService.getBill(item);
                            List<BillOrderDO> billOrders = billOrderService.getBillOrdersByBillId(bill.getId());
                            List<Long> orderIds = billOrders.stream()
                                    .map(BillOrderDO::getOrderId)
                                    .collect(Collectors.toList());

                            if (CollectionUtils.isEmpty(orderIds)) {
                                return;
                            }

                            int batchSize = 100;
                            for (int i = 0; i < orderIds.size(); i += batchSize) {
                                List<Long> batchOrderIds = orderIds.subList(i, Math.min(i + batchSize, orderIds.size()));
                                log.info("批次{}订单id数量{}", i, batchOrderIds.size());
                                // 批量获取订单
                                List<TradeOrderDO> orderList = tradeOrderService.getOrders(batchOrderIds);
                                log.info("批次{}订单数量{}", i, orderList.size());
                                List<Long> userIds = orderList.stream()
                                        .map(TradeOrderDO::getUserId)
                                        .collect(Collectors.toList());
                                // 获取用户信息
                                List<MemberUserDO> users = memberUserService.getUserList(userIds);
                                // 转换为导出对象
                                List<BillExportVO> billExportVOS =
                                        BillConvert.INSTANCE.convertList03(bill, orderList, users);
                                log.info("批次{}导出订单数量{}", i, billExportVOS.size());
                                exportList.addAll(billExportVOS);
                            }

                            log.info("账单{}导出总订单数量{}", bill.getId(), exportList.size());
                        });

                        // 导出 Excel 文件
                        asyncFrontTaskUtils.updateExportProgress(50, null);
                        asyncFrontTaskUtils.exportDone(taskId, exportList);
                    } catch (Exception e) {
                        asyncFrontTaskUtils.exportFail();
                        log.error("导出账单失败：", e);
                    }
                })
        );

        return success(taskId);
    }

    /**
     * 查询账单关联订单
     *
     * @return
     */
    @GetMapping("/get-bill-ref-order")
    @Operation(summary = "查询账单关联订单")
    @PreAuthorize("@ss.hasPermission('trade:bill:query')")
    public CommonResult<PageResult<TradeOrderPageItemRespVO>> getBillRefOrder(BillOrderPageReqVO reqVO) {
        TradeOrderPageReqVO orderReqVO = new TradeOrderPageReqVO();
        orderReqVO.setPageNo(reqVO.getPageNo()).setPageSize(reqVO.getPageSize());
        orderReqVO.setNo(reqVO.getOrderNo());
        orderReqVO.setVoucherNo(reqVO.getVoucherNo());
        orderReqVO.setBillId(reqVO.getBillId());
        orderReqVO.setSortType(2);
        orderReqVO.setParentType(0);
        orderReqVO.setOfflineSettlement(reqVO.getOfflineSettlement());

        PageResult<TradeOrderDO> pageResult = tradeOrderService.getOrderPage2(orderReqVO);
        return success(TradeOrderConvert.INSTANCE.convertPage2(pageResult));
    }

    /**
     * 获得账单分页
     * @param pageVO
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "获得账单分页")
    @PreAuthorize("@ss.hasPermission('trade:bill:query')")
    public CommonResult<PageResult<BillRespVO>> getBillPage(@Valid BillPageReqVO pageVO) {
        List<BillOrderDO> billOrderDOS = null;
        if(StringUtil.isNotBlank(pageVO.getOrderNo()) || StringUtil.isNotBlank(pageVO.getVoucherNo())){
            List<TradeOrderDO> tradeOrderDOS = tradeOrderService.list(
                    new LambdaQueryWrapperX<TradeOrderDO>()
                            .eqIfPresent(TradeOrderDO::getNo, pageVO.getOrderNo())
                            .eqIfPresent(TradeOrderDO::getVoucherNo, pageVO.getVoucherNo()));
            if(CollUtil.isNotEmpty(tradeOrderDOS)){
                List<Long> orderIds = tradeOrderDOS.stream().map(TradeOrderDO::getId).collect(Collectors.toList());
                billOrderDOS = billOrderService.getBillOrderListByOrder(orderIds);
            }

            if(CollUtil.isNotEmpty(billOrderDOS)){
                if(pageVO.getBillId() != null){
                    billOrderDOS = billOrderDOS.stream().filter(item -> item.getBillId().equals(pageVO.getBillId())).collect(Collectors.toList());
                }
                if(StringUtils.isNotBlank(pageVO.getBillName())){
                    List<BillDO> bills = billService.getBillList(billOrderDOS.stream().map(BillOrderDO::getBillId).collect(Collectors.toList()));
                    if(CollUtil.isNotEmpty(bills)){
                        bills = bills.stream().filter(item -> item.getBillName().contains(pageVO.getBillName())).collect(Collectors.toList());
                    }
                    if(CollUtil.isNotEmpty(bills)){
                        List<Long> billIds = bills.stream().map(BillDO::getId).collect(Collectors.toList());
                        billOrderDOS = billOrderDOS.stream().filter(item -> billIds.contains(item.getBillId())).collect(Collectors.toList());
                    }
                }
            }
        }

        if(CollUtil.isNotEmpty(billOrderDOS)){
            pageVO.setBillId(null);
            pageVO.setBillIds(billOrderDOS.stream().map(BillOrderDO::getBillId).collect(Collectors.toList()));
        }

        PageResult<BillDO> pageResult = billService.getBillPage(pageVO);
        return success(BillConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 查询账单关联订单业财状态
     */
    @GetMapping("/get-bill-order-status")
    @Operation(summary = "查询账单关联订单业财状态")
    @PreAuthorize("@ss.hasPermission('trade:bill:query')")
    public CommonResult<BillOrderQueryRespVO> getBillOrderStatus(Long billId) {
        return success(billService.getBillOrderStatus(billId));
    }

    /**
     * 推送账单
     * @param billId
     * @return
     */
    @PostMapping("/push-bill")
    @Operation(summary = "推送账单")
    @PreAuthorize("@ss.hasPermission('trade:bill:push')")
    public CommonResult pushBill(@RequestBody Long billId) {
        billService.pushBill(billId);
        return success(null);
    }

    /**
     * 取消账单
     * @param billId
     * @return
     */
    @PostMapping("/cancel-bill")
    @Operation(summary = "取消账单")
    @PreAuthorize("@ss.hasPermission('trade:bill:cancel')")
    public CommonResult cancelBill(@RequestBody Long billId) {
        billService.cancelBill(billId);
        return success(null);
    }

    /**
     * 账单状态修改为完成
     * @param billId
     * @return
     */
    @PostMapping("/complete-bill")
    @Operation(summary = "账单状态修改为完成")
    @PreAuthorize("@ss.hasPermission('trade:bill:update')")
    public CommonResult completeBill(@RequestBody Long billId) {
        billService.completeBill(billId);
        return success(null);
    }

    /**
     * 账单拆分
     * @param billIds
     * @return
     */
    @PostMapping("/split-bill")
    @Operation(summary = "拆分账单")
    @PreAuthorize("@ss.hasPermission('trade:bill:push')")
    public CommonResult splitBill(@RequestBody List<Long> billIds) {
        billService.splitBills(billIds, 500);
        return success(null);
    }

    /**
     * 强制同步账单内不存在的订单到业财，如果订单存在则取消后补推，然后同步售后状态
     * @return
     */
    @PostMapping("/forceSyncOrder4YcrhInBill")
    @Operation(summary = "强制同步账单内不存在的订单到业财")
    @PreAuthorize("@ss.hasPermission('trade:bill:update')")
    public CommonResult<Boolean> forceSyncOrder4YcrhInBill(@RequestBody Long billId) {
        billService.forceSyncOrder4YcrhInBill(billId);
        return success(true);
    }

    @GetMapping("/sync-order-bill-status")
    @Operation(summary = "同步订单账单状态")
    @PreAuthorize("@ss.hasPermission('trade:bill:update')")
    public CommonResult<Boolean> syncOrderBillStatus(Long billId) {
        BillDO billDO = billService.getBill(billId);
        Assert.notNull(billDO, "账单不存在");
        CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
            billService.refreshOrderBillStatus(billDO);
            return true;
        }));
        return success(true);
    }

    /**
     * 导出结算单
     * @param billId
     * @param exportPdf
     * @param response
     * @throws IOException
     */
    @GetMapping("/exportSettle")
    @Operation(summary = "导出结算单")
    @PreAuthorize("@ss.hasPermission('trade:bill:export-settle')")
    public void exportSettle(@RequestParam("billId") Long billId,
                             Boolean exportPdf,
                             HttpServletResponse response) throws IOException {
        billService.exportSettle(response, billId, exportPdf);
    }

    /**
     * 导出账单结算压缩包
     * @param billId
     */
    @GetMapping("/exportSettleArchive")
    @Operation(summary = "导出账单结算压缩包")
    @PreAuthorize("@ss.hasPermission('trade:bill:export-archive')")
    public void exportSettleArchive(@RequestParam("billId") Long billId) {
        BillDO billDO =  billService.getById(billId);
        Assert.notNull(billDO, "账单{}不存在", billId);
        exportTaskUtil.exportTask(ExportTaskTypeEnum.BILL_SETTLE_ARCHIVE_EXPORT, getLoginUserId(), billId.toString(), () -> {
            billService.exportSettleArchive(billDO);
        });
    }
}
