package cn.iocoder.yudao.module.mall.trade.controller.admin.cartpurchase.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 采购订单购物车 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class CartPurchaseExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("用户id")
    private Long userId;

    @ExcelProperty("是否选中")
    private Boolean selected;

    @ExcelProperty("订单id")
    private Long orderId;

    @ExcelProperty("创建时间")
    private Date createTime;

}
