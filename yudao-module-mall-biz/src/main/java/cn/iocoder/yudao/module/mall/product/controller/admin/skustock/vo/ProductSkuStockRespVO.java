package cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

@Schema(description = "管理后台 - sku库存 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSkuStockRespVO extends ProductSkuStockBaseVO {

    @Schema(description = "自增ID", required = true)
    private Long id;

    @Schema(description = "创建时间")
    private Date createTime;

}
