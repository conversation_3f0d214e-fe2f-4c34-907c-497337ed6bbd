package cn.iocoder.yudao.module.mall.product.dal.mysql.skublackarea;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo.ProductSkuBlackAreaExportReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.skublackarea.vo.ProductSkuBlackAreaPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skublackarea.ProductSkuBlackAreaDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商品sku禁售区域 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductSkuBlackAreaMapper extends BaseMapperX<ProductSkuBlackAreaDO> {

    default PageResult<ProductSkuBlackAreaDO> selectPage(ProductSkuBlackAreaPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductSkuBlackAreaDO>()
                .eqIfPresent(ProductSkuBlackAreaDO::getSpuId, reqVO.getSpuId())
                .likeIfPresent(ProductSkuBlackAreaDO::getSpuName, reqVO.getSpuName())
                .eqIfPresent(ProductSkuBlackAreaDO::getSupplierId, reqVO.getSupplierId())
                .likeIfPresent(ProductSkuBlackAreaDO::getSupplierName, reqVO.getSupplierName())
                .betweenIfPresent(ProductSkuBlackAreaDO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(ProductSkuBlackAreaDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(ProductSkuBlackAreaDO::getProvinceId, reqVO.getProvinceId())
                .eqIfPresent(ProductSkuBlackAreaDO::getCityId, reqVO.getCityId())
                .eqIfPresent(ProductSkuBlackAreaDO::getCountyId, reqVO.getCountyId())
                .eqIfPresent(ProductSkuBlackAreaDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ProductSkuBlackAreaDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductSkuBlackAreaDO::getId));
    }

    default List<ProductSkuBlackAreaDO> selectList(ProductSkuBlackAreaExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ProductSkuBlackAreaDO>()
                .eqIfPresent(ProductSkuBlackAreaDO::getSpuId, reqVO.getSpuId())
                .likeIfPresent(ProductSkuBlackAreaDO::getSpuName, reqVO.getSpuName())
                .eqIfPresent(ProductSkuBlackAreaDO::getSupplierId, reqVO.getSupplierId())
                .likeIfPresent(ProductSkuBlackAreaDO::getSupplierName, reqVO.getSupplierName())
                .betweenIfPresent(ProductSkuBlackAreaDO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(ProductSkuBlackAreaDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(ProductSkuBlackAreaDO::getProvinceId, reqVO.getProvinceId())
                .eqIfPresent(ProductSkuBlackAreaDO::getCityId, reqVO.getCityId())
                .eqIfPresent(ProductSkuBlackAreaDO::getCountyId, reqVO.getCountyId())
                .eqIfPresent(ProductSkuBlackAreaDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ProductSkuBlackAreaDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductSkuBlackAreaDO::getId));
    }

}
