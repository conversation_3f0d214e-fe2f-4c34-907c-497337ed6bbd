package cn.iocoder.yudao.module.mall.member.api.address;

import cn.iocoder.yudao.module.mall.member.api.address.dto.AddressRespDTO;
import cn.iocoder.yudao.module.mall.member.convert.address.AddressConvert;
import cn.iocoder.yudao.module.mall.member.service.address.AddressService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 用户收件地址 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AddressApiImpl implements AddressApi {

    @Resource
    private AddressService addressService;

    @Override
    public AddressRespDTO getAddress(Long id, Long userId) {
        return AddressConvert.INSTANCE.convert02(addressService.getAddress(userId, id));
    }

    @Override
    public AddressRespDTO getAddressIncludeDelete(Long id, Long userId) {
        return AddressConvert.INSTANCE.convert02(addressService.getAddressIncludeDelete(userId, id));
    }

}
