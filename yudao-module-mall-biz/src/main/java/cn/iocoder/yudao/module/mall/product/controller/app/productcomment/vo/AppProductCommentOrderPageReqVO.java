package cn.iocoder.yudao.module.mall.product.controller.app.productcomment.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 用户 APP - 商品评价分页 Request VO
 */
@Schema(description = "用户 APP - 商品评价分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppProductCommentOrderPageReqVO extends PageParam {


    /**
     * 订单id
     */
    private Long orderId;

    /**
     * skuId
     */
    @Schema(description = "skuId")
    private Long skuId;

    /**
     * 1-时间降序 2-时间升序
     */
    private Integer sortType;

}
