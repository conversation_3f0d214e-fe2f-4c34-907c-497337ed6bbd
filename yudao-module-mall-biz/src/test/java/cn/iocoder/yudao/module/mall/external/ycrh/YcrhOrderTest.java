package cn.iocoder.yudao.module.mall.external.ycrh;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.external.ycrh.dto.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.ttl.TtlRunnable;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 业财融合接口测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class YcrhOrderTest {

    @Resource
    private YcrhClient ycrhClient;
    @Resource
    private ThreadPoolExecutor executor;

    @BeforeAll
    public static void init() {
        TenantContextHolder.setTenantId(150L);
    }

    @Test
    public void getGetYgInfo() {
        YgInfoRespDTO dto = ycrhClient.getYgInfo("06601");
        log.info("getGetYgInfo: {}", dto);
    }

    @Test
    public synchronized void testOrder011() {
        UserProjectRespDTO project = getProject();
        BigDecimal balance = getBalance(project.getProjectNo(), project.getDepartmentNo(), project.getChargeNo());
        log.info("==========balance01: {}", balance);
        log.info("==========pushOrder-begin...");
        ShopOrderDTO order = pushOrder(project);
        balance.subtract(order.getTotalPrice());
        checkAndPrintBalance(balance.subtract(order.getTotalPrice()), project.getProjectNo(), project.getDepartmentNo(), project.getChargeNo());
        log.info("==========pushOrder-done, balance ok...");
        cancelOrder(order.getOrderNo());
        checkAndPrintBalance(balance, project.getProjectNo(), project.getDepartmentNo(), project.getChargeNo());
        log.info("==========cancelOrder-done, balance ok...");

        OrderQueryRespDTO orderDetail = getOrderDetail02(order.getOrderNo());
        log.info("ycrh-order-detail: {}", orderDetail);
    }

    public void testOrder012(int seqNo) {
        log.info("taskSeq====={}", seqNo);
        UserProjectRespDTO project = getProject();
        BigDecimal balance = getBalance(project.getProjectNo(), project.getDepartmentNo(), project.getChargeNo());
        log.info("==========balance01: {}", balance);
        log.info("==========pushOrder-begin...");
        ShopOrderDTO order = pushOrder(project);
        log.info("==========pushOrder-done");
        cancelOrder(order.getOrderNo());
        log.info("==========cancelOrder-done");
    }

    @SneakyThrows
    public Map<String, AtomicInteger> testOrder02() {
        List<String> result = Collections.synchronizedList(new ArrayList<>());
        Map<String, AtomicInteger> resultStat = Collections.synchronizedMap(new HashMap<>());
        String key1 = "成功", key2 = "失败";
        resultStat.put(key1, new AtomicInteger(0));
        resultStat.put(key2, new AtomicInteger(0));

        int size = 200;
        CountDownLatch latch = new CountDownLatch(size);
        AtomicInteger counter = new AtomicInteger(1);
        for (int i = 0 ; i < size ; i++) {
            executor.execute(TtlRunnable.get(() -> {
                final Integer ind = counter.getAndIncrement();
                try {
                    testOrder012(ind);
                    result.add(StrUtil.format("第{}执行成功", ind));
                    resultStat.get(key1).getAndIncrement();
                } catch (Exception e) {
                    log.error("失败消息====={}", e.getMessage());
                    result.add(StrUtil.format("第{}执行失败", ind));
                    resultStat.get(key2).getAndIncrement();
                }
                latch.countDown();
            }));
        }
        latch.await();
        log.info("========result: {}", result);
        log.info("========resultStat: {}", resultStat);
        return resultStat;
    }

    @Test
    @SneakyThrows
    public void testOrder03() {
        long start = System.currentTimeMillis();
        List<String> result = new ArrayList<>();
        for(int i = 0 ; i < 10 ;i++) {
            Map<String, AtomicInteger> resultStat = testOrder02();
            result.add(StrUtil.format("第{}轮 成功次数:{},失败次数:{}", i + 1, resultStat.get("成功"), resultStat.get("失败")));
        }
        log.info("========result: {}", result);
        log.info("========总耗时:{}秒", (System.currentTimeMillis() - start) * 1.0f / 1000 );
    }

    @Test
    @SneakyThrows
    public void testCancelOrder01() {
        String orderNo = "JYH-T-1872575830815838208-01";
        cancelOrder(orderNo);
        cancelOrder(orderNo);
        log.info("cancelOrder done. {}", orderNo);
    }

    public ShopOrderDTO pushOrder(UserProjectRespDTO project) {
        String orderNo = "JYH-T-" + IdUtil.getSnowflakeNextIdStr();
        BigDecimal price = new BigDecimal(10);
        Integer skuCount = 2;
        ShopOrderDTO shopOrderDTO = new ShopOrderDTO()
                .setOrderNo(orderNo)
                .setTotalPrice(price.multiply(new BigDecimal(skuCount)))
                .setPayType("1")
                .setFreight(new BigDecimal("0"))
                .setSingleProject("1")
                .setDepartmentNo(project.getDepartmentNo())
                .setProjectNo(project.getProjectNo())
                .setChargeNo(project.getChargeNo())
                .setOrderTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"))
                .setYgNo(project.getChargeNo());
        SkuInfoDTO skuInfoDTO = new SkuInfoDTO()
                .setSkuId("2514")
                .setSkuName("绿联 笔记本电脑包手提14.9英寸内胆包适用苹果小米联想华为减震抗摔")
                .setSkuType("光盘包")
                .setSkuTypeID("5000158")
                .setSkuPrice(price.multiply(new BigDecimal(skuCount)))
                .setEconomyClass("3019902")
                .setCount(skuCount)
                .setUnitPrice(price)
                .setGoodsType("1");

        shopOrderDTO.setWbSkuInfo(Arrays.asList(skuInfoDTO));

        PushOrderReqDTO reqDTO = new PushOrderReqDTO()
                .setBusinessType("01")
                .setWbShopOrder(shopOrderDTO);
        PushOrderRespDTO pushOrderRespDTO = ycrhClient.pushOrder(reqDTO);
        System.out.println(JSON.toJSONString(pushOrderRespDTO));
        return shopOrderDTO;
    }

    public void cancelOrder(String orderNo) {
        OrderCancelReqDTO reqDTO = new OrderCancelReqDTO()
                .setCancelReason("2")
                .setOrderNo(orderNo)
                .setCancelTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        ycrhClient.cancelOrder(reqDTO);
    }

    private void checkAndPrintBalance(BigDecimal compareBalance, String projectNo, String projectDeptNo, String chargeNo) {
        BigDecimal balance = getBalance(projectNo, projectDeptNo, chargeNo);
        Assert.isTrue(compareBalance.compareTo(balance) == 0, "项目余额不一致：期望:{}, 实际:{}", compareBalance, balance);
    }

    private BigDecimal getBalance(String projectNo, String projectDeptNo, String chargeNo) {
        BalanceQueryReqDTO reqDTO = new BalanceQueryReqDTO()
                .setDepartmentNo(projectDeptNo)
                .setProjectNo(projectNo)
                .setChargeNo(chargeNo);
        BigDecimal balance = ycrhClient.getBalance(reqDTO);
        return balance;
    }

    private UserProjectRespDTO getProject() {
        return new UserProjectRespDTO().setProjectNo("124103").setDepartmentNo("024100").setChargeNo("20240001");
    }

    private OrderQueryRespDTO getOrderDetail(String orderNo) {
        try {
            OrderBatchQueryRespDTO respDTO = ycrhClient.queryOrderList(new OrderListQueryReqDTO().setOrderNoList(Arrays.asList(orderNo)));
            return respDTO.getFoundOrderList().get(0);
        } catch (Exception e) {
            log.error("orderquery error: {}", e.getMessage());
        }

        return null;
    }

    private OrderQueryRespDTO getOrderDetail02(String orderNo) {
        try {
            OrderQueryRespDTO respDTO = ycrhClient.queryOrder(new OrderQueryReqDTO().setOrderNo(orderNo));
            return respDTO;
        } catch (Exception e) {
            log.error("orderquery2 error: {}", e.getMessage());
        }

        return null;
    }

    @Test
    @SneakyThrows
    public void testSplitOrder01() {
        UserProjectRespDTO project = getProject();
        ShopOrderDTO parentOrder = pushOrder02(project);
        log.info("pushOrder OK...{}", parentOrder);
        int length = 10;
        for(int i = 0 ; i < length ; i++) {
            List<SplitShopOrderDTO> subOrder = createSubOrder(parentOrder, 3);
            SplitOrderReqDTO splitOrderReqDTO = new SplitOrderReqDTO()
                    .setParentOrderNo(parentOrder.getOrderNo())
                    .setSplitShopOrderList(subOrder)
                    .setIsOverwrite("1");
            ycrhClient.splitOrder(splitOrderReqDTO);
            log.info("第{}次子订单:{}", i + 1, subOrder);
            log.info("splitOrder 第 {} 次OK", i + 1);
            if(i == length - 1) {
                subOrder.forEach(so -> {
                    cancelOrder(so.getOrderNo());
                });
            }
        }

        log.info("splitOrder done...");
    }

    public ShopOrderDTO pushOrder02(UserProjectRespDTO project) {
        String orderNo = "JYH-T-" + IdUtil.getSnowflakeNextIdStr();
        ShopOrderDTO shopOrderDTO = new ShopOrderDTO()
                .setOrderNo(orderNo)
                .setPayType("1")
                .setFreight(new BigDecimal("0"))
                .setSingleProject("1")
                .setDepartmentNo(project.getDepartmentNo())
                .setProjectNo(project.getProjectNo())
                .setChargeNo(project.getChargeNo())
                .setOrderTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"))
                .setYgNo(project.getChargeNo());

        int skuSize = 5;
        List<SkuInfoDTO> skuList = new ArrayList<>();
        BigDecimal totalPrice = new BigDecimal(0);
        for(int i = 0 ; i < skuSize ; i++) {
            BigDecimal price = new BigDecimal(i + 1);
            int skuCount = 1;
            SkuInfoDTO skuInfoDTO = new SkuInfoDTO()
                    .setSkuId("2516-" + i)
                    .setSkuName("绿联 笔记本电脑包手提14.9英寸内胆包适用苹果小米联想华为减震抗摔-" + i)
                    .setSkuType("光盘包")
                    .setSkuTypeID("5000158")
                    .setSkuPrice(price.multiply(new BigDecimal(skuCount)))
                    .setEconomyClass("3019902")
                    .setCount(skuCount)
                    .setUnitPrice(price)
                    .setGoodsType("1");
            totalPrice = totalPrice.add(skuInfoDTO.getSkuPrice());
            skuList.add(skuInfoDTO);
        }
        shopOrderDTO.setWbSkuInfo(skuList);
        shopOrderDTO.setTotalPrice(totalPrice);

        PushOrderReqDTO reqDTO = new PushOrderReqDTO()
                .setBusinessType("01")
                .setWbShopOrder(shopOrderDTO);
        PushOrderRespDTO pushOrderRespDTO = ycrhClient.pushOrder(reqDTO);
        System.out.println(JSON.toJSONString(pushOrderRespDTO));
        return shopOrderDTO;
    }

    private List<SplitShopOrderDTO> createSubOrder(ShopOrderDTO shopOrderDTO, int splitCount) {
        List<SplitShopOrderDTO> list = new ArrayList<>();
        int skuSize = shopOrderDTO.getWbSkuInfo().size();
        Assert.isTrue(splitCount <= skuSize, "splitCount不能大于订单sku数量");

        List<List<SkuInfoDTO>> chunks = CollUtil.split(shopOrderDTO.getWbSkuInfo(), splitCount);
        for(List<SkuInfoDTO> skuList : chunks) {
            BigDecimal totalPrice = skuList.stream().map(SkuInfoDTO::getSkuPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            String orderNo = "JYH-T-SUB-" + IdUtil.getSnowflakeNextIdStr();
            SplitShopOrderDTO subOrder = new SplitShopOrderDTO()
                    .setOrderNo(orderNo)
                    .setTotalPrice(totalPrice)
                    .setFreight(BigDecimal.ZERO)
                    .setOrderTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"))
                    .setRemark("供应商内部拆单");
            subOrder.setWbSkuInfo(skuList);
            list.add(subOrder);
        }

        return list;
    }

    @Test
    @SneakyThrows
    public void testBatchGetBalanceInfo() {
        BatchBalanceInfoQueryReqDTO reqDTO = new BatchBalanceInfoQueryReqDTO();
        BalanceInfoQueryReqDTO b1 = new BalanceInfoQueryReqDTO();
        b1.setEconomyClass("30199");
        b1.setChargeNo("06601");
        b1.setProjectNo("YZ37719603");
        b1.setDepartmentNo("010100");

        BalanceInfoQueryReqDTO b2 = new BalanceInfoQueryReqDTO();
        b2.setEconomyClass("30202");
        b2.setChargeNo("06601");
        b2.setProjectNo("YZ37719603");
        b2.setDepartmentNo("010100");

        BalanceInfoQueryReqDTO b3 = new BalanceInfoQueryReqDTO();
        b3.setEconomyClass("3020204");
        b3.setChargeNo("06601");
        b3.setProjectNo("YZ37719603");
        b3.setDepartmentNo("010100");

        reqDTO.setProjectList(Arrays.asList(b1, b2, b3));
        List<BalanceInfoRespDTO> resp = ycrhClient.getBalanceInfoBatch(reqDTO);
        log.info("resp=====", resp);
    }


}
