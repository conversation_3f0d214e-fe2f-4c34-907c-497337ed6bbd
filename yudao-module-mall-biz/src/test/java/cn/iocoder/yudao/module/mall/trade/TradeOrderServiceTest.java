package cn.iocoder.yudao.module.mall.trade;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.external.bpm.BpmClient;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderPageReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.mysql.order.TradeOrderMapper;
import cn.iocoder.yudao.module.mall.trade.service.purchase.PurchaseService;
import cn.iocoder.yudao.module.mall.trade.util.OrderNoUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Lazy;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

/**
 * 交易订单测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class TradeOrderServiceTest {

    @Resource
    private TradeOrderMapper orderMapper;
    @Resource
    private PurchaseService purchaseService;
    @Resource
    @Lazy
    private BpmClient bpmClient;
    @Resource
    private OrderNoUtils orderNoUtils;

    @BeforeAll
    public static void init() {
        TenantContextHolder.setTenantId(150L);
    }

    @Test
    public void testSelectPage2() {
        TradeOrderPageReqVO reqVO = new TradeOrderPageReqVO();
        reqVO.setNo("112406121538100217");
        reqVO.setUserMobile("13566668888");
        reqVO.setUserNickname("13566668888");
        reqVO.setSkuId(12L);
        reqVO.setUserIds(new HashSet<>(Arrays.asList(2L,3L)));
        reqVO.setStatusList(Arrays.asList(2,3));
        reqVO.setSkuId(11L);
        reqVO.setSubmitTime(new LocalDateTime[]{LocalDateTime.now().minusDays(10), LocalDateTime.now()});
        reqVO.setCreateTime(new LocalDateTime[]{LocalDateTime.now().minusDays(10), LocalDateTime.now()});
        reqVO.setUpdateTime(new LocalDateTime[]{LocalDateTime.now().minusDays(10), LocalDateTime.now()});
        reqVO.setReceiveTime(new LocalDateTime[]{LocalDateTime.now().minusDays(10), LocalDateTime.now()});
        Page pageInfo = new Page(reqVO.getPageNo(), reqVO.getPageSize());
        Page<TradeOrderDO> pageResult = orderMapper.selectPage2(pageInfo, reqVO);
        log.info("查询分页结果：{}", JsonUtils.toJsonPrettyString(pageResult));
    }

    @Test
    public void testPurchaseOrder() {
        purchaseService.handlePurchaseAuditStatus();
    }

    @Test
    public void testPullBpmStatus() {
        String bpmNo = "1829501912416735232";
        bpmClient.pullBpmStatus(bpmNo);
    }

    @Test
    public void testSelectDeletedChildIds() {
        List<Long> parentIds = Arrays.asList(123L);
        List<Long> childIds = orderMapper.selectDeletedChildIds(parentIds);
        log.info("selectDeletedChildIds------{}", childIds);
    }

    @Test
    public void testGenerateOrderNo() {
        for(int i = 0 ; i < 1000 ; i++) {
            log.info("orderNo：" + orderNoUtils.generate("O", 1));
        }
    }

}
