package cn.iocoder.yudao.module.mall.vop;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.enums.vop.VopBizTypeEnum;
import cn.iocoder.yudao.module.mall.external.open.context.OpenContextHolder;
import cn.iocoder.yudao.module.mall.vop.service.VopMessageHandleService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/6/14
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class VopMessage {
    @Resource
    private VopMessageHandleService vopMessageHandleService;

    @Resource
    private SupplierService supplierService;

    @BeforeAll
    public static void init() {
        TenantContextHolder.setTenantId(150L);
    }

    @Test
    public void fetchVopMessage() throws InterruptedException {
        TenantContextHolder.setTenantId(150L);
        OpenContextHolder.setSupplierId(supplierService.getSupplierJD().getId());
//        for (;;) {
            vopMessageHandleService.fetchVopMessage(VopBizTypeEnum.ORDER.getType(),"1,5,10,11,12,15,18,25,26,31,119,200");
//            Thread.sleep(5000L);
//        }
    }
}
