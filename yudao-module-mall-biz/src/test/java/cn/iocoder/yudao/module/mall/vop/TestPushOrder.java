package cn.iocoder.yudao.module.mall.vop;

import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.vopdd.OperaOrderOpenProvider.request.submitOrder.ConsigneeInfoOrderOpenReq;
import com.jd.open.api.sdk.domain.vopdd.OperaOrderOpenProvider.request.submitOrder.InvoiceInfoOrderOpenReq;
import com.jd.open.api.sdk.domain.vopdd.OperaOrderOpenProvider.request.submitOrder.PaymentInfoOrderOpenReq;
import com.jd.open.api.sdk.domain.vopdd.OperaOrderOpenProvider.request.submitOrder.SkuInfoOrderOpenReq;
import com.jd.open.api.sdk.domain.vopdd.OperaOrderOpenProvider.request.submitOrder.SubmitOrderOpenReq;
import com.jd.open.api.sdk.domain.vopdd.QueryOrderOpenProvider.response.queryDeliveryInfo.DeliveryInfoQueryOpenResp;
import com.jd.open.api.sdk.domain.vopdz.ConvertAddressOpenProvider.response.convertFourAreaByDetailStr.QueryAreaFourIdOpenResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.GetSkuPoolInfoGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuImageList.GetSkuImageGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuPoolGoodsProvider.response.getSkuPoolInfo.GetSkuPoolInfoItemGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuPoolGoodsProvider.response.getSkuPoolInfo.OpenRpcResult;
import com.jd.open.api.sdk.request.vopdd.*;
import com.jd.open.api.sdk.request.vopdz.VopAddressConvertFourAreaByDetailStrRequest;
import com.jd.open.api.sdk.request.vopfp.VopInvoiceQueryElectronicInvoiceDetailRequest;
import com.jd.open.api.sdk.request.vopfp.VopInvoiceQueryInvoiceThirdApplyNoRequest;
import com.jd.open.api.sdk.request.vopsh.VopAfsGetAfsOutlineRequest;
import com.jd.open.api.sdk.request.vopsh.VopAfsQueryLogicticsInfoRequest;
import com.jd.open.api.sdk.request.vopsp.VopGoodsGetSkuDetailInfoRequest;
import com.jd.open.api.sdk.request.vopsp.VopGoodsGetSkuImageListRequest;
import com.jd.open.api.sdk.request.vopsp.VopGoodsGetSkuListPageRequest;
import com.jd.open.api.sdk.request.vopsp.VopGoodsGetSkuPoolInfoRequest;
import com.jd.open.api.sdk.request.vopzj.VopAfsGetComponentUrlRequest;
import com.jd.open.api.sdk.response.vopdd.*;
import com.jd.open.api.sdk.response.vopdz.VopAddressConvertFourAreaByDetailStrResponse;
import com.jd.open.api.sdk.response.vopfp.VopInvoiceQueryElectronicInvoiceDetailResponse;
import com.jd.open.api.sdk.response.vopfp.VopInvoiceQueryInvoiceThirdApplyNoResponse;
import com.jd.open.api.sdk.response.vopsh.VopAfsGetAfsOutlineResponse;
import com.jd.open.api.sdk.response.vopsh.VopAfsQueryLogicticsInfoResponse;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetSkuDetailInfoResponse;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetSkuImageListResponse;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetSkuListPageResponse;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetSkuPoolInfoResponse;
import com.jd.open.api.sdk.response.vopzj.VopAfsGetComponentUrlResponse;
import lombok.Data;
import org.junit.Test;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/12 17:24
 */
public class TestPushOrder {

    public static final String appKey = "928569A0D32DE9E1D510DFA98DDFD6A7";
    public static final String appSecret = "8fc0cb89b04940078f8e90c820cf6ae0";

    public static final String SERVER_URL = "https://api.jd.com/routerjson";
    public static final String accessToken = "081174884146426abf4de9b0d209b9410odg";

    public static void main1(String[] args) throws Exception {
////        VopAfsQueryLogicticsInfoResponse response = getComponentUrl("************",274311284758L);
////        System.out.println(response);
//
////        Object response = testSubmitOrderWO();
////        System.out.println(JSONObject.toJSONString(response));
//
        Object response = getComponentUrl333();
        System.out.println(JSONObject.toJSONString(response));
//
//        Long skuId = 1068549L;
//
    }

    public static void main(String[] args) throws Exception {
        VopAfsGetComponentUrlResponse response = getComponentUrl333();
        System.out.println(response.getMsg());
//        for (int i = 0; i < 100; i++) {
//            System.out.println(IdUtil.getSnowflakeNextIdStr());
//        }
        return;



//        Long jdOrderId  =************L;
//        String  orderId  = "************";
//        Long skuId = 1068549L;
//        Object sssss = queryOrderDetail("************", ************L);
////        Object sssss = getSkuDetailInfo(skuId);
//        System.out.println(sssss);

//        Object sssss = queryOrderDetail("************", ************L);
//        System.out.println(sssss);

//        initGoodsPool();
//        VopOrderQueryDeliveryInfoResponse vopOrderQueryDeliveryInfoResponse = queryDeliveryInfo("1762741697868992514", ************L);
//        DeliveryInfoQueryOpenResp deliveryInfoQueryOpenResp = vopOrderQueryDeliveryInfoResponse.getOpenRpcResult().getResult();
//        System.out.println(deliveryInfoQueryOpenResp.getTrackInfoList());

//        VopOrderQueryOrderDetailResponse vopOrderQueryOrderDetailResponse = queryOrderDetail("************", ************L);
//        System.out.println(vopOrderQueryOrderDetailResponse);

    }

    @Test
    public void getOrderReceiptUrl()  throws Exception {
        Long orderId = 295059422585L;
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopOrderGetOrderReceiptUrlRequest request = new VopOrderGetOrderReceiptUrlRequest();
        request.setOrderId(orderId);
        VopOrderGetOrderReceiptUrlResponse response = client.execute(request);
        System.out.println(JsonUtils.toJsonString(response));
    }

    /**
    * @Description: 京东商品池初始化测试
    * @Param: []
    * @return: void
    * @Author: lujun
    * @Date: 2023/10/31 16:11
    */
    public static void initGoodsPool() throws Exception {
        //查询商品池编号
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopGoodsGetSkuPoolInfoRequest request = new VopGoodsGetSkuPoolInfoRequest();
        VopGoodsGetSkuPoolInfoResponse response = client.execute(request);
        if(response.getOpenRpcResult().getResultCode().compareTo("0000") != 0) {
            System.out.println(response.getOpenRpcResult().getResult());
            return;
        }
        List<GetSkuPoolInfoItemGoodsResp> skuPoolInfoResp = response.getOpenRpcResult().getResult().getSkuPoolList();

        //查询池内商品编号
        VopGoodsGetSkuListPageRequest request2 = new VopGoodsGetSkuListPageRequest();
//        request2.setSourceType(3);
        request2.setBizPoolId(skuPoolInfoResp.get(0).getBizPoolId());
        request2.setPageIndex(1);
        VopGoodsGetSkuListPageResponse response2 = client.execute(request2);
        if(response2.getOpenRpcResult().getResultCode().compareTo("0000") != 0){
            return;
        }
        List<Long> SkuIdList = response2.getOpenRpcResult().getResult().getItems();

//        Long skuId = SkuIdList.get(0);
        Long skuId = 100085054295L;

        //获取商品详情
        VopGoodsGetSkuDetailInfoRequest request3 = new VopGoodsGetSkuDetailInfoRequest();
//        request3.setSourceType(3);
        request3.setSkuId(skuId);
        request3.setQueryExtSet("1,2,3,4,5");
        VopGoodsGetSkuDetailInfoResponse response3 = client.execute(request3);
        if(response3.getOpenRpcResult().getResultCode().compareTo("0000") != 0){
            return;
        }
        GetSkuPoolInfoGoodsResp skuDetail = response3.getOpenRpcResult().getResult();

        //查询商品图片
        VopGoodsGetSkuImageListRequest request4 = new VopGoodsGetSkuImageListRequest();
        request4.setSkuId(String.valueOf(skuId));
        VopGoodsGetSkuImageListResponse response4 = client.execute(request4);
        if(response4.getOpenRpcResult().getResultCode().compareTo("0000") != 0){
            return;
        }
        List<GetSkuImageGoodsResp> skuImageList = response4.getOpenRpcResult().getResult();

        System.out.println("1");
    }

    public static VopOrderConfirmReceiveByOrderResponse confirmReceiveByOrder(String thirdOrderId, Long jdOrderId) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopOrderConfirmReceiveByOrderRequest request = new VopOrderConfirmReceiveByOrderRequest();
//        request.setThirdOrderId(thirdOrderId);
        request.setJdOrderId(jdOrderId);
        return (VopOrderConfirmReceiveByOrderResponse) client.execute(request);
    }

    public static VopInvoiceQueryInvoiceThirdApplyNoResponse queryInvoiceThirdApplyNo(String jdOrderId) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);

        VopInvoiceQueryInvoiceThirdApplyNoRequest request = new VopInvoiceQueryInvoiceThirdApplyNoRequest();
        request.setJdOrderId(jdOrderId);
        return (VopInvoiceQueryInvoiceThirdApplyNoResponse) client.execute(request);
    }

    public static VopOrderCheckBalanceChangeInfoResponse checkBalanceChangeInfo() throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopOrderCheckBalanceChangeInfoRequest request = new VopOrderCheckBalanceChangeInfoRequest();
        request.setPageIndex(1);
        request.setPageSize(20);

        Date date1 = Date.from(LocalDate.of(2023,6,1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date date2 = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
        request.setStartDate(date1);
        request.setEndDate(date2);
        return (VopOrderCheckBalanceChangeInfoResponse) client.execute(request);
    }

    public static VopOrderQueryBillsByPageResponse queryBillsByPage(Integer pageNum, Integer pageSize) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopOrderQueryBillsByPageRequest request = new VopOrderQueryBillsByPageRequest();
        request.setPageSize(pageSize);
        request.setPageIndex(pageNum);
        return (VopOrderQueryBillsByPageResponse) client.execute(request);
    }

    public static VopInvoiceQueryElectronicInvoiceDetailResponse queryElectronicInvoiceDetail(Long jdOrderId, Integer ivcType) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);

        VopInvoiceQueryElectronicInvoiceDetailRequest request = new VopInvoiceQueryElectronicInvoiceDetailRequest();
        request.setJdOrderId(jdOrderId);
        request.setIvcType(ivcType);
        return (VopInvoiceQueryElectronicInvoiceDetailResponse) client.execute(request);
    }

    public static VopOrderConfirmOrderResponse confirmOrder(String thirdOrderId, Long jdOrderId) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopOrderConfirmOrderRequest request = new VopOrderConfirmOrderRequest();
//        request.setThirdOrderId(thirdOrderId);
        request.setJdOrderId(jdOrderId);
        return (VopOrderConfirmOrderResponse) client.execute(request);
    }

    public static VopGoodsGetSkuDetailInfoResponse getSkuDetailInfo(Long skuID) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopGoodsGetSkuDetailInfoRequest request = new VopGoodsGetSkuDetailInfoRequest();
        request.setSkuId(skuID);
        request.setQueryExtSet("2");
        return (VopGoodsGetSkuDetailInfoResponse) client.execute(request);
    }

    public  static VopOrderCheckAccountBalanceResponse checkAccountBalance() throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopOrderCheckAccountBalanceRequest request = new VopOrderCheckAccountBalanceRequest();
        return (VopOrderCheckAccountBalanceResponse) client.execute(request);
    }


    public static VopAfsGetComponentUrlResponse getComponentUrl(Integer componentType) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopAfsGetComponentUrlRequest request = new VopAfsGetComponentUrlRequest();

        Map<String, Object> reviewExtMap = new HashMap<>();
        //京东商品编号
        reviewExtMap.put("skuId", 100006006916L);
        reviewExtMap.put("unselectedColor", "#F5FFFA");
        reviewExtMap.put("selectedColor", "#FF4500");
        reviewExtMap.put("hiddenH5Nav", true);
        request.setExt(JSONObject.toJSONString(reviewExtMap));

        request.setComponentType(componentType);
        return (VopAfsGetComponentUrlResponse)client.execute(request);
    }

    public static VopAfsGetComponentUrlResponse getComponentUrl22222(Integer componentType) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopAfsGetComponentUrlRequest request = new VopAfsGetComponentUrlRequest();

        Map<String, Object> reviewExtMap = new HashMap<>();
        //京东商品编号
        reviewExtMap.put("skuId", 100018932614L);
        reviewExtMap.put("unselectedColor", "#F5FFFA");
        reviewExtMap.put("selectedColor", "#FF4500");
        reviewExtMap.put("hiddenH5Nav", true);
        reviewExtMap.put("dataRange", 3);
        reviewExtMap.put("firstUrlId", 3);
        reviewExtMap.put("expiredUrl", "http://localhost:8081/index.html");
        reviewExtMap.put("successUrl", "http://localhost:8081/index.html");
        request.setExt(JSONObject.toJSONString(reviewExtMap));

        request.setComponentType(componentType);
        return (VopAfsGetComponentUrlResponse)client.execute(request);
    }

    public static VopAfsGetComponentUrlResponse getComponentUrl333() throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopAfsGetComponentUrlRequest request = new VopAfsGetComponentUrlRequest();
//1、售后组件M端  2、售后组件PC端
        request.setComponentType(2);
        Map<String, String> mapReq = new HashMap<>();
//dataRange 数据范围 1、合同 2、pin 3、c标识 京东底层有相关配置
        mapReq.put("dataRange", "3");
//售后组件页面过期后跳转地址
        mapReq.put("expiredUrl", "https://127.0.0.1.jd.com/");
//提交售后成功时返回的页面地址 --不传默认返回至JD售后组件的申请列表页
        mapReq.put("successUrl", "https://127.0.0.1.jd.com/");
//首次（访问组件）跳转URL 1、售后申请列表页 2、售后记录列表页 3、申请售后详情页
        mapReq.put("firstUrlId", "3");
//申请售后详情页场景下，需要传入 订单号和商品skuId
mapReq.put("jdOrderId","************");
//mapReq.put("skuId","87654321");
//C客户标识，适用于BBC场景（订单范围局限在于下单的cPin标识上）BB场景也就是数据范围要求合同范围的不需入参该字段 【注】该场景下 下单需传入C客户标识
        mapReq.put("lotteryContacts", "c718");
        mapReq.put("skuId", String.valueOf(100042706396L));
        request.setExt(JSON.toJSONString(mapReq));
        return (VopAfsGetComponentUrlResponse) client.execute(request);
    }

    public static VopOrderQueryDeliveryInfoResponse queryDeliveryInfo(String thirdOrderId, Long jdOrderId) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopOrderQueryDeliveryInfoRequest request = new VopOrderQueryDeliveryInfoRequest();
//        request.setThirdOrderId(thirdOrderId);
        request.setJdOrderId(jdOrderId);
        return (VopOrderQueryDeliveryInfoResponse) client.execute(request);
    }

//    @GetMapping("testSubmitOrder")
//    public VopOrderSubmitOrderResponse testSubmitOrder(){
//
//        String no = "************";
//        String address = "湖北省武汉市东湖网谷园区9号楼珍岛集团";
//        String receiveName = "陈大圣";
//        String receivePhone = "13693211705";
//        List<TradeOrderItemDO> itemDOList = new ArrayList<>();
////        TradeOrderItemDO itemDO1 = new TradeOrderItemDO();
////        itemDO1.setSkuId(100006006916L);
////        itemDO1.setCount(1);
//        TradeOrderItemDO itemDO2 = new TradeOrderItemDO();
//        itemDO2.setSkuId(100029297437L);
//        itemDO2.setCount(1);
////        TradeOrderItemDO itemDO3 = new TradeOrderItemDO();
////        itemDO3.setSkuId(100029297349L);
////        itemDO3.setCount(1);
////        itemDOList.add(itemDO1);
//        itemDOList.add(itemDO2);
////        itemDOList.add(itemDO3);
//
//        SubmitOrderOpenReq orderOpenReq = new SubmitOrderOpenReq();
//
//        // 内部订单唯一code
//        orderOpenReq.setThirdOrderId(no);
//
//        // 是否预占库存，0是预占库存（需要调用确认订单接口），1是不预占库存，直接进入生产
//        orderOpenReq.setSubmitStateType(0);
//
//        // 1.支付信息
//        PaymentInfoOrderOpenReq paymentInfo = new PaymentInfoOrderOpenReq();
//        // 支付方式，枚举值 1：货到付款 4：预存款 5：公司转账 101：京东金采 102：商城金采(一般不适用，仅限确认开通商城账期的特殊情况使用，请与业务确认后使用) 20为混合支付
//        paymentInfo.setPaymentType(4);
//        orderOpenReq.setPaymentInfo(paymentInfo);
//
//        // 2.发票信息
//        InvoiceInfoOrderOpenReq invoiceInfoOrderOpenReq = new InvoiceInfoOrderOpenReq();
//        // 发票类型（2增值税专用发票；3 电子票） 当发票类型为2时，开票方式只支持2集中开票
//        invoiceInfoOrderOpenReq.setInvoiceType(3);
//        // 开票方式(2为集中开票，4 订单完成后开票)
//        invoiceInfoOrderOpenReq.setInvoicePutType(2);
//
//        invoiceInfoOrderOpenReq.setInvoiceName(receiveName);
//        invoiceInfoOrderOpenReq.setInvoicePhone(receivePhone);
//
//        orderOpenReq.setInvoiceInfo(invoiceInfoOrderOpenReq);
//
//        // 3.收货人信息
//        ConsigneeInfoOrderOpenReq consigneeInfoOrderOpenReq = new ConsigneeInfoOrderOpenReq();
//        //  验证订单详细地址是否正确
//        VopAddressConvertFourAreaByDetailStrResponse areaByDetailStrResponse = convertFourAreaByDetailStr(address);
//        if (null == areaByDetailStrResponse || areaByDetailStrResponse.getOpenRpcResult().getResult() == null) {
//            throw new ServiceException(ORDER_CREATE_ADDRESS_NOT_FOUND);
//        }
//        consigneeInfoOrderOpenReq.setConsigneeName(receiveName);
//        QueryAreaFourIdOpenResp areaFourIdOpenResp = areaByDetailStrResponse.getOpenRpcResult().getResult();
//        consigneeInfoOrderOpenReq.setConsigneeProvinceId(areaFourIdOpenResp.getProvinceId());
//        consigneeInfoOrderOpenReq.setConsigneeCityId(areaFourIdOpenResp.getCityId());
//        consigneeInfoOrderOpenReq.setConsigneeCountyId(areaFourIdOpenResp.getCountyId());
//        consigneeInfoOrderOpenReq.setConsigneeTownId(53772L);
//        consigneeInfoOrderOpenReq.setConsigneeAddress(address);
//        consigneeInfoOrderOpenReq.setConsigneeMobile(receivePhone);
//        orderOpenReq.setConsigneeInfo(consigneeInfoOrderOpenReq);
//
//        // 4.商品信息列表
//
//        if (CollectionUtils.isEmpty(itemDOList)) {
//            throw new ServiceException(ORDER_ITEM_NOT_FOUND);
//        }
//        List<SkuInfoOrderOpenReq> skuInfoList = new ArrayList<>();
//        for (TradeOrderItemDO item : itemDOList) {
//            SkuInfoOrderOpenReq skuInfoOrderOpenReq = new SkuInfoOrderOpenReq();
//            skuInfoOrderOpenReq.setSkuId(item.getSkuId());
//            skuInfoOrderOpenReq.setSkuNum(item.getCount());
//            skuInfoList.add(skuInfoOrderOpenReq);
//        }
//        orderOpenReq.setSkuInfoList(skuInfoList);
//
//        // 提交订单
//        return vopOrderService.submitOrder(orderOpenReq);
////        QueryOrderOpenResp orderOpenResp = submitOrderResponse.getVopOrderRpcResult().getResult();
//
//
//    }

//    public static VopAddressConvertFourAreaByDetailStrResponse convertFourAreaByDetailStr(String addressDetailStr) throws Exception {
//        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
//        VopAddressConvertFourAreaByDetailStrRequest request = new VopAddressConvertFourAreaByDetailStrRequest();
//        request.setAddressDetailStr(addressDetailStr);
//        return (VopAddressConvertFourAreaByDetailStrResponse) client.execute(request);
//    }

    public static VopAfsQueryLogicticsInfoResponse queryLogicticsInfo(String thirdApplyId, Long orderId) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopAfsQueryLogicticsInfoRequest request = new VopAfsQueryLogicticsInfoRequest();
        request.setOrderId(orderId);
        request.setThirdApplyId(thirdApplyId);
        request.setOriginalOrderId(orderId);
        request.setPageNo(1);
        request.setPageSize(100);
        return (VopAfsQueryLogicticsInfoResponse) client.execute(request);
    }

    public static VopAfsGetAfsOutlineResponse getAfsOutline(String thirdApplyId, Long orderId, Long wareId) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopAfsGetAfsOutlineRequest request = new VopAfsGetAfsOutlineRequest();
        request.setOrderId(orderId);
        request.setThirdApplyId(thirdApplyId);
        request.setWareId(wareId);
        request.setPageSize(100);
        request.setPageIndex(1);
        return (VopAfsGetAfsOutlineResponse) client.execute(request);
    }


    public static VopOrderQueryOrderDetailResponse queryOrderDetail(String thirdOrderId, Long jdOrderId) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopOrderQueryOrderDetailRequest request = new VopOrderQueryOrderDetailRequest();
//        request.setThirdOrderId(thirdOrderId);
        request.setJdOrderId(jdOrderId);
        return (VopOrderQueryOrderDetailResponse) client.execute(request);
    }

    public static VopOrderCancelOrderResponse cancelOrder(String thirdOrderId, Long jdOrderId, String cancelReason) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopOrderCancelOrderRequest request = new VopOrderCancelOrderRequest();
//        request.setThirdOrderId(thirdOrderId);
        request.setJdOrderId(jdOrderId);
        request.setCancelReason(cancelReason);
        return (VopOrderCancelOrderResponse) client.execute(request);
    }

    public static VopOrderSubmitOrderResponse testSubmitOrderWO() throws Exception {

        String no = "************";
        String address = "湖北省武汉市东湖网谷园区9号楼珍岛集团";
        String receiveName = "陈大圣";
        String receivePhone = "13693211705";
        List<TradeOrderItemDO> itemDOList = new ArrayList<>();
//        TradeOrderItemDO itemDO1 = new TradeOrderItemDO();
//        itemDO1.setSkuId(100006006916L);
//        itemDO1.setCount(1);
        TradeOrderItemDO itemDO2 = new TradeOrderItemDO();
        itemDO2.setSkuId(1068549L);
        itemDO2.setCount(1);
//        TradeOrderItemDO itemDO3 = new TradeOrderItemDO();
//        itemDO3.setSkuId(100042706396L);
//        itemDO3.setCount(1);
//        itemDOList.add(itemDO1);
        itemDOList.add(itemDO2);
//        itemDOList.add(itemDO3);

        SubmitOrderOpenReq orderOpenReq = new SubmitOrderOpenReq();

        // 内部订单唯一code
        orderOpenReq.setThirdOrderId(no);

        // 是否预占库存，0是预占库存（需要调用确认订单接口），1是不预占库存，直接进入生产
        orderOpenReq.setSubmitStateType(0);

        // 1.支付信息
        PaymentInfoOrderOpenReq paymentInfo = new PaymentInfoOrderOpenReq();
        // 支付方式，枚举值 1：货到付款 4：预存款 5：公司转账 101：京东金采 102：商城金采(一般不适用，仅限确认开通商城账期的特殊情况使用，请与业务确认后使用) 20为混合支付
        paymentInfo.setPaymentType(4);
        orderOpenReq.setPaymentInfo(paymentInfo);

        // 2.发票信息
        InvoiceInfoOrderOpenReq invoiceInfoOrderOpenReq = new InvoiceInfoOrderOpenReq();
        // 发票类型（2增值税专用发票；3 电子票） 当发票类型为2时，开票方式只支持2集中开票
        invoiceInfoOrderOpenReq.setInvoiceType(3);
        // 开票方式(2为集中开票，4 订单完成后开票)
        invoiceInfoOrderOpenReq.setInvoicePutType(2);

        invoiceInfoOrderOpenReq.setInvoiceName(receiveName);
        invoiceInfoOrderOpenReq.setInvoicePhone(receivePhone);

        orderOpenReq.setInvoiceInfo(invoiceInfoOrderOpenReq);

        // 3.收货人信息
        ConsigneeInfoOrderOpenReq consigneeInfoOrderOpenReq = new ConsigneeInfoOrderOpenReq();
        //  验证订单详细地址是否正确
        VopAddressConvertFourAreaByDetailStrResponse areaByDetailStrResponse = convertFourAreaByDetailStr(address);

        consigneeInfoOrderOpenReq.setConsigneeName(receiveName);
        QueryAreaFourIdOpenResp areaFourIdOpenResp = areaByDetailStrResponse.getOpenRpcResult().getResult();
        consigneeInfoOrderOpenReq.setConsigneeProvinceId(areaFourIdOpenResp.getProvinceId());
        consigneeInfoOrderOpenReq.setConsigneeCityId(areaFourIdOpenResp.getCityId());
        consigneeInfoOrderOpenReq.setConsigneeCountyId(areaFourIdOpenResp.getCountyId());
        consigneeInfoOrderOpenReq.setConsigneeTownId(53772L);
        consigneeInfoOrderOpenReq.setConsigneeAddress(address);
        consigneeInfoOrderOpenReq.setConsigneeMobile(receivePhone);
        orderOpenReq.setConsigneeInfo(consigneeInfoOrderOpenReq);

        // 4.商品信息列表

        if (CollectionUtils.isEmpty(itemDOList)) {
          return null;
        }
        List<SkuInfoOrderOpenReq> skuInfoList = new ArrayList<>();
        for (TradeOrderItemDO item : itemDOList) {
            SkuInfoOrderOpenReq skuInfoOrderOpenReq = new SkuInfoOrderOpenReq();
            skuInfoOrderOpenReq.setSkuId(item.getSkuId());
            skuInfoOrderOpenReq.setSkuNum(item.getCount());
            skuInfoList.add(skuInfoOrderOpenReq);
        }
        orderOpenReq.setSkuInfoList(skuInfoList);


        orderOpenReq.setCpin("c718");

        // 提交订单
        return submitOrder(orderOpenReq);
//        QueryOrderOpenResp orderOpenResp = submitOrderResponse.getVopOrderRpcResult().getResult();


    }

    public static VopOrderSubmitOrderResponse submitOrder(SubmitOrderOpenReq submitOrderOpenReq) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopOrderSubmitOrderRequest request = new VopOrderSubmitOrderRequest();
        request.setSubmitOrderOpenReq(submitOrderOpenReq);
        return client.execute(request);
    }

    public static VopAddressConvertFourAreaByDetailStrResponse convertFourAreaByDetailStr(String addressDetailStr) throws Exception {
        JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
        VopAddressConvertFourAreaByDetailStrRequest request = new VopAddressConvertFourAreaByDetailStrRequest();
        request.setAddressDetailStr(addressDetailStr);
        return client.execute(request);
    }


    @Data
    static class TradeOrderItemDO{

        Long skuId;
        Integer count;

    }

}
