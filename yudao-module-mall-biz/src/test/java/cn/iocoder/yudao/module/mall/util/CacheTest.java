package cn.iocoder.yudao.module.mall.util;

import cn.iocoder.yudao.framework.redis.util.RedisUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AreaDO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 工具类测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class CacheTest {

    @Resource
    private RedisUtils redisUtils;

    @BeforeAll
    public static void init() {
        TenantContextHolder.setTenantId(150L);
    }

    @Test
    public void testCache() {
        String key = "test_key_11";
        String key2 = "test_key_12";
        List<AreaDO> data = new ArrayList<>();
        data.add(new AreaDO().setId(1L).setName("北京"));

        redisUtils.set(key, data, 100, TimeUnit.SECONDS);
        redisUtils.set(key2, data, 100, TimeUnit.SECONDS);
        List<AreaDO> cacheVal = redisUtils.get(key);
        log.info("cacheVal=====" + cacheVal);
        redisUtils.deleteKeysByPattern("test_key_*");

        String key3 = "test_key_13";
        Integer count = redisUtils.get(key3);
        log.info("count1===={}", count);
        redisUtils.set(key3, 1, 5,TimeUnit.SECONDS);
        count = redisUtils.get(key3);
        log.info("count2===={}", count);
    }

}
