package cn.iocoder.yudao.module.mall.product;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.product.service.spec.ProductSkuSpecService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/10
 */

@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class ProductSkuSpecServiceTest {

    @Resource
    private ProductSkuSpecService skuSpecService;

    @BeforeEach
    public void init() {
        TenantContextHolder.setTenantId(150L);
    }

    @Test
    public void deleteProductSkuSpec(){
        skuSpecService.deleteProductSkuSpec(-1L);
        System.out.println("success");
    }

}
