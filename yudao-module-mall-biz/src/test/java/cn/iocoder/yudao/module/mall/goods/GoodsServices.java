package cn.iocoder.yudao.module.mall.goods;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.external.open.context.OpenContextHolder;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuOpenVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsSearchReq;
import com.jd.open.api.sdk.domain.vopsp.SearchGoodsProvider.response.searchSku.OpenPagingResult;
import com.jd.open.api.sdk.domain.vopsp.SearchGoodsProvider.response.searchSku.SearchSkuGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SearchGoodsProvider.response.searchSku.SkuHitSearchGoodsResp;
import com.jd.open.api.sdk.response.vopsp.VopGoodsSearchSkuResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
public class GoodsServices {
    @Resource
    private VopGoodsBridgeService vopGoodsBridgeService;

    @Resource
    private SupplierService supplierService;

    @Resource
    private VopGoodsService vopGoodsService;

    @Resource
    private ProductSkuService productSkuService;

    @Test
    public void goodsUpdate() {
        TenantContextHolder.setTenantId(150L);
        OpenContextHolder.setSupplierId(supplierService.getSupplierJD().getId());
        SupplierDO supplier = supplierService.getSupplier(supplierService.getSupplierJD().getId());
        OpenContextHolder.setSupplierName(supplier.getName());
        List<Long> result = vopGoodsBridgeService.goodsUpdate(false, null, 199613L);
        System.out.println(result);
    }

    @Test
    public void goodsPoolSyncCount() {
        TenantContextHolder.setTenantId(150L);
        OpenContextHolder.setSupplierId(supplierService.getSupplierJD().getId());
        SupplierDO supplier = supplierService.getSupplier(supplierService.getSupplierJD().getId());
        OpenContextHolder.setSupplierName(supplier.getName());
        Long count = vopGoodsBridgeService.goodsPoolSyncCount("");
        System.out.println(count);
    }

    @Test
    public void goodsPoolSync() {
        TenantContextHolder.setTenantId(150L);
        OpenContextHolder.setSupplierId(supplierService.getSupplierJD().getId());
        SupplierDO supplier = supplierService.getSupplier(supplierService.getSupplierJD().getId());
        OpenContextHolder.setSupplierName(supplier.getName());
        vopGoodsBridgeService.customGoodsPoolSync(TenantContextHolder.getTenantId());
    }

    @Test
    public void goodSearch() {
        TenantContextHolder.setTenantId(150L);
        OpenContextHolder.setSupplierId(supplierService.getSupplierJD().getId());

        List<SkuHitSearchGoodsResp> skuHitSearchGoodsRespList = new ArrayList<>();

        int index = 1;
        while (true) {
            VopGoodsSearchReq request = new VopGoodsSearchReq();
            request.setBrandId("");
            request.setBrandCollect(null);
            request.setExtAttrCollect(null);
            request.setKeyword("");
            request.setPageSize(100);
            request.setUseCacheStore(null);
            request.setMaxPrice(null);
            request.setPageIndex(index);
            request.setCategoryId3(null);
            request.setCategoryId1(15901L);
            request.setCategoryId2(15903L);
            request.setNeedMergeSku(null);
            request.setSortType(null);
            request.setMinPrice(null);
            request.setAreaIds("");
            request.setSortExpandAttrs("");
            VopGoodsSearchSkuResponse response = vopGoodsService.goodsSearchPageList(request);
            SearchSkuGoodsResp goodsResp = response.getOpenRpcResult().getResult();
            OpenPagingResult openPagingResult = goodsResp.getSkuHitResultPaging();
            skuHitSearchGoodsRespList.addAll(openPagingResult.getItems());
            index++;
            if(skuHitSearchGoodsRespList.size() >= openPagingResult.getPageItemTotal()){
                break;
            }
        }

        List<ProductSpuOpenVO> productSpuOpenVOList = new ArrayList<>();
        List<SkuHitSearchGoodsResp> invalidSkuList = new ArrayList<>();
        List<ProductSkuDO> productSkuDOList = new ArrayList<>();
        for (int i = 0; i < skuHitSearchGoodsRespList.size(); i++) {
            SkuHitSearchGoodsResp skuHitSearchGoodsResp = skuHitSearchGoodsRespList.get(i);
//            ProductSpuOpenVO productSpuOpenVO = vopGoodsBridgeService.goodsDetailSync(skuHitSearchGoodsResp.getSkuId(), supplierService.getSupplierJD().getId());
//            if(productSpuOpenVO != null) {
//                productSpuOpenVOList.add(productSpuOpenVO);
//            }
//            else {
//                invalidSkuList.add(skuHitSearchGoodsResp);
//            }

            ProductSkuDO productSkuDO = productSkuService.getSimpleSkuByInnerIdAndSupplierId(String.valueOf(skuHitSearchGoodsResp.getSkuId()), supplierService.getSupplierJD().getId());
            if(productSkuDO != null){
                productSkuDOList.add(productSkuDO);
            }
            else {
                invalidSkuList.add(skuHitSearchGoodsResp);
//                boolean result = vopGoodsBridgeService.goodsUpdate(skuHitSearchGoodsResp.getSkuId());
//                if(!result){
//                    System.out.println("error");
//                }
            }
        }

        System.out.println('1');
    }
}
