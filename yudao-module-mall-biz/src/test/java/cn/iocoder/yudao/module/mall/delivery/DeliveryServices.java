package cn.iocoder.yudao.module.mall.delivery;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.delivery.AppOpenDeliveryBindReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.delivery.AppOpenDeliveryReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo.ConfigDeliveryCompanyPageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo.DeliveryBindReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo.DeliveryCompanyPageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo.DeliveryRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.OrderDetailRespVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.ConfigDeliveryCompanyDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryCompanyDO;
import cn.iocoder.yudao.module.mall.trade.service.delivery.ConfigDeliveryCompanyService;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryCompanyService;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
public class DeliveryServices {
    @Resource
    private DeliveryService deliveryService;

    @Resource
    private DeliveryCompanyService deliveryCompanyService;

    @Resource
    private ConfigDeliveryCompanyService configDeliveryCompanyService;

    @Resource
    private TradeOrderService tradeOrderService;

    @BeforeAll
    public static void init() {
        TenantContextHolder.setTenantId(150L);
    }

    @Test
    public void companyCodeSync() {
        Boolean result = configDeliveryCompanyService.syncCompany();
        System.out.println(result);
    }

    @Test
    public void configCompanyCodeList() {
        ConfigDeliveryCompanyPageReqVO pageReqVO = new ConfigDeliveryCompanyPageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        PageResult<ConfigDeliveryCompanyDO> configDeliveryCompanyDOPageResult =  configDeliveryCompanyService.getConfigDeliveryCompanyPage(pageReqVO);
        System.out.println(configDeliveryCompanyDOPageResult.getTotal());
    }

    @Test
    public void getCompanyCodeList(){
        DeliveryCompanyPageReqVO pageReqVO = new DeliveryCompanyPageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        PageResult<DeliveryCompanyDO> deliveryCompanyDOPageResult =  deliveryCompanyService.getDeliveryCompanyPage(pageReqVO);
        System.out.println(deliveryCompanyDOPageResult.getTotal());
    }

    @Test
    public void bindOrder(){
        DeliveryBindReqVO deliveryBindReqVO = new DeliveryBindReqVO()
                .setOrderNo("112312301602560117")
                .setNum("JT3061108279737")
                .setCompanyCode("jtexpress")
                .setSkuIds("83486");
        Boolean ret = deliveryService.productSend(deliveryBindReqVO, null, null);
        System.out.println(ret);
    }

    @Test
    public void getDetailByOrderNo(){
        List<DeliveryRespVO> deliveryRespVOS = deliveryService.getByOrderNo("112312301602560117");
        System.out.println(deliveryRespVOS);
    }

    @Test
    public void getDetailByDeliveryId(){
        DeliveryRespVO deliveryRespVO = deliveryService.getByDeliveryNum("JT3061108279737");
        System.out.println(deliveryRespVO);
    }

    @Test
    public void getOrderDetail(){
        OrderDetailRespVO orderDetailRespVO = tradeOrderService.getOrderDetail4Admin(123L);
        System.out.println(orderDetailRespVO);
    }

    @Test
    public void deliveryCallback(){
        String param = "{\"status\":\"polling\",\"billstatus\":\"got\",\"message\":\"寄件\",\"lastResult\":{\"message\":\"ok\",\"nu\":\"JT3061108279737\",\"ischeck\":\"0\",\"com\":\"jtexpress\",\"status\":\"200\",\"data\":[{\"time\":\"2021-12-15 20:15:14\",\"context\":\"【苏州转运中心】 已发出 下一站 【无锡转运中心公司】\",\"ftime\":\"2021-12-15 20:15:14\",\"areaCode\":\"CN320500000000\",\"areaName\":\"江苏,苏州市\",\"status\":\"干线\",\"location\":\"\",\"areaCenter\":\"120.585315,31.298886\",\"areaPinYin\":\"su zhou shi\",\"statusCode\":\"1002\"},{\"time\":\"2021-12-15 20:11:25\",\"context\":\"【苏州转运中心公司】 已收入\",\"ftime\":\"2021-12-15 20:11:25\",\"areaCode\":\"CN320500000000\",\"areaName\":\"江苏,苏州市\",\"status\":\"干线\",\"location\":\"\",\"areaCenter\":\"120.585315,31.298886\",\"areaPinYin\":\"su zhou shi\",\"statusCode\":\"1002\"},{\"time\":\"2021-12-15 19:18:27\",\"context\":\"【江苏省无锡市锡新开发区公司】 已收入\",\"ftime\":\"2021-12-15 19:18:27\",\"areaCode\":\"CN320200000000\",\"areaName\":\"江苏,无锡市\",\"status\":\"在途\",\"location\":\"\",\"areaCenter\":\"120.31191,31.491169\",\"areaPinYin\":\"wu xi shi\",\"statusCode\":\"0\"},{\"time\":\"2021-12-15 17:10:09\",\"context\":\"【江苏省苏州市北桥公司】 已揽收\",\"ftime\":\"2021-12-15 17:10:09\",\"areaCode\":\"CN320507004000\",\"areaName\":\"江苏,苏州市,相城区,北桥\",\"status\":\"揽收\",\"location\":\"\",\"areaCenter\":\"120.606531,31.505825\",\"areaPinYin\":\"bei qiao jie dao\",\"statusCode\":\"1\"}],\"state\":\"0\",\"condition\":\"F00\",\"routeInfo\":{\"from\":{\"number\":\"CN320507004000\",\"name\":\"江苏,苏州市,相城区,北桥\"},\"cur\":{\"number\":\"CN320200000000\",\"name\":\"江苏,无锡市\"},\"to\":null},\"isLoop\":false}}";
        Boolean ret = deliveryService.deliveryCallback(param);
        System.out.println(ret);
    }

    @Test
    public void thirdBindOrder(){
        String str = "{\"orderId\":1739620629905874945,\"companyName\":\"中通快递\",\"companyCode\":\"zhongtong\",\"num\":\"78764832291935\",\"skuIds\":\"80551,80552\"}";
        AppOpenDeliveryBindReqVO appOpenDeliveryBindReqVO = JSON.parseObject(str, AppOpenDeliveryBindReqVO.class);
        Boolean ret = deliveryService.thirdBindOrder(appOpenDeliveryBindReqVO);
        System.out.println(ret);
    }

    @Test
    public void thridUpdate(){
        String param = "{\"nu\":\"YT7542296773296\",\"ischeck\":\"0\",\"companyCode\":\"zhongtong\",\"companyName\":\"中通快递\",\"data\":[{\"time\":\"2024-01-23 11:55\",\"context\":\"武汉市,快递超市的武汉江夏佛祖岭A区店, 【武汉市】 快件已在 快递超市 的【武汉江夏佛祖岭A区店】暂放，【取件地址：佛祖岭A区北大门口（老警务室）】，请及时取件。如有疑问请联系业务员：18062089554，代理点电话：18062089554，投诉电话：027-59767205\",\"status\":\"派件中\"},{\"time\":\"2024-01-23 05:53\",\"context\":\"武汉市,武汉藏龙岛, 【武汉市】武汉藏龙岛 的业务员【任语嫣,18062089554】正在为您派件（95720为中通快递员外呼专属号码，请放心接听，如有问题可联系网点:027-59767205,投诉电话:027-59767205）\",\"status\":\"在途\"},{\"time\":\"2024-01-23 05:53\",\"context\":\"武汉市,武汉藏龙岛, 【武汉市】 快件已到达 武汉藏龙岛\",\"status\":\"在途\"},{\"time\":\"2024-01-23 05:53\",\"context\":\"武汉市,武汉藏龙岛大学城, 【武汉市】 快件已发往 武汉藏龙岛\",\"status\":\"在途\"},{\"time\":\"2024-01-23 05:52\",\"context\":\"武汉市,武汉藏龙岛大学城, 【武汉市】 快件已到达 武汉藏龙岛大学城\",\"status\":\"在途\"},{\"time\":\"2024-01-22 21:00\",\"context\":\"鄂州市,鄂州转运中心, 【鄂州市】 快件已发往 武汉藏龙岛\",\"status\":\"在途\"},{\"time\":\"2024-01-22 19:29\",\"context\":\"鄂州市,鄂州转运中心, 【鄂州市】 快件已到达 鄂州转运中心\",\"status\":\"在途\"},{\"time\":\"2024-01-22 02:13\",\"context\":\"揭阳市,潮汕转运中心, 【揭阳市】 快件已发往 鄂州转运中心\",\"status\":\"在途\"},{\"time\":\"2024-01-22 02:03\",\"context\":\"揭阳市,潮汕转运中心, 【揭阳市】 快件已到达 潮汕转运中心\",\"status\":\"在途\"},{\"time\":\"2024-01-22 00:08\",\"context\":\"揭阳市,揭阳普宁一部, 【揭阳市】 快件已发往 鄂州转运中心\",\"status\":\"在途\"},{\"time\":\"2024-01-22 00:00\",\"context\":\"揭阳市,揭阳普宁一部, 【揭阳市】 揭阳普宁一部（0663-8557001）里湖A02（8965（19835806863） 已揽收\",\"status\":\"揽收\"}],\"state\":\"0\"}";
        AppOpenDeliveryReqVO appOpenDeliveryReqVO = JSON.parseObject(param, AppOpenDeliveryReqVO.class);
        Boolean ret = deliveryService.thirdUpdate(appOpenDeliveryReqVO);
        System.out.println(ret);
    }
}
