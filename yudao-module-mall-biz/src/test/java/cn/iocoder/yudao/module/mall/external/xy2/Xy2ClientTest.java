package cn.iocoder.yudao.module.mall.external.xy2;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.external.xy2.dto.Xy2PullBpmInfoRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class Xy2ClientTest {

    @Resource
    private Xy2Client xy2Client;

    @BeforeAll
    public static void init() {
        TenantContextHolder.setTenantId(150L);
    }

    @Test
    public void getGetYgInfo() {
        Xy2PullBpmInfoRespDTO dto = xy2Client.pullBpmStatus("1895420882029694976");
        log.info("pullBpmStatus: {}", dto);
    }
}
