package cn.iocoder.yudao.module.mall.stats;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.OrderStatsReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderSaleVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsSkuSaleVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsTopSkuSaleVO;
import cn.iocoder.yudao.module.mall.basis.dal.mysql.stats.StatsOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

/**
 * 销量统计测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class StatsOrderServiceTest {

    @Resource
    private StatsOrderMapper statsOrderMapper;

    @BeforeAll
    public static void init() {
        TenantContextHolder.setTenantId(150L);
    }

    @Test
    public void testQueryOrderSaleOnDate() {
        OrderStatsReqVO reqVO = new OrderStatsReqVO();
        reqVO.setTenantId(TenantContextHolder.getTenantId());
        reqVO.setStartDate("2024-12-01");
        reqVO.setEndDate("2024-12-31");
        List<StatsOrderSaleVO> result = statsOrderMapper.queryOrderSaleOnDate(reqVO);
        log.info("result1--------{}", result);


        List<StatsSkuSaleVO> result2 = statsOrderMapper.querySkuSaleOnDate(reqVO);
        log.info("result2--------{}", result2);

        List<StatsOrderSaleVO> result3 = statsOrderMapper.queryOrderSaleOnMonth(reqVO);
        log.info("result3--------{}", result3);

        List<StatsTopSkuSaleVO> result4 = statsOrderMapper.queryTopSaleSku(reqVO);
        log.info("result4--------{}", result4);

        List<StatsTopSkuSaleVO> result5 = statsOrderMapper.queryTopSaleCategory2(reqVO);
        log.info("result5--------{}", result5);

    }




}
