package cn.iocoder.yudao.module.mall.product;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.external.open.context.OpenContextHolder;
import cn.iocoder.yudao.module.mall.member.convert.address.AddressConvert;
import cn.iocoder.yudao.module.mall.product.api.sku.ProductSkuApi;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.ProductSkuRespDTO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuDetailRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuPageRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuPageReqVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppSkuDetailInfo;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.GoodsSearchPageResultResp;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.GoodsSearchReq;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuBaseRespOpenVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuOpenVO;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.params.GoodsStockInfoReq;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.AreaStockInfoResp;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsSearchPageResultResp;
import cn.iocoder.yudao.module.mall.product.dal.mysql.sku.ProductSkuMapper;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.skusearch.ProductSkuElasticsearchService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import cn.iocoder.yudao.module.mall.trade.controller.app.cart.vo.CheckOrderDetailRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.cart.vo.CheckOrderReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppOpenAddressRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppTradeOrderDetailOpenRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseCartDetailRespVO;
import cn.iocoder.yudao.module.mall.trade.convert.order.TradeOrderConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.service.cart.TradeCartService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.trade.service.purchase.PurchaseService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopOrderService;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsGetSkuDetailReq;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsSearchReq;
import com.alibaba.fastjson.JSON;
import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.request.vopdd.VopOrderQueryOrderDetailRequest;
import com.jd.open.api.sdk.response.vopdd.VopOrderQueryOrderDetailResponse;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetSkuDetailInfoResponse;
import com.jd.open.api.sdk.response.vopsp.VopGoodsSearchSkuResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/10
 */

@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class ProductSkuServiceTests {

    @Autowired
    private ProductSkuElasticsearchService productSkuElasticsearchService;

    @Autowired
    private ProductSkuService productSkuService;

    @Autowired
    private VopGoodsService vopGoodsService;

    @Autowired
    private VopGoodsBridgeService vopGoodsBridgeService;

    @Autowired
    private TradeCartService tradeCartService;

    @Resource
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Resource
    private PurchaseService purchaseService;

    @Resource
    private ProductSkuMapper productSkuMapper;

    @Resource
    private VopOrderService vopOrderService;

    @Resource
    private ProductSkuApi productSkuApi;

    @Resource
    private TradeOrderService tradeOrderService;


    @BeforeEach
    public void init() {
        TenantContextHolder.setTenantId(150L);
    }


    @Test
    public void saveProductSku(){
        productSkuService.syncProductSkuIndex2ES(Arrays.asList(10305L));
        System.out.println("success");
    }

    @Test
    public void query(){
        GoodsSearchReq req = new GoodsSearchReq();
        req.setPageIndex(1);
        req.setPageSize(20);
        //17,1381,50712,62960
        req.setKeyword("耳机");
        //req.setAreaIds("17,1381,50712,62960");
        req.setAreaIds("17,1381,50712,62960");
        req.setCategoryId2s(Arrays.asList(6130100L,6130200L));
        req.setCategoryId2s(Arrays.asList(6130100L,6130200L));
//        req.setTagIds(Arrays.asList(0L));
        //req.setSupplierId(3L);
        //req.setBrandId("79");
        //req.setCategoryId3(2588L);
        req.setSortType(5);
        //req.setCategoryId2(653L);
        //req.setMinPrice(new BigDecimal("30"));
        //req.setMaxPrice(new BigDecimal("40"));
        GoodsSearchPageResultResp resp = productSkuElasticsearchService.goodsSearchPageList(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void syncAllProductSku(){
        productSkuService.syncAllProductSkuIndex2ES();
        System.out.println("success");
    }

    @Test
    public void testDeleteAllOfTenant(){
        productSkuElasticsearchService.deleteAllOfTenant(150L);
        System.out.println("success");
    }

    @Test
    public void queryVopGoods() {
        VopGoodsSearchReq req = new VopGoodsSearchReq();
        req.setPageIndex(1);
        req.setPageSize(10);
        req.setAreaIds("17,1381,50718,");
        req.setSortType(1);
        //req.setKeyword("笔记本");
        //req.setSortType(3);
        //req.setCategoryId2(653L);
        //req.setMinPrice(new BigDecimal("10"));
        //req.setMaxPrice(new BigDecimal("20"));
        VopGoodsSearchSkuResponse vopGoodsSearchSkuResponse = vopGoodsService.goodsSearchPageList(req);
        System.out.println(vopGoodsSearchSkuResponse);
    }

    @Test
    public void goodsSearchPageList() {
        VopGoodsSearchReq req = new VopGoodsSearchReq();
        req.setPageIndex(1);
        req.setPageSize(20);
        req.setAreaIds("17,1381,50718,");
        req.setSortType(1);
        VopGoodsSearchPageResultResp resp = vopGoodsBridgeService.goodsSearchPageList(req);
        System.out.println(resp);
    }

    @Test
    public void getSkuDetailInfo(){
        VopGoodsGetSkuDetailReq skuDetailReq = new VopGoodsGetSkuDetailReq();
        skuDetailReq.setSkuId(77716L);
        AppSkuDetailInfo appSkuDetailInfo = productSkuService.getSkuDetailInfo(skuDetailReq);
        System.out.println(appSkuDetailInfo);
    }

    @Test
    public void getVopSkuDetailInfo(){
        VopGoodsGetSkuDetailReq skuDetailReq = new VopGoodsGetSkuDetailReq();
        skuDetailReq.setSkuId(100005880599L);
        VopGoodsGetSkuDetailInfoResponse skuDetailInfo = vopGoodsService.getSkuDetailInfo(skuDetailReq);
        System.out.println(skuDetailInfo);
    }

    @Test
    public void getOrder() {
        // 查询订单
        TradeOrderDO order = tradeOrderService.getOrder(1739620629905874945L);
        Assert.notNull(order,"订单不能为空");
        AppOpenAddressRespVO address = AddressConvert.INSTANCE.convertAddress02(order);
        // 查询订单项
        List<TradeOrderItemDO> tradeOrderItemDOS = tradeOrderService.getOrderItemListByOrderId(order.getId());
        AppTradeOrderDetailOpenRespVO appTradeOrderDetailRespVO = TradeOrderConvert.INSTANCE.convert4(order, tradeOrderItemDOS);
        appTradeOrderDetailRespVO.setAddress(address);

        System.out.println(JsonUtils.toJsonString(appTradeOrderDetailRespVO));
    }

    @Test
    public void queryGoodsStockInfo() {
        //{"provinceId":17,"cityId":1381,"countyId":50712,"townId":62960}
        GoodsStockInfoReq stockInfoReq = new GoodsStockInfoReq();
        stockInfoReq.setSkuId(12590761L);
        stockInfoReq.setSupplierId(3L);
        //{"provinceId":"17","cityId":"1381","countyId":"50718","townId":"62957"}
        stockInfoReq.setProvinceId(17L);
        stockInfoReq.setCityId(1381L);
        stockInfoReq.setCountyId(50712L);
        stockInfoReq.setTownId(62960L);
        AreaStockInfoResp areaStockInfoResp = vopGoodsBridgeService.queryGoodsStockInfo(stockInfoReq);
        System.out.println(areaStockInfoResp);
    }

    @Test
    public void getPurchaseCartDetail() {
        PurchaseCartDetailRespVO purchaseCartDetail = purchaseService.getPurchaseCartDetail(258L);
        System.out.println(JSON.toJSONString(purchaseCartDetail));
    }

    @Test
    public void queryDelete() {
        AppProductSkuPageReqVO pageReqVO = new AppProductSkuPageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        productSkuMapper.selectDeleteSkuIdPage(pageReqVO);
        System.out.println("**********");
    }

    @Test
    @SneakyThrows
    public void queryOrderDetail(){
        List<ProductSkuRespDTO> products = productSkuApi.getSkus(Arrays.asList(77913L), true);
        System.out.println(products);
    }

    @Test
    @SneakyThrows
    public void querySpuCategory(){
        String category = productSkuService.getSpuCategory("13537424", 3L);
        System.out.println(category);
    }

    @Test
    public void addProductSku() {
        String content ="[\n" +
                "  {\n" +
                "    \"specType\": 0,\n" +
                "    \"brandName\": \"摩天手（Mofii）\",\n" +
                "    \"spuName\": \"滴露/Dettol 健康抑菌洗手液植物呵护500gtest\",\n" +
                "    \"sellPoint\": \"500g\",\n" +
                "    \"skus\": [\n" +
                "      {\n" +
                "        \"skuName\": \"滴露/Dettol 健康抑菌洗手液植物呵护500gtest\",\n" +
                "        \"picUrl\": \"https://oss.3c2p.com/goodsImages/big_30731656563329717.jpg\",\n" +
                "        \"marketPrice\": 128.0,\n" +
                "        \"salePrice\": 99.0,\n" +
                "        \"skuInnerId\": \"TPP02977363\",\n" +
                "        \"status\": 1,\n" +
                "        \"skuSpecValueList\": [\n" +
                "          {\n" +
                "            \"specName\": \"商品名称\",\n" +
                "            \"specValue\": \"滴露洗手液\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"specName\": \"商品毛重\",\n" +
                "            \"specValue\": \"0.55kg\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"specName\": \"功效\",\n" +
                "            \"specValue\": \"清洁，抑菌\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"specName\": \"适用人群\",\n" +
                "            \"specValue\": \"通用\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"specName\": \"净含量\",\n" +
                "            \"specValue\": \"500mL\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"specName\": \"保质期\",\n" +
                "            \"specValue\": \"3年\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"specName\": \"是否消字号商品\",\n" +
                "            \"specValue\": \"非消字号\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"specName\": \"适合肤质\",\n" +
                "            \"specValue\": \"敏感性\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"specName\": \"商品产地\",\n" +
                "            \"specValue\": \"湖北省荆州市\"\n" +
                "          },\n" +
                "          {\n" +
                "            \"specName\": \"类别\",\n" +
                "            \"specValue\": \"水洗（液体）\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"stockCount\": 99\n" +
                "      }\n" +
                "    ],\n" +
                "    \"sliderPicUrls\": [\n" +
                "      \"https://oss.3c2p.com/goodsImages/big_30731656563329717.jpg\",\n" +
                "      \"https://oss.3c2p.com/goodsImages/big_76571638384109075.jpg\",\n" +
                "      \"https://oss.3c2p.com/goodsImages/big_26471638384110328.jpg\",\n" +
                "      \"https://oss.3c2p.com/goodsImages/big_20971638384111214.jpg\",\n" +
                "      \"https://oss.3c2p.com/goodsImages/big_94291638384113979.jpg\",\n" +
                "      \"https://oss.3c2p.com/goodsImages/big_73251638384115065.jpg\",\n" +
                "      \"https://oss.3c2p.com/goodsImages/big_50851638384116457.jpg\"\n" +
                "    ],\n" +
                "    \"category1Name\": \"家用电器\",\n" +
                "    \"spuInnerId\": \"TPP02977363\",\n" +
                "    \"category2Id\": \"14857\",\n" +
                "    \"description\": \"<div><img src=\\\"http://img30.360buyimg.com/sku/jfs/t1/100609/16/13425/90260/5e561711Eafff4b6d/8837faa1d1ae39cd.jpg\\\" /><br /><img src=\\\"http://img30.360buyimg.com/sku/jfs/t1/74013/26/7255/93773/5d560ac8Ec0e014d1/fb01e15beaeaaa8d.jpg\\\" /><br /><img src=\\\"http://img30.360buyimg.com/sku/jfs/t1/71080/32/7256/141073/5d560ad4E6ddd757a/080270e7fb73d237.jpg\\\" /><br /><img src=\\\"http://img30.360buyimg.com/sku/jfs/t1/129938/29/8414/170921/5f476985E5572a6ca/695ccefcf264262a.jpg\\\" /><br /><img src=\\\"http://img30.360buyimg.com/sku/jfs/t24514/329/525325661/158771/5d5c57f/5b347412N8bd00160.jpg\\\" /><br /><img src=\\\"http://img30.360buyimg.com/sku/jfs/t22387/264/1773789923/115388/43524ba6/5b347449N8608838d.jpg\\\" /><br /><img src=\\\"http://img30.360buyimg.com/sku/jfs/t1/15280/11/2486/189883/5c1cdf8cEf4f601fd/e171bbeee60606d7.jpg\\\" /><br /><img src=\\\"http://img30.360buyimg.com/sku/jfs/t1/101642/4/13379/74068/5e561711Ec2771682/1b7335ff3d6565e8.jpg\\\" /><br /><img src=\\\"http://img30.360buyimg.com/sku/jfs/t21556/278/1782602826/118960/73b24333/5b347449N88f1442a.jpg\\\" /></div>\",\n" +
                "    \"category1Id\": \"737\",\n" +
                "    \"sort\": 0,\n" +
                "    \"picUrl\": \"https://oss.3c2p.com/goodsImages/big_30731656563329717.jpg\",\n" +
                "    \"unit\": \"瓶\",\n" +
                "    \"category3Name\": \"商用压面机\",\n" +
                "    \"brandId\": \"153\",\n" +
                "    \"category2Name\": \"商用电器\",\n" +
                "    \"category3Id\": \"14862\",\n" +
                "    \"spuSpecValueList\": [\n" +
                "      {\n" +
                "        \"specName\": \"商品名称\",\n" +
                "        \"specValue\": \"滴露洗手液\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"specName\": \"商品毛重\",\n" +
                "        \"specValue\": \"0.55kg\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"specName\": \"功效\",\n" +
                "        \"specValue\": \"清洁，抑菌\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"specName\": \"适用人群\",\n" +
                "        \"specValue\": \"通用\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"specName\": \"净含量\",\n" +
                "        \"specValue\": \"500mL\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"specName\": \"保质期\",\n" +
                "        \"specValue\": \"3年\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"specName\": \"是否消字号商品\",\n" +
                "        \"specValue\": \"非消字号\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"specName\": \"适合肤质\",\n" +
                "        \"specValue\": \"敏感性\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"specName\": \"商品产地\",\n" +
                "        \"specValue\": \"湖北省荆州市\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"specName\": \"类别\",\n" +
                "        \"specValue\": \"水洗（液体）\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"status\": 1\n" +
                "  }\n" +
                "]";
        OpenContextHolder.setSupplierId(15L);
        OpenContextHolder.setSupplierName("德致商成");
        TenantContextHolder.setTenantId(150L);
        TenantContextHolder.setIgnore(false);
        List<ProductSpuOpenVO> productSpuOpenVOS = JSON.parseArray(content, ProductSpuOpenVO.class);
        List<ProductSpuBaseRespOpenVO> respOpenVOS = productSkuService.addProductSku(productSpuOpenVOS, 15L);
        System.out.println(respOpenVOS);
    }

    @Test
    public void checkOrder() {
        CheckOrderReqVO checkOrderReqVO = new CheckOrderReqVO();
        checkOrderReqVO.setAddressId(34L);
        CheckOrderReqVO.Item item = new CheckOrderReqVO.Item();
        item.setSkuId(1090486L);
        item.setSupplierId(3L);
        item.setCount(1);
        //checkOrderReqVO.setItem(item);
        CheckOrderDetailRespVO checkOrderDetailRespVO = tradeCartService.checkOrder(258L, checkOrderReqVO);
        System.out.println(checkOrderDetailRespVO);
    }

    @Test
    public void testSelectPage2() {
        ProductSkuPageReqVO reqVO = new ProductSkuPageReqVO();
        PageResult<ProductSkuPageRespVO> pageResult = productSkuService.selectPage2(reqVO);
        System.out.println(pageResult);
    }

    @Test
    public void testSelectSkuDetail2() {
        ProductSkuReqVO reqVO = new ProductSkuReqVO();
        reqVO.setSkuId(1789942652314054658L);
        ProductSkuDetailRespVO respVO = productSkuService.selectSkuDetail2(reqVO);
        System.out.println(respVO);
    }

    @SneakyThrows
    public static void main(String[] args) {
        String SERVER_URL = "https://api.jd.com/routerjson";
        String appKey="928569A0D32DE9E1D510DFA98DDFD6A7";
        String appSecret = "8fc0cb89b04940078f8e90c820cf6ae0";
        String accessToken = "2ac259ed73a547b994a72e55daaad343jmju";
        JdClient client=new DefaultJdClient(SERVER_URL,accessToken,appKey,appSecret);
        VopOrderQueryOrderDetailRequest request=new VopOrderQueryOrderDetailRequest();
//        request.setThirdOrderId("1762741697868992514");
        request.setJdOrderId(290439365806L);
        VopOrderQueryOrderDetailResponse detailResponse = client.execute(request);
        System.out.println(detailResponse);
        List<com.jd.open.api.sdk.domain.vopdd.QueryOrderOpenProvider.response.queryOrderDetail.QueryOrderOpenResp>
                orderList = detailResponse.getOpenRpcResult().getResult();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        com.jd.open.api.sdk.domain.vopdd.QueryOrderOpenProvider.response.queryOrderDetail.QueryOrderOpenResp parentOrder =
                orderList.stream().filter(item -> Objects.equals(item.getParentType(), 1)).findFirst().orElse(null);
        System.out.println(parentOrder);
    }
}
