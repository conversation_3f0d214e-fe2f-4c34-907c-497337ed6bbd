package cn.iocoder.yudao.module.mall.external.assets;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.AssetsConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.integration.assetsconfig.AssetsConfigService;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsCompleteDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsCreateDTO;
import cn.iocoder.yudao.module.mall.external.assets.dto.ExtAssetsResp;
import cn.iocoder.yudao.module.mall.external.assets.enums.AssetClientTypeEnum;
import cn.iocoder.yudao.module.mall.external.assets.util.LBJwtUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 固资集成接口测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class AssetsClientTest {

    @Resource
    private AssetsClient assetsClient;
    @Resource
    private AssetsConfigService assetsConfigService;

    @BeforeAll
    public static void init() {
        TenantContextHolder.setTenantId(150L);
    }

    @Test
    @SneakyThrows
    public void pushAssets() {
        AssetsConfigDO configDO = assetsConfigService.getAssetsConfigBySys(AssetClientTypeEnum.LUOBI.getType());
        String url = "https://img.jslink.com/invoiceUrl/c82bdd664dcc43439c683b3ad90b605a.pdf";
        ExtAssetsCreateDTO dto = new ExtAssetsCreateDTO()
            .setUser("00003924")
            .setDept("2312")
            .setOrderNo("1122334455679")
            .setOrderDetailNo("1122334455661")
            .setPrice(new BigDecimal(599))
            .setQuantity(5)
            .setModel("*")
            .setFormat("*")
            .setSupplier("京东")
            .setPhone("13707132233")
            .setInvoice("1133456")
            .setInvoiceImg(new String[]{url})
            .setBuyDate("2024-03-20");
        ExtAssetsResp<String> resp = assetsClient.pushAssets(configDO, dto);
        log.info("getPushAssets: {}", resp);
    }

    @Test
    @SneakyThrows
    public void pushAssets2() {
        AssetsConfigDO configDO = assetsConfigService.getAssetsConfigBySys(AssetClientTypeEnum.PULUODI.getType());
        String url = "https://img.jslink.com/invoiceUrl/c82bdd664dcc43439c683b3ad90b605a.pdf";
        ExtAssetsCreateDTO dto = new ExtAssetsCreateDTO()
                .setUser("00003924")
                .setDept("2312")
                .setOrderNo("1122334455679")
                .setOrderDetailNo("1122334455666")
                .setPrice(new BigDecimal(599))
                .setQuantity(5)
                .setModel("*")
                .setFormat("*")
                .setSupplier("京东")
                .setSkuName("佳能打印机")
                .setSkuUrl("https://www.jcy360.cn")
                .setPhone("13707132233")
                .setInvoice("1133456")
                .setInvoiceUrl(url)
                .setBuyDate("2024-03-20");
        ExtAssetsResp<String> resp = assetsClient.pushAssets(configDO, dto);
        log.info("getPushAssets: {}", resp);
    }

    @Test
    @SneakyThrows
    public void pushAssets3() {
        AssetsConfigDO configDO = assetsConfigService.getAssetsConfigBySys(AssetClientTypeEnum.LUOBI_V20.getType());
        String url = "https://img.jslink.com/invoiceUrl/c82bdd664dcc43439c683b3ad90b605a.pdf";
        ExtAssetsCreateDTO dto = new ExtAssetsCreateDTO()
                .setUser("00003924")
                .setDept("2312")
                .setOrderNo("1122334455612")
                .setOrderDetailNo("112233444562")
                .setPrice(new BigDecimal(599))
                .setQuantity(5)
                .setModel("*")
                .setFormat("Apple/苹果 iPhone 15 Pro Max (A3108) 256GB 白色钛金属 支持移动联通电信5G 双卡双待手机")
                .setSupplier("京东")
                .setSkuName("Apple/苹果 iPhone 15 Pro Max (A3108) 256GB 白色钛金属 支持移动联通电信5G 双卡双待手机")
                .setSkuUrl("https://www.jcy360.cn")
                .setPhone("13707132233")
                .setInvoice("1133456")
                .setInvoiceImg(new String[]{url})
                .setBuyDate("2024-03-20");
        ExtAssetsResp<String> resp = assetsClient.pushAssets(configDO, dto);
        log.info("getPushAssets: {}", resp);
    }

    @Test
    @SneakyThrows
    public void testAssetsEncoding() {
        AssetsConfigDO configDO = assetsConfigService.getAssetsConfigBySys(AssetClientTypeEnum.PULUODI.getType());
        String json = "[{\"buyDate\":\"2024-10-11\",\"dept\":\"010100\",\"format\":\"测试商品-单反相机\",\"model\":\"*\",\"orderDetailNo\":\"1844646677273575426\",\"orderNo\":\"1844646677151940608\",\"phone\":\"13600000000\",\"price\":1000.00,\"quantity\":1,\"skuName\":\"测试商品-单反相机\",\"skuUrl\":\"https://hbzgh-pre.jcy360.cn/#/detail/11/1815279675950174210\",\"supplier\":\"自建型_001\",\"user\":\"06601\"}]";
        String encryptStr = LBJwtUtil.encrty(json, configDO.getKey1(), configDO.getKey2(), Long.valueOf(configDO.getKey3()));
        String encryptStr2 = "AAABkoBkfnMCAAAAAAABhqo%3D.bRh7mBvnC4qJU2%2BLawqIhTLAsjbsm96NhF8C%2FGmcmmZc2zYyQgUhkNEm3biZE7qW5PRdjG4sHSFSRSVAGSRuHIIHIJKdKZbuP46dZ3Eg1DvbsSSLH%2FYjVOQ9ucZsxfoaxfJ1rnb65T7ZOcb60zc4%2BWL5D2wsSZTx6dWJq5dDGntHz6keOa8Pxs5Bczq5TmmikuSmg%2BH0zIWfCraEBM5k%2FhAn4CECPZrKn7BdNKOvazaR5bSTNPjr2garAzCZ6uNWwo5wX7vk%2BLnXSYG9736pu%2FjbA6kMDve0WaQj1ldqPwctoDV%2BR9lmzgopX1%2FKoG6vSX6guaTeUeH0DFrHos8j%2FKVf4ro8B0253lfTKpekQ5kQZFHvN%2Fb4qRMdsOkzKE2iAoQAV0z6IfWT6C0GZ0hk%2F4TIYizWS7ErSBsD0l6yahovgksfeVnmHuI5USH2gMxVK8XNrlzbkQXtOwsFpO%2FVD4GXArxW36nWo1NbDvQ7ezaoKZsmlinscJZagRVv8grIB8OBNu1HaoDtvE3vTpgs%2Fw%3D%3D.RXe6ML1N3ZOaczVGJdglxyyrVjHcfHL6FdP%2FNaPCjvc%3D";
        String decryptStr = LBJwtUtil.dencrty(encryptStr2, configDO.getKey1(), configDO.getKey3(), Long.valueOf(configDO.getKey3()));
        log.info("source: {}", encryptStr);
        log.info("encryptStr: {}", encryptStr);
        log.info("decryptStr: {}", decryptStr);
    }

    @Test
    @SneakyThrows
    public void pushAssets2Pld() {
        AssetsConfigDO configDO = assetsConfigService.getAssetsConfigBySys(AssetClientTypeEnum.PULUODI.getType());
        String url = "https://img.jslink.com/invoiceUrl/c82bdd664dcc43439c683b3ad90b605a.pdf";
        String skuUrl = "https://hbzgh-pre.jcy360.cn/#/detail/11/1815280071116525570";
        String skuName = "Apple/苹果 iPhone 15 Pro Max (A3108) 256GB 白色钛金属 支持移动联通电信5G 双卡双待手机";
        ExtAssetsCreateDTO dto = new ExtAssetsCreateDTO()
                .setUser("00003924")
                .setDept("2312")
                .setOrderNo("1122334455679")
                .setOrderDetailNo("112233445566-7")
                .setPrice(new BigDecimal(599))
                .setQuantity(5)
                .setModel("*")
                .setFormat("*")
                .setSupplier("京东")
                .setPhone("13707132233")
                .setInvoice("1133456")
                .setInvoiceUrl(url)
                .setSkuUrl(skuUrl)
                .setSkuName(skuName)
                .setBuyDate("2024-03-20");
        ExtAssetsResp<String> resp = assetsClient.pushAssets(configDO, dto);
        log.info("getPushAssets: {}", resp);
    }

    @Test
    public void completeAssets() {
        AssetsConfigDO configDO = assetsConfigService.getAssetsConfigBySys(AssetClientTypeEnum.LUOBI.getType());
        ExtAssetsCompleteDTO dto = new ExtAssetsCompleteDTO()
                .setAcceptNo("20240320160209713")
                .setOrderDetailNo("112233445566-1")
                .setFinanceNo("112233")
                .setFinanceUser("王处")
                .setFinanceDesc("同意")
                .setFinanceDate("2024-03-20");
        boolean result = assetsClient.completeAssets(configDO, dto);
        log.info("completeAssets: {}", result);
    }

    @Test
    public void completeAssets02() {
        AssetsConfigDO configDO = assetsConfigService.getAssetsConfigBySys(AssetClientTypeEnum.LUOBI_V20.getType());
        ExtAssetsCompleteDTO dto = new ExtAssetsCompleteDTO()
                .setAcceptNo("J202410300002")
                .setOrderDetailNo("1851162592689475585")
                .setFinanceNo("112233")
                .setFinanceUser("王处")
                .setFinanceDesc("同意")
                .setFinanceDate("2024-10-30");
        boolean result = assetsClient.completeAssets(configDO, dto);
        log.info("completeAssets: {}", result);
    }

    @Test
    @SneakyThrows
    public void decryptToken() {
        String source = "helloworld";
        AssetsConfigDO config = assetsConfigService.getAssetsConfig(1L);
        log.info("config: {}", config);
        String token = LBJwtUtil.encrty(source, config.getKey1(), config.getKey2(), Long.valueOf(config.getKey3()));
        log.info("source: {}, toToken: {}", source, token);
        String json = LBJwtUtil.dencrty(token, config.getKey1(), config.getKey2(), Long.valueOf(config.getKey3()));
        log.info("decrypt:{}", json);
    }

    @Test
    @SneakyThrows
    public void testSpel01() {
        Map<String, Object> params = new HashMap<>();
        Map<String, Object> subParam = new HashMap<>();

        subParam.put("price", 1500);
        subParam.put("count", 5);

        params.put("priceThreshold", new BigDecimal(1000));
        params.put("categoryIds", Arrays.asList("123","23456"));
        params.put("orderItem", subParam);

        // 创建 SpEL 解析器
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariable("param", params);

        Boolean b1 = parser.parseExpression("5 > 3").getValue(context, Boolean.class);
        log.info("b1======{}", b1);
        // 访问属性
        Object pv = parser.parseExpression("#param['orderItem']").getValue(context);
        log.info("pv======{}", pv);

        Object pv2 = parser.parseExpression("#param['orderItem']['count'] > 1").getValue(context);
        log.info("pv2======{}", pv2);

        BigDecimal priceThreshold = parser.parseExpression("#param['priceThreshold']").getValue(context, BigDecimal.class);
        log.info("priceThreshold======{}", priceThreshold);
    }

    public static void main(String[] args) {
        new AssetsClientTest().testSpel01();
    }

}
