package cn.iocoder.yudao.module.mall.product;

import cn.hutool.core.util.RandomUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.product.controller.app.skusearch.vo.SkuContentDemoPageReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.SkuContentDemoDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.SkuSpecValueDO;
import cn.iocoder.yudao.module.mall.product.service.skusearch.SkuContentDemoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 业财融合接口测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class SkuContentDemoServiceTest {

    @Resource
    private SkuContentDemoService skuContentDemoService;

    private SkuContentDemoDO generateEntity(Long skuId) {
        String[] spuNames = {"苹果手机Iphone15", "苹果手机Iphone14", "苹果手机Iphone13", "苹果手机IphoneXR", "苹果笔记本电脑macbookpro", "苹果智能手表"};
        Long specId1 = 101L;
        Long specId2 = 102L;
        String[] specValues1 = {"128G", "256G", "512G", "1T"};
        String[] specValues2 = {"8G", "12G", "16G"};
        Long[] brandIds = {1L,2L,3L,4L,5L,6L,7L,8L};

        SkuContentDemoDO entity = new SkuContentDemoDO();
        entity.setSkuId(skuId);
        entity.setTenantId(TenantContextHolder.getTenantId());
        entity.setSpuName(spuNames[RandomUtil.randomInt(0, spuNames.length - 1)]);
        entity.setBrandId(brandIds[RandomUtil.randomInt(0, brandIds.length - 1)]);
        entity.setCategoryId1(10001L);
        entity.setCategoryIdPath("101,10056,10001");
        entity.setCategoryName1("数码通讯");
        entity.setStatus(RandomUtil.randomInt(0, 1));
        SkuSpecValueDO spec1 = new SkuSpecValueDO(specId1, specValues1[RandomUtil.randomInt(0, specValues1.length - 1)]);
        SkuSpecValueDO spec2 = new SkuSpecValueDO(specId2, specValues2[RandomUtil.randomInt(0, specValues2.length - 1)]);

        entity.setSpecValueList(Arrays.asList(spec1, spec2));

        return entity;
    }

    @Test
    public void testSave() {
        TenantContextHolder.setTenantId(150L);
        for(int i = 0 ; i < 100 ; i++) {
            skuContentDemoService.save(generateEntity(1000001L + i));
        }

        log.info("保存成功");
    }

    @Test
    public void testDelete() {
        TenantContextHolder.setTenantId(150L);
        for(int i = 0 ; i < 100 ; i++) {
            skuContentDemoService.delete(1000001L + i);
        }
        log.info("删除成功");
    }

    @Test
    public void testGetById() {
        TenantContextHolder.setTenantId(150L);
        SkuContentDemoDO entity = skuContentDemoService.getById(1000001L);
        log.info("查询成功: {}", entity);
    }

    @Test
    public void testSeachByPage() {
        TenantContextHolder.setTenantId(150L);
        SkuContentDemoPageReqVO reqVo = new SkuContentDemoPageReqVO();
        reqVo.setKeyword("苹果笔记本电脑");
        //reqVo.setSpecValueVo(new SkuContentDemoPageReqVO.SpecValueVo(101L, "256G"));
        //reqVo.setBrandId(5L);
        //reqVo.setCategoryIdPath("101,10056");

        PageResult<SkuContentDemoDO> pageResult = skuContentDemoService.seachByPage(reqVo);
        log.info("分页搜索成功: {}", pageResult);
    }

}
