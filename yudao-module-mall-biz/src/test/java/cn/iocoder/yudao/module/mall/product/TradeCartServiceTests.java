package cn.iocoder.yudao.module.mall.product;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.AreaDTO;
import cn.iocoder.yudao.module.mall.trade.controller.app.cart.vo.CartDetailRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.cart.vo.CheckOrderDetailRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.cart.vo.CheckOrderReqVO;
import cn.iocoder.yudao.module.mall.trade.service.cart.TradeCartService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * <AUTHOR>
 * @date 2024/2/6
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class TradeCartServiceTests {

    @Autowired
    private TradeCartService tradeCartService;


    @BeforeEach
    public void init() {
        TenantContextHolder.setTenantId(150L);
    }


    @Test
    public void checkOrder() {
        CheckOrderReqVO reqVO = new CheckOrderReqVO()
                .setAddressId(34L)
                .setItem(new CheckOrderReqVO.Item()
                        .setSkuId(67782L)
                        .setCount(2)
                        .setSupplierId(3L));
        CheckOrderDetailRespVO checkOrderDetailRespVO = tradeCartService.checkOrder(258L, reqVO);
        System.out.println(JSON.toJSONString(checkOrderDetailRespVO));
    }

    @Test
    public void getCartDetail() {
        AreaDTO area = new AreaDTO()
                .setProvinceId(17L)
                .setCityId(1381L)
                .setCountyId(50718L);
        CartDetailRespVO cart = tradeCartService.getCartDetail(258L, area);
        System.out.println(JSON.toJSONString(cart));
    }
}
