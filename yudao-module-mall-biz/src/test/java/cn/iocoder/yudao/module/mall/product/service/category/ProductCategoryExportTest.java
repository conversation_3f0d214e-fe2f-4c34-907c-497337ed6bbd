package cn.iocoder.yudao.module.mall.product.service.category;

import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryExcelVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryListReqVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * ProductCategoryService 导出功能测试类
 */
@SpringBootTest
public class ProductCategoryExportTest {

    @Resource
    private ProductCategoryService productCategoryService;

    @Test
    public void testGetCategoryExcelData() {
        // 测试获取分类Excel导出数据
        ProductCategoryListReqVO listReqVO = new ProductCategoryListReqVO();
        
        List<ProductCategoryExcelVO> excelData = productCategoryService.getCategoryExcelData(listReqVO);
        
        System.out.println("导出数据总数: " + excelData.size());
        
        // 打印前10条数据作为示例
        int count = Math.min(10, excelData.size());
        for (int i = 0; i < count; i++) {
            ProductCategoryExcelVO item = excelData.get(i);
            System.out.println(String.format("第%d行: %s|%s|%s|%s|%s|%s", 
                i + 1,
                item.getCategory1Id(), 
                item.getCategory1Name(),
                item.getCategory2Id(), 
                item.getCategory2Name(),
                item.getCategory3Id(), 
                item.getCategory3Name()
            ));
        }
    }

    @Test
    public void testCategoryHierarchy() {
        // 测试分类层级结构
        ProductCategoryListReqVO listReqVO = new ProductCategoryListReqVO();
        
        List<ProductCategoryExcelVO> excelData = productCategoryService.getCategoryExcelData(listReqVO);
        
        // 统计各级分类数量
        long level1Count = excelData.stream().filter(item -> item.getCategory1Id() != null).count();
        long level2Count = excelData.stream().filter(item -> item.getCategory2Id() != null).count();
        long level3Count = excelData.stream().filter(item -> item.getCategory3Id() != null).count();
        
        System.out.println("一级分类数据行数: " + level1Count);
        System.out.println("二级分类数据行数: " + level2Count);
        System.out.println("三级分类数据行数: " + level3Count);
        
        // 验证数据完整性
        for (ProductCategoryExcelVO item : excelData) {
            if (item.getCategory2Id() != null && item.getCategory1Id() == null) {
                System.err.println("数据错误：存在二级分类但缺少一级分类");
            }
            if (item.getCategory3Id() != null && item.getCategory2Id() == null) {
                System.err.println("数据错误：存在三级分类但缺少二级分类");
            }
        }
    }
}
