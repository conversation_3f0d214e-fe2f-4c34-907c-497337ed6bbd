package cn.iocoder.yudao.module.mall.project;

import cn.hutool.extra.expression.engine.spel.SpELEngine;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.Map;

/**
 * 项目规则测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class ProjectRuleServiceTest {

    @BeforeAll
    public static void init() {
        TenantContextHolder.setTenantId(150L);
    }

    @Test
    public void testRule1() {
        String content = "!#projectNo.equals('999999')";

        SpELEngine spELEngine = new SpELEngine();
        Map<String, Object> context = new HashMap<>();
        context.put("projectNo", "999999");

        Boolean result = (Boolean) spELEngine.eval(content, context);
        log.info("result--------{}", result);
    }


}
