package cn.iocoder.yudao.module.mall.member;

import cn.iocoder.yudao.module.mall.MallServerApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * 会员认证相关用例
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class MemberAuthServiceTest {

    @Resource
    private PasswordEncoder passwordEncoder;

    @Test
    public void testCreatePassword() {
        String rawPassword = "jcY@2309";
        log.info("raw: {}, encoded: {}", rawPassword, passwordEncoder.encode(rawPassword));
    }

}
