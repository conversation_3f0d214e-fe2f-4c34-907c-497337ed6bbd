package cn.iocoder.yudao.module.mall.external.ycrh;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.external.ycrh.dto.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 业财融合接口测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class YcrhClientTest {

    @Resource
    private YcrhClient YcrhClient;
    @Resource
    private YcrhBpmClient ycrhBpmClient;

    @BeforeAll
    public static void init() {
        TenantContextHolder.setTenantId(150L);
    }

    @Test
    public void getGetYgInfo() {
        YgInfoRespDTO dto = YcrhClient.getYgInfo("06601");
        log.info("getGetYgInfo: {}", dto);
    }

    @Test
    public void searchGetYgInfo() {
        SearchYgInfoReqDTO reqDTO = new SearchYgInfoReqDTO();
//        reqDTO.setYgbh("06601");
        reqDTO.setYgmc("张");
//        reqDTO.setBmbh("1");

        List<YgInfoRespDTO> list = YcrhClient.searchYgInfo(reqDTO);
        log.info("searchGetYgInfo: {}", list);
    }

    @Test
    public void getUserProject() {
        UserProjectReqDTO userProjectReqDTO = new UserProjectReqDTO()
                .setYgNo("06601")
                .setType("1")
                .setIsRelease("0");
        List<UserProjectRespDTO> projectInfos = YcrhClient.getUserProjectInfo(userProjectReqDTO);
        System.out.println("getUserProject: " + JSON.toJSONString(projectInfos));
    }

    @Test
    public void getProjectInfo() {
        ProjectInfoReqDTO projectInfoReqDTO = new ProjectInfoReqDTO()
                .setProjectNo("0015")
                .setDepartmentNo("010100");
        List<ProjectInfoRespDTO> projectInfos = YcrhClient.getProjectInfo(projectInfoReqDTO);
        System.out.println("getProjectInfo: " + JSON.toJSONString(projectInfos));
    }

    @Test
    public void checkBalance() {
        TenantContextHolder.setTenantId(150L);
        CheckBalanceReqDTO reqDTO = new CheckBalanceReqDTO()
                .setDepartmentNo("010100")
                .setProjectNo("0015")
                .setCheckAmount(new BigDecimal("10"))
                .setChargeNo("06601");
        YcrhResp resp = YcrhClient.checkBalance(reqDTO);
        System.out.println(resp);
    }

    @Test
    public void getBalance() {
        BalanceQueryReqDTO reqDTO = new BalanceQueryReqDTO()
                .setDepartmentNo("010100")
                .setProjectNo("0015")
                .setChargeNo("06601");
        BigDecimal balance = YcrhClient.getBalance(reqDTO);
        System.out.println(balance);
    }

    @Test
    public void getBalanceInfo() {
        BalanceInfoQueryReqDTO reqDTO = new BalanceInfoQueryReqDTO()
                .setDepartmentNo("010100")
                .setProjectNo("0015")
                .setChargeNo("06601")
                .setEconomyClass("30201");
        BalanceInfoRespDTO balanceInfo = YcrhClient.getBalanceInfo(reqDTO);
        System.out.println(balanceInfo);
    }

    @Test
    public void queryOrderList() {
        OrderListQueryReqDTO orderListQueryReqDTO = new OrderListQueryReqDTO();
        orderListQueryReqDTO.setOrderNoList(Arrays.asList("1846112288700706816", "1846112288700706817", "1846112288700706818"));
        OrderBatchQueryRespDTO orderBatchQueryRespDTO = YcrhClient.queryOrderList(orderListQueryReqDTO);
        log.info("{}", orderBatchQueryRespDTO.getFoundOrderList());
        log.info("{}", orderBatchQueryRespDTO.getNotFoundOrderList());
    }

    @Test
    public void getApprovalUsers() {
        // 方法一：代码里设置
        //System.setProperty("http.proxyHost", "127.0.0.1");
        //System.setProperty("http.proxyPort", "8899");

        ApprovalParamDTO param = new ApprovalParamDTO()
                .setBusinessType("002")
                .setApprovalType("1")
                .setDepartmentNo("021700")
                .setProjectNo("112011")
                .setYgDepartmentNo("021700")
                .setYgNo("2019001")
                .setYgName("1212")
                .setAmount("100")
                .setBorrower("");

        ApprovalUserQueryReqDTO reqDTO = new ApprovalUserQueryReqDTO()
                .setSystemType("14")
                .setObj(param);
        List<ApprovalUserRespDTO> approvalUsers = ycrhBpmClient.getApprovalUsers(reqDTO);

        System.out.println(JSON.toJSONString(approvalUsers));
    }


    @Test
    public void pushOrder() {
        ShopOrderDTO shopOrderDTO = new ShopOrderDTO()
                .setOrderNo("1691286055379693569")
                .setTotalPrice(new BigDecimal("500"))
                .setPayType("1")
                .setFreight(new BigDecimal("0"))
                .setSingleProject("1")
                .setDepartmentNo("010100")
                .setProjectNo("0015")
                .setChargeNo("06601")
                .setOrderTime("2023-08-02 10:00:00")
                .setYgNo("06601");
        SkuInfoDTO skuInfoDTO = new SkuInfoDTO()
                .setSkuId("2514")
                .setSkuName("绿联 笔记本电脑包手提14.9英寸内胆包适用苹果小米联想华为减震抗摔")
                .setSkuType("电脑包")
                .setSkuTypeID("123")
                .setSkuPrice(new BigDecimal("500"))
                .setEconomyClass("3019902")
                .setCount(5)
                .setUnitPrice(new BigDecimal("100"))
                .setGoodsType("1");

        shopOrderDTO.setWbSkuInfo(Arrays.asList(skuInfoDTO));

        PushOrderReqDTO reqDTO = new PushOrderReqDTO()
                .setBusinessType("01")
                .setWbShopOrder(shopOrderDTO);
        PushOrderRespDTO pushOrderRespDTO = YcrhClient.pushOrder(reqDTO);
        System.out.println(JSON.toJSONString(pushOrderRespDTO));
    }


    @Test
    public void cancelOrder() {
        OrderCancelReqDTO reqDTO = new OrderCancelReqDTO()
                .setCancelReason("2")
                .setOrderNo("1691286055379693569")
                .setCancelTime("2023-08-15 16:10:10");
        YcrhClient.cancelOrder(reqDTO);
    }

    @Test
    public void queryOrder() {
        OrderQueryReqDTO reqDTO = new OrderQueryReqDTO()
                .setOrderNo("1691286055379693569--");
        OrderQueryRespDTO result = YcrhClient.queryOrder(reqDTO);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void pushOrderAssetInfo() {
        OrderAssetsInfoReqDTO reqDTO = new OrderAssetsInfoReqDTO()
                .setOrderNo("1691286055379693569");
        List<WbSkuInfoDTO> skuList = new ArrayList<>();
        skuList.add(new WbSkuInfoDTO().setSkuId("11223").setSkuName("apple iphone14").setCount(1)
                .setUnitPrice(new BigDecimal(10)).setSkuPrice(new BigDecimal(10)).setAssetsAcceptanceNo("112233")
                .setAssetsNo("11223344").setAssetsTypeSix("61").setAssetsTypeSixteen("161"));
        reqDTO.setWbSkuInfoZcs(skuList);

        YcrhResp result = YcrhClient.pushOrderAssetInfo(reqDTO);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void uploadVoucher() {
        // 方法一：代码里设置
        //System.setProperty("http.proxyHost", "127.0.0.1");
        //System.setProperty("http.proxyPort", "8899");
        String title = "{\n" +
                "\t\"title\": \"采购申请\",\n" +
                "\t\"申请单号\": \"appr1690766518202906\",\n" +
                "\t\"项目名称\": \"(151702)20202000606Transformer类模型的优化器探索与预测加速\",\n" +
                "\t\"报销金额\": 288.00,\n" +
                "\t\"备注\": \"采购平台订单\"\n" +
                "}";
        JSONObject appTitle = JSON.parseObject(title);

        String content = "{\n" +
                "\t\"订单信息\": [{\n" +
                "\t\t\"名称\": \"小米台灯\",\n" +
                "\t\t\"数量\": 1,\n" +
                "\t\t\"价格\": 88.00\n" +
                "\t}, {\n" +
                "\t\t\"名称\": \"抽纸\",\n" +
                "\t\t\"数量\": 1,\n" +
                "\t\t\"价格\": 200.00\n" +
                "\t}],\n" +
                "\t\"申请信息\": [{\n" +
                "\t\t\"申请人\": \"毕晓昉\",\n" +
                "\t\t\"批次号\": 1690766124502906,\n" +
                "\t\t\"申请原因\": \"测试\",\n" +
                "\t\t\"使用项目\": \"(151702)20202000606Transformer类模型的优化器探索与预测加速\",\n" +
                "\t\t\"报销金额\": 288.00,\n" +
                "\t\t\"备注\": \"采购平台订单\"\n" +
                "\t}]\n" +
                "}";
        JSONObject contentObj = JSON.parseObject(content);

        VoucherDTO voucherDTO = new VoucherDTO()
                .setModuleName("YC")
                .setBusinessCode("CG")
                .setBusinessNo("25624584659527")
                .setApplicantName("bixiaofang")
                .setApplicantNo("06601")
                .setApplicantDate("20230731")
                .setApplicantTime("2023-07-31")
                .setContentOverview(contentObj)
                .setApprovalLevel("1")
                .setApprovalStatus("0")
                .setApprovalTitle(appTitle);


        ApprovalInfoDTO approvalInfoDTO = new ApprovalInfoDTO()
                .setBusinessNo("25624584659527")
                .setIsUpdateApproval("1")
                .setIsSendMessage("0")
                .setIsSignature("0")
                .setIsSign("0")
                .setIsCountersign("1")
                .setApprovalLevel("1")
                .setApplicantName("bixiaofang")
                .setApplicantNo("06601")
                .setApprovalUserName("zhangsan")
                .setApprovalUserNo("2019001")
                .setApprovalStatus("0")
                .setIsCountersign("0")
                .setApprovalRoleName("ceshi");

        UploadVoucherDTO reqDTO = new UploadVoucherDTO()
                .setVoucher(voucherDTO)
                .setApprovalInfos(Arrays.asList(approvalInfoDTO));

        ycrhBpmClient.uploadVoucher(reqDTO);
    }

    @Test
    public void deleteVoucher() {
        DeleteVoucherDTO reqDTO = new DeleteVoucherDTO()
                .setModuleName("YC")
                .setBusinessCode("CG")
                .setBusinessNo("25624584659527");

        boolean result = ycrhBpmClient.deleteVoucher(reqDTO);
        System.out.println("删除单据调用结果: " + result);
    }


    @Test
    @SneakyThrows
    public void uploadAttachments() {
        File file = new File("D:/1.txt");
        byte[] bytes = Files.readAllBytes(file.toPath());
        String attachments = Base64.encodeBase64String(bytes);
        UploadAttachmentsDTO uploadAttachmentsDTO = new UploadAttachmentsDTO()
                .setBusinessNo("25624584659527")
                .setModuleName("YC")
                .setBusinessCode("CG")
                .setFileName("1.txt")
                .setIsImportant("0")
                .setSerialNumber("123")
                .setAttachments(attachments);
        ycrhBpmClient.uploadAttachments(uploadAttachmentsDTO);

    }

    @Test
    @SneakyThrows
    public void getVoucher() {
        GetVoucherDTO getVoucherDTO = new GetVoucherDTO()
                .setBusinessNo("1691286436411240449")
                .setModuleName("YC");
        List<ApprovalResultDTO> approvalResults = ycrhBpmClient.getVoucher(getVoucherDTO);
        System.out.println(JSON.toJSONString(approvalResults));
    }

    @Test
    @SneakyThrows
    public void getFinalVoucher() {
        GetVoucherDTO getVoucherDTO = new GetVoucherDTO()
                .setBusinessNo("1691286436411240449");
        YcrhResp approvalResults = ycrhBpmClient.getFinalVoucher(getVoucherDTO);
        System.out.println(JSON.toJSONString(approvalResults));
    }

    @Test
    public void pushBill() {
        /**
         * 开户名：武汉京东世纪贸易有限公司，账号：[***************]，开户行：招商银行股份有限公司北京青年路支行，联行号：[************]
         */

        AccountDTO accountDTO = new AccountDTO()
                .setCompanyName("武汉京东世纪贸易有限公司")
                .setBankAccount("***************")
                .setCity("北京")
                .setProvince("北京")
                //.setAmount("")
                .setBankName("招商银行股份有限公司北京青年路支行");

        SkuInfoDTO skuInfoDTO = new SkuInfoDTO()
                .setSkuId("2514")
                .setSkuName("小米台灯")
                .setSkuType("灯具")
                .setSkuTypeID("123")
                .setSkuPrice(new BigDecimal("500"))
                .setCount(5)
                .setUnitPrice(new BigDecimal("100"))
                .setGoodsType("1");

        OrderInfoDTO orderInfoDTO = new OrderInfoDTO()
                .setOrderId("*********")
                .setOrderAmount(new BigDecimal("500"))
                .setRefundAmount(new BigDecimal("0"))
                .setSkuInfo(Arrays.asList(skuInfoDTO));
        PushBillReqDTO reqDTO = new PushBillReqDTO()
                .setBillNo("********")
                .setBusinessType("03")
                .setBillStartDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .setBillAmount(new BigDecimal("500"))
                .setOrderInfo(Arrays.asList(orderInfoDTO))
                .setAccount(accountDTO);
        String content = YcrhClient.pushBill(reqDTO);
        System.out.println(content);

    }

    @Test
    public void getBill() {
        BillQueryReqDTO reqDTO = new BillQueryReqDTO()
                .setBillNo("4");
        BillInfoRespDTO bill = YcrhClient.getBill(reqDTO);
        System.out.println(JSON.toJSONString(bill));
    }

    @Test
    public void cancelBill() {
        BillCancelReqDTO reqDTO = new BillCancelReqDTO()
                .setBillNo("4")
                .setCancelReason("1")
                .setCancelTime("2024-04-29 09:00:00");
        YcrhResp resp = YcrhClient.cancelBill(reqDTO, true);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void getBillVoucherDetail() {
        BillVoucherReqDTO reqDTO = new BillVoucherReqDTO()
                .setVoucherNo("**************");
        List<BillVoucherRespDTO> list = YcrhClient.getBillVoucherDetail(reqDTO);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void completeVoucher() {
        CompleteVoucherReqDTO reqDTO = new CompleteVoucherReqDTO();
        reqDTO.setBusinessNo("112233");
        YcrhResp resp = ycrhBpmClient.completeVoucher(reqDTO);
        System.out.println(JSON.toJSONString(resp));
    }

}
