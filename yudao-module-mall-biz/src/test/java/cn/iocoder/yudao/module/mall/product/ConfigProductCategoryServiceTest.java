package cn.iocoder.yudao.module.mall.product;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.product.controller.admin.category.vo.ProductCategoryListReqVO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.service.category.ConfigProductCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

/**
 * 平台配置商品分类接口测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class ConfigProductCategoryServiceTest {

    @Resource
    private ConfigProductCategoryService configProductCategoryService;


    @Test
    public void testGetAllCategoryList() {
        List<ConfigProductCategoryDO> list = configProductCategoryService.getAllCategoryList(new ProductCategoryListReqVO());
        log.info("查询配置产品分类数量: {}", list.size());
    }


}
