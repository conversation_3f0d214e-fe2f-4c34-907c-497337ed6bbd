package cn.iocoder.yudao.module.mall.vop;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.jackson.core.databind.LocalDateTimeDeserializer;
import cn.iocoder.yudao.framework.jackson.core.databind.LocalDateTimeSerializer;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.product.api.sku.ProductSkuApi;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.AreaDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.FreightRespDTO;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.SkuBaseReqDTO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppTradeOrderPageItemRespVO;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopAddressService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopAfterSalesService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopOrderService;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jd.open.api.sdk.domain.vopdd.QueryOrderOpenProvider.request.querySkuFreight.AreaBaseInfoOpenReq;
import com.jd.open.api.sdk.domain.vopdd.QueryOrderOpenProvider.request.querySkuFreight.FreightQueryOpenReq;
import com.jd.open.api.sdk.domain.vopdd.QueryOrderOpenProvider.request.querySkuFreight.SkuInfoOrderOpenReq;
import com.jd.open.api.sdk.response.vopdd.VopOrderGetOrderReceiptUrlResponse;
import com.jd.open.api.sdk.response.vopdd.VopOrderQueryDeliveryInfoResponse;
import com.jd.open.api.sdk.response.vopdd.VopOrderQuerySkuFreightResponse;
import com.jd.open.api.sdk.response.vopdz.VopAddressQueryJdAreaIdListResponse;
import com.jd.open.api.sdk.response.vopsh.VopAfsQueryLogicticsInfoResponse;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/14
 */
@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
public class VopServices {

    @Resource
    private VopAddressService vopAddressService;

    @Resource
    private JdClientHelper jdClientHelper;

    @Resource
    private ObjectMapper objectMapper;

    @Autowired
    private VopOrderService vopOrderService;

    @Autowired
    private VopAfterSalesService vopAfterSalesService;

    @Resource
    private ProductSkuApi productSkuApi;

    @Test
    public void getAddress() {
        TenantContextHolder.setTenantId(150L);
        VopAddressQueryJdAreaIdListResponse response = vopAddressService.queryJdAreaIdList(4, 242L);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void getAuthorizeCallbackUrl() {
        TenantContextHolder.setTenantId(150L);
        System.out.println("===" + jdClientHelper.generateCallbackUrl());
    }

    @Test
    @SneakyThrows
    public void serial() {
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance).addSerializer(Long.TYPE, ToStringSerializer.instance).addSerializer(LocalDateTime.class, LocalDateTimeSerializer.INSTANCE).addDeserializer(LocalDateTime.class, LocalDateTimeDeserializer.INSTANCE);

        AppTradeOrderPageItemRespVO res = new AppTradeOrderPageItemRespVO();
        res.setId(73783346715612563L);
        res.setNo("4545454");
        System.out.println(JsonUtils.toJsonString(res));
        ObjectMapper objectMapper1 = new ObjectMapper();
        objectMapper1.registerModules(simpleModule);
        System.out.println(objectMapper1.writeValueAsString(res));
        JsonUtils.init(objectMapper1);
        System.out.println(JsonUtils.toJsonString(res));
        System.out.println(objectMapper.writeValueAsString(res));

        objectMapper.registerModules(new Module[]{simpleModule});
        System.out.println(objectMapper.writeValueAsString(res));
    }

    @Test
    public void queryDeliveryInfo() {
        //------ 子订单1--------
        //thirdOrderId：202306290001
        //jdOrderId： 274311284758
        VopOrderQueryDeliveryInfoResponse vopOrderQueryDeliveryInfoResponse = vopOrderService.queryDeliveryInfo(274311284758L);
        System.out.println(JsonUtils.toJsonString(vopOrderQueryDeliveryInfoResponse));
    }

    @Test
    public void queryLogicticsInfo() {
        //------ 子订单1--------
        //thirdOrderId：202306290001
        //jdOrderId： 274311284758
        TenantContextHolder.setTenantId(150L);
        VopAfsQueryLogicticsInfoResponse logicticsInfoResponse = vopAfterSalesService.queryLogicticsInfo("202306290001", 274311284758L);
        System.out.println(JsonUtils.toJsonString(logicticsInfoResponse));

    }

    @Test
    public void queryFreight() {
        TenantContextHolder.setTenantId(150L);
        FreightQueryOpenReq req = new FreightQueryOpenReq();
        req.setPaymentType(4);
        AreaBaseInfoOpenReq areaBaseInfoOpenReq = new AreaBaseInfoOpenReq();
        areaBaseInfoOpenReq.setProvinceId(17L);
        areaBaseInfoOpenReq.setCityId(1381L);
        areaBaseInfoOpenReq.setCountyId(50718L);
        areaBaseInfoOpenReq.setTownId(62957L);
        req.setAreaInfo(areaBaseInfoOpenReq);
        SkuInfoOrderOpenReq skuInfo = new SkuInfoOrderOpenReq();
        skuInfo.setSkuId(100036634728L);
        skuInfo.setSkuNum(1);
        req.setSkuInfoList(Arrays.asList(skuInfo));
        VopOrderQuerySkuFreightResponse response = vopOrderService.querySkuFreight(req);
        System.out.println(JSON.toJSONString(response));


        SkuBaseReqDTO skuBaseReqDTO = new SkuBaseReqDTO()
                .setSkuId(100036634728L)
                .setSkuNumber(1);
        List<SkuBaseReqDTO> skuBaseReqDTOS = Arrays.asList(skuBaseReqDTO);
        AreaDTO area = new AreaDTO()
                .setProvinceId(17L)
                .setCityId(1381L)
                .setCountyId(50718L)
                .setTownId(62957L);
        FreightRespDTO freightRespDTO = productSkuApi.querySkuFreight(skuBaseReqDTOS, area, 4);
        System.out.println(JSON.toJSONString(freightRespDTO));

    }

}


