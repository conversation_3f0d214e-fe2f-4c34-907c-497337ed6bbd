package cn.iocoder.yudao.module.mall.vop;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Assert;
import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.vopxx.MsgRecordProvider.response.queryTransByVopNormal.VopBizTransMessage;
import com.jd.open.api.sdk.request.vopxx.VopMessageDeleteClientMsgByIdListRequest;
import com.jd.open.api.sdk.request.vopxx.VopMessageQueryTransByVopNormalRequest;
import com.jd.open.api.sdk.response.vopxx.VopMessageDeleteClientMsgByIdListResponse;
import com.jd.open.api.sdk.response.vopxx.VopMessageQueryTransByVopNormalResponse;

import java.util.List;
import java.util.stream.Collectors;

public class VopMessageConsume {

    public static final String appKey = "891FC235ABF957ADFEEC46681224E65A";
    public static final String appSecret = "ee3ffa98337d467c8725488f64e1d8a6";

    public static final String SERVER_URL = "https://api.jd.com/routerjson";
    public static final String accessToken = "e07df43f7f8840d09d8e9ecaeb55c9a33zta";

    public static void main(String[] args) throws Exception {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        for (int i = 0; i < 10; i++) {
            JdClient client = new DefaultJdClient(SERVER_URL, accessToken, appKey, appSecret);
//        String msgType = "6,4,16,17,48,1,5,10,11,12,15,18,25,26,31,119,200";
            String msgType = "17";
            VopMessageQueryTransByVopNormalRequest request = new VopMessageQueryTransByVopNormalRequest();
            request.setType(msgType);
            request.setReadType(2);
            VopMessageQueryTransByVopNormalResponse response = client.execute(request);
            Assert.isTrue(response.getOpenRpcResult().getSuccess(), "获取vop消息失败");

            List<VopBizTransMessage> messageList = response.getOpenRpcResult().getResult();
            System.out.println("待处理消息数量为:" + messageList.size() + "条消息");

            List<Long> messageIdList = messageList.stream().map(VopBizTransMessage::getId).collect(Collectors.toList());
            String todeleteMsgId = messageIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
            VopMessageDeleteClientMsgByIdListRequest request1 = new VopMessageDeleteClientMsgByIdListRequest();
            request1.setId(todeleteMsgId);
            VopMessageDeleteClientMsgByIdListResponse response1 = client.execute(request1);
            Assert.isTrue(response1.getVopOrderRpcResult().getSuccess(), "删除vop消息失败");
        }
        stopWatch.stop();
        System.out.println("耗时: " + stopWatch.getTotalTimeMillis() + " 毫秒");
    }
}
