package cn.iocoder.yudao.module.mall.external.ycrh;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.basisconfig.InvoiceConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.basisconfig.InvoiceConfigService;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade.AppOrderSplitOrderReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade.AppOrderSplitReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade.AppOrderSplitSkuReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade.AppTradeOrderSendInvoiceReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.bill.vo.BillCreateReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.OrderDetailRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.OrderSkuInfoRespVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.bill.BillDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderCancelTypeEnum;
import cn.iocoder.yudao.module.mall.trade.service.bill.BillService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.trade.service.settle.OrderSettleService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.ttl.TtlWrappers;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class YcrhTradeSettlementTest {

    @Resource
    private TradeOrderService tradeOrderService;
    @Resource
    private OrderSettleService orderSettleService;
    @Resource
    private InvoiceConfigService invoiceConfigService;
    @Resource
    private BillService billService;
    @Resource
    private AdminUserApi adminUserApi;

    @BeforeAll
    public static void init() {
        TenantContextHolder.setTenantId(150L);
    }
    @Test
    public void testOrderDelivery() {
        Long subOrderId = 1871116114029703168L;
        try {
            // 发票申请
            orderSettleService.invoiceApply(subOrderId);
        }
        catch (Exception e) {
        }
        TradeOrderDO tradeOrderDO = tradeOrderService.getOrder(subOrderId);
        // 接收第三方供应商传递的发票信息
        InvoiceConfigDO invoiceConfig = invoiceConfigService.getInvoiceConfig();
        AppTradeOrderSendInvoiceReqVO reqVO = new AppTradeOrderSendInvoiceReqVO();
        reqVO.setOrderId(subOrderId)
                .setCanInvoice(true)
                .setInvoiceDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"))
                .setInvoiceId(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 10))
                .setInvoicePrice(tradeOrderDO.getOrderPrice())
                .setFileUrl("https://dev-static.gomro.cn/test/foundation-finance/settle/bank-serial/pdf/202407/760697722799677440.pdf")
                .setTotalBatchInvoiceAmount(tradeOrderDO.getOrderPrice());
        orderSettleService.receiveInvoice4ThirdSupplier(tradeOrderDO, invoiceConfig, reqVO);
    }

    @Test
    public void testSplitOrder() throws ExecutionException, InterruptedException {
        List<AppOrderSplitReqVO> appOrderSplitReqVOS = new ArrayList<>();
        List<Long> orderIds = new ArrayList<>();
        orderIds.add(1870811019547185152L);
//        orderIds.add(1870008628484706304L);
//        orderIds.add(1869950144137334784L);
//        orderIds.add(1869945449121517568L);
//        orderIds.add(1869943463793856512L);
//        List<TradeOrderDO> tradeOrderDOS = tradeOrderService.list(new LambdaQueryWrapperX<TradeOrderDO>()
//                .eq(TradeOrderDO::getUserId, 248L)
//                .eq(TradeOrderDO::getStatus, TradeOrderStatusEnum.HAS_CONFIRM.getStatus()));
//        orderIds = tradeOrderDOS.stream().map(TradeOrderDO::getId).collect(Collectors.toList());
        for (Long orderId : orderIds) {
            OrderDetailRespVO orderDetailRespVO = tradeOrderService.getOrderDetail4Admin(orderId);
            if(CollectionUtil.isEmpty(orderDetailRespVO.getSkuInfoList())) {
                continue;
            }
            AppOrderSplitReqVO reqVO = new AppOrderSplitReqVO();
            reqVO.setParentOrderId(orderId);
            List<AppOrderSplitOrderReqVO> orderReqVOList = new ArrayList<>();
            for (int j = 0; j < orderDetailRespVO.getSkuInfoList().size(); j++) {
                OrderSkuInfoRespVO skuInfoRespVO = orderDetailRespVO.getSkuInfoList().get(j);
                AppOrderSplitOrderReqVO orderReqVO = new AppOrderSplitOrderReqVO();
                List<AppOrderSplitSkuReqVO> skuReqVOList = new ArrayList<>();
                AppOrderSplitSkuReqVO skuReqVO = new AppOrderSplitSkuReqVO();
                skuReqVO.setSkuId(skuInfoRespVO.getSkuId());
                skuReqVO.setCount(skuInfoRespVO.getSkuNum());
                skuReqVOList.add(skuReqVO);
                orderReqVO.setSkuReqVOList(skuReqVOList);
                orderReqVO.setThirdOrderId(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8));
                orderReqVOList.add(orderReqVO);
            }
            reqVO.setOrderReqVOList(orderReqVOList);
            appOrderSplitReqVOS.add(reqVO);
        }
        List<CompletableFuture<List<Long>>> futures = new ArrayList<>();
        for (int j = 0; j < orderIds.size(); j++) {
            int finalJ = j;
            futures.add(CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
                List<Long> subOrderIds = tradeOrderService.splitOrder(appOrderSplitReqVOS.get(finalJ));
                return subOrderIds;
            })));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{})).get();
        List<Long> completeOrderIds = new ArrayList<>();
        boolean flag = true;
        for (Future<List<Long>> future : futures) {
            List<Long> subOrderIds = future.get();
            log.info("subOrderIds:{}", subOrderIds);
            BillCreateReqVO billCreateReqVO = new BillCreateReqVO();
            billCreateReqVO.setBillName(String.format("测试账单_%s", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
            billCreateReqVO.setSupplierId(19L);
            AdminUserRespDTO user = adminUserApi.getUser(126L).getData();
            Long billId = billService.saveBill(user, billCreateReqVO);
            for (Long subOrderId : subOrderIds) {
                if(flag) {
                    // 取消第一个子订单
                    tradeOrderService.cancelOrder(subOrderId, TradeOrderCancelTypeEnum.MEMBER_CANCEL, "测试取消", true);
                    flag = false;
                    continue;
                }
                TradeOrderDO tradeOrderDO = tradeOrderService.getOrder(subOrderId);
                // 订单发货
                tradeOrderService.updateOrderDelivery(tradeOrderDO.getSupplierId(), subOrderId);
                // 订单妥投
                tradeOrderService.updateOrderDeliveryDone(tradeOrderDO.getSupplierId(), subOrderId);
                // 签收订单
                tradeOrderService.signOrder(subOrderId);
                // 交易订单完成
                tradeOrderService.completeOrder(subOrderId);
                try {
                    // 发票申请
                    orderSettleService.invoiceApply(subOrderId);
                }
                catch (Exception e) {
                }
                tradeOrderDO = tradeOrderService.getOrder(subOrderId);
                // 接收第三方供应商传递的发票信息
                InvoiceConfigDO invoiceConfig = invoiceConfigService.getInvoiceConfig();
                AppTradeOrderSendInvoiceReqVO reqVO = new AppTradeOrderSendInvoiceReqVO();
                reqVO.setOrderId(subOrderId)
                        .setCanInvoice(true)
                        .setInvoiceDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"))
                        .setInvoiceId(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 10))
                        .setInvoicePrice(tradeOrderDO.getOrderPrice())
                        .setFileUrl("https://dev-static.gomro.cn/test/foundation-finance/settle/bank-serial/pdf/202407/760697722799677440.pdf")
                        .setTotalBatchInvoiceAmount(tradeOrderDO.getOrderPrice());
                orderSettleService.receiveInvoice4ThirdSupplier(tradeOrderDO, invoiceConfig, reqVO);
                completeOrderIds.add(subOrderId);
                // 添加账单订单关联
                billService.addBillOrderRelation(billId, Arrays.asList(tradeOrderDO.getNo()));
            }
            // 推送账单
            billService.pushBill(billId);
            BillDO billDO = billService.getBill(billId);
            // 刷新账单状态
            billService.refreshOrderBillStatus(billDO);

            // 取消账单
            billService.cancelBill(billId);

            // 再次推送账单
            billDO = billService.getBill(billId);
            billService.refreshOrderBillStatus(billDO);
        }
    }

}
