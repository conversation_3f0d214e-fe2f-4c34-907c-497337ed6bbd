package cn.iocoder.yudao.module.mall.vop;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.MallServerApplication;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.trade.service.aftersale.VopAfterSalesBridgeService;
import cn.iocoder.yudao.module.mall.vop.service.messagehandle.messagevo.OrderAfterSellComponentVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

@Data
class MessageContent {
    private String content;
}

@SpringBootTest(classes = MallServerApplication.class)
@ActiveProfiles("local")
@Slf4j
public class TestAfterSaleVop {

    @Resource
    private SupplierService supplierService;

    @Resource
    private VopAfterSalesBridgeService vopAfterSalesBridgeService;

    @Test
    public void handleAfterSellComponent() {
        String jsonArray = "[\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":20,\\\"outApplyId\\\":\\\"0062171682328607671\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048699448,\\\"afsApplyId\\\":1864102571,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"refund\\\":false}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":20,\\\"outApplyId\\\":\\\"0062171682328607671\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048702691,\\\"afsApplyId\\\":1864102571,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"refund\\\":false}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":20,\\\"outApplyId\\\":\\\"0062171682328607671\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048702691,\\\"afsApplyId\\\":1864102571,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"refund\\\":false}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":30,\\\"outApplyId\\\":\\\"0064171682320400496\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048683660,\\\"afsApplyId\\\":1864102128,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":1315382,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":1315382,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"refund\\\":false}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":40,\\\"outApplyId\\\":\\\"0064171682320400496\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048683660,\\\"afsApplyId\\\":1864102128,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":1315382,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":1315382,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"afsServiceProcessResult\\\":40,\\\"refund\\\":false}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":60,\\\"outApplyId\\\":\\\"0064171682320400496\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048683660,\\\"afsApplyId\\\":1864102128,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":1315382,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":1315382,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"afsServiceProcessResult\\\":40,\\\"realRefundAmount\\\":12.870,\\\"refund\\\":true}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":50,\\\"outApplyId\\\":\\\"0064171682320400496\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048683660,\\\"afsApplyId\\\":1864102128,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":1315382,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":1315382,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"afsServiceProcessResult\\\":40,\\\"realRefundAmount\\\":12.870,\\\"refund\\\":true}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":50,\\\"outApplyId\\\":\\\"0064171682320400496\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048683660,\\\"afsApplyId\\\":1864102128,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":1315382,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":1315382,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"afsServiceProcessResult\\\":40,\\\"realRefundAmount\\\":12.870,\\\"refund\\\":true}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":40,\\\"outApplyId\\\":\\\"0079171682326801310\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048694221,\\\"afsApplyId\\\":1864103340,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"afsServiceProcessResult\\\":40,\\\"refund\\\":false}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":30,\\\"outApplyId\\\":\\\"0079171682326801310\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048694221,\\\"afsApplyId\\\":1864103340,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"refund\\\":false}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":60,\\\"outApplyId\\\":\\\"0079171682326801310\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048694221,\\\"afsApplyId\\\":1864103340,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"afsServiceProcessResult\\\":40,\\\"realRefundAmount\\\":24.210,\\\"refund\\\":true}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":50,\\\"outApplyId\\\":\\\"0079171682326801310\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048694221,\\\"afsApplyId\\\":1864103340,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"afsServiceProcessResult\\\":40,\\\"realRefundAmount\\\":24.210,\\\"refund\\\":true}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":50,\\\"outApplyId\\\":\\\"0079171682326801310\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048694221,\\\"afsApplyId\\\":1864103340,\\\"afsServiceApprovedResult\\\":31,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"afsServiceProcessResult\\\":40,\\\"realRefundAmount\\\":24.210,\\\"refund\\\":true}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":20,\\\"outApplyId\\\":\\\"0062171682328607671\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048702691,\\\"afsApplyId\\\":1864102571,\\\"afsServiceApprovedResult\\\":33,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"refund\\\":false}\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"content\": \"{\\\"questionDesc\\\":\\\"\\\",\\\"stepType\\\":20,\\\"outApplyId\\\":\\\"0062171682328607671\\\",\\\"orderId\\\":293415875376,\\\"afsServiceId\\\":2048699448,\\\"afsApplyId\\\":1864102571,\\\"afsServiceApprovedResult\\\":33,\\\"customerExpect\\\":10,\\\"wares\\\":[{\\\"annex\\\":false,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1},{\\\"annex\\\":true,\\\"wareId\\\":100003787989,\\\"wareNum\\\":1}],\\\"offline\\\":false,\\\"originalOrderId\\\":293415875376,\\\"refund\\\":false}\"\n" +
                "  }\n" +
                "]";

        // 使用fastjson解析
        List<MessageContent> contentList = JsonUtils.parseArray(jsonArray, MessageContent.class);

        TenantContextHolder.setTenantId(150L);

        // 输出解析的数据
        for (MessageContent content : contentList) {
            System.out.println(content.getContent());
            OrderAfterSellComponentVO afterSellComponentVO = JsonUtils.parseObject2(content.getContent(), OrderAfterSellComponentVO.class);
            log.info(afterSellComponentVO.toString());
            vopAfterSalesBridgeService.afterSaleComponentOrder4Vop(afterSellComponentVO);
        }
    }
}
